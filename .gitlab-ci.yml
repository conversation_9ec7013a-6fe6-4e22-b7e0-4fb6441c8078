stages:
  - scan
  - report
include:
  - project: mit/infra/ci-templates
    ref: master
    file: maven/scan.gitlab-ci.yml
variables:
  # 排除不需要Sonar扫描的文件
  SONAR_SCANNER_PARAMS: -Dsonar.exclusions=commons-config-app/src/main/java/com/mi/oa/asset/commons/config/app/util/TranslateUtil.java,commons-config-app/src/main/java/com/mi/oa/asset/commons/config/app/util/PullErrorMessageEntryUtil.java,com/mi/oa/asset/commons/config/app/provider/AssetSkuProviderImpl.java,com/mi/oa/asset/commons/config/app/ability/AssetSkuAbility.java,commons-config-infra/src/main/java/com/mi/oa/asset/commons/config/infra/rpc/ads/**,**/**Test.java,com/mi/oa/asset/commons/config/app/ability/AssetShareAbility.java -Dsonar.coverage.exclusions=**/com/mi/oa/asset/commons/config/infra/**/**.java,**/com/mi/oa/asset/commons/config/domain/**/**.java,**/com/mi/oa/asset/commons/config/api/**/**.java,**/*Req.java,**/*Res.java,**/**DO.java,**/**Do.java,**/**DTO.java,**/**Converter.java,**/**Dto.java,**/**VO.java,**/**Vo.java,**/**PO.java,**/**Po.java,**/**Model.java,**/**Entity.java,**/**Controller.java,**/com/mi/oa/asset/commons/config/app/ability/AssetShareAbility.java,**/com/mi/oa/asset/commons/config/app/util/TranslateUtil.java,**/com/mi/oa/asset/commons/config/app/util/PullErrorMessageEntryUtil.java,**/com/mi/oa/asset/commons/config/app/provider/AssetSkuProviderImpl.java,**/com/mi/oa/asset/commons/config/app/ability/AssetSkuAbility.java,commons-config-app/src/main/java/com/mi/oa/asset/commons/config/app/scheduler/SyncMeasurement.java