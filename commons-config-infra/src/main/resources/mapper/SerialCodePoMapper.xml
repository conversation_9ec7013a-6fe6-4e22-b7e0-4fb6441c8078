<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.SerialCodePoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.SerialCodePo">
    <!--@mbg.generated-->
    <!--@Table amg_serial_code-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="code_prefix" jdbcType="VARCHAR" property="codePrefix" />
    <result column="start_num" jdbcType="INTEGER" property="startNum" />
    <result column="index_code" jdbcType="VARCHAR" property="indexCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, code_prefix, start_num, index_code, create_time, update_time
  </sql>
</mapper>