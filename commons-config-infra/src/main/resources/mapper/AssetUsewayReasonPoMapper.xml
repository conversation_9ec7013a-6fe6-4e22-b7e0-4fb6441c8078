<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.AssetUsewayReasonPoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.AssetUsewayReasonPo">
        <result column="id" property="id" />
        <result column="useway_reason" property="usewayReason" />
        <result column="add_userid" property="addUserid" />
        <result column="add_date" property="addDate" />
        <result column="modify_userid" property="modifyUserid" />
        <result column="modify_date" property="modifyDate" />
        <result column="tenant_id" property="tenantId" />
        <result column="useway_id" property="usewayId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        useway_reason, add_userid, add_date, modify_userid, modify_date, tenant_id, useway_id
    </sql>

</mapper>
