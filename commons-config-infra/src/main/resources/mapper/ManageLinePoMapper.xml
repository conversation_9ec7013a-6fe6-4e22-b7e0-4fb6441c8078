<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.ManageLinePoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.ManageLinePo">
    <!--@mbg.generated-->
    <!--@Table amg_manage_line-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="manage_line" jdbcType="VARCHAR" property="manageLine" />
    <result column="manage_line_name" jdbcType="VARCHAR" property="manageLineName" />
    <result column="manager_user" jdbcType="VARCHAR" property="managerUser" />
    <result column="manager_user_name" jdbcType="VARCHAR" property="managerUserName" />
    <result column="approval_user" jdbcType="VARCHAR" property="approvalUser" />
    <result column="approval_user_name" jdbcType="VARCHAR" property="approvalUserName" />
    <result column="is_valid" jdbcType="VARCHAR" property="isValid" />
    <result column="is_new_line" jdbcType="VARCHAR" property="isNewLine" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="record_status" jdbcType="VARCHAR" property="recordStatus" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, manage_line, manage_line_name, manager_user, manager_user_name, approval_user, 
    approval_user_name, is_valid, is_new_line, create_user, create_time, update_user, 
    update_time, is_deleted, business_key, record_status, create_user_name, update_user_name
  </sql>
</mapper>