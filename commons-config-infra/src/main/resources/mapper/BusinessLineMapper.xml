<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.BusinessLineMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.BusinessLinePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
    <result column="business_line_name" jdbcType="VARCHAR" property="businessLineName" />
    <result column="business_line_name_en" jdbcType="VARCHAR" property="businessLineNameEn" />
    <result column="father_id" jdbcType="INTEGER" property="fatherId" />
    <result column="manage_line_code" jdbcType="VARCHAR" property="manageLineCode" />
    <result column="manage_line_name" jdbcType="VARCHAR" property="manageLineName" />
    <result column="is_effective" jdbcType="INTEGER" property="isEffective" />
    <result column="is_modify_company" jdbcType="INTEGER" property="isModifyCompany" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="is_new_line" jdbcType="CHAR" property="isNewLine" />
  </resultMap>
  <sql id="Base_Column_List">
    id, business_line, business_line_name, business_line_name_en, father_id, manage_line_code, manage_line_name,
    is_effective, is_modify_company,create_user, create_time, update_user, update_time, is_deleted, is_new_line
  </sql>

</mapper>