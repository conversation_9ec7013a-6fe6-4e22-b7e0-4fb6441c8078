<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.AssetReceiveAddressPoMapper">

    <select id="listCountry" resultType="com.mi.oa.asset.commons.config.api.myfunctions.Country">
        select country,country_name
        from asset_country_currency
        where bus_type = '0'
    </select>
</mapper>