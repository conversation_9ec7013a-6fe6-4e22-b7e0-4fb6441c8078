<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.CountryBusinessLinePoMapper">

    <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.CountryBusinessLinePo">
        <id property="id" column="id" />
        <result property="countryCodeAlphaThree" column="country_code_alpha_three" />
        <result property="countryCodeAlphaTwo" column="country_code_alpha_two" />
        <result property="countryName" column="country_name" />
        <result property="countryEnglishName" column="country_english_name" />
        <result property="provinceId" column="province_id" />
        <result property="timeZone" column="time_zone" />
        <result property="countryConfigId" column="country_config_id" />
        <result property="businessLine" column="business_line" />
        <result property="defaultLanguage" column="default_language" />
        <result property="isDeleted" column="is_deleted" />
        <result property="createUserName" column="create_user_name" />
        <result property="createUser" column="create_user" />
        <result property="createTime" column="create_time" />
        <result property="updateUserName" column="update_user_name" />
        <result property="updateUser" column="update_user" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,country_code_alpha_three,country_code_alpha_two,country_name,country_english_name,
        province_id,time_zone,country_config_id,business_line,default_language,
        is_deleted,create_user_name,create_user,create_time,update_user_name,
        update_user,update_time
    </sql>
</mapper>
