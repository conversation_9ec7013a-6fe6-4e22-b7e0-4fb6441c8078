<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.PositionPoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.PositionPo">
    <!--@mbg.generated-->
    <!--@Table amg_position-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="position_code" jdbcType="VARCHAR" property="positionCode" />
    <result column="position_name" jdbcType="VARCHAR" property="positionName" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="position_path" jdbcType="VARCHAR" property="positionPath" />
    <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="disabled" jdbcType="INTEGER" property="disabled" />
    <result column="out_sys_code" jdbcType="VARCHAR" property="outSysCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, position_code, position_name, parent_code, position_path, business_line, data_source, 
    disabled, out_sys_code, remark, is_deleted, create_user, create_time, update_user, 
    update_time
  </sql>
</mapper>