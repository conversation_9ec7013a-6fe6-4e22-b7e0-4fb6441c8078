<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.AssetCategoryPoMapper">
    <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.AssetCategoryPo">
        <!--@mbg.generated-->
        <!--@Table amg_asset_category-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="business_line" jdbcType="VARCHAR" property="businessLine"/>
        <result column="cate_code" jdbcType="VARCHAR" property="cateCode"/>
        <result column="cate_name" jdbcType="VARCHAR" property="cateName"/>
        <result column="cate_name_en" jdbcType="VARCHAR" property="cateNameEn"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="parent_cate_code" jdbcType="VARCHAR" property="parentCateCode"/>
        <result column="cate_path" jdbcType="VARCHAR" property="catePath"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="disabled" jdbcType="INTEGER" property="disabled"/>
        <result column="sap_cate_code" jdbcType="VARCHAR" property="sapCateCode"/>
        <result column="sap_cate_name" jdbcType="VARCHAR" property="sapCateName"/>
        <result column="use_year" jdbcType="INTEGER" property="useYear"/>
        <result column="use_month" jdbcType="INTEGER" property="useMonth"/>
        <result column="is_serial_code_manage" jdbcType="INTEGER" property="isSerialCodeManage"/>
        <result column="purchase_catalog_code" jdbcType="VARCHAR" property="purchaseCatalogCode"/>
        <result column="purchase_catalog_code_path" jdbcType="VARCHAR" property="purchaseCatalogCodePath"/>
        <result column="purchase_catalog_name" jdbcType="VARCHAR" property="purchaseCatalogName"/>
        <result column="purchase_catalog_name_path" jdbcType="VARCHAR" property="purchaseCatalogNamePath"/>
        <result column="data_source" jdbcType="VARCHAR" property="dataSource"/>
        <result column="is_multiple_manage" jdbcType="INTEGER" property="isMultipleManage"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="mg_model" jdbcType="VARCHAR" property="mgModel"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

</mapper>