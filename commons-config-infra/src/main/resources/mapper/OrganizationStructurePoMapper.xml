<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.OrganizationStructurePoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.OrganizationStructurePo">
    <!--@mbg.generated-->
    <!--@Table amg_organization_structure-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="org_name_en" jdbcType="VARCHAR" property="orgNameEn" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="default_cost_center" jdbcType="VARCHAR" property="defaultCostCenter" />
    <result column="is_virtual" jdbcType="INTEGER" property="isVirtual" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
    <result column="disabled" jdbcType="INTEGER" property="disabled" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_code, org_name, org_name_en, parent_code, default_cost_center, is_virtual, `level`, business_line,
    disabled, is_deleted, create_user, create_time, update_user, update_time
  </sql>
</mapper>