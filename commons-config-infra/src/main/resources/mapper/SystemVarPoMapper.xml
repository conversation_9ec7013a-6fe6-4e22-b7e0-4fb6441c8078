<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.SystemVarPoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.SystemVarPo">
    <!--@mbg.generated-->
    <!--@Table amg_system_var-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="var_code" jdbcType="VARCHAR" property="varCode" />
    <result column="var_desc" jdbcType="VARCHAR" property="varDesc" />
    <result column="var_value" jdbcType="VARCHAR" property="varValue" />
    <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, var_code, var_desc, var_value, business_line, is_deleted, create_user, create_user_name, 
    create_time, update_user, update_user_name, update_time
  </sql>
</mapper>