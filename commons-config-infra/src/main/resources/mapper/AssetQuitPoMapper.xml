<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.AssetQuitPoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.AssetQuitPo">
        <id column="quit_id" property="quitId" />
        <result column="user_name" property="userName" />
        <result column="user_code" property="userCode" />
        <result column="dept_name" property="deptName" />
        <result column="create_date" property="createDate" />
        <result column="dept_id" property="deptId" />
        <result column="quit_date" property="quitDate" />
        <result column="post_code" property="postCode" />
        <result column="post_name" property="postName" />
        <result column="emp_code" property="empCode" />
        <result column="dept_code" property="deptCode" />
        <result column="phone" property="phone" />
        <result column="post_id" property="postId" />
        <result column="add_userid" property="addUserid" />
        <result column="add_date" property="addDate" />
        <result column="modify_userid" property="modifyUserid" />
        <result column="modify_date" property="modifyDate" />
        <result column="tenant_id" property="tenantId" />
        <result column="quit_state" property="quitState" />
        <result column="office_address" property="officeAddress" />
        <result column="email" property="email" />
        <result column="is_quit" property="isQuit" />
        <result column="revoke_date" property="revokeDate" />
        <result column="confirm_user" property="confirmUser" />
        <result column="confirm_usercode" property="confirmUsercode" />
        <result column="confirm_date" property="confirmDate" />
        <result column="is_exceed" property="isExceed" />
        <result column="exceed_day" property="exceedDay" />
        <result column="use_num" property="useNum" />
        <result column="num_action" property="numAction" />
        <result column="dept_code_lv1" property="deptCodeLv1" />
        <result column="dept_name_lv1" property="deptNameLv1" />
        <result column="full_dept_name" property="fullDeptName" />
        <result column="have_pub_asset" property="havePubAsset" />
        <result column="country" property="country" />
        <result column="country_name" property="countryName" />
        <result column="is_push_ps" property="isPushPs" />
        <result column="total_money" property="totalMoney" />
        <result column="scrap_id" property="scrapId" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_name, user_code, dept_name, create_date, dept_id, quit_date, post_code, post_name, emp_code, dept_code, phone, post_id, quit_id, add_userid, add_date, modify_userid, modify_date, tenant_id, quit_state, office_address, email, is_quit, revoke_date, confirm_user, confirm_usercode, confirm_date, is_exceed, exceed_day, use_num, num_action, dept_code_lv1, dept_name_lv1, full_dept_name, have_pub_asset, country, country_name, is_push_ps, total_money, scrap_id, is_delete
    </sql>

    <select id="queryResignEmpByTime" resultType="com.mi.oa.asset.commons.config.infra.database.dataobject.AssetQuitPo">
        select * from asset_quit where (quit_date = #{dateTime} and quit_state = '0') or (DATE_FORMAT(confirm_date, '%Y-%m-%d') = #{dateTime})
    </select>
</mapper>
