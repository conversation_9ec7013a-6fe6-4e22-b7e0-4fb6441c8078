<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.RegionConfigPoMapper">
    <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.RegionConfigPo">
            <id property="id" column="id" />
            <result property="regionName" column="region_name" />
            <result property="regionEnglishName" column="region_english_name" />
            <result property="regionSortOrder" column="region_sort_order" />
            <result property="isDeleted" column="is_deleted" />
            <result property="createUserName" column="create_user_name" />
            <result property="createUser" column="create_user" />
            <result property="createTime" column="create_time" />
            <result property="updateUserName" column="update_user_name" />
            <result property="updateUser" column="update_user" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,region_name,region_english_name,region_sort_order,is_deleted,create_user_name,
        create_user,create_time,update_user_name,update_user,update_time
    </sql>
</mapper>
