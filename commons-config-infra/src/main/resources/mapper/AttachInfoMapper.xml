<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.AttachInfoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.AttachInfoPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="record_no" jdbcType="VARCHAR" property="recordNo" />
    <result column="record_type" jdbcType="VARCHAR" property="recordType" />
    <result column="origin_name" jdbcType="VARCHAR" property="originName" />
    <result column="attach_name" jdbcType="VARCHAR" property="attachName" />
    <result column="attach_link" jdbcType="VARCHAR" property="attachLink" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, record_id, record_no, record_type, origin_name, attach_name, attach_link, create_user, 
    create_time, update_user, update_time
  </sql>
</mapper>