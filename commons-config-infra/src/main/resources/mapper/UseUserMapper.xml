<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.UseUserMapper">

    <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.UseUserPo">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="useId" column="use_id" jdbcType="VARCHAR"/>
            <result property="userCode" column="user_code" jdbcType="VARCHAR"/>
            <result property="userName" column="user_name" jdbcType="VARCHAR"/>
            <result property="deptCode" column="dept_code" jdbcType="VARCHAR"/>
            <result property="deptName" column="dept_name" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,use_id,user_code,
        user_name,dept_code,dept_name,
        create_user,create_user_name,create_time,
        update_user,update_user_name,update_time,
        is_deleted
    </sql>
</mapper>
