<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.AssetUsewayPoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.AssetUsewayPo">
        <id column="useway_id" property="usewayId" />
        <result column="id" property="id" />
        <result column="useway_code" property="usewayCode" />
        <result column="useway_memo" property="usewayMemo" />
        <result column="useway_name" property="usewayName" />
        <result column="useway_type" property="usewayType" />
        <result column="add_date" property="addDate" />
        <result column="add_userid" property="addUserid" />
        <result column="modify_date" property="modifyDate" />
        <result column="modify_userid" property="modifyUserid" />
        <result column="tenant_id" property="tenantId" />
        <result column="useway_qualif" property="usewayQualif" />
        <result column="is_valid" property="isValid" />
        <result column="eam_code" property="eamCode" />
        <result column="useway_name_en" property="usewayNameEn" />
        <result column="useway_memo_en" property="usewayMemoEn" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        useway_code, useway_memo, useway_name, useway_type, useway_id, add_date, add_userid, modify_date, modify_userid, tenant_id, useway_qualif, is_valid, eam_code, useway_name_en, useway_memo_en
    </sql>

</mapper>
