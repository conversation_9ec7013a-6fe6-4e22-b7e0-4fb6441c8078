<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.LabelTemplateMapper">

    <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.LabelTemplatePo" >
        <result column="id" property="id" />
        <result column="template_name" property="templateName" />
        <result column="template_type" property="templateType" />
        <result column="template_style" property="templateStyle" />
        <result column="business_line" property="businessLine" />
        <result column="is_default" property="isDefault" />
        <result column="label_width" property="labelWidth" />
        <result column="label_height" property="labelHeight" />
        <result column="margin_top" property="marginTop" />
        <result column="margin_bottom" property="marginBottom" />
        <result column="margin_left" property="marginLeft" />
        <result column="margin_right" property="marginRight" />
        <result column="scan_display_field" property="scanDisplayField" />
        <result column="title_name" property="titleName" />
        <result column="title_font_size" property="titleFontSize" />
        <result column="title_font_style" property="titleFontStyle" />
        <result column="title_alignment" property="titleAlignment" />
        <result column="barcode_type" property="barcodeType" />
        <result column="barcode_position" property="barcodePosition" />
        <result column="barcode_width" property="barcodeWidth" />
        <result column="barcode_height" property="barcodeHeight" />
        <result column="barcode_scale_factor" property="barcodeScaleFactor" />
        <result column="barcode_display_field" property="barcodeDisplayField" />
        <result column="code_display_field" property="codeDisplayField" />
        <result column="is_show" property="isShow" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                template_name,
                template_type,
                template_style,
                business_line,
                is_default,
                label_width,
                label_height,
                margin_top,
                margin_bottom,
                margin_left,
                margin_right,
                scan_display_field,
                title_name,
                title_font_size,
                title_font_style,
                title_alignment,
                barcode_type,
                barcode_position,
                barcode_width,
                barcode_height,
                barcode_scale_factor,
                barcode_display_field,
                code_display_field,
                is_show,
                create_user,
                create_user_name,
                create_time,
                update_user,
                update_user_name,
                update_time,
                is_deleted
    </sql>

</mapper>