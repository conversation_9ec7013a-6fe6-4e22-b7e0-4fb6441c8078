<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.AsyncTaskPoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.AsyncTaskPo">
    <!--@mbg.generated-->
    <!--@Table amg_async_task-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="execution_status" jdbcType="VARCHAR" property="executionStatus" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
    <result column="record_no" jdbcType="VARCHAR" property="recordNo" />
    <result column="execution_result" jdbcType="VARCHAR" property="executionResult" />
    <result column="origin_file_url" jdbcType="VARCHAR" property="originFileUrl" />
    <result column="result_file_url" jdbcType="VARCHAR" property="resultFileUrl" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, execution_status, task_type, business_line, record_no, execution_result, origin_file_url, 
    result_file_url, create_user, create_user_name, create_time, update_user, update_user_name, 
    update_time, is_deleted
  </sql>
</mapper>