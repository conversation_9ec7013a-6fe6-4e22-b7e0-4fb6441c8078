<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.AssetSkuPoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.AssetSkuPo">
    <!--@mbg.generated-->
    <!--@Table amg_asset_sku-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="sku_name_en" jdbcType="VARCHAR" property="skuNameEn" />
    <result column="alias_name" jdbcType="VARCHAR" property="aliasName" />
    <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="cate_id" jdbcType="INTEGER" property="cateId" />
    <result column="cate_code" jdbcType="VARCHAR" property="cateCode" />
    <result column="cate_name" jdbcType="VARCHAR" property="cateName" />
    <result column="mi_sku_code" jdbcType="VARCHAR" property="miSkuCode" />
    <result column="mi_goods_id" jdbcType="VARCHAR" property="miGoodsId" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="spec" jdbcType="VARCHAR" property="spec" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="new_old" jdbcType="VARCHAR" property="newOld" />
    <result column="material_type" jdbcType="VARCHAR" property="materialType" />
    <result column="is_multiple_manage" jdbcType="INTEGER" property="isMultipleManage" />
    <result column="is_sn" jdbcType="INTEGER" property="isSn" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="project_phase" jdbcType="VARCHAR" property="projectPhase" />
    <result column="sale_date" jdbcType="TIMESTAMP" property="saleDate" />
    <result column="measure_code" jdbcType="VARCHAR" property="measureCode" />
    <result column="measure_name" jdbcType="VARCHAR" property="measureName" />
    <result column="disabled" jdbcType="INTEGER" property="disabled" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="mdm_create_status" javaType="INTEGER" property="mdmCreateStatus"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sku_code, sku_name, sku_name_en, alias_name, business_line, price, cate_id, cate_code,
    cate_name, mi_sku_code, mi_goods_id, brand, spec,model,new_old, material_type, is_multiple_manage,
    is_sn, product_name,project_code,project_phase, sale_date,measure_code, measure_name, disabled, remark, is_deleted, create_user, create_user_name,
    create_time, update_user, update_user_name, update_time, mdm_create_status
  </sql>

  <select id="pageSku" resultType="com.mi.oa.asset.commons.config.infra.database.dataobject.AssetSkuPo">
    SELECT
    s.id,
    s.sku_code,
    s.sku_name,
    s.sku_name_en,
    s.alias_name,
    s.price,
    s.cate_id,
    s.cate_code,
    s.cate_name,
    s.mi_sku_code,
    s.mi_goods_id,
    s.country,
    s.country_name,
    s.brand,
    s.spec,
    s.model,
    s.new_old,
    s.material_type,
    s.is_multiple_manage,
    s.is_sn,
    s.measure_code,
    s.measure_name,
    s.disabled,
    s.product_name,
    s.project_code,
    s.project_phase,
    s.sale_date,
    s.remark,
    s.is_deleted,
    s.create_user,
    s.create_user_name,
    s.create_time,
    s.update_user,
    s.update_user_name,
    s.update_time,
    s.mdm_create_status,
    GROUP_CONCAT(DISTINCT m.business_line SEPARATOR ',') AS business_line
    FROM
    amg_asset_sku_manage m
    LEFT JOIN
    amg_asset_sku s ON m.sku_id = s.id
    <where>
      s.is_deleted = 0
      AND m.is_deleted = 0
      <if test="keyword != null and keyword != ''">
        and (s.sku_code like concat('%',#{keyword},'%')
        or s.sku_name like concat('%',#{keyword},'%')
        or s.sku_name_en like concat('%',#{keyword},'%')
        or m.cate_code like concat('%',#{keyword},'%')
        or s.mi_goods_id like concat('%',#{keyword},'%'))
      </if>
      <if test="disabled != null">
        and s.disabled = #{disabled}
      </if>
      <if test="containMiGoods != null and containMiGoods == false">
        and s.mi_goods_id = '' and s.mi_sku_code = ''
      </if>
      <if test="relationCateIds != null and relationCateIds.size() > 0">
        and m.cate_id in
        <foreach collection="relationCateIds" item="cateId" open="(" separator="," close=")">
          #{cateId}
        </foreach>
      </if>
      <if test="businessCodes != null and businessCodes.size() > 0">
        and (m.business_line in
        <foreach collection="businessCodes" item="businessCode" open="(" separator="," close=")">#{businessCode}</foreach>
        )
      </if>
    </where>
    GROUP BY s.id
    ORDER BY s.id DESC, s.update_time DESC
  </select>

</mapper>