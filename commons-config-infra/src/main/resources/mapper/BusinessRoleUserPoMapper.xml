<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.BusinessRoleUserPoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.BusinessRoleUserPo">
    <!--@mbg.generated-->
    <!--@Table amg_business_role_user-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="role_code" jdbcType="VARCHAR" property="roleCode" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="role_desc" jdbcType="VARCHAR" property="roleDesc" />
    <result column="user_code" jdbcType="VARCHAR" property="userCode" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, role_code, role_name, role_desc, user_code, user_name, dept_name, avatar, org_code, 
    business_line, is_deleted, create_user, create_time, update_user, update_time, create_user_name, 
    update_user_name
  </sql>
</mapper>