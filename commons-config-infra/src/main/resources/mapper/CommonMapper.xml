<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mi.oa.asset.eam.mybatis.CommonMapper">
    <insert id="commonInsert" parameterType="java.util.Map" useGeneratedKeys="true" keyProperty="valueMap.id">
        ${sql}
    </insert>

    <update id="commonUpdate">
        ${sql}
    </update>

    <select id="commonQuery" resultType="com.mi.oa.asset.eam.mybatis.CamelKeyMap" parameterType="java.util.Map">
        ${sql}
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.Map">
        ${sql}
    </select>
</mapper>