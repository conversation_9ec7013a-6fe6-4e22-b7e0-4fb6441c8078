<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.OrganizationUnitPoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.OrganizationUnitPo">
    <!--@mbg.generated-->
    <!--@Table amg_organization_unit-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="org_name_en" jdbcType="VARCHAR" property="orgNameEn" />
    <result column="alias_name" jdbcType="VARCHAR" property="aliasName" />
    <result column="default_cost_center" jdbcType="VARCHAR" property="defaultCostCenter" />
    <result column="org_code_path" jdbcType="VARCHAR" property="orgCodePath" />
    <result column="org_name_path" jdbcType="VARCHAR" property="orgNamePath" />
    <result column="org_name_path_en" jdbcType="VARCHAR" property="orgNamePathEn" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="is_virtual" jdbcType="INTEGER" property="isVirtual" />
    <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
    <result column="is_asset_manage_org" jdbcType="INTEGER" property="isAssetManageOrg" />
    <result column="is_asset_use_org" jdbcType="INTEGER" property="isAssetUseOrg" />
    <result column="org_type" jdbcType="VARCHAR" property="orgType" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="cost_center" jdbcType="VARCHAR" property="costCenter" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_code, org_name,org_name_en, alias_name, default_cost_center, org_code_path, org_name_path, org_name_path_en,
    parent_code, `level`, is_virtual, business_line, is_asset_manage_org, is_asset_use_org, 
    org_type, company_code, company_name, cost_center, address, is_deleted, create_user, 
    create_time, update_user, update_time
  </sql>

  <select id="getUseOrgDataRange" resultType="com.mi.oa.asset.commons.config.infra.database.dataobject.OrganizationUnitPo">
    SELECT DISTINCT o.* from amg_organization_unit o,amg_business_role_user r
    WHERE o.is_asset_use_org = 1 and o.org_code = r.org_code and o.is_deleted =0 and r.is_deleted = 0
    and  r.user_code = #{userName} and o.business_line in
    <foreach collection="businessLine" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>

  <select id="getManageOrgDataRange" resultType="com.mi.oa.asset.commons.config.infra.database.dataobject.OrganizationUnitPo">
    SELECT DISTINCT o.* from amg_organization_unit o,amg_business_role_user r
    WHERE o.is_asset_manage_org = 1 and o.org_code = r.org_code and o.is_deleted =0 and r.is_deleted = 0
    and  r.user_code = #{userName} and  o.business_line in
    <foreach collection="businessLine" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>
</mapper>