<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.QueryConfigPoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.QueryConfigPo">
    <!--@mbg.generated-->
    <!--@Table amg_query_config-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="manage_line" jdbcType="VARCHAR" property="manageLine" />
    <result column="fun_id" jdbcType="VARCHAR" property="funId" />
    <result column="query_name" jdbcType="VARCHAR" property="queryName" />
    <result column="query_cond" jdbcType="VARCHAR" property="queryCond" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, manage_line, fun_id, query_name, query_cond, create_user, create_user_name, create_time,
    update_user, update_user_name, update_time, is_deleted
  </sql>
</mapper>