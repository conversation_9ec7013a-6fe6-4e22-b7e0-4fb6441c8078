<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.CountryCurrencyPoMapper">

    <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.CountryCurrencyPo">
        <id property="id" column="id" />
        <result property="currencyCode" column="currency_code" />
        <result property="currencyName" column="currency_name" />
        <result property="currencySymbol" column="currency_symbol" />
        <result property="defaultCurrency" column="default_currency" />
        <result property="countryConfigId" column="country_config_id" />
        <result property="countryCodeAlphaThree" column="country_code_alpha_three" />
        <result property="countryCodeAlphaTwo" column="country_code_alpha_two" />
        <result property="countryName" column="country_name" />
        <result property="isLimitDecimal" column="is_limit_decimal" />
        <result property="isDeleted" column="is_deleted" />
        <result property="createUserName" column="create_user_name" />
        <result property="createUser" column="create_user" />
        <result property="createTime" column="create_time" />
        <result property="updateUserName" column="update_user_name" />
        <result property="updateUser" column="update_user" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,currency_code,currency_name,currency_symbol,defaultCurrency,country_config_id,country_code_alpha_three,
        country_code_alpha_two,country_name,is_limit_decimal,is_deleted,create_user_name,
        create_user,create_time,update_user_name,update_user,update_time
    </sql>
</mapper>
