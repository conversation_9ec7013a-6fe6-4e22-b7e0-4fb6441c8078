<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.AllotConfigPoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.AllotConfigPo">
    <!--@mbg.generated-->
    <!--@Table amg_allot_config-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_line_id" jdbcType="INTEGER" property="businessLineId" />
    <result column="field_code" jdbcType="VARCHAR" property="fieldCode" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="field_property" jdbcType="VARCHAR" property="fieldProperty" />
    <result column="data_rage" jdbcType="VARCHAR" property="dataRage" />
    <result column="limit_biz_data" jdbcType="VARCHAR" property="limitBizData" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, business_line_id, field_code, field_name, field_property, data_rage, limit_biz_data, 
    create_user, create_user_name, create_time, update_user, update_user_name, update_time, 
    is_deleted
  </sql>
</mapper>