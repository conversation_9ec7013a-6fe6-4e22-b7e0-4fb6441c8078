<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.CompanyPoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.CompanyPo">
    <!--@mbg.generated-->
    <!--@Table amg_company-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="disabled" jdbcType="INTEGER" property="disabled" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, company_code, company_name, country_code, disabled, create_time, update_time
  </sql>
</mapper>