<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.PurchaseCatalogPoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.PurchaseCatalogPo">
    <!--@mbg.generated-->
    <!--@Table amg_purchase_catalog-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="catalog_code" jdbcType="VARCHAR" property="catalogCode" />
    <result column="catalog_name" jdbcType="VARCHAR" property="catalogName" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="sap_cate_code" jdbcType="VARCHAR" property="sapCateCode" />
    <result column="sap_cate_name" jdbcType="VARCHAR" property="sapCateName" />
    <result column="use_year" jdbcType="INTEGER" property="useYear" />
    <result column="disabled" jdbcType="INTEGER" property="disabled" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, catalog_code, `catalog_name`, parent_code, `level`, sap_cate_code, sap_cate_name, 
    use_year, disabled, create_time, update_time
  </sql>
</mapper>