<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.AssetSapTypeMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.AssetSapTypePo">
    <id column="acc_id" jdbcType="VARCHAR" property="accId" />
    <result column="acc_name" jdbcType="VARCHAR" property="accName" />
    <result column="acc_code" jdbcType="VARCHAR" property="accCode" />
    <result column="use_year" jdbcType="DECIMAL" property="useYear" />
    <result column="dep_rate" jdbcType="DECIMAL" property="depRate" />
    <result column="less_rate" jdbcType="DECIMAL" property="lessRate" />
    <result column="acc_memo" jdbcType="VARCHAR" property="accMemo" />
    <result column="use_month" jdbcType="DECIMAL" property="useMonth" />
    <result column="dep_code" jdbcType="VARCHAR" property="depCode" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="is_qy" jdbcType="VARCHAR" property="isQy" />
    <result column="is_valid" jdbcType="CHAR" property="isValid" />
    <result column="scrap_type" jdbcType="VARCHAR" property="scrapType" />
    <result column="settle_account" jdbcType="VARCHAR" property="settleAccount" />
    <result column="acc_level" jdbcType="DECIMAL" property="accLevel" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
  </resultMap>
  <sql id="Base_Column_List">
    acc_id, acc_name, acc_code, use_year, dep_rate, less_rate, acc_memo, use_month, dep_code, 
    add_userid, add_date, modify_userid, modify_date, tenant_id, is_qy, is_valid, scrap_type, 
    settle_account, acc_level, company_code
  </sql>

</mapper>