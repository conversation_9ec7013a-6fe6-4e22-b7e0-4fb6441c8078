<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.ProviderPoMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.ProviderPo">
    <!--@mbg.generated-->
    <!--@Table amg_provider-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="provider_code" jdbcType="VARCHAR" property="providerCode" />
    <result column="provider_name" jdbcType="VARCHAR" property="providerName" />
    <result column="disabled" jdbcType="INTEGER" property="disabled" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, provider_code, provider_name, disabled, create_time, update_time
  </sql>
</mapper>