<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.LabelTemplateUserDefaultMapper">

    <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.LabelTemplateUserDefaultPo" >
        <result column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="business_line" property="businessLine" />
        <result column="template_id" property="templateId" />
        <result column="recently_used_template_id" property="recentlyUsedTemplateId" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                user_id,
                business_line,
                template_id,
                recently_used_template_id,
                create_user,
                create_user_name,
                create_time,
                update_user,
                update_user_name,
                update_time,
                is_deleted
    </sql>

</mapper>