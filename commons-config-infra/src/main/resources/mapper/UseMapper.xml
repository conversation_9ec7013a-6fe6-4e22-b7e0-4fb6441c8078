<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.UseMapper">

    <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.UsePo">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="businessLine" column="business_line" jdbcType="VARCHAR"/>
            <result property="useCode" column="use_code" jdbcType="VARCHAR"/>
            <result property="useName" column="use_name" jdbcType="VARCHAR"/>
            <result property="useNameEn" column="use_name_en" jdbcType="VARCHAR"/>
            <result property="useType" column="use_type" jdbcType="INTEGER"/>
            <result property="disabled" column="disabled" jdbcType="INTEGER"/>
            <result property="sorted" column="sorted" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="remarkEn" column="remark_en" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,business_line,use_code,
        use_name, use_name_en, use_type, disabled, sorted,
        remark, remark_en, create_user,create_user_name,
        create_time,update_user,update_user_name,
        update_time,is_deleted
    </sql>
</mapper>
