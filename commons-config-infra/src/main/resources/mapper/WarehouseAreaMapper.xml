<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.WarehouseAreaMapper">

  <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.WarehouseAreaPo">
    <id property="id" column="id" jdbcType="INTEGER"/>
    <result property="warehouseCode" column="warehouse_code" jdbcType="VARCHAR"/>
    <result property="warehouseName" column="warehouse_name" jdbcType="VARCHAR"/>
    <result property="businessLine" column="business_line" jdbcType="VARCHAR"/>
    <result property="priority" column="priority" jdbcType="INTEGER"/>
    <result property="areaId" column="area_id" jdbcType="VARCHAR"/>
    <result property="areaName" column="area_name" jdbcType="VARCHAR"/>
    <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
    <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
    <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
  </resultMap>

  <sql id="Base_Column_List">
    id,warehouse_code,warehouse_name,
        business_line,priority,area_id,
        area_name,create_user,create_user_name,
        create_time,update_user,update_user_name,
        update_time,is_deleted
  </sql>
</mapper>
