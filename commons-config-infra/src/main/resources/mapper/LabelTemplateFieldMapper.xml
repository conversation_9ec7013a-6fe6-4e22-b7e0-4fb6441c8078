<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.commons.config.infra.database.mapper.LabelTemplateFieldMapper">

    <resultMap id="BaseResultMap" type="com.mi.oa.asset.commons.config.infra.database.dataobject.LabelTemplateFieldPo" >
        <result column="id" property="id" />
        <result column="template_id" property="templateId" />
        <result column="field_name" property="fieldName" />
        <result column="field_desc" property="fieldDesc" />
        <result column="field_margin_top" property="fieldMarginTop" />
        <result column="field_margin_bottom" property="fieldMarginBottom" />
        <result column="field_order" property="fieldOrder" />
        <result column="font_size" property="fontSize" />
        <result column="font_style" property="fontStyle" />
        <result column="alignment" property="alignment" />
        <result column="data_source" property="dataSource" />
        <result column="is_hide" property="isHide" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                template_id,
                field_name,
                field_desc,
                field_margin_top,
                field_margin_bottom,
                field_order,
                font_size,
                font_style,
                alignment,
                data_source,
                is_hide,
                create_user,
                create_user_name,
                create_time,
                update_user,
                update_user_name,
                update_time,
                is_deleted
    </sql>



</mapper>