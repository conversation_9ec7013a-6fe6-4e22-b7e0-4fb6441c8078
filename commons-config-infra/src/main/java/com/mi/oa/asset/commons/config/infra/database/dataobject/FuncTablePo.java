package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/2/25 14:14
 */

@Data
@TableName(value = "amg_func_table")
public class FuncTablePo extends BasePo {

    @TableField(value = "title")
    private String title;

    @TableField(value = "table_name")
    private String tableName;

    @TableField(value = "code")
    private String code;

    @TableField(value = "remark")
    private String remark;

    @TableField(value = "parent_id")
    private Integer parentId;

    @TableField(value = "valid")
    private Integer valid;
}
