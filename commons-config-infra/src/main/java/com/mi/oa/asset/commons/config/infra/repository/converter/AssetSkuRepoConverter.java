package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSku;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetSkuPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/9 14:45
 */

@Mapper(componentModel = "spring")
public interface AssetSkuRepoConverter extends CommonConverter {

    @Mapping(source = "skuId", target = "id")
    AssetSkuPo toAssetSkuPo(AssetSku assetSku);

    List<AssetSkuPo> toAssetSkuPos(List<AssetSku> assetSkus);

    @Mapping(source = "id", target = "skuId")
    AssetSku toAssetSku(AssetSkuPo assetSkuPo);

    List<AssetSku> toAssetSkus(List<AssetSkuPo> assetSkuPos);
}
