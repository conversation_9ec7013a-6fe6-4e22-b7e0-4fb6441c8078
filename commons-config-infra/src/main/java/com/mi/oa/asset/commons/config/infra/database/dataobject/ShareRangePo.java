package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025-01-06 15:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "amg_share_range")
public class ShareRangePo extends BasePo {
    /**
     * 共享记录主键
     */
    private Integer shareId;

    /**
     * 筛选字段编码
     */
    private String fieldCode;

    /**
     * 运算逻辑符号
     */
    private String queryCond;

    /**
     * 目标值
     */
    private String fieldValues;
}

