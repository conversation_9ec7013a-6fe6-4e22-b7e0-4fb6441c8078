package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.queryfield.entity.FieldConfig;
import com.mi.oa.asset.commons.config.domain.queryfield.repository.FieldConfigRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FieldConfigPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.FieldConfigPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.QueryFieldRepoConverter;
import com.mi.oa.asset.eam.auth.AuthFacade;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant.COMMA;

/**
 * <AUTHOR>
 * @date 2024/1/9 15:29
 */

@Service
public class FieldConfigRepoImpl extends ServiceImpl<FieldConfigPoMapper, FieldConfigPo> implements FieldConfigRepo {
    @Resource
    QueryFieldRepoConverter queryFieldConverter;

    @Override
    public FieldConfig searchFieldConfig(String manageLine, String funId, String userName) {
        List<String> manageLineList = StringUtils.isEmpty(manageLine) ? Collections.emptyList() : Arrays.asList(manageLine.split(COMMA));
        FieldConfigPo fieldConfigPo = getOne(new LambdaQueryWrapper<FieldConfigPo>()
                .in(!CollectionUtils.isEmpty(manageLineList), FieldConfigPo::getManageLine, manageLineList)
                .eq(FieldConfigPo::getFunId, funId)
                .eq(FieldConfigPo::getCreateUser, userName)
                .last("limit 1"));

        return queryFieldConverter.fieldConfigPoToDo(fieldConfigPo);
    }

    @Override
    public void saveFieldConfig(FieldConfig fieldConfig) {
        String userName = AuthFacade.authedUserName();
        saveFieldConfigImpl(fieldConfig, userName);
    }
    
    /**
     * 用于测试的方法，便于为AuthFacade.authedUserName()提供测试值
     * @param fieldConfig 字段配置
     * @param userName 用户名
     */
    protected void saveFieldConfigImpl(FieldConfig fieldConfig, String userName) {
        FieldConfig config = this.searchFieldConfig(fieldConfig.getManageLine(), fieldConfig.getFunId(), userName);
        if (config != null) {
            fieldConfig.setFieldId(config.getFieldId());
        }
        FieldConfigPo fieldConfigPo = queryFieldConverter.fieldConfigToPo(fieldConfig);
        saveOrUpdate(fieldConfigPo);
        fieldConfig.setFieldId(fieldConfigPo.getId());
    }
}
