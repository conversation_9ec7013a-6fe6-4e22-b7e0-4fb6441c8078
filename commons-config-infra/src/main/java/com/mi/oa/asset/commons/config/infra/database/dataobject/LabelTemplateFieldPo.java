package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @description 标签模版字段设置表
 * <AUTHOR>
 * @date 2025-02-14
 */
@Data
@TableName(value = "amg_label_template_field")
public class LabelTemplateFieldPo implements Serializable {

    /**
    * 所属模版ID
    */
    private Integer templateId;

    /**
    * 字段编码
    */
    private String fieldName;

    /**
    * 字段名称
    */
    private String fieldDesc;

    /**
    * 字段上间距
    */
    private String fieldMarginTop;

    /**
    * 字段下间距
    */
    private String fieldMarginBottom;

    /**
    * 字段顺序
    */
//    private String fieldOrder;

    /**
    * 字段字号
    */
    private String fontSize;

    /**
    * 字段字体样式 正常：normal、加粗：bold
    */
    private String fontStyle;

    /**
    * 字段对齐方式 左对齐：left，居中对齐:center，右对齐:right
    */
    private String alignment;

    /**
    * 数据来源
    */
//    private String dataSource;

    /**
    * 是否隐藏字段名称，0-否，1-是
    */
    private Integer isHide;

}