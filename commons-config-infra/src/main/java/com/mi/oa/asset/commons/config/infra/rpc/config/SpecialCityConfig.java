package com.mi.oa.asset.commons.config.infra.rpc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 特殊城市配置
 * @Date 2024/7/17 21:44
 **/

@RefreshScope
@ConfigurationProperties(prefix = "configs.ads")
@Component
@Data
public class SpecialCityConfig {

    private List<String> cityNames;

    public List<String> getCityNames() {
        return cityNames;
    }

}
