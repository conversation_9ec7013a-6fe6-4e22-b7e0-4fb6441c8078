/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.protocol.TBinaryProtocol;
import org.mi.thrift.protocol.TCompactProtocol;
import org.mi.thrift.protocol.TProtocol;
import org.mi.thrift.protocol.TProtocolException;
import org.mi.thrift.rpc.XRpc;
import org.mi.thrift.rpc.XService;
import org.mi.thrift.rpc.util.XUtil;
import org.mi.thrift.transport.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;


public class AddressServiceWrapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(AddressServiceWrapper.class);
    private String service_ = null;
    private static int MaxConnRetry = 3;
    private static int DefReadTimeout = 1000;

    private XContext context_ = null;
    private ConcurrentHashMap<String, Integer> timeoutMap_ = null;
    private int timeout_ = 0;
    private String invokeId;

    private XRpc rpc_ = null;
    private String balanceKey;

    public AddressServiceWrapper(String service, long traceId, String... invokeId) {
        if (invokeId != null && invokeId.length > 0) {
            this.invokeId = invokeId[0];
        } else {
            this.invokeId = null;
        }
        service_ = service;
        timeoutMap_ = new ConcurrentHashMap<String, Integer>();
        rpc_ = XRpc.getInstance();
        context_ = new XContext(rpc_.getServiceName(), traceId, 0);
    }

    public AddressServiceWrapper(String service, XContext context, String... invokeId) {

        if (invokeId != null && invokeId.length > 0) {
            this.invokeId = invokeId[0];
        } else {
            this.invokeId = null;
        }
        context_ = new XContext(context);
        service_ = service;
        timeoutMap_ = new ConcurrentHashMap<String, Integer>();
        rpc_ = XRpc.getInstance();
        context.setAppId(rpc_.getServiceName());
    }

    public void setBalanceKey(String balanceKey) {
        this.balanceKey = balanceKey;
    }

    public void setTimeout(int timeout) {
        if (timeout <= 0) {
            return;
        }

        timeout_ = timeout;
    }

    public void setTimeout(String method, int timeout) {
        if (timeout <= 0) {
            return;
        }

        timeoutMap_.put(method, new Integer(timeout));
    }

    public int getTimeout(String method, XService service) {

        int timeout = service.getReadTimeout();
        Integer timeoutInt = timeoutMap_.get(method);

        if (timeoutInt != null) {
            timeout = timeoutInt.intValue();
        } else {
            if (timeout_ > 0) {
                timeout = timeout_;
            }
        }

        if (timeout <= 0) {
            timeout = DefReadTimeout;
        }

        return timeout;
    }

    public TSocket getConn(String method, XService service) throws Exception {

        TSocket sock = null;
        Exception save = new Exception("Unknown exception");
        for (int i = 0; i < MaxConnRetry; i++) {
            try {
                sock = rpc_.getConn(service_, balanceKey);
                int timeout = getTimeout(method, service);
                sock.setTimeout(timeout);
                return sock;
            } catch (Exception e) {
                save = e;
                rpc_.putConn(sock, true);
                continue;
            }
        }

        rpc_.putConn(sock, true);
        throw save;
    }

    public TTransport getTransport(TSocket sock, XService service) throws Exception {

        String transName = service.getTransport();
        TTransport transport = null;

        if (transName.equals("xm_header")) {
            return new TXmHeaderTransport(sock);
        } else if (transName.equals("frame")) {
            return new TFramedTransport(sock);
        } else if (transName.equals("tcp")) {
            return sock;
        }

        throw new Exception("not found transport " + transName + ", service:" + service_);
    }

    public TProtocol getProtocol(TTransport transport, XService service) throws Exception {

        String protocolName = service.getProtocol();
        TProtocol protocol = null;
        if (protocolName.equals("binary")) {
            return new TBinaryProtocol(transport);
        } else if (protocolName.equals("compact")) {
            return new TCompactProtocol(transport);
        }

        throw new Exception("not found protocol " + protocolName + ", service:" + service_);
    }

    public Response getCountry(CountryRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("getCountry", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.getCountry(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getCountry", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getCountry", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }

    public Response getProvince(ProvinceRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("getProvince", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.getProvince(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getProvince", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getProvince", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }

    public Response getCity(CityRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("getCity", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.getCity(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getCity", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getCity", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }

    public Response getDistrict(DistrictRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("getDistrict", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.getDistrict(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getDistrict", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getDistrict", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }

    public Response GetStreet(StreetRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("GetStreet", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.getStreet(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "GetStreet", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "GetStreet", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }

    public Response syncData(SyncRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("syncData", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.syncData(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "syncData", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "syncData", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }

    public Response getInfoByIds(IdRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("getInfoByIds", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.getInfoByIds(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getInfoByIds", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getInfoByIds", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }

    public Response getInfoByNames(NameRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("getInfoByNames", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.getInfoByNames(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getInfoByNames", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getInfoByNames", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }

    public Response getSubset(SubsetRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("getSubset", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.getSubset(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getSubset", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getSubset", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }

    public Response getRegionTag(RegionTagRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("getRegionTag", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.getRegionTag(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getRegionTag", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getRegionTag", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }

    public Response getRegionTagByIds(TagIdRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("getRegionTagByIds", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.getRegionTagByIds(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getRegionTagByIds", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getRegionTagByIds", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }

    public Response getIndRegionTag(RegionTagRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("getIndRegionTag", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.getIndRegionTag(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getIndRegionTag", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getIndRegionTag", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }

    public List<Integer> getCountryNum(CountryNumRequest request) throws Exception {
        long startTime = System.currentTimeMillis();
        XService service = rpc_.getService(service_);
        Exception save = new Exception("Unknown exception");
        int maxRetry = service.getMaxRetry();
        for (int i = 0; i < maxRetry + 1; i++) {
            TSocket sock = null;
            boolean exception = false;
            try {
                sock = getConn("getCountryNum", service);
                TTransport trans = getTransport(sock, service);
                TProtocol proto = getProtocol(trans, service);
                AddressService.Client client = new AddressService.Client(proto);
                client.setContext(context_);
                return client.getCountryNum(request);
            } catch (TTransportException | TProtocolException e) {
                exception = true;
                save = e;
            } finally {
                if (!exception) {
                    long cost = System.currentTimeMillis() - startTime;
                    if (this.invokeId != null) {
                        String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                        LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getCountryNum", XUtil.getLocalIP(), sock.getHost(), 0, 0, cost);
                    }
                }
                if (sock != null) {
                    rpc_.putConn(sock, exception);
                }
            }
        }
        long cost = System.currentTimeMillis() - startTime;
        if (this.invokeId != null) {
            String dateString = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            LOGGER.info("{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}\t{}", dateString, "archtech_modelcall_milib", this.invokeId, this.service_, "getCountryNum", XUtil.getLocalIP(), "0.0.0.0", 1, 1, cost);
        }
        throw save;
    }


}

