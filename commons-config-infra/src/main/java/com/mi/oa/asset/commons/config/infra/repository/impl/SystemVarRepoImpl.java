package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.domain.systemvar.entity.SystemVar;
import com.mi.oa.asset.commons.config.domain.systemvar.repository.SystemVarRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.SystemVarPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.SystemVarPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.SystemVarRepoConverter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/19 15:10
 */

@Service
public class SystemVarRepoImpl implements SystemVarRepo {

    @Resource
    private SystemVarPoMapper systemVarPoMapper;

    @Resource
    private SystemVarRepoConverter converter;

    @Override
    public void saveSystemVar(SystemVar systemVar) {
        systemVarPoMapper.insert(converter.toSystemVarPo(systemVar));
    }

    @Override
    public void updateSystemVar(SystemVar systemVar) {
        SystemVarPo systemVarPo = converter.toSystemVarPo(systemVar);
        SystemVarPo varPo = new LambdaQueryChainWrapper<>(systemVarPoMapper)
                .eq(SystemVarPo::getVarCode, systemVar.getVarCode())
                .eq(SystemVarPo::getBusinessLine, systemVar.getBusinessLine()).one();
        if (null == varPo) {
            systemVarPoMapper.insert(systemVarPo);
        } else {
            systemVarPo.setId(varPo.getId());
            systemVarPoMapper.updateById(systemVarPo);
        }
    }

    @Override
    public SystemVar getSystemVar(String varCode, BusinessLine businessLine) {
        List<SystemVar> list = getSystemVar(Collections.singletonList(varCode), businessLine);

        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public List<SystemVar> getSystemVar(List<String> varCodes, BusinessLine businessLine) {
        if(CollectionUtils.isEmpty(varCodes) || null == businessLine) return new ArrayList<>();

        List<SystemVarPo> list = new LambdaQueryChainWrapper<>(systemVarPoMapper)
                        .in(SystemVarPo::getVarCode, varCodes)
                        .eq(SystemVarPo::getBusinessLine, businessLine.getCode())
                        .list();


        return converter.toSystemVars(list);
    }
}
