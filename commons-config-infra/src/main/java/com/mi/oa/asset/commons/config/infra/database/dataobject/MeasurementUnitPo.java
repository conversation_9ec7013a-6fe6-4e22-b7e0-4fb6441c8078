package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "amg_measurement_unit")
public class MeasurementUnitPo extends BasePo {
    /**
     * 计量单位编码
     */
    @TableField(value = "measure_code")
    private String measureCode;

    /**
     * 计量单位名称
     */
    @TableField(value = "measure_name")
    private String measureName;
    /**
     * 计量单位名称-英文
     */
    @TableField(value = "measure_name_en")
    private String measureNameEn;

    /**
     * 技术代码
     */
    @TableField(value = "tech_code")
    private String techCode;

    /**
     * 计量单位唯一值
     */
    @TableField(value = "mu_id")
    private String muId;

    /**
     * 单元类型码
     */
    @TableField(value = "unit_type_code")
    private String unitTypeCode;

    /**
     * 单元类型名
     */
    @TableField(value = "unit_type_name")
    private String unitTypeName;

    /**
     * 来源
     */
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * 禁用
     */
    @TableField(value = "disabled")
    private Integer disabled;

}