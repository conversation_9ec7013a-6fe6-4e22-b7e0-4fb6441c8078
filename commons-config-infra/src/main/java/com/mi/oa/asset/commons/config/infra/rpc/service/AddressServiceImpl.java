package com.mi.oa.asset.commons.config.infra.rpc.service;


import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mi.oa.asset.common.model.AdsCityDTO;
import com.mi.oa.asset.common.model.AdsDTO;
import com.mi.oa.asset.commons.config.domain.address.enums.SubsetLevelEnum;
import com.mi.oa.asset.commons.config.infra.rpc.ads.AddressServiceWrapper;
import com.mi.oa.asset.commons.config.infra.rpc.ads.CityRequest;
import com.mi.oa.asset.commons.config.infra.rpc.ads.Response;
import com.mi.oa.asset.commons.config.infra.rpc.ads.SubsetRequest;
import com.mi.oa.asset.commons.config.infra.rpc.config.SpecialCityConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @Desc 地址库相关接口
 * <AUTHOR>
 * @Date 2021/8/27 0:56
 */

@Slf4j
@Service("AddressServiceImpl")
public class AddressServiceImpl {

    @Resource(name = "adsClient")
    public AddressServiceWrapper client;

    @Resource
    private SpecialCityConfig cityConfig;

    public List<AdsDTO> getCountry() {
        // 1 - 中国 3385 - 中国香港 3386 - 中国台湾
        return getSubset(SubsetLevelEnum.COUNTRY, 1);
    }

    public List<AdsDTO> getProvince() {
        // 1 - 中国 3385 - 中国香港 3386 - 中国台湾
        return getProvince(1);
    }

    public List<AdsDTO> getProvince(int provinceId) {
        // 1 - 中国 3385 - 中国香港 3386 - 中国台湾
        return getSubset(SubsetLevelEnum.PROVINCE, provinceId);
    }

    public List<AdsDTO> getCity(int provinceId) {
        return getSubset(SubsetLevelEnum.CITY, provinceId);
    }

    public List<AdsDTO> getDistrict(int cityId) {
        return getSubset(SubsetLevelEnum.DISTRICT, cityId);
    }

    public List<AdsDTO> getStreet(int districtId) {
        return getSubset(SubsetLevelEnum.STREET, districtId);
    }

    /**
     * @param subset   要获取的子集地址级别1:国家2:省3:市4:区5:街道
     * @param parentId 父级地址id
     * @return
     */
    public List<AdsDTO> getSubset(SubsetLevelEnum subset, int parentId) {
        List<AdsDTO> data = new ArrayList<>();

        int level = subset.getLv();
        SubsetRequest request = new SubsetRequest();
        request.setLevel(level);
        request.setParent_id(parentId);
        request.setEnable("1");

        try {
            Response res = client.getSubset(request);
            Gson gson = new Gson();
            data = gson.fromJson(res.getData(), new TypeToken<List<AdsDTO>>() {
            }.getType());
        } catch (Exception e) {
            log.error("Get subset list failed, level {} parentId {}", level, parentId);
        }

        return data;
    }

    public List<AdsCityDTO> queryCityByName(String cityName) {
        List<AdsCityDTO> cityDTOS;
        CityRequest cityRequest = new CityRequest();
        cityRequest.setCity_name(cityName);
        cityRequest.setEnable("1");
        try {
            Response res = client.getCity(cityRequest);
            Gson gson = new Gson();
            log.info("Get city by name: {}", res.getData());
            cityDTOS = gson.fromJson(res.getData(), new TypeToken<List<AdsCityDTO>>() {
            }.getType());
            return cityDTOS;
        } catch (Exception e) {
            log.error("Get city by name failed");
        }
        return Collections.emptyList();
    }

    /**
	 * 根据城市名称模糊查询城市信息
	 *
	 * @param cityName 城市名称
	 * @return 符合条件的城市信息列表，如果没有找到则返回空列表
	 */
	public List<AdsCityDTO> queryCityByFuzzyName(String cityName) {
        String concatCity = cityName;
        if (!cityName.contains("市") && cityName.length() < 3) {
            concatCity = cityName + "市";
        }
        List<AdsCityDTO> cityDTOS = queryCityByName(concatCity);
        if (CollectionUtils.isNotEmpty(cityDTOS)) {
            return cityDTOS;
        }
        // 没查到
        List<String> cityNames = cityConfig.getCityNames();
        // 如果特殊城市中有前缀是cityName，则直接返回
        String finalCityName = cityName;
        Optional<String> first = cityNames.stream().filter(city -> city.startsWith(finalCityName)).findFirst();
        if (first.isPresent()) {
            String s = first.get();
            return queryCityByName(s);
        }
        return Collections.emptyList();
    }
}
