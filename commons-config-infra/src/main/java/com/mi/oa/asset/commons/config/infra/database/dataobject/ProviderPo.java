package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/10/25 19:41
 */

@Data
@TableName(value = "amg_provider")
public class ProviderPo {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 供应商编码
     */
    @TableField(value = "provider_code")
    private String providerCode;

    /**
     * 供应商名称
     */
    @TableField(value = "provider_name")
    private String providerName;

    /**
     * 禁用
     */
    @TableField(value = "disabled")
    private Integer disabled;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;
}