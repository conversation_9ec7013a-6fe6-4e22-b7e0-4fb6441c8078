package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSkuManage;
import com.mi.oa.asset.commons.config.domain.assetsku.repository.AssetSkuManageRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetSkuManagePo;
import com.mi.oa.asset.commons.config.infra.database.mapper.AssetSkuManageMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AssetSkuManageConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AssetSkuManageRepoImpl extends ServiceImpl<AssetSkuManageMapper, AssetSkuManagePo> implements AssetSkuManageRepo {
    @Resource
    private AssetSkuManageConverter converter;

    @Override
    public void saveAssetSkuManage(AssetSkuManage manage) {
        this.baseMapper.insert(converter.toManagePo(manage));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchAssetSkuManage(List<AssetSkuManage> assetSkuMgList) {
        if(CollectionUtils.isEmpty(assetSkuMgList)) return;
        List<AssetSkuManagePo> managePoList = converter.toManagePoList(assetSkuMgList);
        this.saveOrUpdateBatch(managePoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAssetSkuManage(List<AssetSkuManage> manages) {
        if (CollectionUtils.isEmpty(manages)) {
            return;
        }
        List<AssetSkuManagePo> managePoList = converter.toManagePoList(manages);
        managePoList.forEach(po -> po.setSerialMg(StringUtils.defaultString(po.getSerialMg(), StringUtils.EMPTY)));
        this.saveOrUpdateBatch(managePoList);
    }

    @Override
    public void deleteAssetSkuManage(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) return;
        this.baseMapper.deleteBatchIds(ids);

    }

    @Override
    public void deleteAssetSkuManageBySkuIds(List<Integer> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) return;
        this.baseMapper.delete(Wrappers.lambdaQuery(AssetSkuManagePo.class).eq(AssetSkuManagePo::getSkuId, skuIds));
    }

    @Override
    public List<AssetSkuManage> getByAssetSkuId(Integer skuId) {
        List<AssetSkuManagePo> list = this.baseMapper.selectList(Wrappers.lambdaQuery(AssetSkuManagePo.class)
                .eq(AssetSkuManagePo::getSkuId, skuId)
                .eq(AssetSkuManagePo::getIsDeleted, 0));
        return converter.toManageDoList(list);
    }

    @Override
    public Map<Integer, List<AssetSkuManage>> getAssetSkusManages(List<Integer> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) return Collections.emptyMap();
        List<AssetSkuManagePo> list = this.baseMapper.selectList(Wrappers.lambdaQuery(AssetSkuManagePo.class)
                .in(AssetSkuManagePo::getSkuId, skuIds)
                .eq(AssetSkuManagePo::getIsDeleted, 0));
        List<AssetSkuManage> skuManages = converter.toManageDoList(list);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return skuManages.stream().collect(Collectors.groupingBy(AssetSkuManage::getSkuId));
    }

    @Override
    public Map<Integer, List<AssetSkuManage>> getAssetSkusManagesByBusinessLines(List<Integer> skuIds, List<String> businessLines) {
        if (CollectionUtils.isEmpty(skuIds)) return Collections.emptyMap();
        List<AssetSkuManagePo> list = this.baseMapper.selectList(Wrappers.lambdaQuery(AssetSkuManagePo.class)
                .in(AssetSkuManagePo::getSkuId, skuIds)
                .in(AssetSkuManagePo::getBusinessLine, businessLines)
                .eq(AssetSkuManagePo::getIsDeleted, 0));
        List<AssetSkuManage> skuManages = converter.toManageDoList(list);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return skuManages.stream().collect(Collectors.groupingBy(AssetSkuManage::getSkuId));
    }

    @Override
    public int sharedBusinessLine(Integer skuId, List<String> businessLines) {
        return this.baseMapper.update(null, Wrappers.lambdaUpdate(AssetSkuManagePo.class)
                .set(AssetSkuManagePo::getSharedBusinessLines, StringUtils.join(businessLines,","))
                .eq(AssetSkuManagePo::getSkuId, skuId));
    }
}
