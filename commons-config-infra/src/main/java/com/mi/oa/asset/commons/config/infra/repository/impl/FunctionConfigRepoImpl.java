package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfig;
import com.mi.oa.asset.commons.config.domain.common.repository.FunctionConfigRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FunctionConfigPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.FunctionConfigMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.FunctionConfigConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/28 17:40
 **/
@Service
public class FunctionConfigRepoImpl extends ServiceImpl<FunctionConfigMapper, FunctionConfigPo> implements FunctionConfigRepo {

    @Resource
    private FunctionConfigConverter converter;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveFunctionConfig(FunctionConfig config) {
        FunctionConfigPo po = converter.toPo(config);
        saveOrUpdate(po);
        // 保存部门授权
        return po.getId();
    }

    @Override
    public FunctionConfig getConfig(String manageLine, String functionName, String functionNameEn) {
        if (StringUtils.isBlank(manageLine) || (StringUtils.isBlank(functionName) && StringUtils.isBlank(functionNameEn)))
            return null;
        FunctionConfigPo functionConfigPo = baseMapper.selectOne(Wrappers.lambdaQuery(FunctionConfigPo.class)
                .eq(FunctionConfigPo::getManageLine, manageLine)
                .eq(StringUtils.isNotBlank(functionName), FunctionConfigPo::getName, functionName)
                .eq(StringUtils.isNotBlank(functionNameEn), FunctionConfigPo::getNameEn, functionNameEn));
        return converter.toConfig(functionConfigPo);
    }

    @Override
    public List<FunctionConfig> listByIds(List<Integer> configIds) {
        if (CollectionUtils.isEmpty(configIds)) return Collections.emptyList();
        List<FunctionConfigPo> functionConfigPos = baseMapper.selectList(Wrappers.lambdaQuery(FunctionConfigPo.class)
                .in(FunctionConfigPo::getId, configIds).orderByAsc(FunctionConfigPo::getSort));
        return converter.toConfigs(functionConfigPos);
    }

    @Override
    public List<FunctionConfig> listByManageLine(String manageLine) {
        if (StringUtils.isBlank(manageLine)) return Collections.emptyList();
        List<FunctionConfigPo> functionConfigPos = baseMapper.selectList(Wrappers.lambdaQuery(FunctionConfigPo.class)
                .eq(FunctionConfigPo::getManageLine, manageLine));
        return converter.toConfigs(functionConfigPos);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByConfigIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) return;
        baseMapper.deleteBatchIds(ids);
    }
}
