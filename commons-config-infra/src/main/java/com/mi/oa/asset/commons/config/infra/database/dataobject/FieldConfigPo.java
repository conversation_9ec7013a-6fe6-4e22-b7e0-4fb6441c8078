package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/1/9 15:10
 * 字段管理配置表
 * 
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "amg_field_config")
public class FieldConfigPo extends BasePo {
    /**
     * 管理线
     */
    @TableField(value = "manage_line")
    private String manageLine;

    /**
     * 功能Id
     */
    @TableField(value = "fun_id")
    private String funId;

    /**
     * 字段配置信息
     */
    @TableField(value = "field_config")
    private String fieldConfig;

    /**
     * 扩展字段配置
     */
    @TableField(value = "extra")
    private String extra;
}