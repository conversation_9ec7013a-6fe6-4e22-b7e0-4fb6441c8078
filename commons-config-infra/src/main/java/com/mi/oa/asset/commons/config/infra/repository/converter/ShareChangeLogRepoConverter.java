package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareChangeLog;
import com.mi.oa.asset.commons.config.infra.database.dataobject.ShareChangeLogPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-07 11:01:35
 */
@Mapper(componentModel = "spring")
public interface ShareChangeLogRepoConverter {
    ShareChangeLogPo doToPo(ShareChangeLog shareChangeLog);

    ShareChangeLog poToDo(ShareChangeLogPo logPo);

    List<ShareChangeLog> poToDoList(List<ShareChangeLogPo> changeLogPos);
}
