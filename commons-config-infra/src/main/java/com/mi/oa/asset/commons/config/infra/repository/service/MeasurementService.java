package com.mi.oa.asset.commons.config.infra.repository.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.infra.database.dataobject.MeasurementUnitPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.MeasurementUnitPoMapper;
import org.springframework.stereotype.Service;


@Service
public class MeasurementService extends ServiceImpl<MeasurementUnitPoMapper, MeasurementUnitPo> {
}
