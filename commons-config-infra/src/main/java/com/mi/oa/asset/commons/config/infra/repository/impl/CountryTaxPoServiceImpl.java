package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryTaxDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryTaxRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.CountryTaxPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.CountryTaxPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.CountryTaxPoConvertor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 【amg_country_tax(税率配置表)】
* @createDate 2025-04-29 19:34:18
*/
@Service
public class CountryTaxPoServiceImpl extends ServiceImpl<CountryTaxPoMapper, CountryTaxPo>
    implements CountryTaxRepo {

    @Resource
    private CountryTaxPoConvertor converter;

    @Resource
    private CountryTaxPoMapper countryTaxPoMapper;
    @Override
    public List<CountryTaxDo> searchAll() {
        List<CountryTaxPo> countryTaxPoList = countryTaxPoMapper.selectList(new LambdaQueryWrapper<>());
        return converter.poToDoList(countryTaxPoList);
    }

    @Override
    public CountryTaxDo getById(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        CountryTaxPo po = this.baseMapper.selectById(id);
        return converter.poToDo(po);
    }

    @Override
    public Integer save(CountryTaxDo countryTaxDo) {
        CountryTaxPo po = converter.doToPo(countryTaxDo);
        this.save(po);
        return po.getId();
    }

    @Override
    public void updateById(CountryTaxDo entity) {
        CountryTaxPo po = converter.doToPo(entity);
        updateById(po);
    }

    @Override
    public void deleteByIds(List<Integer> idList) {
        this.baseMapper.deleteBatchIds(idList);
    }

    @Override
    public List<CountryTaxDo> getByCountryId(Integer countryId) {
        List<CountryTaxPo> countryTaxPoList = countryTaxPoMapper.selectList(new LambdaQueryWrapper<CountryTaxPo>()
                .eq(CountryTaxPo::getCountryConfigId, countryId));
        return converter.poToDoList(countryTaxPoList);
    }
}




