package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.mi.oa.asset.common.enums.YesNo;
import com.mi.oa.asset.commons.config.domain.common.repository.CommonDataRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.*;
import com.mi.oa.asset.commons.config.infra.common.CacheKey;
import com.mi.oa.asset.commons.config.infra.database.dataobject.*;
import com.mi.oa.asset.commons.config.infra.database.mapper.*;
import com.mi.oa.asset.commons.config.infra.repository.converter.CommonDataRepoConverter;
import com.mi.oa.asset.eam.mybatis.BasePo;
import com.mi.oa.asset.eam.mybatis.FunctionSubscribePo;
import com.mi.oa.asset.eam.mybatis.FunctionSubscribePoMapper;
import com.mi.oa.infra.oaucf.redis.annotation.OACacheSet;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/9/18 17:50
 */

@Service
public class CommonDataRepoImpl implements CommonDataRepo {

    @Value("${not-distinguish-business-subscribe.funCode}")
    private String spanBusinessSubscribe;

    @Resource
    private PurchaseCatalogPoMapper purchaseCatalogMapper;

    @Resource
    private CompanyPoMapper companyMapper;

    @Resource
    private ProviderPoMapper providerMapper;

    @Resource
    private TemplateUrlPoMapper templateUrlPoMapper;

    @Resource
    private FunctionSubscribePoMapper subscribePoMapper;

    @Resource
    private CommonDataRepoConverter commonDataConverter;

    @Resource
    private AssetSapTypeMapper sapTypeMapper;

    @Override
    @OACacheSet(cacheEnum = CacheKey.class, cacheEnumField = "ALL_PURCHASE_CATALOG", refreshCacheTime = 24 * 60 * 3600)
    public List<PurchaseCatalog> getAllPurchaseCatalogs() {
        List<PurchaseCatalogPo> list = new LambdaQueryChainWrapper<>(purchaseCatalogMapper)
                .eq(PurchaseCatalogPo::getDisabled, 0)
                .list();

        return commonDataConverter.toPurchaseCatalogs(list);
    }

    @Override
    public List<Company> getCompanies(String keyword, String countryCode) {
        List<CompanyPo> list = new LambdaQueryChainWrapper<>(companyMapper)
                .eq(CompanyPo::getDisabled, 0)
                .eq(CompanyPo::getCountryCode, countryCode)
                .and(StringUtils.isNotBlank(keyword), q ->
                        q.like(CompanyPo::getCompanyCode, keyword).or().like(CompanyPo::getCompanyName, keyword))
                .list();

        return commonDataConverter.toCompanies(list);
    }

    @Override
    public Company getCompany(String companyCode) {
        CompanyPo list = new LambdaQueryChainWrapper<>(companyMapper)
                .eq(CompanyPo::getDisabled, 0)
                .eq(CompanyPo::getCompanyCode, companyCode)
                .one();
        return commonDataConverter.toCompany(list);
    }

    @Override
    public List<Company> getBatchCompany(List<String> companyCodes) {
        List<CompanyPo> list = new LambdaQueryChainWrapper<>(companyMapper)
                .eq(CompanyPo::getDisabled, 0)
                .in(CompanyPo::getCompanyCode, companyCodes)
                .list();
        return commonDataConverter.toCompanies(list);
    }

    @Override
    public Company getCompany(String keyword, String countryCode) {
        CompanyPo po = companyMapper.selectOne(Wrappers.lambdaQuery(CompanyPo.class)
                .eq(CompanyPo::getDisabled, 0)
                .eq(CompanyPo::getCountryCode, countryCode)
                .and(q -> q.eq(CompanyPo::getCompanyCode, keyword).or().eq(CompanyPo::getCompanyName, keyword))
                .last("limit 1"));

        return commonDataConverter.toCompany(po);
    }

    @Override
    @OACacheSet(cacheEnum = CacheKey.class, cacheEnumField = "ALL_SAP_ASSET_CATEGORY", refreshCacheTime = 24 * 60 * 3600)
    public List<SapAssetCategory> getSapAssetCategories() {
        List<PurchaseCatalogPo> list = purchaseCatalogMapper.selectList(
                new QueryWrapper<PurchaseCatalogPo>().select("DISTINCT(sap_cate_code)", "sap_cate_name").lambda()
                        .eq(PurchaseCatalogPo::getDisabled, 0)
        );

        return commonDataConverter.toSapCategories(list);
    }

    @Override
//    @OACacheSet(cacheEnum = CacheKey.class, cacheEnumField = "ALL_SAP_ASSET_CATEGORY_EAM", refreshCacheTime = 24 * 60 * 3600)
    public List<SapAssetCategory> getSapAssetCategories4EAM() {
        List<AssetSapTypePo> assetSapTypePos = sapTypeMapper.selectList(
                new QueryWrapper<AssetSapTypePo>().select("acc_code", "acc_name", "acc_name_en").lambda()
                        .eq(AssetSapTypePo::getIsValid, "1"));
        return commonDataConverter.toSapCategories4EAM(assetSapTypePos);
    }

    @Override
    public List<Provider> getProviders(String keyword) {
        List<ProviderPo> list = new LambdaQueryChainWrapper<>(providerMapper)
                .eq(ProviderPo::getDisabled, 0)
                .and(StringUtils.isNotBlank(keyword), q ->
                        q.like(ProviderPo::getProviderCode, keyword).or().like(ProviderPo::getProviderName, keyword))
                .last("limit 50")
                .list();

        return commonDataConverter.toProviders(list);
    }

    @Override
    public List<Provider> getProviders(List<String> providerCodes) {
        List<ProviderPo> list = new LambdaQueryChainWrapper<>(providerMapper)
                .eq(ProviderPo::getDisabled, 0)
                .in(ProviderPo::getProviderCode, providerCodes)
                .list();
        return commonDataConverter.toProviders(list);
    }

    @Override
    public Provider getProvider(String keyword) {
        ProviderPo po = providerMapper.selectOne(Wrappers.lambdaQuery(ProviderPo.class)
                .eq(ProviderPo::getDisabled, 0)
                .and(q -> q.eq(ProviderPo::getProviderCode, keyword).or().eq(ProviderPo::getProviderName, keyword))
                .last("limit 1"));
        return commonDataConverter.toProvider(po);
    }

    @Override
    public TemplateUrl getTemplateUrl(String funId) {
        TemplateUrlPo po = templateUrlPoMapper.selectOne(Wrappers.lambdaQuery(TemplateUrlPo.class)
                .eq(TemplateUrlPo::getIsDeleted, 0)
                .eq(TemplateUrlPo::getFunId, funId)
                .last("limit 1"));
        return commonDataConverter.toTemplateUrl(po);
    }

    @Override
    public FunctionSubscribe getSubscribe(String businessLine, String funCode, String userCode) {
        // 配置了可跨业务线订阅的功能，滞空业务线参数
        if (spanBusinessSubscribe.contains(funCode)) {
            businessLine = "";
        }
        FunctionSubscribePo shareSubscribePo = subscribePoMapper.selectOne(Wrappers.lambdaQuery(FunctionSubscribePo.class)
                .eq(StringUtils.isNotBlank(businessLine), FunctionSubscribePo::getBusinessLine, businessLine)
                .eq(FunctionSubscribePo::getFunCode, funCode)
                .eq(BasePo::getCreateUser, userCode)
        );
        return commonDataConverter.toSubscribe(shareSubscribePo);
    }

    @Override
    public List<FunctionSubscribe> getSubscribeByFunCode(String businessLine, String funCode) {
        if (StringUtils.isBlank(businessLine) && StringUtils.isBlank(funCode)) {
            return new ArrayList<>();
        }
        List<FunctionSubscribePo> subscribePos = subscribePoMapper.selectList(Wrappers.lambdaQuery(FunctionSubscribePo.class)
                .eq(StringUtils.isNotBlank(businessLine), FunctionSubscribePo::getBusinessLine, businessLine)
                .eq(FunctionSubscribePo::getIsSubscribe, YesNo.YES.getCode())
                .eq(StringUtils.isNotBlank(funCode), FunctionSubscribePo::getFunCode, funCode));
        return commonDataConverter.toSubscribeList(subscribePos);
    }

    @Override
    public void createOrUpdateSubscribe(FunctionSubscribe subscribe) {
        // 配置了可跨业务线订阅的功能，滞空业务线参数
        if (spanBusinessSubscribe.contains(subscribe.getFunCode())) {
            subscribe.setBusinessLine(null);
        }
        FunctionSubscribePo subscribePo = commonDataConverter.toSubscribePo(subscribe);
        if (Objects.isNull(subscribe.getId())) {
            subscribePoMapper.insert(subscribePo);
        } else {
            subscribePoMapper.updateById(subscribePo);
        }
    }
}
