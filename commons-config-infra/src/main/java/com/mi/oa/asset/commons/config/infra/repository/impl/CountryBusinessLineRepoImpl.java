package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryBusinessLineDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryBusinessLineRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.CountryBusinessLinePo;
import com.mi.oa.asset.commons.config.infra.database.mapper.CountryBusinessLinePoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.CountryBusinessLinePoConvertor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 【amg_country_business_line(国家和业务线关系表)】
*/
@Service
public class CountryBusinessLineRepoImpl extends ServiceImpl<CountryBusinessLinePoMapper, CountryBusinessLinePo>
    implements CountryBusinessLineRepo {

    @Resource
    private CountryBusinessLinePoConvertor converter;

    @Resource
    private CountryBusinessLinePoMapper countryBusinessLinePoMapper;

    @Override
    public List<CountryBusinessLineDo> searchAll() {
        return converter.poToDoList(countryBusinessLinePoMapper.selectList(new LambdaQueryWrapper<>()));
    }

    @Override
    public List<CountryBusinessLineDo> getByCountryId(Integer countryConfigId) {
        return converter.poToDoList(countryBusinessLinePoMapper.selectList(new LambdaQueryWrapper<CountryBusinessLinePo>()
                .eq(CountryBusinessLinePo::getCountryConfigId, countryConfigId)));
    }

    @Override
    public List<CountryBusinessLineDo> getByCountryIds(List<Integer> idList) {
        return converter.poToDoList(countryBusinessLinePoMapper.selectList(new LambdaQueryWrapper<CountryBusinessLinePo>()
                .in(CountryBusinessLinePo::getCountryConfigId, idList)));
    }
    @Override
    public CountryBusinessLineDo getById(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        CountryBusinessLinePo po = this.baseMapper.selectById(id);
        return converter.poToDo(po);
    }

    @Override
    public Integer save(CountryBusinessLineDo countryBusinessLineDo) {
        CountryBusinessLinePo po = converter.doToPo(countryBusinessLineDo);
        this.save(po);
        return po.getId();
    }

    @Override
    public void updateById(CountryBusinessLineDo entity) {
        CountryBusinessLinePo po = converter.doToPo(entity);
        updateById(po);
    }

    @Override
    public void updateBatchById(List<CountryBusinessLineDo> doList) {
        List<CountryBusinessLinePo> countryBusinessLinePos = converter.listDoToPo(doList);
        updateBatchById(countryBusinessLinePos);
    }


    @Override
    public void deleteByIds(List<Integer> idList) {
        this.baseMapper.deleteBatchIds(idList);
    }

    @Override
    public List<CountryBusinessLineDo> getByBusinessLine(String businessLine) {
        String[] businessLines = businessLine.split(",");
        List<CountryBusinessLinePo> CountryBusinessLinePoList = countryBusinessLinePoMapper.selectList(new LambdaQueryWrapper<CountryBusinessLinePo>()
                .in(CountryBusinessLinePo::getBusinessLine, businessLines));
        return converter.poToDoList(CountryBusinessLinePoList);
    }

    @Override
    public List<CountryBusinessLineDo> getByBusinessLineAndCountryId(String businessLine, Integer countryConfigId) {
        List<CountryBusinessLinePo> CountryBusinessLinePoList = countryBusinessLinePoMapper.selectList(new LambdaQueryWrapper<CountryBusinessLinePo>()
                .eq(CountryBusinessLinePo::getBusinessLine, countryConfigId)
                .eq(CountryBusinessLinePo::getCountryConfigId, countryConfigId));
        return converter.poToDoList(CountryBusinessLinePoList);
    }

    @Override
    public void deleteByCountryIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        this.baseMapper.delete(
                Wrappers.lambdaUpdate(CountryBusinessLinePo.class)
                        .in(CountryBusinessLinePo::getCountryConfigId, ids));
    }

    @Override
    public void saveAll(List<CountryBusinessLineDo> countryBusinessLineDoList) {
        List<CountryBusinessLinePo> countryBusinessLinePos = converter.listDoToPo(countryBusinessLineDoList);
        this.saveBatch(countryBusinessLinePos);
    }


}




