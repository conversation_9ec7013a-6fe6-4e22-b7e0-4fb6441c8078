/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.EncodingUtils;
import org.mi.thrift.TException;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;
import org.mi.thrift.server.AbstractNonblockingServer.*;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2020-5-26")
public class CountryNumRequest implements org.mi.thrift.TBase<CountryNumRequest, CountryNumRequest._Fields>, java.io.Serializable, Cloneable, Comparable<CountryNumRequest> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("CountryNumRequest");

    private static final org.mi.thrift.protocol.TField COUNTRY_ID_FIELD_DESC = new org.mi.thrift.protocol.TField("country_id", org.mi.thrift.protocol.TType.I64, (short) 1);
    private static final org.mi.thrift.protocol.TField REGION_LEVEL_FIELD_DESC = new org.mi.thrift.protocol.TField("region_level", org.mi.thrift.protocol.TType.I16, (short) 2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();

    static {
        schemes.put(StandardScheme.class, new CountryNumRequestStandardSchemeFactory());
        schemes.put(TupleScheme.class, new CountryNumRequestTupleSchemeFactory());
    }

    public long country_id; // required
    public short region_level; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        COUNTRY_ID((short) 1, "country_id"),
        REGION_LEVEL((short) 2, "region_level");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
            for (_Fields field : EnumSet.allOf(_Fields.class)) {
                byName.put(field.getFieldName(), field);
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch (fieldId) {
                case 1: // COUNTRY_ID
                    return COUNTRY_ID;
                case 2: // REGION_LEVEL
                    return REGION_LEVEL;
                default:
                    return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
            _Fields fields = findByThriftId(fieldId);
            if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
            return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
            return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
            _thriftId = thriftId;
            _fieldName = fieldName;
        }

        public short getThriftFieldId() {
            return _thriftId;
        }

        public String getFieldName() {
            return _fieldName;
        }
    }

    // isset id assignments
    private static final int __COUNTRY_ID_ISSET_ID = 0;
    private static final int __REGION_LEVEL_ISSET_ID = 1;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;

    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.COUNTRY_ID, new org.mi.thrift.meta_data.FieldMetaData("country_id", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I64)));
        tmpMap.put(_Fields.REGION_LEVEL, new org.mi.thrift.meta_data.FieldMetaData("region_level", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I16)));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CountryNumRequest.class, metaDataMap);
    }

    public CountryNumRequest() {
    }

    public CountryNumRequest(
            long country_id,
            short region_level) {
        this();
        this.country_id = country_id;
        setCountry_idIsSet(true);
        this.region_level = region_level;
        setRegion_levelIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public CountryNumRequest(CountryNumRequest other) {
        __isset_bitfield = other.__isset_bitfield;
        this.country_id = other.country_id;
        this.region_level = other.region_level;
    }

    public CountryNumRequest deepCopy() {
        return new CountryNumRequest(this);
    }

    @Override
    public void clear() {
        setCountry_idIsSet(false);
        this.country_id = 0;
        setRegion_levelIsSet(false);
        this.region_level = 0;
    }

    public long getCountry_id() {
        return this.country_id;
    }

    public CountryNumRequest setCountry_id(long country_id) {
        this.country_id = country_id;
        setCountry_idIsSet(true);
        return this;
    }

    public void unsetCountry_id() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUNTRY_ID_ISSET_ID);
    }

    /** Returns true if field country_id is set (has been assigned a value) and false otherwise */
    public boolean isSetCountry_id() {
        return EncodingUtils.testBit(__isset_bitfield, __COUNTRY_ID_ISSET_ID);
    }

    public void setCountry_idIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUNTRY_ID_ISSET_ID, value);
    }

    public short getRegion_level() {
        return this.region_level;
    }

    public CountryNumRequest setRegion_level(short region_level) {
        this.region_level = region_level;
        setRegion_levelIsSet(true);
        return this;
    }

    public void unsetRegion_level() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __REGION_LEVEL_ISSET_ID);
    }

    /** Returns true if field region_level is set (has been assigned a value) and false otherwise */
    public boolean isSetRegion_level() {
        return EncodingUtils.testBit(__isset_bitfield, __REGION_LEVEL_ISSET_ID);
    }

    public void setRegion_levelIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __REGION_LEVEL_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
        switch (field) {
            case COUNTRY_ID:
                if (value == null) {
                    unsetCountry_id();
                } else {
                    setCountry_id((Long) value);
                }
                break;

            case REGION_LEVEL:
                if (value == null) {
                    unsetRegion_level();
                } else {
                    setRegion_level((Short) value);
                }
                break;

        }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
            case COUNTRY_ID:
                return Long.valueOf(getCountry_id());

            case REGION_LEVEL:
                return Short.valueOf(getRegion_level());

        }
        throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
        if (field == null) {
            throw new IllegalArgumentException();
        }

        switch (field) {
            case COUNTRY_ID:
                return isSetCountry_id();
            case REGION_LEVEL:
                return isSetRegion_level();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
            return false;
        if (that instanceof CountryNumRequest)
            return this.equals((CountryNumRequest) that);
        return false;
    }

    public boolean equals(CountryNumRequest that) {
        if (that == null)
            return false;

        boolean this_present_country_id = true;
        boolean that_present_country_id = true;
        if (this_present_country_id || that_present_country_id) {
            if (!(this_present_country_id && that_present_country_id))
                return false;
            if (this.country_id != that.country_id)
                return false;
        }

        boolean this_present_region_level = true;
        boolean that_present_region_level = true;
        if (this_present_region_level || that_present_region_level) {
            if (!(this_present_region_level && that_present_region_level))
                return false;
            if (this.region_level != that.region_level)
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_country_id = true;
        list.add(present_country_id);
        if (present_country_id)
            list.add(country_id);

        boolean present_region_level = true;
        list.add(present_region_level);
        if (present_region_level)
            list.add(region_level);

        return list.hashCode();
    }

    @Override
    public int compareTo(CountryNumRequest other) {
        if (!getClass().equals(other.getClass())) {
            return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetCountry_id()).compareTo(other.isSetCountry_id());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetCountry_id()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.country_id, other.country_id);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetRegion_level()).compareTo(other.isSetRegion_level());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetRegion_level()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.region_level, other.region_level);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
        return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CountryNumRequest(");
        boolean first = true;

        sb.append("country_id:");
        sb.append(this.country_id);
        first = false;
        if (!first) sb.append(", ");
        sb.append("region_level:");
        sb.append(this.region_level);
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        try {
            write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        try {
            // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
            __isset_bitfield = 0;
            read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private static class CountryNumRequestStandardSchemeFactory implements SchemeFactory {
        public CountryNumRequestStandardScheme getScheme() {
            return new CountryNumRequestStandardScheme();
        }
    }

    private static class CountryNumRequestStandardScheme extends StandardScheme<CountryNumRequest> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, CountryNumRequest struct) throws TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true) {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                    break;
                }
                switch (schemeField.id) {
                    case 1: // COUNTRY_ID
                        if (schemeField.type == org.mi.thrift.protocol.TType.I64) {
                            struct.country_id = iprot.readI64();
                            struct.setCountry_idIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // REGION_LEVEL
                        if (schemeField.type == org.mi.thrift.protocol.TType.I16) {
                            struct.region_level = iprot.readI16();
                            struct.setRegion_levelIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                        org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, CountryNumRequest struct) throws TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            oprot.writeFieldBegin(COUNTRY_ID_FIELD_DESC);
            oprot.writeI64(struct.country_id);
            oprot.writeFieldEnd();
            oprot.writeFieldBegin(REGION_LEVEL_FIELD_DESC);
            oprot.writeI16(struct.region_level);
            oprot.writeFieldEnd();
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class CountryNumRequestTupleSchemeFactory implements SchemeFactory {
        public CountryNumRequestTupleScheme getScheme() {
            return new CountryNumRequestTupleScheme();
        }
    }

    private static class CountryNumRequestTupleScheme extends TupleScheme<CountryNumRequest> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, CountryNumRequest struct) throws TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetCountry_id()) {
                optionals.set(0);
            }
            if (struct.isSetRegion_level()) {
                optionals.set(1);
            }
            oprot.writeBitSet(optionals, 2);
            if (struct.isSetCountry_id()) {
                oprot.writeI64(struct.country_id);
            }
            if (struct.isSetRegion_level()) {
                oprot.writeI16(struct.region_level);
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, CountryNumRequest struct) throws TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(2);
            if (incoming.get(0)) {
                struct.country_id = iprot.readI64();
                struct.setCountry_idIsSet(true);
            }
            if (incoming.get(1)) {
                struct.region_level = iprot.readI16();
                struct.setRegion_levelIsSet(true);
            }
        }
    }

}

