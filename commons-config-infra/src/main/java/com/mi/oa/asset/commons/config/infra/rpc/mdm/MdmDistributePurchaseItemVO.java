package com.mi.oa.asset.commons.config.infra.rpc.mdm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.mi.oa.asset.eam.feign.config.BooleanTo01Deserializer;
import com.mi.oa.asset.eam.feign.config.BooleanTo01Serializer;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class MdmDistributePurchaseItemVO{
    /**
     * 物料号
     */
    @JsonProperty("MATERIALCODE")
    private String skuId;

    /**
     * 物料编码
     */
    @JsonProperty("PICODE")
    private String miSkuCode;

    /**
     * 是否耗材
     */
    @JsonProperty("ISCONSUMABLE")
    @JsonSerialize(using = BooleanTo01Serializer.class)
    @JsonDeserialize(using = BooleanTo01Deserializer.class)
    private Boolean isConsumable;

    /**
     * 来源系统
     * EAM 固定值
     */
    @JsonProperty("SourceSystem")
    private String sys;

    /**
     * 来源系统数据序列
     * 9位唯一标识
     */
    @JsonProperty("SourceSystemSeq")
    private String seq;

    private String bussId;

    private String masterId;

    /**
     * 主数据状态
     * 2:可用  3:冻结
     */
    private String masterStatus;

    public boolean isActive() { return "2".equals(masterStatus); }
}
