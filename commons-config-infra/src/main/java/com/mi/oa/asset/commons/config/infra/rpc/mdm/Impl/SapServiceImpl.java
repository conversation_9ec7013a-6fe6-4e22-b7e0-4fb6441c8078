package com.mi.oa.asset.commons.config.infra.rpc.mdm.Impl;

import com.mi.oa.asset.commons.config.infra.rpc.mdm.DistributeFeedbackDTO;
import com.mi.oa.asset.commons.config.infra.rpc.mdm.SapService;
import com.mi.oa.asset.eam.utils.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;


@Slf4j
@Service("SapService")
public class SapServiceImpl implements SapService {


    // todo 配置文件
    @Value("${sap.account}")
    private String account;

    // todo 配置文件
    @Value("${sap.password}")
    private String password;

    // todo 配置文件 读取不到
//    @Value("${sap.host}")
//    private String sapUrl;
    //  定义常量 先测试
    private  static final String SAP_URL = "http://mipod.mioffice.cn:50000";

    @Override
    public void distributeFeedback(DistributeFeedbackDTO info) {
        log.info("开始分发反馈信息: {}", info);
        String authString = String.format("%s:%s", account, password);
        authString = Base64.getEncoder().encodeToString(authString.getBytes());
        Map<String, String> headers = new HashMap<>(4);
        headers.put("Authorization", "Basic " + authString);
        Map<String, Object> params = new HashMap<>();
        params.put("body", info);
        params.put("code", "200");
        headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        log.info("请求SAP接口URL: {}, headers: {}, params: {}", SAP_URL + "/RESTAdapter/mdg/mi-eam-v2/public/result-feedback", headers, params);
        HttpClientUtil.doPostJson(SAP_URL+"/RESTAdapter/mdg/mi-eam-v2/public/result-feedback", params, headers);
        log.info("分发反馈信息完成");
    }
}
