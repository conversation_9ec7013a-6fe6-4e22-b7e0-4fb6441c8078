package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.position.entity.PositionSpacePark;
import com.mi.oa.asset.commons.config.domain.position.repository.PositionSpaceParkRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.PositionSpaceParkPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.PositionSpaceParkPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.PositionRepoConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
public class PositionSpaceParkRepoImpl extends ServiceImpl<PositionSpaceParkPoMapper, PositionSpaceParkPo> implements PositionSpaceParkRepo {

    @Resource
    private PositionRepoConverter converter;

    @Override
    public void savePositionSpaceParks(List<PositionSpacePark> positions) {
        if (CollectionUtils.isEmpty(positions)) return;
        this.saveOrUpdateBatch(converter.toPositionSpaceParkPoList(positions));
    }

    @Override
    public List<PositionSpacePark> listPositionSpaceParks(List<String> positionCodes) {
        return converter.toPositionSpaceParkList(this.list(Wrappers.<PositionSpaceParkPo>lambdaQuery().in(PositionSpaceParkPo::getPositionCode, positionCodes)));
    }
}
