package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/19 16:43
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_asset_sku_manage")
public class AssetSkuManagePo extends BasePo {

    /**
     * skuId
     */
     
    private Integer skuId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 分类
     */

    private Integer cateId;

    /**
     * 分类编码
     */
    private String cateCode;
    /**
     * 分类名称
     */
    private String cateName;

    /**
     * 管理模式 asset_account：资产台账管理 asset_stock：资产库存管理 stock：库存管理
     */
    private String mgModel;

    /**
     * 串号管理
     */
    private String serialMg;

    /**
     * 存货成本核算
     */
    private String costing;

    /**
     * 安全库存
     */
    private BigDecimal secureQuantity;

    /**
     * 最高库存
     */
    private BigDecimal highestQuantity;

    /**
     * 最低库存
     */
    private BigDecimal miniQuantity;

    /**
     * 物料类型
     */
    private String materialType;

    /**
     * 共享业务线
     */
    private String sharedBusinessLines;

}
