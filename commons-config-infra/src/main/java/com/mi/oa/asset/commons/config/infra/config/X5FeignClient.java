package com.mi.oa.asset.commons.config.infra.config;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2021/8/13 14:16
 */


@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@FeignClient
@Component
public @interface X5FeignClient {

    @AliasFor(annotation = FeignClient.class)

    String value() default "";

    String url() default "";

    String appId() default "";

    String appKey() default "";

    boolean form() default false;

    Class<?>[] configuration() default {X5ClientConfig.class};

    Class<?> fallback() default void.class;

    Class<?> fallbackFactory() default void.class;

    String path() default "";

    boolean primary() default true;
}
