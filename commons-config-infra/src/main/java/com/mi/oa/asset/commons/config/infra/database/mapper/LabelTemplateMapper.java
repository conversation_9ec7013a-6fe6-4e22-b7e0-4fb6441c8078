package com.mi.oa.asset.commons.config.infra.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.commons.config.infra.database.dataobject.LabelTemplatePo;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @description 标签模版表
 * @date 2025-02-14
 */
public interface LabelTemplateMapper extends BaseMapper<LabelTemplatePo> {

    @Select("select * from amg_label_template where id = #{templateId}")
    LabelTemplatePo selectLabelTemplateById(Integer templateId);


}