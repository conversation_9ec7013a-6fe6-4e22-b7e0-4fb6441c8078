package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.common.valobj.AttachInfo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AttachInfoPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @autho zhan
 * @date 2023-10-26 14:55
 */
@Mapper(componentModel = "spring")
public interface AttachInfoRepoConverter {

    /**
     * 附件转换
     *
     * @param attachInfo
     * @return
     */
    List<AttachInfoPo> attachDtoListToPoList(List<AttachInfo> attachInfo);

    AttachInfoPo attachDtoListToPo(AttachInfo attachInfo);

    /**
     * 附件转换
     *
     * @param attachInfoPOS
     * @return
     */
    List<AttachInfo> attachPoListToDo(List<AttachInfoPo> attachInfoPOS);

    AttachInfo attachPoToDo(AttachInfoPo attachInfoPO);
}
