package com.mi.oa.asset.commons.config.infra.rpc.config;


import com.mi.oa.asset.commons.config.infra.rpc.ads.AddressServiceWrapper;
import lombok.extern.slf4j.Slf4j;
import org.mi.thrift.rpc.XRpc;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2021/8/27 11:18
 */

@Slf4j
@Configuration
public class RpcConfig implements InitializingBean {

    @Value("${mis.host}")
    private String host;

    @Value("${mis.group}")
    private String group;

    @Value("${mis.app}")
    private String app;

    @Value("${mis.ads.serviceName}")
    private String adsServiceName;

    private AddressServiceWrapper adsClient;


    @Override
    public void afterPropertiesSet() throws Exception {
        XRpc rpc = XRpc.getInstance();

        try {
            rpc.init(host, group, app);
            adsClient = new AddressServiceWrapper(adsServiceName, System.currentTimeMillis());
        } catch (Exception e) {
            log.error("Init rpc failed");
        }
    }

    @Bean(name = "adsClient")
    public AddressServiceWrapper adsClient() {
        return adsClient;
    }


    public String getApp() {
        return this.app;
    }

    public String getRequestId() {
        return this.app + Thread.currentThread().getId();
    }
}
