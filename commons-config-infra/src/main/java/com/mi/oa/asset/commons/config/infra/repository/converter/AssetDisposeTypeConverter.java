package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.common.entity.AssetDisposeType;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetDisposeTypePO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @autho zhan
 * @date 2023-10-26 14:55
 */
@Mapper(componentModel = "spring")
public interface AssetDisposeTypeConverter extends CommonConverter {

    List<AssetDisposeTypePO> assetDisposeTypeListToPoList(List<AssetDisposeType> disposeTypes);

    AssetDisposeTypePO assetDisposeTypeToPo(AssetDisposeType disposeType);

    List<AssetDisposeType> assetDisposeTypePoListToDo(List<AssetDisposeTypePO> disposeTypePOS);

    AssetDisposeType assetDisposeTypePoToDo(AssetDisposeTypePO disposeTypePO);
}
