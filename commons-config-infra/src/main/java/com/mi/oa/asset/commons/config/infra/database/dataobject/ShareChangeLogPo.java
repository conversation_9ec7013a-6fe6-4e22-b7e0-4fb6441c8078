package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 共享规则变动记录
 *
 * <AUTHOR>
 * @date 2025-03-07 09:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "amg_share_change_log")
public class ShareChangeLogPo extends BasePo {
    /**
     * 共享记录主键
     */
    private Integer shareId;

    /**
     * 变动前数据JSON
     */
    private String beforeChange;

    /**
     * 变动后数据JSON
     */
    private String afterChange;
}