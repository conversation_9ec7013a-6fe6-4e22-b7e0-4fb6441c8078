package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mi.oa.asset.common.enums.YesNo;
import com.mi.oa.asset.commons.config.domain.use.entity.Use;
import com.mi.oa.asset.commons.config.domain.use.enums.UseScope;
import com.mi.oa.asset.commons.config.domain.use.enums.UseType;
import com.mi.oa.asset.commons.config.domain.use.repository.UseRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.UseDeptPo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.UsePo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.UseUserPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.UseMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.UseRepoConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【amg_use(用途)】的数据库操作Service实现
* @createDate 2024-07-26 20:33:42
*/
@Slf4j
@Service
public class UseRepoImpl extends ServiceImpl<UseMapper, UsePo> implements UseRepo {

    @Autowired
    private UseMapper useMapper;

    @Resource
    private UseRepoConverter converter;

    @Override
    public List<Use> list(Use entity) {
        LambdaQueryWrapper<UsePo> wrapper = Wrappers.lambdaQuery(UsePo.class);
        wrapper.eq(UsePo::getBusinessLine, entity.getBusinessLine().getCode())
                .eq(UsePo::getDisabled, YesNo.NO.getCode())
                .eq(UsePo::getUseType, UseType.ALL.getCode())
                .in(UsePo::getScope, UseScope.ALL_STAFF.getCode());
        List<UsePo> usePos = useMapper.selectList(wrapper);
        return converter.toUses(usePos);
    }

    @Override
    public List<Use> listUseByUserCode(String businessLine, String userCode) {
        if (StringUtils.isAnyBlank(businessLine, userCode)) {
            return Collections.emptyList();
        }
        MPJLambdaWrapper<UsePo> useWayWrapper = new MPJLambdaWrapper<>();
        useWayWrapper
                .select(UsePo::getId)
                .select(UsePo::getUseCode)
                .select(UsePo::getUseName)
                .leftJoin(UseUserPo.class, UseUserPo::getUseId, UsePo::getId)
                .eq(UsePo::getBusinessLine, businessLine)
                .eq(UsePo::getDisabled, YesNo.NO.getCode())
                .eq(UsePo::getScope, UseScope.AUTHORIZED_STAFF.getCode())
                .eq(UsePo::getUseType, UseType.ALL.getCode())
                .eq(UseUserPo::getUserCode, userCode);
        List<UsePo> usePos = useMapper.selectJoinList(UsePo.class, useWayWrapper);
        return converter.toUses(usePos);
    }

    @Override
    public List<Use> listUseByDeptCode(String businessLine, List<String> deptCode) {
        if (StringUtils.isAnyBlank(businessLine) || CollectionUtils.isEmpty(deptCode)) {
            return Collections.emptyList();
        }

        MPJLambdaWrapper<UsePo> useWayWrapper = new MPJLambdaWrapper<>();
        useWayWrapper
                .select(UsePo::getId)
                .select(UsePo::getUseCode)
                .select(UsePo::getUseName)
                .leftJoin(UseDeptPo.class, UseDeptPo::getUseId, UsePo::getId)
                .eq(UsePo::getBusinessLine, businessLine)
                .eq(UsePo::getDisabled, YesNo.NO.getCode())
                .eq(UsePo::getScope, UseScope.AUTHORIZED_STAFF.getCode())
                .eq(UsePo::getUseType, UseType.ALL.getCode())
                .in(UseDeptPo::getDeptCode, deptCode);
        List<UsePo> usePos = useMapper.selectJoinList(UsePo.class, useWayWrapper);
        return converter.toUses(usePos);
    }
}




