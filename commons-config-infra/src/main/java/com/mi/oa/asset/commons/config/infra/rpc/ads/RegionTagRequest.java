/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.EncodingUtils;
import org.mi.thrift.TException;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2020-5-26")
public class RegionTagRequest implements org.mi.thrift.TBase<RegionTagRequest, RegionTagRequest._Fields>, java.io.Serializable, Cloneable, Comparable<RegionTagRequest> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("RegionTagRequest");

    private static final org.mi.thrift.protocol.TField TYPE_ID_FIELD_DESC = new org.mi.thrift.protocol.TField("type_id", org.mi.thrift.protocol.TType.I16, (short) 1);
    private static final org.mi.thrift.protocol.TField PAGE_FIELD_DESC = new org.mi.thrift.protocol.TField("page", org.mi.thrift.protocol.TType.I32, (short) 2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();

    static {
        schemes.put(StandardScheme.class, new RegionTagRequestStandardSchemeFactory());
        schemes.put(TupleScheme.class, new RegionTagRequestTupleSchemeFactory());
    }

    public short type_id; // required
    public int page; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        TYPE_ID((short) 1, "type_id"),
        PAGE((short) 2, "page");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
            for (_Fields field : EnumSet.allOf(_Fields.class)) {
                byName.put(field.getFieldName(), field);
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch (fieldId) {
                case 1: // TYPE_ID
                    return TYPE_ID;
                case 2: // PAGE
                    return PAGE;
                default:
                    return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
            _Fields fields = findByThriftId(fieldId);
            if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
            return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
            return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
            _thriftId = thriftId;
            _fieldName = fieldName;
        }

        public short getThriftFieldId() {
            return _thriftId;
        }

        public String getFieldName() {
            return _fieldName;
        }
    }

    // isset id assignments
    private static final int __TYPE_ID_ISSET_ID = 0;
    private static final int __PAGE_ISSET_ID = 1;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;

    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.TYPE_ID, new org.mi.thrift.meta_data.FieldMetaData("type_id", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I16)));
        tmpMap.put(_Fields.PAGE, new org.mi.thrift.meta_data.FieldMetaData("page", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RegionTagRequest.class, metaDataMap);
    }

    public RegionTagRequest() {
    }

    public RegionTagRequest(
            short type_id,
            int page) {
        this();
        this.type_id = type_id;
        setType_idIsSet(true);
        this.page = page;
        setPageIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public RegionTagRequest(RegionTagRequest other) {
        __isset_bitfield = other.__isset_bitfield;
        this.type_id = other.type_id;
        this.page = other.page;
    }

    public RegionTagRequest deepCopy() {
        return new RegionTagRequest(this);
    }

    @Override
    public void clear() {
        setType_idIsSet(false);
        this.type_id = 0;
        setPageIsSet(false);
        this.page = 0;
    }

    public short getType_id() {
        return this.type_id;
    }

    public RegionTagRequest setType_id(short type_id) {
        this.type_id = type_id;
        setType_idIsSet(true);
        return this;
    }

    public void unsetType_id() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TYPE_ID_ISSET_ID);
    }

    /** Returns true if field type_id is set (has been assigned a value) and false otherwise */
    public boolean isSetType_id() {
        return EncodingUtils.testBit(__isset_bitfield, __TYPE_ID_ISSET_ID);
    }

    public void setType_idIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TYPE_ID_ISSET_ID, value);
    }

    public int getPage() {
        return this.page;
    }

    public RegionTagRequest setPage(int page) {
        this.page = page;
        setPageIsSet(true);
        return this;
    }

    public void unsetPage() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGE_ISSET_ID);
    }

    /** Returns true if field page is set (has been assigned a value) and false otherwise */
    public boolean isSetPage() {
        return EncodingUtils.testBit(__isset_bitfield, __PAGE_ISSET_ID);
    }

    public void setPageIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGE_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
        switch (field) {
            case TYPE_ID:
                if (value == null) {
                    unsetType_id();
                } else {
                    setType_id((Short) value);
                }
                break;

            case PAGE:
                if (value == null) {
                    unsetPage();
                } else {
                    setPage((Integer) value);
                }
                break;

        }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
            case TYPE_ID:
                return Short.valueOf(getType_id());

            case PAGE:
                return Integer.valueOf(getPage());

        }
        throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
        if (field == null) {
            throw new IllegalArgumentException();
        }

        switch (field) {
            case TYPE_ID:
                return isSetType_id();
            case PAGE:
                return isSetPage();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
            return false;
        if (that instanceof RegionTagRequest)
            return this.equals((RegionTagRequest) that);
        return false;
    }

    public boolean equals(RegionTagRequest that) {
        if (that == null)
            return false;

        boolean this_present_type_id = true;
        boolean that_present_type_id = true;
        if (this_present_type_id || that_present_type_id) {
            if (!(this_present_type_id && that_present_type_id))
                return false;
            if (this.type_id != that.type_id)
                return false;
        }

        boolean this_present_page = true;
        boolean that_present_page = true;
        if (this_present_page || that_present_page) {
            if (!(this_present_page && that_present_page))
                return false;
            if (this.page != that.page)
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_type_id = true;
        list.add(present_type_id);
        if (present_type_id)
            list.add(type_id);

        boolean present_page = true;
        list.add(present_page);
        if (present_page)
            list.add(page);

        return list.hashCode();
    }

    @Override
    public int compareTo(RegionTagRequest other) {
        if (!getClass().equals(other.getClass())) {
            return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetType_id()).compareTo(other.isSetType_id());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetType_id()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.type_id, other.type_id);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetPage()).compareTo(other.isSetPage());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetPage()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.page, other.page);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
        return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("RegionTagRequest(");
        boolean first = true;

        sb.append("type_id:");
        sb.append(this.type_id);
        first = false;
        if (!first) sb.append(", ");
        sb.append("page:");
        sb.append(this.page);
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        try {
            write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        try {
            // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
            __isset_bitfield = 0;
            read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private static class RegionTagRequestStandardSchemeFactory implements SchemeFactory {
        public RegionTagRequestStandardScheme getScheme() {
            return new RegionTagRequestStandardScheme();
        }
    }

    private static class RegionTagRequestStandardScheme extends StandardScheme<RegionTagRequest> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, RegionTagRequest struct) throws TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true) {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                    break;
                }
                switch (schemeField.id) {
                    case 1: // TYPE_ID
                        if (schemeField.type == org.mi.thrift.protocol.TType.I16) {
                            struct.type_id = iprot.readI16();
                            struct.setType_idIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // PAGE
                        if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                            struct.page = iprot.readI32();
                            struct.setPageIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                        org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, RegionTagRequest struct) throws TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            oprot.writeFieldBegin(TYPE_ID_FIELD_DESC);
            oprot.writeI16(struct.type_id);
            oprot.writeFieldEnd();
            oprot.writeFieldBegin(PAGE_FIELD_DESC);
            oprot.writeI32(struct.page);
            oprot.writeFieldEnd();
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class RegionTagRequestTupleSchemeFactory implements SchemeFactory {
        public RegionTagRequestTupleScheme getScheme() {
            return new RegionTagRequestTupleScheme();
        }
    }

    private static class RegionTagRequestTupleScheme extends TupleScheme<RegionTagRequest> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, RegionTagRequest struct) throws TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetType_id()) {
                optionals.set(0);
            }
            if (struct.isSetPage()) {
                optionals.set(1);
            }
            oprot.writeBitSet(optionals, 2);
            if (struct.isSetType_id()) {
                oprot.writeI16(struct.type_id);
            }
            if (struct.isSetPage()) {
                oprot.writeI32(struct.page);
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, RegionTagRequest struct) throws TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(2);
            if (incoming.get(0)) {
                struct.type_id = iprot.readI16();
                struct.setType_idIsSet(true);
            }
            if (incoming.get(1)) {
                struct.page = iprot.readI32();
                struct.setPageIsSet(true);
            }
        }
    }

}

