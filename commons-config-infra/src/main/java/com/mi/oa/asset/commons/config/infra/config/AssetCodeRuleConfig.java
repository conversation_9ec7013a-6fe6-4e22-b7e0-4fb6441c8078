package com.mi.oa.asset.commons.config.infra.config;

import com.mi.oa.asset.commons.config.domain.systemvar.valobj.FieldRuleConfig;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@ConfigurationProperties(prefix = "codingrules")
public class AssetCodeRuleConfig {
    @Getter
    @Setter
    private List<FieldRuleConfig> defaultList;

    @Getter
    @Setter
    private List<FieldRuleConfig> codeRuleList;
}
