package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "amg_warehouse")
public class WarehousePo extends BasePo {
    private String houseCode;
    private String houseName;
    private String houseType;
    private String adminCodes;
    private String adminNames;
    private String country;
    private String currency;
    private String address;
    private Boolean shelves;
    private String shelvesNum;
    private String thirdHouseCode;
    private String sapHouseCode;
    private String factoryCode;
    private String companyCode;
    private String services;
    private String servicesName;
    private String zitiArea;
    private String mailArea;
    private Integer houseStatus;
    private String businessLine;
    private String logisticsType;
    private String departCode;
    private String departName;
    private String departFullName;
    private String phone;
    private Integer sort;
    private String area;
    private String email;
    private String productTypes;
    private String stockAllotConfig;

}
