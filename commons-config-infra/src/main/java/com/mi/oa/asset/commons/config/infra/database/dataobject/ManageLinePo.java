package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/8/16 17:09
 * 
 */
/**
    * 管理线配置表
    */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "amg_manage_line")
public class ManageLinePo extends BasePo {
    /**
     * 管理线
     */
    @TableField(value = "manage_line")
    private String manageLine;

    /**
     * 管理线名称
     */
    @TableField(value = "manage_line_name")
    private String manageLineName;

    @TableField(value = "manage_line_name_en")
    private String manageLineNameEn;

    /**
     * 资产管理员账号
     */
    @TableField(value = "manager_user")
    private String managerUser;

    /**
     * 资产管理员姓名
     */
    @TableField(value = "manager_user_name")
    private String managerUserName;

    /**
     * 审批人账号
     */
    @TableField(value = "approval_user")
    private String approvalUser;

    /**
     * 审批人姓名
     */
    @TableField(value = "approval_user_name")
    private String approvalUserName;

    /**
     * 是否生效
     */
    @TableField(value = "is_valid")
    private String isValid;

    /**
     * 是否新管理线
     */
    @TableField(value = "is_new_line")
    private String isNewLine;

    /**
     * bpm唯一标识
     */
    @TableField(value = "business_key")
    private String businessKey;

    /**
     * 单据状态
     */
    @TableField(value = "record_status")
    private String recordStatus;
}