package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.commonfunc.entity.MyCommonFunc;
import com.mi.oa.asset.commons.config.domain.commonfunc.repository.MyCommonFuncRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.MyCommonFuncPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.MyCommonFuncPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.MyCommonFuncRepoConverter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/13 11:20
 * @description
 */
@Service
public class MyCommonFuncRepoImpl extends ServiceImpl<MyCommonFuncPoMapper, MyCommonFuncPo> implements MyCommonFuncRepo {
    @Resource
    private MyCommonFuncRepoConverter funcRepoConverter;


    @Override
    public void saveMyFuncSort(MyCommonFunc commonFunc) {
        LambdaQueryWrapper<MyCommonFuncPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MyCommonFuncPo::getManageLineCode, commonFunc.getManageLineCode())
                .eq(MyCommonFuncPo::getCreateUser, commonFunc.getCreateUser())
                .eq(MyCommonFuncPo::getIsDefault, 0);
        MyCommonFuncPo existingFunc = this.getOne(queryWrapper);
        MyCommonFuncPo myCommonFuncPo = funcRepoConverter.doToPo(commonFunc);
        if (existingFunc != null) {
            myCommonFuncPo.setId(existingFunc.getId());
            this.updateById(myCommonFuncPo);
        } else {
            this.save(myCommonFuncPo);
        }
    }


    @Override
    public MyCommonFunc listMyFuncSort(String manageLineCode, String createUser) {
        //根据 manageLineCode和createUser查询非默认的数据，如果存在则返回，如果不存在则根据manageLineCode查询isdefault=1的数据
        LambdaQueryWrapper<MyCommonFuncPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MyCommonFuncPo::getManageLineCode, manageLineCode)
                .eq(MyCommonFuncPo::getCreateUser, createUser)
                .eq(MyCommonFuncPo::getIsDefault, 0);
        MyCommonFuncPo func = this.getOne(queryWrapper);
        if (func==null) {
            queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MyCommonFuncPo::getManageLineCode, manageLineCode)
                    .eq(MyCommonFuncPo::getIsDefault, 1);
            func = this.getOne(queryWrapper);
        }
        return funcRepoConverter.poToDo(func);

    }
}
