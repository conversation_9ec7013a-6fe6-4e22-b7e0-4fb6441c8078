package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/05/29/09:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("asset_acc")
public class AssetSapTypePo implements Serializable {
    /**
     * SAP资产分类ID acc_id
     */
    @TableId(type = IdType.INPUT)
    private String accId;

    /**
     * 资产分类 acc_name
     */
    private String accName;

    /**
     * 资产分类(英文) acc_name_en
     */
    private String accNameEn;

    /**
     * 分类编码 acc_code
     */
    private String accCode;

    /**
     * 使用年限 use_year
     */
    private BigDecimal useYear;

    /**
     * 年折旧率% dep_rate
     */
    private BigDecimal depRate;

    /**
     * 净残值比例% less_rate
     */
    private BigDecimal lessRate;

    /**
     * 分类说明 acc_memo
     */
    private String accMemo;

    /**
     * 使用期间(月) use_month
     */
    private BigDecimal useMonth;

    /**
     * 折旧码 dep_code
     */
    private String depCode;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 是否启用 is_qy
     */
    private String isQy;

    /**
     * 是否生效 is_valid
     */
    private String isValid;

    /**
     * 报废惯例 scrap_type
     */
    private String scrapType;

    /**
     * 资产结算科目 settle_account
     */
    private String settleAccount;

    /**
     * 级别 acc_level
     */
    private BigDecimal accLevel;

    /**
     * 公司代码 company_code
     */
    private String companyCode;
}