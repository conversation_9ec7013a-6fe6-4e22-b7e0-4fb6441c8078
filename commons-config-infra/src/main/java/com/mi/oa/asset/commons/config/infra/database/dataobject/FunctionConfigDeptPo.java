package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/04/28/03:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_function_config_dept")
public class FunctionConfigDeptPo extends BasePo {

    /**
     * 配置明细id config_item_id
     */
    private Integer configItemId;

    /**
     * 配置id config_id
     */
    private Integer configId;

    /**
     * 部门编码 dept_code
     */
    private String deptCode;

    /**
     * 部门名称 dept_name
     */
    private String deptName;

}