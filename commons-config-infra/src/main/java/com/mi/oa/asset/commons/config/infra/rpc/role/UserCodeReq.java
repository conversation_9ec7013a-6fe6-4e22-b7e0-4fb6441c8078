package com.mi.oa.asset.commons.config.infra.rpc.role;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/12 20:52
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserCodeReq implements Serializable {
    private static final long serialVersionUID = 5076907748123526863L;

    private String userCode;

    private List<String> roleNos;
}
