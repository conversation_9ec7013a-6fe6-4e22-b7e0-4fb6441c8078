package com.mi.oa.asset.commons.config.infra.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetSkuPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/12 16:33
 */

public interface AssetSkuPoMapper extends BaseMapper<AssetSkuPo> {

    IPage<AssetSkuPo> pageSku(@Param("relationCateIds") List<Integer> relationCateIds,
                              @Param("businessCodes") List<String> businessCodes,
                              @Param("keyword") String keyword,
                              @Param("disabled") Boolean disabled,
                              @Param("containMiGoods") Boolean containMiGoods,
                              @Param("page") IPage<AssetSkuPo> page);
}