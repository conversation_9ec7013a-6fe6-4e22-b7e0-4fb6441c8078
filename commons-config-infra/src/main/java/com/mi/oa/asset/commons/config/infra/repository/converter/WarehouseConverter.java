package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.warehouse.entity.Warehouse;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.WarehousePo;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Mapper(componentModel = "spring")
public interface WarehouseConverter extends CommonConverter {
    @Mapping(target = "stockAllotConfig", expression = "java(toJsonString(source.getStockAllotConfig()))")
    WarehousePo toPo(Warehouse source);

    List<WarehousePo> toPoList(List<Warehouse> source);
    @Mapping(target = "stockAllotConfig", expression = "java(toMap(source.getStockAllotConfig()))")
    Warehouse toDo(WarehousePo source);

    List<Warehouse> toDoList(List<WarehousePo> source);

    @Named("toJsonString")
    default String toJsonString(Map<String, String> source) {
        if (Objects.isNull(source) || source.isEmpty()) return StringUtils.EMPTY;
        return JacksonUtils.bean2Json(source);
    }

    @Named("toMap")
    default Map<String, String> toMap(String source) {
        if (StringUtils.isEmpty(source)) return null;
        return JacksonUtils.json2Bean(source, Map.class);
    }
}
