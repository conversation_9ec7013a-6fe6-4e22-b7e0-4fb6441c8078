package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/02/24/03:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_dimension_resource")
public class DimensionResourcePo extends BasePo {
    /**
     *  dimension_code
     */
    private String dimensionCode;

    /**
     *  name
     */
    private String dimensionName;

    /**
     *  sql
     */
    private String resourceSql;
    /**
     *  field_code
     */
    private String fieldCode;
    /**
     *  field_name
     */
    private String fieldName;
    /**
     *  field_level
     */
    private String fieldLevel;
    /**
     *  parent_field_code
     */
    private String parentFieldCode;
    /**
     *  resource_type
     */
    private String resourceType;
    /**
     *  first_level_value
     */
    private String firstLevelValue;
    /**
     *  is_layer
     */
    private Boolean isLayer;


}