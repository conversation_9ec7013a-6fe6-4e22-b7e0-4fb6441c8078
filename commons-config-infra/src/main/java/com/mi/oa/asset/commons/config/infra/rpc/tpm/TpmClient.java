package com.mi.oa.asset.commons.config.infra.rpc.tpm;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mi.oa.asset.commons.config.domain.position.valobj.TpmMdmItem;
import com.mi.oa.asset.commons.config.domain.position.valobj.TpmPage;
import com.mi.oa.asset.commons.config.infra.rpc.config.TpmConfig;
import com.mi.oa.asset.commons.config.infra.rpc.utils.X5HttpUtil;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.xiaomi.core.auth.x5.X5AppInfo;
import com.xiaomi.core.auth.x5.X5Response;
import com.xiaomi.core.auth.x5.X5Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class TpmClient {

    @Resource
    private TpmConfig tpmConfig;

    private static final String METHOD = "queryMdmForEAM";

    public TpmPage<List<TpmMdmItem>> getPositions(String mdmType, int pageNum, int pageSize) {
        X5AppInfo x5AppInfo = new X5AppInfo(tpmConfig.getAppId(), tpmConfig.getAppSecret(), METHOD);
        Map<String, Object> params = new HashMap<>();
        params.put("mdmType", mdmType);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        log.info("请求TPM服务，参数：{}", JacksonUtils.bean2Json(params));
        String reqData = X5Utils.buildX5RequestBody(x5AppInfo, JacksonUtils.bean2Json(params));
        try {
            String response = X5HttpUtil.doX5Post(tpmConfig.getHost(), reqData);
            log.info("TPM服务返回结果：{}", response);
            TypeReference<X5Response<TpmPage<List<TpmMdmItem>>>> typeReference = new TypeReference<X5Response<TpmPage<List<TpmMdmItem>>>>() {
            };
            X5Response<TpmPage<List<TpmMdmItem>>> result = JacksonUtils.json2TypeReference(response, typeReference);
            return result.getBody();
        } catch (Exception e) {
            log.error("请求TPM服务失败", e);
        }
        return null;
    }
}
