package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.BusinessLinePo;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Map;
import java.util.Objects;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/31 19:34
 */

@Mapper(componentModel = "spring")
public interface BusinessLinePoConvertor extends CommonConverter {

    @Mapping(source = "id", target = "businessLineId")
    @Mapping(target = "configs", expression = "java(toMap(businessLinePo.getConfigs()))")
    BusinessLineDo poToDo(BusinessLinePo businessLinePo);

    @Mapping(source = "businessLineId", target = "id")
    @Mapping(target = "configs", expression = "java(toJsonString(businessLineDo.getConfigs()))")
    BusinessLinePo doToPo(BusinessLineDo businessLineDo);

    List<BusinessLineDo> poToDoList(List<BusinessLinePo> businessLinePo);


    @Named("toJsonString")
    default String toJsonString(Map<String, String> source) {
        if (Objects.isNull(source) || source.isEmpty()) return StringUtils.EMPTY;
        return JacksonUtils.bean2Json(source);
    }

    @Named("toMap")
    default Map<String, String> toMap(String source) {
        if (StringUtils.isEmpty(source)) return null;
        return JacksonUtils.json2Bean(source, Map.class);
    }
}
