package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.international.entity.CountryCurrencyDo;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.CountryCurrencyPo;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 *
 */
@Mapper(componentModel = "spring")
public interface CountryCurrencyPoConvertor extends CommonConverter {

    CountryCurrencyDo poToDo(CountryCurrencyPo countryCurrencyPo);

    CountryCurrencyPo doToPo(CountryCurrencyDo countryCurrencyDo);

    List<CountryCurrencyDo> poToDoList(List<CountryCurrencyPo> countryCurrencyPoList);

    @Named("toJsonString")
    default String toJsonString(Map<String, String> source) {
        if (Objects.isNull(source) || source.isEmpty()) return StringUtils.EMPTY;
        return JacksonUtils.bean2Json(source);
    }

    @Named("toMap")
    default Map<String, String> toMap(String source) {
        if (StringUtils.isEmpty(source)) return null;
        return JacksonUtils.json2Bean(source, Map.class);
    }
}
