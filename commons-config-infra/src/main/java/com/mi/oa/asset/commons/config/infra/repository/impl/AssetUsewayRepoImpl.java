package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.assetuseway.entity.AssetUseway;
import com.mi.oa.asset.commons.config.domain.assetuseway.repository.AssetUsewayRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetUsewayPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.AssetUsewayPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AssetUsewayRepoConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 办公用途 接口实现类
 *
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@Slf4j
@Service
public class AssetUsewayRepoImpl extends ServiceImpl<AssetUsewayPoMapper, AssetUsewayPo> implements AssetUsewayRepo {

    @Resource
    private AssetUsewayRepoConverter converter;

    private LambdaQueryWrapper<AssetUsewayPo> getWrapper(AssetUseway entity) {
        AssetUsewayPo po = converter.toAssetUsewayPo(entity);
        LambdaQueryWrapper<AssetUsewayPo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StringUtils.isNotBlank(po.getUsewayCode()), AssetUsewayPo::getUsewayCode, po.getUsewayCode());
        wrapper.eq(StringUtils.isNotBlank(po.getUsewayName()), AssetUsewayPo::getUsewayName, po.getUsewayName());
        return wrapper;
    }

    @Override
    public List<AssetUseway> list(AssetUseway entity) {
        List<AssetUsewayPo> list = list(getWrapper(entity));
        return converter.toAssetUseways(list);
    }

}
