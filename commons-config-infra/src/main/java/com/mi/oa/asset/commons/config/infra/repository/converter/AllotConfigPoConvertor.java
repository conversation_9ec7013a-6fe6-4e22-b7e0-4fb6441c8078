package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.commons.config.domain.common.valobj.AllotConfig;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AllotConfigPo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/31 19:34
 */

@Mapper(componentModel = "spring")
public interface AllotConfigPoConvertor extends CommonConverter {

    @Mapping(target = "configId", source = "id")
    AllotConfig poToDo(AllotConfigPo allotConfigPo);

    List<AllotConfig> poToDoList(List<AllotConfigPo> allotConfigPoList);

    @Mapping(target = "id", source = "configId")
    AllotConfigPo doToPo(AllotConfig AllotConfig);

    List<AllotConfigPo> doToPoList(List<AllotConfig> allotConfigList);

    // str转list
    default List<String> str2List(String src) {
        if (StringUtils.isBlank(src)) return new ArrayList<>();
        String[] split = src.split(CommonConstant.COMMA);
        return Arrays.asList(split);
    }

    // list转str
    default String list2Str(List<String> src) {
        if (CollectionUtils.isEmpty(src)) {
            return "";
        }
        return StringUtils.join(src, CommonConstant.COMMA);
    }
}
