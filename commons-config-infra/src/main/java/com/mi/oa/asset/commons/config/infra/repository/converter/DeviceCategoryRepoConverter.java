package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.assetcategory.entity.old.DeviceCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.old.MaterialCategory;
import com.mi.oa.asset.commons.config.infra.database.dataobject.old.DeviceCategoryPO;
import com.mi.oa.asset.commons.config.infra.database.dataobject.old.MaterialCategoryPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/26 21:14
 * @description 旧设备分类数据-用于数据同步
 */
@Mapper(componentModel = "spring")
public interface DeviceCategoryRepoConverter {
    DeviceCategory toDeviceCategory(DeviceCategoryPO source);

    List<DeviceCategory> toDeviceCategories(List<DeviceCategoryPO> source);

    MaterialCategory toMaterialCategory(MaterialCategoryPO source);

    List<MaterialCategory> toMaterialCategorys(List<MaterialCategoryPO> source);

}
