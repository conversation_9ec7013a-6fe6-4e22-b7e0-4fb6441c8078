package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2024/6/12 17:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "amg_template_url")
public class TemplateUrlPo extends BasePo {
    @TableField(value = "fun_id")
    private String funId;

    @TableField(value = "template_name")
    private String templateName;

    @TableField(value = "template_url")
    private String templateUrl;

    @TableField(value = "template_url_en")
    private String templateUrlEn;
}
