package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 用途
 * @TableName amg_use
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_use")
public class UsePo extends BasePo {

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 用途编码
     */
    private String useCode;

    /**
     * 用途名称
     */
    private String useName;

    /**
     * 用途名称（英文）
     */
    private String useNameEn;

    /**
     * 用途类别
     */
    private Integer useType;

    /**
     * 适用范围
     */
    private Integer scope;

    /**
     * 是否禁用，1-是，0-否
     */
    private Integer disabled;

    /**
     * 排序
     */
    private Integer sorted;

    /**
     * 备注
     */
    private String remark;

    /**
     * 备注（英文）
     */
    private String remarkEn;
}