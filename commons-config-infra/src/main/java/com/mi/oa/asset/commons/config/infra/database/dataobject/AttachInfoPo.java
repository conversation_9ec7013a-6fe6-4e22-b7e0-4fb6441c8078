package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/10/26/02:37
 */
@Data
@TableName("amg_attach_info")
public class AttachInfoPo extends BasePo {

    /**
     * 业务单据id
     */
    @TableField(value = "record_id")
    private Integer recordId;

    /**
     * 业务单据编码
     */
    @TableField(value = "record_no")
    private String recordNo;

    /**
     * 业务单据类型
     */
    @TableField(value = "record_type")
    private String recordType;

    /**
     * 附件原始名称
     */
    @TableField(value = "origin_name")
    private String originName;

    /**
     * 附件名称
     */
    @TableField(value = "attach_name")
    private String attachName;

    /**
     * fds文件链接
     */
    @TableField(value = "attach_link")
    private String attachLink;
}