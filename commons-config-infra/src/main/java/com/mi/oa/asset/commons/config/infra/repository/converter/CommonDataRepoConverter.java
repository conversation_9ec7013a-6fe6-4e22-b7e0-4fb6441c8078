package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.common.valobj.*;
import com.mi.oa.asset.commons.config.domain.labeltemplate.entity.LabelTemplate;
import com.mi.oa.asset.commons.config.domain.labeltemplate.valobj.LabelTemplateField;
import com.mi.oa.asset.commons.config.infra.database.dataobject.*;
import com.mi.oa.asset.eam.mybatis.FunctionSubscribePo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/11 15:49
 */

@Mapper(componentModel = "Spring")
public interface CommonDataRepoConverter {

    PurchaseCatalog toPurchaseCatalog(PurchaseCatalogPo source);

    List<PurchaseCatalog> toPurchaseCatalogs(List<PurchaseCatalogPo> source);

    Company toCompany(CompanyPo source);

    List<Company> toCompanies(List<CompanyPo> source);

    List<CompanyPo> toCompanyPo(List<Company> source);

    SapAssetCategory toSapCategory(PurchaseCatalogPo source);

    List<SapAssetCategory> toSapCategories(List<PurchaseCatalogPo> source);

    List<SapAssetCategory> toSapCategories4EAM(List<AssetSapTypePo> source);

    @Mapping(source = "accCode", target = "sapCateCode")
    @Mapping(source = "accName", target = "sapCateName")
    @Mapping(source = "accNameEn", target = "sapCateNameEn")
    SapAssetCategory toSapCategory4EAM(AssetSapTypePo source);

    Provider toProvider(ProviderPo source);

    List<Provider> toProviders(List<ProviderPo> source);

    List<ProviderPo> toProviderPo(List<Provider> source);

    TemplateUrl toTemplateUrl(TemplateUrlPo source);

    LabelTemplate toLabelTemplate(LabelTemplatePo source);

    List<LabelTemplate> toLabelTemplates(List<LabelTemplatePo> source);

    List<LabelTemplateField> toLabelTemplateFields(List<LabelTemplateFieldPo> source);

    LabelTemplatePo toLabelTemplate(LabelTemplate source);

    LabelTemplateFieldPo toLabelTemplateField(LabelTemplateField source);

    FunctionSubscribe toSubscribe(FunctionSubscribePo subscribePo);

    List<FunctionSubscribe> toSubscribeList(List<FunctionSubscribePo> subscribePos);

    FunctionSubscribePo toSubscribePo(FunctionSubscribe subscribe);
}
