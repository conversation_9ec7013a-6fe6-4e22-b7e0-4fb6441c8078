/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.TException;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2020-5-26")
public class IdRequest implements org.mi.thrift.TBase<IdRequest, IdRequest._Fields>, java.io.Serializable, Cloneable, Comparable<IdRequest> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("IdRequest");

    private static final org.mi.thrift.protocol.TField IDS_FIELD_DESC = new org.mi.thrift.protocol.TField("ids", org.mi.thrift.protocol.TType.LIST, (short) 1);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();

    static {
        schemes.put(StandardScheme.class, new IdRequestStandardSchemeFactory());
        schemes.put(TupleScheme.class, new IdRequestTupleSchemeFactory());
    }

    public List<Long> ids; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        IDS((short) 1, "ids");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
            for (_Fields field : EnumSet.allOf(_Fields.class)) {
                byName.put(field.getFieldName(), field);
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch (fieldId) {
                case 1: // IDS
                    return IDS;
                default:
                    return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
            _Fields fields = findByThriftId(fieldId);
            if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
            return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
            return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
            _thriftId = thriftId;
            _fieldName = fieldName;
        }

        public short getThriftFieldId() {
            return _thriftId;
        }

        public String getFieldName() {
            return _fieldName;
        }
    }

    // isset id assignments
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;

    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.IDS, new org.mi.thrift.meta_data.FieldMetaData("ids", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.ListMetaData(org.mi.thrift.protocol.TType.LIST,
                        new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I64))));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(IdRequest.class, metaDataMap);
    }

    public IdRequest() {
    }

    public IdRequest(
            List<Long> ids) {
        this();
        this.ids = ids;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public IdRequest(IdRequest other) {
        if (other.isSetIds()) {
            List<Long> __this__ids = new ArrayList<Long>(other.ids);
            this.ids = __this__ids;
        }
    }

    public IdRequest deepCopy() {
        return new IdRequest(this);
    }

    @Override
    public void clear() {
        this.ids = null;
    }

    public int getIdsSize() {
        return (this.ids == null) ? 0 : this.ids.size();
    }

    public Iterator<Long> getIdsIterator() {
        return (this.ids == null) ? null : this.ids.iterator();
    }

    public void addToIds(long elem) {
        if (this.ids == null) {
            this.ids = new ArrayList<Long>();
        }
        this.ids.add(elem);
    }

    public List<Long> getIds() {
        return this.ids;
    }

    public IdRequest setIds(List<Long> ids) {
        this.ids = ids;
        return this;
    }

    public void unsetIds() {
        this.ids = null;
    }

    /** Returns true if field ids is set (has been assigned a value) and false otherwise */
    public boolean isSetIds() {
        return this.ids != null;
    }

    public void setIdsIsSet(boolean value) {
        if (!value) {
            this.ids = null;
        }
    }

    public void setFieldValue(_Fields field, Object value) {
        switch (field) {
            case IDS:
                if (value == null) {
                    unsetIds();
                } else {
                    setIds((List<Long>) value);
                }
                break;

        }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
            case IDS:
                return getIds();

        }
        throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
        if (field == null) {
            throw new IllegalArgumentException();
        }

        switch (field) {
            case IDS:
                return isSetIds();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
            return false;
        if (that instanceof IdRequest)
            return this.equals((IdRequest) that);
        return false;
    }

    public boolean equals(IdRequest that) {
        if (that == null)
            return false;

        boolean this_present_ids = true && this.isSetIds();
        boolean that_present_ids = true && that.isSetIds();
        if (this_present_ids || that_present_ids) {
            if (!(this_present_ids && that_present_ids))
                return false;
            if (!this.ids.equals(that.ids))
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_ids = true && (isSetIds());
        list.add(present_ids);
        if (present_ids)
            list.add(ids);

        return list.hashCode();
    }

    @Override
    public int compareTo(IdRequest other) {
        if (!getClass().equals(other.getClass())) {
            return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetIds()).compareTo(other.isSetIds());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetIds()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.ids, other.ids);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
        return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("IdRequest(");
        boolean first = true;

        sb.append("ids:");
        if (this.ids == null) {
            sb.append("null");
        } else {
            sb.append(this.ids);
        }
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        try {
            write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        try {
            read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private static class IdRequestStandardSchemeFactory implements SchemeFactory {
        public IdRequestStandardScheme getScheme() {
            return new IdRequestStandardScheme();
        }
    }

    private static class IdRequestStandardScheme extends StandardScheme<IdRequest> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, IdRequest struct) throws TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true) {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                    break;
                }
                switch (schemeField.id) {
                    case 1: // IDS
                        if (schemeField.type == org.mi.thrift.protocol.TType.LIST) {
                            {
                                org.mi.thrift.protocol.TList _list0 = iprot.readListBegin();
                                struct.ids = new ArrayList<Long>(_list0.size);
                                long _elem1;
                                for (int _i2 = 0; _i2 < _list0.size; ++_i2) {
                                    _elem1 = iprot.readI64();
                                    struct.ids.add(_elem1);
                                }
                                iprot.readListEnd();
                            }
                            struct.setIdsIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                        org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, IdRequest struct) throws TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            if (struct.ids != null) {
                oprot.writeFieldBegin(IDS_FIELD_DESC);
                {
                    oprot.writeListBegin(new org.mi.thrift.protocol.TList(org.mi.thrift.protocol.TType.I64, struct.ids.size()));
                    for (long _iter3 : struct.ids) {
                        oprot.writeI64(_iter3);
                    }
                    oprot.writeListEnd();
                }
                oprot.writeFieldEnd();
            }
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class IdRequestTupleSchemeFactory implements SchemeFactory {
        public IdRequestTupleScheme getScheme() {
            return new IdRequestTupleScheme();
        }
    }

    private static class IdRequestTupleScheme extends TupleScheme<IdRequest> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, IdRequest struct) throws TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetIds()) {
                optionals.set(0);
            }
            oprot.writeBitSet(optionals, 1);
            if (struct.isSetIds()) {
                {
                    oprot.writeI32(struct.ids.size());
                    for (long _iter4 : struct.ids) {
                        oprot.writeI64(_iter4);
                    }
                }
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, IdRequest struct) throws TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(1);
            if (incoming.get(0)) {
                {
                    org.mi.thrift.protocol.TList _list5 = new org.mi.thrift.protocol.TList(org.mi.thrift.protocol.TType.I64, iprot.readI32());
                    struct.ids = new ArrayList<Long>(_list5.size);
                    long _elem6;
                    for (int _i7 = 0; _i7 < _list5.size; ++_i7) {
                        _elem6 = iprot.readI64();
                        struct.ids.add(_elem6);
                    }
                }
                struct.setIdsIsSet(true);
            }
        }
    }

}

