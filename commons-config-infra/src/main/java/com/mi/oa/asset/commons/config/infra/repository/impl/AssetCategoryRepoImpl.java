package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.AssetCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.repository.AssetCategoryRepo;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetCategoryPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.AssetCategoryPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AssetCategoryRepoConverter;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:39
 */

@Service
public class AssetCategoryRepoImpl extends ServiceImpl<AssetCategoryPoMapper, AssetCategoryPo> implements AssetCategoryRepo {

    @Resource
    private AssetCategoryPoMapper assetCategoryPoMapper;

    @Resource
    private AssetCategoryRepoConverter converter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAssetCategory(AssetCategory assetCategory) {
        AssetCategoryPo data = converter.toAssetCategoryPo(assetCategory);
        saveOrUpdate(data);
        assetCategory.setCateId(data.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAssetCategories(List<AssetCategory> assetCategories) {
        List<AssetCategoryPo> data = converter.toAssetCategoryPos(assetCategories);
        saveOrUpdateBatch(data);
        assetCategories.forEach(c -> data.stream()
                .filter(d -> d.getCateCode().equals(c.getCateCode()))
                .findFirst()
                .ifPresent(d -> c.setCateId(d.getId())));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCatePath(AssetCategory assetCategory) {
        assetCategory.setCatePath(assetCategory.getCatePath() + "-" + assetCategory.getCateId());
        saveOrUpdate(converter.toAssetCategoryPo(assetCategory));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCatePathBatch(List<AssetCategory> assetCategorys){
        assetCategorys.forEach(o->o.setCatePath(StringUtils.isNotBlank(o.getCatePath()) ? o.getCatePath() + "-" + o.getCateId() : "0-"+o.getCateId()));
        saveOrUpdateBatch(converter.toAssetCategoryPos(assetCategorys));
    }

    @Override
    public List<AssetCategory> getByCateCode(String businessLine, List<String> cateCodes) {
        if(CollectionUtils.isEmpty(cateCodes)) return Collections.emptyList();
        return converter.toAssetCategories(
                lambdaQuery().eq(StringUtils.isNotBlank(businessLine), AssetCategoryPo::getBusinessLine,businessLine)
                        .in(AssetCategoryPo::getCateCode, cateCodes)
                        .list()
        );
    }

    @Override
    public AssetCategory getByCateCode(String businessLine, String cateCode) {
        return converter.toAssetCategory(
                lambdaQuery().eq(AssetCategoryPo::getBusinessLine,businessLine)
                        .eq(AssetCategoryPo::getCateCode, cateCode)
                        .one()
        );
    }

    @Override
    public List<AssetCategory> getByCateCodes(List<String> cateCodes) {
        return converter.toAssetCategories(lambdaQuery().in(AssetCategoryPo::getCateCode, cateCodes).list());
    }

    @Override
    public List<AssetCategory> getAllAssetCategory(List<String> businessLineCodes, boolean enabled) {
        return converter.toAssetCategories(
                lambdaQuery().eq(enabled, AssetCategoryPo::getDisabled, 0)
                        .in(CollectionUtils.isNotEmpty(businessLineCodes), AssetCategoryPo::getBusinessLine, businessLineCodes)
                        .list()
        );
    }

    @Override
    public AssetCategory getAssetCategory(Integer id) {
        return converter.toAssetCategory(lambdaQuery().eq(AssetCategoryPo::getId, id).one());
    }

    @Override
    public List<AssetCategory> getAssetCategories(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) return new ArrayList<>();

        return converter.toAssetCategories(lambdaQuery().in(AssetCategoryPo::getId, ids).list());
    }

    @Override
    public void deleteByPath(List<String> catePaths) {
        if(CollectionUtils.isEmpty(catePaths)) return;

        lambdaUpdate().in(AssetCategoryPo::getCatePath, catePaths)
                .or().nested(q -> catePaths.forEach(p -> q.or().likeRight(AssetCategoryPo::getCatePath, p + "-")))
                .remove();
    }

    @Override
    public PageData<AssetCategory> assetCategoryPageData(List<String> businessLineCodes, String keyword, PageRequest pageRequest) {
        IPage<AssetCategoryPo> page = page(
                new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize()),
                Wrappers.lambdaQuery(AssetCategoryPo.class)
                        .in(CollectionUtils.isNotEmpty(businessLineCodes), AssetCategoryPo::getBusinessLine, businessLineCodes)
                        .nested(StringUtils.isNotBlank(keyword),
                                q -> q.like(AssetCategoryPo::getCateCode, keyword)
                                        .or().like(AssetCategoryPo::getCateName, keyword)
                                        .or().like(AssetCategoryPo::getCateNameEn, keyword)
                        ).orderByDesc(AssetCategoryPo::getSort)
        );

        return converter.toPageData(page, converter::toAssetCategories);
    }

    @Override
    public PageData<AssetCategory> assetCategoryPageData(String catePath, String keyword, PageRequest pageRequest) {
        IPage<AssetCategoryPo> page = page(
                new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize()),
                Wrappers.lambdaQuery(AssetCategoryPo.class)
                        .eq(AssetCategoryPo::getIsDeleted, 0)
                        .nested(StringUtils.isNotBlank(catePath),
                                q -> q.eq(AssetCategoryPo::getCatePath, catePath)
                                        .or().likeRight(AssetCategoryPo::getCatePath, catePath + "-")

                        )
                        .nested(StringUtils.isNotBlank(keyword),
                                q -> q.like(AssetCategoryPo::getCateCode, keyword)
                                        .or().like(AssetCategoryPo::getCateName, keyword)
                                        .or().like(AssetCategoryPo::getCateNameEn, keyword)
                        ).orderByDesc(AssetCategoryPo::getSort)
        );

        return converter.toPageData(page, converter::toAssetCategories);
    }

    @Override
    public List<AssetCategory> getByCatalogCodes(List<String> catalogCodes, BusinessLine businessLine) {
        return converter.toAssetCategories(
                lambdaQuery()
                        .in(AssetCategoryPo::getPurchaseCatalogCode, catalogCodes)
                        .eq(AssetCategoryPo::getBusinessLine, null != businessLine ? businessLine.getCode() : "null")
                        .eq(AssetCategoryPo::getDataSource, DataCreateSource.SYS_MDM.getCode())
                        .list()
        );
    }

    @Override
    public List<AssetCategory> getByCatePaths(List<String> catePaths) {
        if (CollectionUtils.isEmpty(catePaths)) return new ArrayList<>();

        return converter.toAssetCategories(
                lambdaQuery()
                        .eq(AssetCategoryPo::getIsDeleted, 0)
                        .in(AssetCategoryPo::getCatePath, catePaths)
                        .or().nested(q -> catePaths.forEach(p -> q.or().likeRight(AssetCategoryPo::getCatePath, p + "-")))
                        .list()
        );
    }

    @Override
    public boolean isExists(String cateCode) {
        if(StringUtils.isBlank(cateCode)) return false;
        return lambdaQuery()
                .eq(AssetCategoryPo::getCateCode, cateCode)
                .eq(AssetCategoryPo::getIsDeleted, 0)
                .exists();
    }

}
