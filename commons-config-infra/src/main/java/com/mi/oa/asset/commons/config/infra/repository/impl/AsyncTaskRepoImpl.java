package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.common.entity.AsyncTask;
import com.mi.oa.asset.commons.config.domain.common.repository.AsyncTaskRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AsyncTaskPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.AsyncTaskPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AsyncTaskRepoConvertor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/7/12 10:04
 */

@Service
public class AsyncTaskRepoImpl extends ServiceImpl<AsyncTaskPoMapper, AsyncTaskPo> implements AsyncTaskRepo {

    @Resource
    private AsyncTaskRepoConvertor asyncTaskRepoConvertor;

    @Override
    public Integer saveTask(AsyncTask asyncTask) {
        AsyncTaskPo asyncTaskPo = asyncTaskRepoConvertor.doToPo(asyncTask);
        this.saveOrUpdate(asyncTaskPo);
        return asyncTaskPo.getId();
    }

    @Override
    public AsyncTask getTaskById(Integer taskId) {
        return asyncTaskRepoConvertor.poToDo(this.getById(taskId));
    }
}
