package com.mi.oa.asset.commons.config.infra.rpc.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@RefreshScope
@ConfigurationProperties(prefix = "tpm")
@Component
@Data
public class TpmConfig {
    private String host;

    private String appId;

    private String appSecret;

}
