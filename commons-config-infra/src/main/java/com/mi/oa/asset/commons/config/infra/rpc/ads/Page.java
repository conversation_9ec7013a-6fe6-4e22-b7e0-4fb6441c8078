/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.EncodingUtils;
import org.mi.thrift.TException;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;
import org.mi.thrift.server.AbstractNonblockingServer.*;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2020-5-26")
public class Page implements org.mi.thrift.TBase<Page, Page._Fields>, java.io.Serializable, Cloneable, Comparable<Page> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("Page");

    private static final org.mi.thrift.protocol.TField TOTAL_COUNT_FIELD_DESC = new org.mi.thrift.protocol.TField("total_count", org.mi.thrift.protocol.TType.I32, (short) 1);
    private static final org.mi.thrift.protocol.TField PAGE_COUNT_FIELD_DESC = new org.mi.thrift.protocol.TField("page_count", org.mi.thrift.protocol.TType.I32, (short) 2);
    private static final org.mi.thrift.protocol.TField CURRENT_PAGE_FIELD_DESC = new org.mi.thrift.protocol.TField("current_page", org.mi.thrift.protocol.TType.I32, (short) 3);
    private static final org.mi.thrift.protocol.TField PER_PAGE_FIELD_DESC = new org.mi.thrift.protocol.TField("per_page", org.mi.thrift.protocol.TType.I32, (short) 4);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();

    static {
        schemes.put(StandardScheme.class, new PageStandardSchemeFactory());
        schemes.put(TupleScheme.class, new PageTupleSchemeFactory());
    }

    public int total_count; // required
    public int page_count; // required
    public int current_page; // required
    public int per_page; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        TOTAL_COUNT((short) 1, "total_count"),
        PAGE_COUNT((short) 2, "page_count"),
        CURRENT_PAGE((short) 3, "current_page"),
        PER_PAGE((short) 4, "per_page");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
            for (_Fields field : EnumSet.allOf(_Fields.class)) {
                byName.put(field.getFieldName(), field);
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch (fieldId) {
                case 1: // TOTAL_COUNT
                    return TOTAL_COUNT;
                case 2: // PAGE_COUNT
                    return PAGE_COUNT;
                case 3: // CURRENT_PAGE
                    return CURRENT_PAGE;
                case 4: // PER_PAGE
                    return PER_PAGE;
                default:
                    return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
            _Fields fields = findByThriftId(fieldId);
            if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
            return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
            return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
            _thriftId = thriftId;
            _fieldName = fieldName;
        }

        public short getThriftFieldId() {
            return _thriftId;
        }

        public String getFieldName() {
            return _fieldName;
        }
    }

    // isset id assignments
    private static final int __TOTAL_COUNT_ISSET_ID = 0;
    private static final int __PAGE_COUNT_ISSET_ID = 1;
    private static final int __CURRENT_PAGE_ISSET_ID = 2;
    private static final int __PER_PAGE_ISSET_ID = 3;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;

    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.TOTAL_COUNT, new org.mi.thrift.meta_data.FieldMetaData("total_count", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
        tmpMap.put(_Fields.PAGE_COUNT, new org.mi.thrift.meta_data.FieldMetaData("page_count", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
        tmpMap.put(_Fields.CURRENT_PAGE, new org.mi.thrift.meta_data.FieldMetaData("current_page", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
        tmpMap.put(_Fields.PER_PAGE, new org.mi.thrift.meta_data.FieldMetaData("per_page", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(Page.class, metaDataMap);
    }

    public Page() {
    }

    public Page(
            int total_count,
            int page_count,
            int current_page,
            int per_page) {
        this();
        this.total_count = total_count;
        setTotal_countIsSet(true);
        this.page_count = page_count;
        setPage_countIsSet(true);
        this.current_page = current_page;
        setCurrent_pageIsSet(true);
        this.per_page = per_page;
        setPer_pageIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public Page(Page other) {
        __isset_bitfield = other.__isset_bitfield;
        this.total_count = other.total_count;
        this.page_count = other.page_count;
        this.current_page = other.current_page;
        this.per_page = other.per_page;
    }

    public Page deepCopy() {
        return new Page(this);
    }

    @Override
    public void clear() {
        setTotal_countIsSet(false);
        this.total_count = 0;
        setPage_countIsSet(false);
        this.page_count = 0;
        setCurrent_pageIsSet(false);
        this.current_page = 0;
        setPer_pageIsSet(false);
        this.per_page = 0;
    }

    public int getTotal_count() {
        return this.total_count;
    }

    public Page setTotal_count(int total_count) {
        this.total_count = total_count;
        setTotal_countIsSet(true);
        return this;
    }

    public void unsetTotal_count() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TOTAL_COUNT_ISSET_ID);
    }

    /** Returns true if field total_count is set (has been assigned a value) and false otherwise */
    public boolean isSetTotal_count() {
        return EncodingUtils.testBit(__isset_bitfield, __TOTAL_COUNT_ISSET_ID);
    }

    public void setTotal_countIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TOTAL_COUNT_ISSET_ID, value);
    }

    public int getPage_count() {
        return this.page_count;
    }

    public Page setPage_count(int page_count) {
        this.page_count = page_count;
        setPage_countIsSet(true);
        return this;
    }

    public void unsetPage_count() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGE_COUNT_ISSET_ID);
    }

    /** Returns true if field page_count is set (has been assigned a value) and false otherwise */
    public boolean isSetPage_count() {
        return EncodingUtils.testBit(__isset_bitfield, __PAGE_COUNT_ISSET_ID);
    }

    public void setPage_countIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGE_COUNT_ISSET_ID, value);
    }

    public int getCurrent_page() {
        return this.current_page;
    }

    public Page setCurrent_page(int current_page) {
        this.current_page = current_page;
        setCurrent_pageIsSet(true);
        return this;
    }

    public void unsetCurrent_page() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CURRENT_PAGE_ISSET_ID);
    }

    /** Returns true if field current_page is set (has been assigned a value) and false otherwise */
    public boolean isSetCurrent_page() {
        return EncodingUtils.testBit(__isset_bitfield, __CURRENT_PAGE_ISSET_ID);
    }

    public void setCurrent_pageIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CURRENT_PAGE_ISSET_ID, value);
    }

    public int getPer_page() {
        return this.per_page;
    }

    public Page setPer_page(int per_page) {
        this.per_page = per_page;
        setPer_pageIsSet(true);
        return this;
    }

    public void unsetPer_page() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PER_PAGE_ISSET_ID);
    }

    /** Returns true if field per_page is set (has been assigned a value) and false otherwise */
    public boolean isSetPer_page() {
        return EncodingUtils.testBit(__isset_bitfield, __PER_PAGE_ISSET_ID);
    }

    public void setPer_pageIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PER_PAGE_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
        switch (field) {
            case TOTAL_COUNT:
                if (value == null) {
                    unsetTotal_count();
                } else {
                    setTotal_count((Integer) value);
                }
                break;

            case PAGE_COUNT:
                if (value == null) {
                    unsetPage_count();
                } else {
                    setPage_count((Integer) value);
                }
                break;

            case CURRENT_PAGE:
                if (value == null) {
                    unsetCurrent_page();
                } else {
                    setCurrent_page((Integer) value);
                }
                break;

            case PER_PAGE:
                if (value == null) {
                    unsetPer_page();
                } else {
                    setPer_page((Integer) value);
                }
                break;

        }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
            case TOTAL_COUNT:
                return Integer.valueOf(getTotal_count());

            case PAGE_COUNT:
                return Integer.valueOf(getPage_count());

            case CURRENT_PAGE:
                return Integer.valueOf(getCurrent_page());

            case PER_PAGE:
                return Integer.valueOf(getPer_page());

        }
        throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
        if (field == null) {
            throw new IllegalArgumentException();
        }

        switch (field) {
            case TOTAL_COUNT:
                return isSetTotal_count();
            case PAGE_COUNT:
                return isSetPage_count();
            case CURRENT_PAGE:
                return isSetCurrent_page();
            case PER_PAGE:
                return isSetPer_page();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
            return false;
        if (that instanceof Page)
            return this.equals((Page) that);
        return false;
    }

    public boolean equals(Page that) {
        if (that == null)
            return false;

        boolean this_present_total_count = true;
        boolean that_present_total_count = true;
        if (this_present_total_count || that_present_total_count) {
            if (!(this_present_total_count && that_present_total_count))
                return false;
            if (this.total_count != that.total_count)
                return false;
        }

        boolean this_present_page_count = true;
        boolean that_present_page_count = true;
        if (this_present_page_count || that_present_page_count) {
            if (!(this_present_page_count && that_present_page_count))
                return false;
            if (this.page_count != that.page_count)
                return false;
        }

        boolean this_present_current_page = true;
        boolean that_present_current_page = true;
        if (this_present_current_page || that_present_current_page) {
            if (!(this_present_current_page && that_present_current_page))
                return false;
            if (this.current_page != that.current_page)
                return false;
        }

        boolean this_present_per_page = true;
        boolean that_present_per_page = true;
        if (this_present_per_page || that_present_per_page) {
            if (!(this_present_per_page && that_present_per_page))
                return false;
            if (this.per_page != that.per_page)
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_total_count = true;
        list.add(present_total_count);
        if (present_total_count)
            list.add(total_count);

        boolean present_page_count = true;
        list.add(present_page_count);
        if (present_page_count)
            list.add(page_count);

        boolean present_current_page = true;
        list.add(present_current_page);
        if (present_current_page)
            list.add(current_page);

        boolean present_per_page = true;
        list.add(present_per_page);
        if (present_per_page)
            list.add(per_page);

        return list.hashCode();
    }

    @Override
    public int compareTo(Page other) {
        if (!getClass().equals(other.getClass())) {
            return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetTotal_count()).compareTo(other.isSetTotal_count());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetTotal_count()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.total_count, other.total_count);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetPage_count()).compareTo(other.isSetPage_count());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetPage_count()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.page_count, other.page_count);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetCurrent_page()).compareTo(other.isSetCurrent_page());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetCurrent_page()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.current_page, other.current_page);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetPer_page()).compareTo(other.isSetPer_page());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetPer_page()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.per_page, other.per_page);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
        return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Page(");
        boolean first = true;

        sb.append("total_count:");
        sb.append(this.total_count);
        first = false;
        if (!first) sb.append(", ");
        sb.append("page_count:");
        sb.append(this.page_count);
        first = false;
        if (!first) sb.append(", ");
        sb.append("current_page:");
        sb.append(this.current_page);
        first = false;
        if (!first) sb.append(", ");
        sb.append("per_page:");
        sb.append(this.per_page);
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        try {
            write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        try {
            // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
            __isset_bitfield = 0;
            read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private static class PageStandardSchemeFactory implements SchemeFactory {
        public PageStandardScheme getScheme() {
            return new PageStandardScheme();
        }
    }

    private static class PageStandardScheme extends StandardScheme<Page> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, Page struct) throws TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true) {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                    break;
                }
                switch (schemeField.id) {
                    case 1: // TOTAL_COUNT
                        if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                            struct.total_count = iprot.readI32();
                            struct.setTotal_countIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // PAGE_COUNT
                        if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                            struct.page_count = iprot.readI32();
                            struct.setPage_countIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 3: // CURRENT_PAGE
                        if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                            struct.current_page = iprot.readI32();
                            struct.setCurrent_pageIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 4: // PER_PAGE
                        if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                            struct.per_page = iprot.readI32();
                            struct.setPer_pageIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                        org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, Page struct) throws TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            oprot.writeFieldBegin(TOTAL_COUNT_FIELD_DESC);
            oprot.writeI32(struct.total_count);
            oprot.writeFieldEnd();
            oprot.writeFieldBegin(PAGE_COUNT_FIELD_DESC);
            oprot.writeI32(struct.page_count);
            oprot.writeFieldEnd();
            oprot.writeFieldBegin(CURRENT_PAGE_FIELD_DESC);
            oprot.writeI32(struct.current_page);
            oprot.writeFieldEnd();
            oprot.writeFieldBegin(PER_PAGE_FIELD_DESC);
            oprot.writeI32(struct.per_page);
            oprot.writeFieldEnd();
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class PageTupleSchemeFactory implements SchemeFactory {
        public PageTupleScheme getScheme() {
            return new PageTupleScheme();
        }
    }

    private static class PageTupleScheme extends TupleScheme<Page> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, Page struct) throws TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetTotal_count()) {
                optionals.set(0);
            }
            if (struct.isSetPage_count()) {
                optionals.set(1);
            }
            if (struct.isSetCurrent_page()) {
                optionals.set(2);
            }
            if (struct.isSetPer_page()) {
                optionals.set(3);
            }
            oprot.writeBitSet(optionals, 4);
            if (struct.isSetTotal_count()) {
                oprot.writeI32(struct.total_count);
            }
            if (struct.isSetPage_count()) {
                oprot.writeI32(struct.page_count);
            }
            if (struct.isSetCurrent_page()) {
                oprot.writeI32(struct.current_page);
            }
            if (struct.isSetPer_page()) {
                oprot.writeI32(struct.per_page);
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, Page struct) throws TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(4);
            if (incoming.get(0)) {
                struct.total_count = iprot.readI32();
                struct.setTotal_countIsSet(true);
            }
            if (incoming.get(1)) {
                struct.page_count = iprot.readI32();
                struct.setPage_countIsSet(true);
            }
            if (incoming.get(2)) {
                struct.current_page = iprot.readI32();
                struct.setCurrent_pageIsSet(true);
            }
            if (incoming.get(3)) {
                struct.per_page = iprot.readI32();
                struct.setPer_pageIsSet(true);
            }
        }
    }

}

