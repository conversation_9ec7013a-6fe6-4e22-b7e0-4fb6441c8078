package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfigDept;
import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfigItem;
import com.mi.oa.asset.commons.config.domain.common.repository.FunctionConfigItemRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FunctionConfigItemPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.FunctionConfigItemMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.FunctionConfigConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 首页功能配置-业务线配置规则
 * @Date 2025/4/29 19:00
 **/
@Service
public class FunctionConfigItemRepoImpl extends ServiceImpl<FunctionConfigItemMapper, FunctionConfigItemPo> implements FunctionConfigItemRepo {

    @Resource
    private FunctionConfigConverter converter;

    @Override
    public List<FunctionConfigItem> saveConfigItems(List<FunctionConfigItem> configItems) {
        if (CollectionUtils.isEmpty(configItems)) {
            return Collections.emptyList();
        }
        List<FunctionConfigItemPo> itemPos = converter.toItemPos(configItems);
        saveOrUpdateBatch(itemPos);

        Map<String, Integer> configItemMap = itemPos.stream().collect(Collectors.toMap(FunctionConfigItemPo::getBusinessLine, FunctionConfigItemPo::getId));
        configItems.forEach(item -> {
            Integer itemId = configItemMap.get(item.getBusinessLine());
            item.setItemId(itemId);
            List<FunctionConfigDept> authDeptList = item.getAuthDeptList();
            authDeptList.forEach(dept -> dept.setConfigItemId(itemId));
        });
        return configItems;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByConfigId(Integer configId) {
        if (configId == null) return;
        baseMapper.delete(Wrappers.lambdaUpdate(FunctionConfigItemPo.class).eq(FunctionConfigItemPo::getConfigId, configId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByConfigIds(List<Integer> configIds) {
        if (CollectionUtils.isEmpty(configIds)) return;
        baseMapper.delete(Wrappers.lambdaUpdate(FunctionConfigItemPo.class).in(FunctionConfigItemPo::getConfigId, configIds));
    }

    @Override
    public List<FunctionConfigItem> getByCountry(String country) {
        List<FunctionConfigItemPo> functionConfigItemPos = baseMapper.selectList(Wrappers.lambdaQuery(FunctionConfigItemPo.class)
                .like(FunctionConfigItemPo::getCountry, country));
        return converter.poToItems(functionConfigItemPos);
    }

    @Override
    public List<FunctionConfigItem> listByConfigIds(List<Integer> configIds) {
        if (CollectionUtils.isEmpty(configIds)) return Collections.emptyList();
        List<FunctionConfigItemPo> functionConfigItemPos = baseMapper.selectList(Wrappers.lambdaQuery(FunctionConfigItemPo.class)
                .in(FunctionConfigItemPo::getConfigId, configIds));
        return converter.poToItems(functionConfigItemPos);
    }
}
