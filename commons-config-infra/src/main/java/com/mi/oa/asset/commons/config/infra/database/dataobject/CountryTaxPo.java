package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 税率配置表
 * @TableName amg_country_tax
 */
@TableName(value ="amg_country_tax")
@Data
public class CountryTaxPo {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 税码
     */
    private String taxCode;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 默认税率（多个币种）
     */
    private Integer defaultTaxRate;

    /**
     * 国家数据表id
     */
    private Integer countryConfigId;

    /**
     * 国家/地区代码（3位）
     */
    private String countryCodeAlphaThree;

    /**
     * 国家/地区代码（2位）
     */
    private String countryCodeAlphaTwo;

    /**
     * 国家/地区
     */
    private String countryName;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建人用户名
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新人用户名
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}