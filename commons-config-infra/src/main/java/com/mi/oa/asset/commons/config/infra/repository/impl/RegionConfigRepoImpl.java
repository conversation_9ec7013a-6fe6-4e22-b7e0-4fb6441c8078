package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.international.entity.RegionConfigDo;
import com.mi.oa.asset.commons.config.domain.international.repository.RegionConfigRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.RegionConfigPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.RegionConfigPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.RegionConfigPoConvertor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 【amg_region_config(区域配置表)】
* @createDate 2025-04-28 15:43:27
*/
@Service
public class RegionConfigRepoImpl extends ServiceImpl<RegionConfigPoMapper, RegionConfigPo>
    implements RegionConfigRepo {

    @Resource
    private RegionConfigPoConvertor converter;

    @Resource
    private RegionConfigPoMapper regionConfigPoMapper;

    @Override
    public List<RegionConfigDo> searchAll() {
        List<RegionConfigPo> regionConfigPoList = regionConfigPoMapper.selectList(new LambdaQueryWrapper<RegionConfigPo>()
                .orderBy(true, true, RegionConfigPo::getRegionSortOrder));
        return converter.poToDoList(regionConfigPoList);
    }

    @Override
    public RegionConfigDo getById(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        RegionConfigPo po = this.baseMapper.selectById(id);
        return converter.poToDo(po);
    }

    @Override
    public Integer save(RegionConfigDo regionConfigDo) {
        RegionConfigPo po = converter.doToPo(regionConfigDo);
        this.save(po);
        return po.getId();
    }

    @Override
    public int updateById(RegionConfigDo entity) {
        RegionConfigPo po = converter.doToPo(entity);
        return baseMapper.updateById(po);
    }

    @Override
    public void deleteByIds(List<Integer> idList) {
        this.baseMapper.deleteBatchIds(idList);
    }


}




