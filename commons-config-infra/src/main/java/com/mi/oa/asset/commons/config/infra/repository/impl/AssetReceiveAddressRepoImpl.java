package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.api.myfunctions.Country;
import com.mi.oa.asset.commons.config.domain.address.entity.AssetReceiveAddress;
import com.mi.oa.asset.commons.config.domain.address.repository.AssetReceiveAddressRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetReceiveAddressPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.AssetReceiveAddressPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AssetReceiveAddressRepoConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 接口实现类
 *
 * <AUTHOR>
 * @date 2024-04-08 11:14:07
 */
@Slf4j
@Service
public class AssetReceiveAddressRepoImpl extends ServiceImpl<AssetReceiveAddressPoMapper, AssetReceiveAddressPo> implements AssetReceiveAddressRepo {

    @Resource
    private AssetReceiveAddressRepoConverter converter;

    private LambdaQueryWrapper<AssetReceiveAddressPo> getWrapper(AssetReceiveAddress entity) {
        AssetReceiveAddressPo po = converter.toAssetReceiveAddressPo(entity);
        LambdaQueryWrapper<AssetReceiveAddressPo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AssetReceiveAddressPo::getUserId, po.getUserId());
        wrapper.eq(StringUtils.isNotBlank(entity.getProvinceCode()), AssetReceiveAddressPo::getProvinceCode, po.getProvinceCode());
        wrapper.eq(StringUtils.isNotBlank(entity.getCityCode()), AssetReceiveAddressPo::getCityCode, po.getCityCode());
        wrapper.eq(StringUtils.isNotBlank(entity.getAreaCode()), AssetReceiveAddressPo::getAreaCode, po.getAreaCode());
        wrapper.eq(StringUtils.isNotBlank(entity.getStreetCode()), AssetReceiveAddressPo::getStreetCode, po.getStreetCode());
        wrapper.eq(StringUtils.isNotBlank(entity.getDetailAddress()), AssetReceiveAddressPo::getDetailAddress, po.getDetailAddress());
        wrapper.eq(entity.getIsDefault() != null, AssetReceiveAddressPo::getIsDefault, po.getIsDefault());
        wrapper.eq(AssetReceiveAddressPo::getStatus, 1);
        wrapper.orderByDesc(AssetReceiveAddressPo::getIsDefault);
        return wrapper;
    }

    @Override
    public List<AssetReceiveAddress> list(AssetReceiveAddress entity) {
        if (StringUtils.isBlank(entity.getUserId())) return Collections.emptyList();
        List<AssetReceiveAddressPo> list = list(getWrapper(entity));
        return converter.toAssetReceiveAddresss(list);
    }

    @Override
    public AssetReceiveAddress getById(Long id) {
        AssetReceiveAddressPo po = baseMapper.selectById(id);
        return converter.toAssetReceiveAddress(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(AssetReceiveAddress entity) {
        AssetReceiveAddressPo po = converter.toAssetReceiveAddressPo(entity);
        this.save(po);
        return po.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateById(AssetReceiveAddress entity) {
        AssetReceiveAddressPo po = converter.toAssetReceiveAddressPo(entity);
        updateById(po);
    }

    @Override
    public void cancelDefault(String userId) {
        this.update(Wrappers.lambdaUpdate(AssetReceiveAddressPo.class)
                .set(AssetReceiveAddressPo::getIsDefault, false)
                .eq(AssetReceiveAddressPo::getUserId, userId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> idList) {
        this.update(Wrappers.lambdaUpdate(AssetReceiveAddressPo.class)
                .set(AssetReceiveAddressPo::getStatus, 0)
                .in(AssetReceiveAddressPo::getId, idList));
    }

    @Override
    public AssetReceiveAddress getDefault(String userId) {
        return converter.toAssetReceiveAddress(this.getOne(Wrappers.lambdaQuery(AssetReceiveAddressPo.class)
                .eq(AssetReceiveAddressPo::getUserId, userId)
                .eq(AssetReceiveAddressPo::getIsDefault, true)
                .eq(AssetReceiveAddressPo::getStatus, 1)
                .last("limit 1")));
    }

    @Override
    public Boolean setDefault(Long id) {
        cancelDefault(getById(id).getUserId());
        return this.update(Wrappers.lambdaUpdate(AssetReceiveAddressPo.class)
                .set(AssetReceiveAddressPo::getIsDefault, true)
                .eq(AssetReceiveAddressPo::getId, id));
    }

    @Override
    public List<Country> listCountryV1() {
        return baseMapper.listCountry();
    }
}
