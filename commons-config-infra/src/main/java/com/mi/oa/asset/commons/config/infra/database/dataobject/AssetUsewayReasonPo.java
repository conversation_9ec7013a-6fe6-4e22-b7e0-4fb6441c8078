package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 申请理由
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11 11:29:51
 */
@Getter
@Setter
@TableName("asset_useway_reason")
public class AssetUsewayReasonPo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 申请理由
     */
    private String usewayReason;

    /**
     * 添加人ID
     */
    private String addUserid;

    /**
     * 添加时间
     */
    private Date addDate;

    /**
     * 修改人ID
     */
    private String modifyUserid;

    /**
     * 修改时间
     */
    private Date modifyDate;

    /**
     * 系统租户ID
     */
    private String tenantId;

    /**
     * 申请用途id
     */
    private String usewayId;


}
