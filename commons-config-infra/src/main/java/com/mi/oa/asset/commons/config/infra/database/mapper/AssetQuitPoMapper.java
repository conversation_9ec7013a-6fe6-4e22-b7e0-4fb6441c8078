package com.mi.oa.asset.commons.config.infra.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetQuitPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 离职员工信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12 09:59:49
 */
public interface AssetQuitPoMapper extends BaseMapper<AssetQuitPo> {

    List<AssetQuitPo> queryResignEmpByTime(@Param("dateTime") String dateTime);
}
