package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date    2024/3/19 11:36
 */

@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "amg_system_var")
public class SystemVarPo extends BasePo {
    /**
     * 变量编码
     */
    @TableField(value = "var_code")
    private String varCode;

    /**
     * 变量描述
     */
    @TableField(value = "var_desc")
    private String varDesc;

    /**
     * 变量值
     */
    @TableField(value = "var_value")
    private String varValue;

    /**
     * 业务线
     */
    @TableField(value = "business_line")
    private String businessLine;
}