package com.mi.oa.asset.commons.config.infra.database.dataobject.old;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.old.MaterialCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.repository.old.MaterialCategoryRepo;
import com.mi.oa.asset.commons.config.infra.database.mapper.MaterialCategoryPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.DeviceCategoryRepoConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:39
 */

@Service
public class MaterialCategoryRepoImpl extends ServiceImpl<MaterialCategoryPoMapper, MaterialCategoryPO> implements MaterialCategoryRepo {

    @Resource
    private DeviceCategoryRepoConverter converter;




    @Override
    public MaterialCategory getMaterialCategory(String showId) {
        return converter.toMaterialCategory(
                lambdaQuery().eq(StringUtils.isNotEmpty(showId), MaterialCategoryPO::getShowId, showId)
                        .one()
        );
    }

}
