package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/04/28/03:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_function_config")
public class FunctionConfigPo extends BasePo {

    /**
     * 管理线 manage_line
     */
    private String manageLine;

    /**
     * 管理线名称 manage_line_name
     */
    private String manageLineName;

    /**
     * 功能名称 name
     */
    private String name;

    /**
     * 功能英文名称 name_en
     */
    private String nameEn;

    /**
     * icon url icon_url
     */
    private String iconUrl;

    /**
     * 功能url function_url
     */
    private String functionUrl;

    /**
     * 功能描述 description
     */
    private String description;

    /**
     * 功能英文描述 description_en
     */
    private String descriptionEn;

    /**
     * 排序值 sort
     */
    private Integer sort;

}