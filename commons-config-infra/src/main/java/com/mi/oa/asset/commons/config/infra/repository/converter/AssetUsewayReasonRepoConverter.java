package com.mi.oa.asset.commons.config.infra.repository.converter;


import com.mi.oa.asset.commons.config.domain.assetuseway.entity.AssetUsewayReason;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetUsewayReasonPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 申请理由 converter 转换器
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@Mapper(componentModel = "spring")
public interface AssetUsewayReasonRepoConverter extends CommonConverter {

    AssetUsewayReason toAssetUsewayReason(AssetUsewayReasonPo source);

    AssetUsewayReasonPo toAssetUsewayReasonPo(AssetUsewayReason source);

    List<AssetUsewayReason> toAssetUsewayReasons(List<AssetUsewayReasonPo> source);
}