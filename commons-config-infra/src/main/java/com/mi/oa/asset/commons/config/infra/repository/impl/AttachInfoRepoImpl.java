package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.common.repository.AttachInfoRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.AttachInfo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AttachInfoPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.AttachInfoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AttachInfoRepoConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 附件
 *
 * <AUTHOR>
 * @date 2023/10/26 18:54
 */
@Service
public class AttachInfoRepoImpl extends ServiceImpl<AttachInfoMapper, AttachInfoPo> implements AttachInfoRepo {

    @Resource
    private AttachInfoRepoConverter converter;

    @Override
    public void batchSaveAttach(List<AttachInfo> attachInfoList) {
        List<AttachInfoPo> attachInfoPOS = converter.attachDtoListToPoList(attachInfoList);
        this.saveBatch(attachInfoPOS);
    }

    @Override
    public void deleteAttach(Integer recordId, String recordType) {
        baseMapper.delete(Wrappers.lambdaQuery(AttachInfoPo.class)
                .eq(AttachInfoPo::getRecordId, recordId)
                .eq(AttachInfoPo::getRecordType, recordType));
    }

    @Override
    public void deleteAttach(Integer recordId, List<String> recordTypes) {
        if (CollectionUtils.isEmpty(recordTypes) || recordId == null) return;
        baseMapper.delete(Wrappers.lambdaQuery(AttachInfoPo.class)
                .eq(AttachInfoPo::getRecordId, recordId)
                .in(AttachInfoPo::getRecordType, recordTypes));
    }

    @Override
    public void batchDeleteAttach(List<Integer> recordIds, String recordType) {
        baseMapper.delete(Wrappers.lambdaQuery(AttachInfoPo.class)
                .in(AttachInfoPo::getRecordId, recordIds)
                .eq(AttachInfoPo::getRecordType, recordType));
    }

    @Override
    public void deleteAttach(Integer recordId, List<String> attachFileNames, String recordType) {
        if (CollectionUtils.isEmpty(attachFileNames) || StringUtils.isBlank(recordType))
            return;

        baseMapper.delete(
                Wrappers.lambdaQuery(AttachInfoPo.class)
                        .eq(AttachInfoPo::getRecordId, recordId)
                        .eq(AttachInfoPo::getRecordType, recordType)
                        .in(AttachInfoPo::getAttachName, attachFileNames)
        );
    }

    @Override
    public void deleteById(List<Integer> idList) {
        baseMapper.deleteBatchIds(idList);
    }

    @Override
    public List<AttachInfo> getAttachList(List<Integer> recordIds, String recordType) {
        List<AttachInfoPo> attachInfoPOS = baseMapper.selectList(Wrappers.lambdaQuery(AttachInfoPo.class)
                .in(AttachInfoPo::getRecordId, recordIds)
                .eq(StringUtils.isNotEmpty(recordType), AttachInfoPo::getRecordType, recordType));
        return converter.attachPoListToDo(attachInfoPOS);
    }

    @Override
    public List<AttachInfo> listByTypes(Integer recordId, List<String> recordTypes) {
        if (Objects.isNull(recordId) || CollectionUtils.isEmpty(recordTypes)) return Collections.emptyList();
        List<AttachInfoPo> attachInfoPOS = baseMapper.selectList(Wrappers.lambdaQuery(AttachInfoPo.class)
                .eq(AttachInfoPo::getRecordId, recordId)
                .in(AttachInfoPo::getRecordType, recordTypes));
        return converter.attachPoListToDo(attachInfoPOS);
    }

    @Override
    public AttachInfo getTemplateAttach(String recordNo) {
        AttachInfoPo attachInfoPo = baseMapper.selectOne(Wrappers.lambdaQuery(AttachInfoPo.class)
                .eq(AttachInfoPo::getRecordNo, recordNo)
                .eq(AttachInfoPo::getRecordType, "template"));
        return converter.attachPoToDo(attachInfoPo);
    }
}
