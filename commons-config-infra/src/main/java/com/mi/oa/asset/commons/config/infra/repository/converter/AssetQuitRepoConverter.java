package com.mi.oa.asset.commons.config.infra.repository.converter;


import com.mi.oa.asset.commons.config.domain.assetquit.entity.AssetQuit;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetQuitPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 离职员工信息表 converter 转换器
 * <AUTHOR>
 * @date 2024-04-12 02:05:55
 */
@Mapper(componentModel = "spring")
public interface AssetQuitRepoConverter {

    AssetQuit toAssetQuit(AssetQuitPo source);

    AssetQuitPo toAssetQuitPo(AssetQuit source);

    List<AssetQuit> toAssetQuits(List<AssetQuitPo> source);

    List<AssetQuitPo> toAssetQuitPos(List<AssetQuit> source);
}