package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/3/12 16:33
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "amg_asset_sku")
public class AssetSkuPo extends BasePo {
    /**
     * sku编码
     */
    @TableField(value = "sku_code")
    private String skuCode;

    /**
     * sku名称
     */
    @TableField(value = "sku_name")
    private String skuName;

    /**
     * sku名称(英文)
     */
    @TableField(value = "sku_name_en")
    private String skuNameEn;

    /**
     * 别名
     */
    @TableField(value = "alias_name")
    private String aliasName;

    /**
     * 业务线
     */
    @TableField(value = "business_line")
    private String businessLine;

    /**
     * 单价
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 分类id
     */
    @TableField(value = "cate_id")
    private Integer cateId;

    /**
     * 分类编码
     */
    @TableField(value = "cate_code")
    private String cateCode;

    /**
     * 分类名称
     */
    @TableField(value = "cate_name")
    private String cateName;

    /**
     * 小米sku编码
     */
    @TableField(value = "mi_sku_code")
    private String miSkuCode;

    /**
     * 小米商品id
     */
    @TableField(value = "mi_goods_id")
    private String miGoodsId;
    /**
     * 国家编码
     */
    @TableField(value = "country")
    private String country;

    /**
     * 国家名称
     */
    @TableField(value = "country_name")
    private String countryName;

    /**
     * 品牌
     */
    @TableField(value = "brand")
    private String brand;

    /**
     * 规格
     */
    @TableField(value = "spec")
    private String spec;
    /**
     * 型号
     */
    @TableField(value = "model")
    private String model;

    /**
     * 新旧属性
     */
    @TableField(value = "new_old")
    private String newOld;
    /**
     * 物料类型
     */
    @TableField(value = "material_type")
    private String materialType;

    /**
     * 是否一卡多物管理
     */
    @TableField(value = "is_multiple_manage")
    private Integer isMultipleManage;

    /**
     * 是否SN
     */
    @TableField(value = "is_sn")
    private Integer isSn;

    /**
     * 计量单位编码
     */
    @TableField(value = "measure_code")
    private String measureCode;

    /**
     * 计量单位名称
     */
    @TableField(value = "measure_name")
    private String measureName;

    /**
     * 禁用
     */
    @TableField(value = "disabled")
    private Integer disabled;
    /**
     * 商品名称
     */
    @TableField(value = "product_name")
    private String productName;


    /**
     * 项目编码
     */
    @TableField(value = "project_code")
    private String projectCode;


    /**
     * 项目阶段
     */
    @TableField(value = "project_phase")
    private String projectPhase;


    /**
     * 上市时间
     */
    @TableField(value = "sale_date")
    private Date saleDate;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * mdm创建状态
     * 1 可用   其他不可用
     */
    @TableField(value = "mdm_create_status")
    private Integer mdmCreateStatus;

}