/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.TException;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;
import org.mi.thrift.server.AbstractNonblockingServer.*;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2020-5-26")
public class SyncRequest implements org.mi.thrift.TBase<SyncRequest, SyncRequest._Fields>, java.io.Serializable, Cloneable, Comparable<SyncRequest> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("SyncRequest");

    private static final org.mi.thrift.protocol.TField AREA_CODE_FIELD_DESC = new org.mi.thrift.protocol.TField("area_code", org.mi.thrift.protocol.TType.STRING, (short) 1);
    private static final org.mi.thrift.protocol.TField UPDATE_TIME_FIELD_DESC = new org.mi.thrift.protocol.TField("update_time", org.mi.thrift.protocol.TType.STRING, (short) 2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();

    static {
        schemes.put(StandardScheme.class, new SyncRequestStandardSchemeFactory());
        schemes.put(TupleScheme.class, new SyncRequestTupleSchemeFactory());
    }

    public String area_code; // required
    public String update_time; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        AREA_CODE((short) 1, "area_code"),
        UPDATE_TIME((short) 2, "update_time");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
            for (_Fields field : EnumSet.allOf(_Fields.class)) {
                byName.put(field.getFieldName(), field);
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch (fieldId) {
                case 1: // AREA_CODE
                    return AREA_CODE;
                case 2: // UPDATE_TIME
                    return UPDATE_TIME;
                default:
                    return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
            _Fields fields = findByThriftId(fieldId);
            if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
            return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
            return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
            _thriftId = thriftId;
            _fieldName = fieldName;
        }

        public short getThriftFieldId() {
            return _thriftId;
        }

        public String getFieldName() {
            return _fieldName;
        }
    }

    // isset id assignments
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;

    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.AREA_CODE, new org.mi.thrift.meta_data.FieldMetaData("area_code", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        tmpMap.put(_Fields.UPDATE_TIME, new org.mi.thrift.meta_data.FieldMetaData("update_time", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SyncRequest.class, metaDataMap);
    }

    public SyncRequest() {
    }

    public SyncRequest(
            String area_code,
            String update_time) {
        this();
        this.area_code = area_code;
        this.update_time = update_time;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public SyncRequest(SyncRequest other) {
        if (other.isSetArea_code()) {
            this.area_code = other.area_code;
        }
        if (other.isSetUpdate_time()) {
            this.update_time = other.update_time;
        }
    }

    public SyncRequest deepCopy() {
        return new SyncRequest(this);
    }

    @Override
    public void clear() {
        this.area_code = null;
        this.update_time = null;
    }

    public String getArea_code() {
        return this.area_code;
    }

    public SyncRequest setArea_code(String area_code) {
        this.area_code = area_code;
        return this;
    }

    public void unsetArea_code() {
        this.area_code = null;
    }

    /** Returns true if field area_code is set (has been assigned a value) and false otherwise */
    public boolean isSetArea_code() {
        return this.area_code != null;
    }

    public void setArea_codeIsSet(boolean value) {
        if (!value) {
            this.area_code = null;
        }
    }

    public String getUpdate_time() {
        return this.update_time;
    }

    public SyncRequest setUpdate_time(String update_time) {
        this.update_time = update_time;
        return this;
    }

    public void unsetUpdate_time() {
        this.update_time = null;
    }

    /** Returns true if field update_time is set (has been assigned a value) and false otherwise */
    public boolean isSetUpdate_time() {
        return this.update_time != null;
    }

    public void setUpdate_timeIsSet(boolean value) {
        if (!value) {
            this.update_time = null;
        }
    }

    public void setFieldValue(_Fields field, Object value) {
        switch (field) {
            case AREA_CODE:
                if (value == null) {
                    unsetArea_code();
                } else {
                    setArea_code((String) value);
                }
                break;

            case UPDATE_TIME:
                if (value == null) {
                    unsetUpdate_time();
                } else {
                    setUpdate_time((String) value);
                }
                break;

        }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
            case AREA_CODE:
                return getArea_code();

            case UPDATE_TIME:
                return getUpdate_time();

        }
        throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
        if (field == null) {
            throw new IllegalArgumentException();
        }

        switch (field) {
            case AREA_CODE:
                return isSetArea_code();
            case UPDATE_TIME:
                return isSetUpdate_time();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
            return false;
        if (that instanceof SyncRequest)
            return this.equals((SyncRequest) that);
        return false;
    }

    public boolean equals(SyncRequest that) {
        if (that == null)
            return false;

        boolean this_present_area_code = true && this.isSetArea_code();
        boolean that_present_area_code = true && that.isSetArea_code();
        if (this_present_area_code || that_present_area_code) {
            if (!(this_present_area_code && that_present_area_code))
                return false;
            if (!this.area_code.equals(that.area_code))
                return false;
        }

        boolean this_present_update_time = true && this.isSetUpdate_time();
        boolean that_present_update_time = true && that.isSetUpdate_time();
        if (this_present_update_time || that_present_update_time) {
            if (!(this_present_update_time && that_present_update_time))
                return false;
            if (!this.update_time.equals(that.update_time))
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_area_code = true && (isSetArea_code());
        list.add(present_area_code);
        if (present_area_code)
            list.add(area_code);

        boolean present_update_time = true && (isSetUpdate_time());
        list.add(present_update_time);
        if (present_update_time)
            list.add(update_time);

        return list.hashCode();
    }

    @Override
    public int compareTo(SyncRequest other) {
        if (!getClass().equals(other.getClass())) {
            return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetArea_code()).compareTo(other.isSetArea_code());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetArea_code()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.area_code, other.area_code);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetUpdate_time()).compareTo(other.isSetUpdate_time());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetUpdate_time()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.update_time, other.update_time);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
        return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("SyncRequest(");
        boolean first = true;

        sb.append("area_code:");
        if (this.area_code == null) {
            sb.append("null");
        } else {
            sb.append(this.area_code);
        }
        first = false;
        if (!first) sb.append(", ");
        sb.append("update_time:");
        if (this.update_time == null) {
            sb.append("null");
        } else {
            sb.append(this.update_time);
        }
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        try {
            write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        try {
            read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private static class SyncRequestStandardSchemeFactory implements SchemeFactory {
        public SyncRequestStandardScheme getScheme() {
            return new SyncRequestStandardScheme();
        }
    }

    private static class SyncRequestStandardScheme extends StandardScheme<SyncRequest> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, SyncRequest struct) throws TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true) {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                    break;
                }
                switch (schemeField.id) {
                    case 1: // AREA_CODE
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.area_code = iprot.readString();
                            struct.setArea_codeIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // UPDATE_TIME
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.update_time = iprot.readString();
                            struct.setUpdate_timeIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                        org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, SyncRequest struct) throws TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            if (struct.area_code != null) {
                oprot.writeFieldBegin(AREA_CODE_FIELD_DESC);
                oprot.writeString(struct.area_code);
                oprot.writeFieldEnd();
            }
            if (struct.update_time != null) {
                oprot.writeFieldBegin(UPDATE_TIME_FIELD_DESC);
                oprot.writeString(struct.update_time);
                oprot.writeFieldEnd();
            }
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class SyncRequestTupleSchemeFactory implements SchemeFactory {
        public SyncRequestTupleScheme getScheme() {
            return new SyncRequestTupleScheme();
        }
    }

    private static class SyncRequestTupleScheme extends TupleScheme<SyncRequest> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, SyncRequest struct) throws TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetArea_code()) {
                optionals.set(0);
            }
            if (struct.isSetUpdate_time()) {
                optionals.set(1);
            }
            oprot.writeBitSet(optionals, 2);
            if (struct.isSetArea_code()) {
                oprot.writeString(struct.area_code);
            }
            if (struct.isSetUpdate_time()) {
                oprot.writeString(struct.update_time);
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, SyncRequest struct) throws TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(2);
            if (incoming.get(0)) {
                struct.area_code = iprot.readString();
                struct.setArea_codeIsSet(true);
            }
            if (incoming.get(1)) {
                struct.update_time = iprot.readString();
                struct.setUpdate_timeIsSet(true);
            }
        }
    }

}

