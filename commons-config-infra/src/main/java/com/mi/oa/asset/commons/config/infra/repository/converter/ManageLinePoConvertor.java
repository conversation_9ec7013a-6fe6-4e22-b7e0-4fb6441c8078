package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.common.entity.ManageLineDo;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.ManageLinePo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/31 19:34
 */

@Mapper(componentModel = "spring")
public interface ManageLinePoConvertor extends CommonConverter {

    ManageLineDo poToDo(ManageLinePo manageLinePo);

    List<ManageLineDo> poToDoList(List<ManageLinePo> manageLinePos);

}
