package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/01/13/11:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_my_common_func")
public class MyCommonFuncPo extends BasePo {

    /**
     * 管理线编码 manage_line_code
     */
    private String manageLineCode;

    /**
     * 我的功能列表（有序） resource_codes
     */
    private String resourceCodes;

    /**
     * 0-否 1-是  如果没添加个人功能，返回默认的管理功能列表 is_default
     */
    private Byte isDefault;
}