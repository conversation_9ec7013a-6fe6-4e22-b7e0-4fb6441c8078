package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date    2023/9/11 15:17
 */

@Data
@TableName(value = "amg_purchase_catalog")
public class PurchaseCatalogPo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 采购目录编码
     */
    @TableField(value = "catalog_code")
    private String catalogCode;

    /**
     * 采购目录名称 
     */
    @TableField(value = "catalog_name")
    private String catalogName;

    /**
     * 采购目录名称
     */
    @TableField(value = "catalog_name_en")
    private String catalogNameEn;

    /**
     * 上级编码
     */
    @TableField(value = "parent_code")
    private String parentCode;

    /**
     * 级别
     */
    @TableField(value = "level")
    private Integer level;

    /**
     * sap分类编码
     */
    @TableField(value = "sap_cate_code")
    private String sapCateCode;

    /**
     * sap分类名称
     */
    @TableField(value = "sap_cate_name")
    private String sapCateName;

    /**
     * 使用年限
     */
    @TableField(value = "use_year")
    private Integer useYear;

    /**
     * 禁用
     */
    @TableField(value = "disabled")
    private Integer disabled;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}