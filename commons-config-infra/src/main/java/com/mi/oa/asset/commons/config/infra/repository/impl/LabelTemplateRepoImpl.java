package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.labeltemplate.entity.LabelTemplate;
import com.mi.oa.asset.commons.config.domain.labeltemplate.repository.LabelTemplateRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.LabelTemplatePo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.LabelTemplateUserDefaultPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.LabelTemplateMapper;
import com.mi.oa.asset.commons.config.infra.database.mapper.LabelTemplateUserDefaultMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.CommonDataRepoConverter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Service
public class LabelTemplateRepoImpl extends ServiceImpl<LabelTemplateMapper, LabelTemplatePo> implements LabelTemplateRepo {

    @Resource
    private LabelTemplateMapper labelTemplateMapper;

    @Resource
    private LabelTemplateUserDefaultMapper labelTemplateUserDefaultMapper;

    @Resource
    private CommonDataRepoConverter commonDataConverter;

    @Override
    public List<LabelTemplate> findListByType(String templateType, boolean filterActive) {
        List<LabelTemplatePo> list = new LambdaQueryChainWrapper<>(labelTemplateMapper)
                .eq(LabelTemplatePo::getTemplateType, templateType)
                .eq(LabelTemplatePo::getIsDeleted, 0)
                .eq(filterActive, LabelTemplatePo::getIsActive, 1)
                .list();
        return commonDataConverter.toLabelTemplates(list);
    }

    private List<LabelTemplate> findListByBusinessLine(String businessLine) {
        List<LabelTemplatePo> list = new LambdaQueryChainWrapper<>(labelTemplateMapper)
                .eq(LabelTemplatePo::getTemplateType, "business")
                .eq(LabelTemplatePo::getBusinessLine, businessLine)
                .eq(LabelTemplatePo::getIsDeleted, 0)
                .eq(LabelTemplatePo::getIsActive, 1)
                .list();
        return commonDataConverter.toLabelTemplates(list);
    }

    @Override
    public LabelTemplate findById(Integer id) {
        LabelTemplatePo labelTemplatePo = labelTemplateMapper.selectById(id);
        return commonDataConverter.toLabelTemplate(labelTemplatePo);
    }

    @Override
    public LabelTemplate findHistoryById(Integer id) {
        LabelTemplatePo labelTemplatePo = labelTemplateMapper.selectLabelTemplateById(id);
        return commonDataConverter.toLabelTemplate(labelTemplatePo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(LabelTemplate labelTemplate) {
        LabelTemplatePo labelTemplatePo = commonDataConverter.toLabelTemplate(labelTemplate);
        labelTemplateMapper.insert(labelTemplatePo);
        labelTemplate.setId(labelTemplatePo.getId());
        setOnlyDefault(labelTemplatePo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(LabelTemplate labelTemplate) {
        LabelTemplatePo labelTemplatePo = commonDataConverter.toLabelTemplate(labelTemplate);
        labelTemplateMapper.updateById(labelTemplatePo);
        setOnlyDefault(labelTemplatePo);
    }

    /**
     * 确保一个业务线只有一个默认模版
     *
     * @param labelTemplatePo
     */
    private void setOnlyDefault(LabelTemplatePo labelTemplatePo) {
        //如果是默认,需要将其它模版设置为非默认
        if (labelTemplatePo.getIsDefault() == 1) {
            //循环变量其它模版,将其它模版设置为非默认模版
            this.findListByBusinessLine(labelTemplatePo.getBusinessLine())
                    .stream()
                    .filter(item -> item.getIsDefault() == 1 && !Objects.equals(item.getId(), labelTemplatePo.getId()))
                    .forEach(before -> this.cancelTemplateDefault(before.getId()));
        }
    }

    @Override
    public void deleteById(Integer id) {
        labelTemplateMapper.deleteById(id);
    }

    @Override
    public void toggleTemplateActive(Integer id, Boolean isActive) {
        labelTemplateMapper.update(new LabelTemplatePo(), Wrappers.lambdaUpdate(LabelTemplatePo.class)
                .eq(LabelTemplatePo::getId, id)
                .set(LabelTemplatePo::getIsActive, isActive ? 1 : 0));
    }


    public void cancelTemplateDefault(Integer id) {
        labelTemplateMapper.update(new LabelTemplatePo(), Wrappers.lambdaUpdate(LabelTemplatePo.class)
                // 设置更新条件，根据 id 来确定要更新的记录
                .eq(LabelTemplatePo::getId, id)
                // 设置要更新的字段和对应的值
                .set(LabelTemplatePo::getIsDefault, 0));
    }

    @Override
    public List<LabelTemplate> getBusinessTemplateList(String businessLine, String userId) {
        List<LabelTemplate> labelTemplateList = this.findListByBusinessLine(businessLine);
        if (userId != null) {
            //查询用户设置的默认模版
            LabelTemplateUserDefaultPo labelTemplateUserDefaultPo = labelTemplateUserDefaultMapper.selectOne(Wrappers.lambdaQuery(LabelTemplateUserDefaultPo.class)
                    .eq(LabelTemplateUserDefaultPo::getUserId, userId)
                    .eq(LabelTemplateUserDefaultPo::getBusinessLine, businessLine));
            //设置用户默认的模版
            if (labelTemplateUserDefaultPo != null) {
                labelTemplateList.forEach(labelTemplate -> {
                    if (Objects.equals(labelTemplate.getId(), labelTemplateUserDefaultPo.getTemplateId())) {
                        labelTemplate.setIsUserDefault(1);
                    }else{
                        labelTemplate.setIsUserDefault(0);
                    }
                });
                // 对模板列表进行排序
                labelTemplateList.sort(Comparator.comparing((LabelTemplate t) -> !Objects.equals(t.getId(), labelTemplateUserDefaultPo.getTemplateId()))
                        .thenComparing(t -> t.getIsDefault() != 1)
                        .thenComparing(t -> !Objects.equals(t.getId(), labelTemplateUserDefaultPo.getRecentlyUsedTemplateId()))
                        .thenComparing(LabelTemplate::getId));
            }
        }
        //给业务模版添加显示字段
        return labelTemplateList;
    }

    @Override
    public void addUserDefaultTemplate(String userId, LabelTemplate labelTemplate) {
        //查找用户某个业务线是否已经设置了默认模版
        LabelTemplateUserDefaultPo labelTemplateUserDefaultPo = labelTemplateUserDefaultMapper.selectOne(Wrappers.lambdaQuery(LabelTemplateUserDefaultPo.class)
                .eq(LabelTemplateUserDefaultPo::getUserId, userId)
                .eq(LabelTemplateUserDefaultPo::getBusinessLine, labelTemplate.getBusinessLine()));
        if (labelTemplateUserDefaultPo != null) {
            labelTemplateUserDefaultMapper.update(new LabelTemplateUserDefaultPo(), Wrappers.lambdaUpdate(LabelTemplateUserDefaultPo.class)
                    .set(LabelTemplateUserDefaultPo::getTemplateId, labelTemplate.getId())
                    .eq(LabelTemplateUserDefaultPo::getId, labelTemplateUserDefaultPo.getId()));
        } else {
            labelTemplateUserDefaultPo = new LabelTemplateUserDefaultPo();
            labelTemplateUserDefaultPo.setUserId(userId);
            labelTemplateUserDefaultPo.setTemplateId(labelTemplate.getId());
            labelTemplateUserDefaultPo.setBusinessLine(labelTemplate.getBusinessLine());
            labelTemplateUserDefaultMapper.insert(labelTemplateUserDefaultPo);
        }
    }

    @Override
    public void addLastUsedTemplate(String authedUserName, LabelTemplate labelTemplate) {
        //查询用户某个业务线是否已经设置了默认模版
        LabelTemplateUserDefaultPo labelTemplateUserDefaultPo = labelTemplateUserDefaultMapper.selectOne(Wrappers.lambdaQuery(LabelTemplateUserDefaultPo.class)
                .eq(LabelTemplateUserDefaultPo::getUserId, authedUserName)
                .eq(LabelTemplateUserDefaultPo::getBusinessLine, labelTemplate.getBusinessLine()));
        if (labelTemplateUserDefaultPo != null) {
            labelTemplateUserDefaultMapper.update(new LabelTemplateUserDefaultPo(), Wrappers.lambdaUpdate(LabelTemplateUserDefaultPo.class)
                    .set(LabelTemplateUserDefaultPo::getRecentlyUsedTemplateId, labelTemplate.getId())
                    .eq(LabelTemplateUserDefaultPo::getId, labelTemplateUserDefaultPo.getId()));
        } else {
            labelTemplateUserDefaultPo = new LabelTemplateUserDefaultPo();
            labelTemplateUserDefaultPo.setUserId(authedUserName);
            labelTemplateUserDefaultPo.setRecentlyUsedTemplateId(labelTemplate.getId());
            labelTemplateUserDefaultPo.setBusinessLine(labelTemplate.getBusinessLine());
            labelTemplateUserDefaultMapper.insert(labelTemplateUserDefaultPo);
        }
    }
}
