package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRange;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.ShareRangeRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.ShareRangePo;
import com.mi.oa.asset.commons.config.infra.database.mapper.ShareRangePoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.ShareRangePoConverter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-07 14:50
 */
@Service
public class ShareRangeRepoImpl extends ServiceImpl<ShareRangePoMapper, ShareRangePo> implements ShareRangeRepo {

    @Resource
    private ShareRangePoConverter converter;

    @Override
    public List<ShareRange> getByShareId(Integer shareId) {
        List<ShareRangePo> shareRangePos = baseMapper.selectList(Wrappers.lambdaQuery(ShareRangePo.class).eq(ShareRangePo::getShareId, shareId));
        return converter.poToDoList(shareRangePos);
    }

    @Override
    public List<ShareRange> getByShareIds(List<Integer> shareIds) {
        List<ShareRangePo> shareRangePos = baseMapper.selectList(Wrappers.lambdaQuery(ShareRangePo.class).in(ShareRangePo::getShareId, shareIds));
        return converter.poToDoList(shareRangePos);
    }


    @Override
    public void batchSaveShareRange(List<ShareRange> shareRangeList) {
        List<ShareRangePo> shareRangePoList = converter.doToPoList(shareRangeList);
        this.saveOrUpdateBatch(shareRangePoList);
    }

    @Override
    public void removeByShareId(Integer shareId) {
        this.remove(Wrappers.lambdaQuery(ShareRangePo.class).eq(ShareRangePo::getShareId, shareId));
    }
}
