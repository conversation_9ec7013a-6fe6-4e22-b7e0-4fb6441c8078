package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/9/18 17:15
 */

@Data
@TableName(value = "amg_company")
public class CompanyPo {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 公司代码
     */
    @TableField(value = "company_code")
    private String companyCode;

    /**
     * 公司名称
     */
    @TableField(value = "company_name")
    private String companyName;

    /**
     * 国家编码
     */
    @TableField(value = "country_code")
    private String countryCode;

    /**
     * 禁用
     */
    @TableField(value = "disabled")
    private Integer disabled;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;
}