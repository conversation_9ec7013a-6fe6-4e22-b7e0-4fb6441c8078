package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.assetuseway.entity.AssetUsewayReason;
import com.mi.oa.asset.commons.config.domain.assetuseway.repository.AssetUsewayReasonRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetUsewayReasonPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.AssetUsewayReasonPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AssetUsewayReasonRepoConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 申请理由 接口实现类
 *
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@Slf4j
@Service
public class AssetUsewayReasonRepoImpl extends ServiceImpl<AssetUsewayReasonPoMapper, AssetUsewayReasonPo> implements AssetUsewayReasonRepo {

    @Resource
    private AssetUsewayReasonRepoConverter converter;

    private LambdaQueryWrapper<AssetUsewayReasonPo> getWrapper(AssetUsewayReason entity) {
        AssetUsewayReasonPo po = converter.toAssetUsewayReasonPo(entity);
        return Wrappers.lambdaQuery(AssetUsewayReasonPo.class)
                .eq(StringUtils.isNotBlank(po.getUsewayId()), AssetUsewayReasonPo::getUsewayId, po.getUsewayId());
    }

    @Override
    public List<AssetUsewayReason> list(AssetUsewayReason entity) {
        List<AssetUsewayReasonPo> list = list(getWrapper(entity));
        return converter.toAssetUsewayReasons(list);
    }

}
