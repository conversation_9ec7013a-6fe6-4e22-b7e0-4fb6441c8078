package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.assetquit.entity.AssetQuit;
import com.mi.oa.asset.commons.config.domain.assetquit.enums.ResignStatus;
import com.mi.oa.asset.commons.config.domain.assetquit.repository.AssetQuitRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetQuitPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.AssetQuitPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AssetQuitRepoConverter;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 离职员工信息表 接口实现类
 *
 * <AUTHOR>
 * @date 2024-04-12 02:05:55
 */
@Slf4j
@Service
public class AssetQuitRepoImpl extends ServiceImpl<AssetQuitPoMapper, AssetQuitPo> implements AssetQuitRepo {

    @Resource
    private AssetQuitRepoConverter converter;

    @Override
    public PageData<AssetQuit> page(AssetQuit entity, PageRequest request) {
        IPage<AssetQuitPo> page = page(new Page<>(request.getPageNum(), request.getPageSize()), getWrapper(entity));

        return PageData.of(converter.toAssetQuits(page.getRecords()), request.getPageSize(), request.getPageNum(), (int) page.getTotal());
    }

    private LambdaQueryWrapper<AssetQuitPo> getWrapper(AssetQuit entity) {
        AssetQuitPo po = converter.toAssetQuitPo(entity);
        LambdaQueryWrapper<AssetQuitPo> wrapper = Wrappers.lambdaQuery(AssetQuitPo.class)
                .eq(StringUtils.isNotBlank(po.getUserCode()), AssetQuitPo::getUserCode, po.getUserCode())
                .eq(StringUtils.isNotBlank(po.getQuitState()), AssetQuitPo::getQuitState, po.getQuitState());
        return wrapper;
    }

    @Override
    public List<AssetQuit> list(AssetQuit entity) {
        List<AssetQuitPo> list = list(getWrapper(entity));
        return converter.toAssetQuits(list);
    }

    @Override
    public AssetQuit getById(Integer id) {
        AssetQuitPo po = baseMapper.selectById(id);
        return converter.toAssetQuit(po);
    }

    @Override
    public boolean exists(String userCode) {
        Long count = baseMapper.selectCount(Wrappers.lambdaQuery(AssetQuitPo.class)
                .eq(AssetQuitPo::getUserCode, userCode)
                .ne(AssetQuitPo::getQuitState, ResignStatus.REVOKE_RESIGN.getCode()));
        return count > 0 ? true : false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AssetQuit entity) {
        AssetQuitPo po = converter.toAssetQuitPo(entity);
        save(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateById(AssetQuit entity) {
        AssetQuitPo po = converter.toAssetQuitPo(entity);
        updateById(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Integer> idList) {
        removeByIds(idList);
    }

    @Override
    public List<AssetQuit> queryResignEmpByTime(String dateTime) {
        if(StringUtils.isBlank(dateTime)){
            dateTime = DateUtils.dateToStringByType(new Date(), DateUtils.YEAR_MONTH_DATE);
        }
        List<AssetQuitPo> assetQuitPos = baseMapper.queryResignEmpByTime(dateTime);
        return converter.toAssetQuits(assetQuitPos);
    }

}
