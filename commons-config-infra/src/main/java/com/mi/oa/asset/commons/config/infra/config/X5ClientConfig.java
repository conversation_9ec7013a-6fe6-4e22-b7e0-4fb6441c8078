package com.mi.oa.asset.commons.config.infra.config;

import com.google.gson.JsonIOException;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.feign.x5.model.X5Header;
import com.mi.oa.infra.oaucf.feign.x5.model.X5Request;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.xiaomi.core.auth.x5.X5Response;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.codec.Decoder;
import feign.codec.Encoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.io.Reader;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import static feign.Util.UTF_8;
import static feign.Util.ensureClosed;

/**
 * @Desc 默认的 X5 接口请求 feign 客户端配置
 * <AUTHOR>
 * @Date 2021/8/30 21:09
 */

@Slf4j
@Component
public class X5ClientConfig {

    protected static final String X5_RESPONSE_SUCCESS_CODE = "200";

    @Autowired
    private Environment environment;

    /**
     * X5 接口请求参数编码器
     * @return
     */
    @Bean
    protected Encoder x5Encoder() {
        return (o, type, requestTemplate) -> requestTemplate.body(JacksonUtils.bean2Json(o));
    }

    /**
     * X5 接口请求参数解码器
     * @return
     */
    @Bean
    protected Decoder x5Decoder() {
        return (response, type) -> {
            if (response.body() == null) return null;

            Reader reader = response.body().asReader(UTF_8);
            String resDataString = IOUtils.toString(reader);
            log.info("x5 url: {}, response: {}", response.request().url(), resDataString);

            try {
                X5Response res = JacksonUtils.json2Bean(resDataString, X5Response.class);

                return responseHandler(resDataString, res, type);
            } catch (JsonIOException e) {
                if (e.getCause() != null && e.getCause() instanceof IOException) {
                    throw IOException.class.cast(e.getCause());
                }
                throw e;
            } finally {
                ensureClosed(reader);
            }
        };
    }

    /**
     * 默认的 X5 接口请求拦截器，签名，参数编码
     * @return
     */
    @Bean
    protected RequestInterceptor x5RequestInterceptor() {
        return requestTemplate -> {
            X5FeignClient ann = requestTemplate.feignTarget().type().getAnnotation(X5FeignClient.class);
            if(null == ann) {
                throw new BizException(InfraErrorCodeEnum.INFRA_X5_INIT_ERROR);
            }

            String appId = environment.resolvePlaceholders(ann.appId());
            String appKey = environment.resolvePlaceholders(ann.appKey());
            if(StringUtils.isBlank(appId) || StringUtils.isBlank(appKey)) {
                throw new BizException(InfraErrorCodeEnum.INFRA_X5_EMPTY_ID_OR_KEY);
            }

            boolean form = ann.form();

            String bodyString = StringUtils.toEncodedString(requestTemplate.body(), StandardCharsets.UTF_8);

            String sign = DigestUtils.md5DigestAsHex((appId + bodyString + appKey).getBytes()).toUpperCase();
            X5Request request = X5Request.builder().header(X5Header.builder().appid(appId).sign(sign).build()).body(bodyString).build();
            log.info("X5 request body: {}", request.toString());

            String body = Base64.encodeBase64String(JacksonUtils.bean2Json(request).getBytes());
            doRequest(requestTemplate, body, form);
        };
    }

    /**
     * 发生 X5 请求，各个系统对于 X5 请求体的编码要求不同，子类可覆盖处理
     * @param requestTemplate
     * @param body
     */
    protected void doRequest(RequestTemplate requestTemplate, String body, boolean form) {
        log.info("X5 data: " + body);
        if (form) {
            requestTemplate.header("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
            try {
                requestTemplate.body("data=" + URLEncoder.encode(body, StandardCharsets.UTF_8.name()));
            } catch (Exception e) {
                log.warn("url encode failed: ", e);
            }
        } else {
            requestTemplate.body(body);
        }
    }

    /**
     * 默认的 X5 接口返回处理，子类可 override
     * @param resDataString
     * @param res
     * @param type
     * @return
     */
    protected Object responseHandler(String resDataString, X5Response res, Type type) {
        if(!res.getHeader().getCode().equals(X5_RESPONSE_SUCCESS_CODE)) {
            throw new BizException(InfraErrorCodeEnum.INFRA_X5_RESPONSE_ERROR, res.getHeader().getDesc());
        }

        if(X5Response.class.equals(type)) return res;

        return JacksonUtils.object2Bean(res.getBody(), type);
    }
}


