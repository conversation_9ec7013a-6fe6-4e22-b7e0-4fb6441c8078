package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.domain.project.entity.ProjectCfg;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.BusinessLinePo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.ProjectCfgPo;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.Map;
import java.util.Objects;


@Mapper(componentModel = "spring")
public interface ProjectCfgConvertor  {


    ProjectCfg po2do(ProjectCfgPo source);

    List<ProjectCfg> po2do(List<ProjectCfgPo> source);

    ProjectCfgPo do2po(ProjectCfg source);
}
