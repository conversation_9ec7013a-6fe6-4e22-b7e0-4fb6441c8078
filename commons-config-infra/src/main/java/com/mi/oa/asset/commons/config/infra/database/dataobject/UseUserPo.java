package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 用途用户
 * @TableName amg_use_user
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_use_user")
public class UseUserPo extends BasePo {

    /**
     * 申请用途ID
     */
    private String useId;

    /**
     * 账号
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;
}