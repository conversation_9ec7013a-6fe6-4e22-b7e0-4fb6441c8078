/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.EncodingUtils;
import org.mi.thrift.TException;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;
import org.mi.thrift.server.AbstractNonblockingServer.*;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2020-5-26")
public class SubsetRequest implements org.mi.thrift.TBase<SubsetRequest, SubsetRequest._Fields>, java.io.Serializable, Cloneable, Comparable<SubsetRequest> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("SubsetRequest");

    private static final org.mi.thrift.protocol.TField PARENT_ID_FIELD_DESC = new org.mi.thrift.protocol.TField("parent_id", org.mi.thrift.protocol.TType.I64, (short) 1);
    private static final org.mi.thrift.protocol.TField LEVEL_FIELD_DESC = new org.mi.thrift.protocol.TField("level", org.mi.thrift.protocol.TType.I32, (short) 2);
    private static final org.mi.thrift.protocol.TField PAGE_FIELD_DESC = new org.mi.thrift.protocol.TField("page", org.mi.thrift.protocol.TType.I32, (short) 3);
    private static final org.mi.thrift.protocol.TField ENABLE_FIELD_DESC = new org.mi.thrift.protocol.TField("enable", org.mi.thrift.protocol.TType.STRING, (short) 4);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();

    static {
        schemes.put(StandardScheme.class, new SubsetRequestStandardSchemeFactory());
        schemes.put(TupleScheme.class, new SubsetRequestTupleSchemeFactory());
    }

    public long parent_id; // required
    public int level; // required
    public int page; // required
    public String enable; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        PARENT_ID((short) 1, "parent_id"),
        LEVEL((short) 2, "level"),
        PAGE((short) 3, "page"),
        ENABLE((short) 4, "enable");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
            for (_Fields field : EnumSet.allOf(_Fields.class)) {
                byName.put(field.getFieldName(), field);
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch (fieldId) {
                case 1: // PARENT_ID
                    return PARENT_ID;
                case 2: // LEVEL
                    return LEVEL;
                case 3: // PAGE
                    return PAGE;
                case 4: // ENABLE
                    return ENABLE;
                default:
                    return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
            _Fields fields = findByThriftId(fieldId);
            if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
            return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
            return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
            _thriftId = thriftId;
            _fieldName = fieldName;
        }

        public short getThriftFieldId() {
            return _thriftId;
        }

        public String getFieldName() {
            return _fieldName;
        }
    }

    // isset id assignments
    private static final int __PARENT_ID_ISSET_ID = 0;
    private static final int __LEVEL_ISSET_ID = 1;
    private static final int __PAGE_ISSET_ID = 2;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;

    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.PARENT_ID, new org.mi.thrift.meta_data.FieldMetaData("parent_id", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I64)));
        tmpMap.put(_Fields.LEVEL, new org.mi.thrift.meta_data.FieldMetaData("level", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
        tmpMap.put(_Fields.PAGE, new org.mi.thrift.meta_data.FieldMetaData("page", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
        tmpMap.put(_Fields.ENABLE, new org.mi.thrift.meta_data.FieldMetaData("enable", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SubsetRequest.class, metaDataMap);
    }

    public SubsetRequest() {
    }

    public SubsetRequest(
            long parent_id,
            int level,
            int page,
            String enable) {
        this();
        this.parent_id = parent_id;
        setParent_idIsSet(true);
        this.level = level;
        setLevelIsSet(true);
        this.page = page;
        setPageIsSet(true);
        this.enable = enable;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public SubsetRequest(SubsetRequest other) {
        __isset_bitfield = other.__isset_bitfield;
        this.parent_id = other.parent_id;
        this.level = other.level;
        this.page = other.page;
        if (other.isSetEnable()) {
            this.enable = other.enable;
        }
    }

    public SubsetRequest deepCopy() {
        return new SubsetRequest(this);
    }

    @Override
    public void clear() {
        setParent_idIsSet(false);
        this.parent_id = 0;
        setLevelIsSet(false);
        this.level = 0;
        setPageIsSet(false);
        this.page = 0;
        this.enable = null;
    }

    public long getParent_id() {
        return this.parent_id;
    }

    public SubsetRequest setParent_id(long parent_id) {
        this.parent_id = parent_id;
        setParent_idIsSet(true);
        return this;
    }

    public void unsetParent_id() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PARENT_ID_ISSET_ID);
    }

    /** Returns true if field parent_id is set (has been assigned a value) and false otherwise */
    public boolean isSetParent_id() {
        return EncodingUtils.testBit(__isset_bitfield, __PARENT_ID_ISSET_ID);
    }

    public void setParent_idIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PARENT_ID_ISSET_ID, value);
    }

    public int getLevel() {
        return this.level;
    }

    public SubsetRequest setLevel(int level) {
        this.level = level;
        setLevelIsSet(true);
        return this;
    }

    public void unsetLevel() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LEVEL_ISSET_ID);
    }

    /** Returns true if field level is set (has been assigned a value) and false otherwise */
    public boolean isSetLevel() {
        return EncodingUtils.testBit(__isset_bitfield, __LEVEL_ISSET_ID);
    }

    public void setLevelIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LEVEL_ISSET_ID, value);
    }

    public int getPage() {
        return this.page;
    }

    public SubsetRequest setPage(int page) {
        this.page = page;
        setPageIsSet(true);
        return this;
    }

    public void unsetPage() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGE_ISSET_ID);
    }

    /** Returns true if field page is set (has been assigned a value) and false otherwise */
    public boolean isSetPage() {
        return EncodingUtils.testBit(__isset_bitfield, __PAGE_ISSET_ID);
    }

    public void setPageIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGE_ISSET_ID, value);
    }

    public String getEnable() {
        return this.enable;
    }

    public SubsetRequest setEnable(String enable) {
        this.enable = enable;
        return this;
    }

    public void unsetEnable() {
        this.enable = null;
    }

    /** Returns true if field enable is set (has been assigned a value) and false otherwise */
    public boolean isSetEnable() {
        return this.enable != null;
    }

    public void setEnableIsSet(boolean value) {
        if (!value) {
            this.enable = null;
        }
    }

    public void setFieldValue(_Fields field, Object value) {
        switch (field) {
            case PARENT_ID:
                if (value == null) {
                    unsetParent_id();
                } else {
                    setParent_id((Long) value);
                }
                break;

            case LEVEL:
                if (value == null) {
                    unsetLevel();
                } else {
                    setLevel((Integer) value);
                }
                break;

            case PAGE:
                if (value == null) {
                    unsetPage();
                } else {
                    setPage((Integer) value);
                }
                break;

            case ENABLE:
                if (value == null) {
                    unsetEnable();
                } else {
                    setEnable((String) value);
                }
                break;

        }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
            case PARENT_ID:
                return Long.valueOf(getParent_id());

            case LEVEL:
                return Integer.valueOf(getLevel());

            case PAGE:
                return Integer.valueOf(getPage());

            case ENABLE:
                return getEnable();

        }
        throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
        if (field == null) {
            throw new IllegalArgumentException();
        }

        switch (field) {
            case PARENT_ID:
                return isSetParent_id();
            case LEVEL:
                return isSetLevel();
            case PAGE:
                return isSetPage();
            case ENABLE:
                return isSetEnable();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
            return false;
        if (that instanceof SubsetRequest)
            return this.equals((SubsetRequest) that);
        return false;
    }

    public boolean equals(SubsetRequest that) {
        if (that == null)
            return false;

        boolean this_present_parent_id = true;
        boolean that_present_parent_id = true;
        if (this_present_parent_id || that_present_parent_id) {
            if (!(this_present_parent_id && that_present_parent_id))
                return false;
            if (this.parent_id != that.parent_id)
                return false;
        }

        boolean this_present_level = true;
        boolean that_present_level = true;
        if (this_present_level || that_present_level) {
            if (!(this_present_level && that_present_level))
                return false;
            if (this.level != that.level)
                return false;
        }

        boolean this_present_page = true;
        boolean that_present_page = true;
        if (this_present_page || that_present_page) {
            if (!(this_present_page && that_present_page))
                return false;
            if (this.page != that.page)
                return false;
        }

        boolean this_present_enable = true && this.isSetEnable();
        boolean that_present_enable = true && that.isSetEnable();
        if (this_present_enable || that_present_enable) {
            if (!(this_present_enable && that_present_enable))
                return false;
            if (!this.enable.equals(that.enable))
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_parent_id = true;
        list.add(present_parent_id);
        if (present_parent_id)
            list.add(parent_id);

        boolean present_level = true;
        list.add(present_level);
        if (present_level)
            list.add(level);

        boolean present_page = true;
        list.add(present_page);
        if (present_page)
            list.add(page);

        boolean present_enable = true && (isSetEnable());
        list.add(present_enable);
        if (present_enable)
            list.add(enable);

        return list.hashCode();
    }

    @Override
    public int compareTo(SubsetRequest other) {
        if (!getClass().equals(other.getClass())) {
            return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetParent_id()).compareTo(other.isSetParent_id());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetParent_id()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.parent_id, other.parent_id);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetLevel()).compareTo(other.isSetLevel());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetLevel()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.level, other.level);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetPage()).compareTo(other.isSetPage());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetPage()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.page, other.page);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetEnable()).compareTo(other.isSetEnable());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetEnable()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.enable, other.enable);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
        return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("SubsetRequest(");
        boolean first = true;

        sb.append("parent_id:");
        sb.append(this.parent_id);
        first = false;
        if (!first) sb.append(", ");
        sb.append("level:");
        sb.append(this.level);
        first = false;
        if (!first) sb.append(", ");
        sb.append("page:");
        sb.append(this.page);
        first = false;
        if (!first) sb.append(", ");
        sb.append("enable:");
        if (this.enable == null) {
            sb.append("null");
        } else {
            sb.append(this.enable);
        }
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        try {
            write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        try {
            // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
            __isset_bitfield = 0;
            read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private static class SubsetRequestStandardSchemeFactory implements SchemeFactory {
        public SubsetRequestStandardScheme getScheme() {
            return new SubsetRequestStandardScheme();
        }
    }

    private static class SubsetRequestStandardScheme extends StandardScheme<SubsetRequest> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, SubsetRequest struct) throws TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true) {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                    break;
                }
                switch (schemeField.id) {
                    case 1: // PARENT_ID
                        if (schemeField.type == org.mi.thrift.protocol.TType.I64) {
                            struct.parent_id = iprot.readI64();
                            struct.setParent_idIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // LEVEL
                        if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                            struct.level = iprot.readI32();
                            struct.setLevelIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 3: // PAGE
                        if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                            struct.page = iprot.readI32();
                            struct.setPageIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 4: // ENABLE
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.enable = iprot.readString();
                            struct.setEnableIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                        org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, SubsetRequest struct) throws TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            oprot.writeFieldBegin(PARENT_ID_FIELD_DESC);
            oprot.writeI64(struct.parent_id);
            oprot.writeFieldEnd();
            oprot.writeFieldBegin(LEVEL_FIELD_DESC);
            oprot.writeI32(struct.level);
            oprot.writeFieldEnd();
            oprot.writeFieldBegin(PAGE_FIELD_DESC);
            oprot.writeI32(struct.page);
            oprot.writeFieldEnd();
            if (struct.enable != null) {
                oprot.writeFieldBegin(ENABLE_FIELD_DESC);
                oprot.writeString(struct.enable);
                oprot.writeFieldEnd();
            }
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class SubsetRequestTupleSchemeFactory implements SchemeFactory {
        public SubsetRequestTupleScheme getScheme() {
            return new SubsetRequestTupleScheme();
        }
    }

    private static class SubsetRequestTupleScheme extends TupleScheme<SubsetRequest> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, SubsetRequest struct) throws TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetParent_id()) {
                optionals.set(0);
            }
            if (struct.isSetLevel()) {
                optionals.set(1);
            }
            if (struct.isSetPage()) {
                optionals.set(2);
            }
            if (struct.isSetEnable()) {
                optionals.set(3);
            }
            oprot.writeBitSet(optionals, 4);
            if (struct.isSetParent_id()) {
                oprot.writeI64(struct.parent_id);
            }
            if (struct.isSetLevel()) {
                oprot.writeI32(struct.level);
            }
            if (struct.isSetPage()) {
                oprot.writeI32(struct.page);
            }
            if (struct.isSetEnable()) {
                oprot.writeString(struct.enable);
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, SubsetRequest struct) throws TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(4);
            if (incoming.get(0)) {
                struct.parent_id = iprot.readI64();
                struct.setParent_idIsSet(true);
            }
            if (incoming.get(1)) {
                struct.level = iprot.readI32();
                struct.setLevelIsSet(true);
            }
            if (incoming.get(2)) {
                struct.page = iprot.readI32();
                struct.setPageIsSet(true);
            }
            if (incoming.get(3)) {
                struct.enable = iprot.readString();
                struct.setEnableIsSet(true);
            }
        }
    }

}

