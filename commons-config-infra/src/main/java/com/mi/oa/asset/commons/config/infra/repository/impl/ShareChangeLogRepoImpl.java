package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareChangeLog;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.ShareChangeLogRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.ShareChangeLogPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.ShareChangeLogPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.ShareChangeLogRepoConverter;
import com.mi.oa.asset.eam.mybatis.BasePo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-07 10:53:29
 */
@Service
public class ShareChangeLogRepoImpl extends ServiceImpl<ShareChangeLogPoMapper, ShareChangeLogPo> implements ShareChangeLogRepo {

    @Resource
    private ShareChangeLogRepoConverter converter;

    @Override
    public void save(ShareChangeLog shareChangeLog) {
        ShareChangeLogPo shareChangeLogPo = converter.doToPo(shareChangeLog);
        baseMapper.insert(shareChangeLogPo);
    }

    @Override
    public List<ShareChangeLog> getByShareIds(List<Integer> shareIds, Date startTime) {
        List<ShareChangeLogPo> logPos = baseMapper.selectList(Wrappers.lambdaQuery(ShareChangeLogPo.class)
                .in(ShareChangeLogPo::getShareId, shareIds)
                .gt(BasePo::getUpdateTime, startTime));
        return converter.poToDoList(logPos);
    }
}
