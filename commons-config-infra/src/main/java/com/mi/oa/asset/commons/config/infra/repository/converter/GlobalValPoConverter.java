package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.api.function.GlobalValReq;
import com.mi.oa.asset.commons.config.domain.function.entity.GlobalVal;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.GlobalValPo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface GlobalValPoConverter extends CommonConverter {

    GlobalValPo reqToPo(GlobalValReq source);
    GlobalValPo toPo(GlobalVal source);

    List<GlobalValPo> toPoList(List<GlobalVal> source);

    GlobalVal toDo(GlobalValPo source);

    List<GlobalVal> toDoList(List<GlobalValPo> source);

}
