package com.mi.oa.asset.commons.config.infra.database.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.commons.config.api.myfunctions.Country;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetReceiveAddressPo;

import java.util.List;

/**
 *  DAO映射器
 * <AUTHOR>
 * @date 2024-04-08 11:14:07
 */
public interface AssetReceiveAddressPoMapper extends BaseMapper<AssetReceiveAddressPo> {

    List<Country> listCountry();
}
