package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "amg_position_space_park")
public class PositionSpaceParkPo extends BasePo {
    /**
     * 位置编码
     */
    @TableField(value = "position_code")
    private String positionCode;

    /**
     * 位置名称
     */
    @TableField(value = "position_name")
    private String positionName;
    /**
     * 英文名称
     */
    @TableField(value = "position_name_en")
    private String positionNameEn;

    /**
     * 上级编码
     */
    @TableField(value = "parent_code")
    private String parentCode;
}