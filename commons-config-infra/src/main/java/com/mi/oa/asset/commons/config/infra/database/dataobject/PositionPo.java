package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2023/10/17 11:34
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "amg_position")
public class PositionPo extends BasePo {
    /**
     * 位置编码
     */
    @TableField(value = "position_code")
    private String positionCode;

    /**
     * 位置名称
     */
    @TableField(value = "position_name")
    private String positionName;
    /**
     * 英文名称
     */
    @TableField(value = "position_name_en")
    private String positionNameEn;

    /**
     * 位置类型
     */
    @TableField(value = "position_type")
    private String positionType;

    /**
     * 上级编码
     */
    @TableField(value = "parent_code")
    private String parentCode;

    /**
     * 位置路径
     */
    @TableField(value = "position_path")
    private String positionPath;

    /**
     * 业务线
     */
    @TableField(value = "business_line")
    private String businessLine;

    /**
     * 数据来源, 默认手动录入
     */
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * 禁用
     */
    @TableField(value = "disabled")
    private Integer disabled;

    /**
     * 外部系统编码
     */
    @TableField(value = "out_sys_code")
    private String outSysCode;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
}