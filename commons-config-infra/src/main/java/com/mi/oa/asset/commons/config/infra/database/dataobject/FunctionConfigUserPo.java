package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/5/6 21:11
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_function_config_user")
public class FunctionConfigUserPo extends BasePo {

    /**
     * 管理线 manage_line
     */
    private String manageLine;

    /**
     * 管理线名称 manage_line_name
     */
    private String manageLineName;

    /**
     * 用户账号 user_code
     */
    private String userCode;

    /**
     * 用户姓名 user_name
     */
    private String userName;

    /**
     * 排序
     */
    private Integer sort;
}
