package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/05/11/10:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_asset_dispose_type")
public class AssetDisposeTypePO extends BasePo implements Serializable {
    /**
     * 处置类型编码 record_no
     */
    private String recordNo;

    /**
     * 类型名称 record_name
     */
    private String recordName;

    /**
     * 记录状态：生效-1，失效-0 record_status
     */
    private Integer recordStatus;

    /**
     * 业务分类，业务侧根据业务场景自定义的细分的处置类型 business_cate
     */
    private String businessCate;

    /**
     * 处置类型：transfer-转让处置，normal-正常损毁报废，disasters-自然灾害损毁报废，scan_lose-盘亏，lost-丢失，other-其他 dispose_type
     */
    private String disposeType;

    /**
     * 备注 remark
     */
    private String remark;

    /**
     * 业务线 business_line
     */
    private String businessLine;
}