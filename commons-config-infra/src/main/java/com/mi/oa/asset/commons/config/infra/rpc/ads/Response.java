/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.EncodingUtils;
import org.mi.thrift.TException;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;
import org.mi.thrift.server.AbstractNonblockingServer.*;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2020-5-26")
public class Response implements org.mi.thrift.TBase<Response, Response._Fields>, java.io.Serializable, Cloneable, Comparable<Response> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("Response");

    private static final org.mi.thrift.protocol.TField DATA_FIELD_DESC = new org.mi.thrift.protocol.TField("data", org.mi.thrift.protocol.TType.STRING, (short) 1);
    private static final org.mi.thrift.protocol.TField ERROR_NO_FIELD_DESC = new org.mi.thrift.protocol.TField("errorNo", org.mi.thrift.protocol.TType.I32, (short) 2);
    private static final org.mi.thrift.protocol.TField ERROR_MSG_FIELD_DESC = new org.mi.thrift.protocol.TField("errorMsg", org.mi.thrift.protocol.TType.STRING, (short) 3);
    private static final org.mi.thrift.protocol.TField PAGE_FIELD_DESC = new org.mi.thrift.protocol.TField("page", org.mi.thrift.protocol.TType.STRUCT, (short) 4);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();

    static {
        schemes.put(StandardScheme.class, new ResponseStandardSchemeFactory());
        schemes.put(TupleScheme.class, new ResponseTupleSchemeFactory());
    }

    public String data; // required
    public int errorNo; // required
    public String errorMsg; // required
    public Page page; // required

    /**
     * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
     */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        DATA((short) 1, "data"),
        ERROR_NO((short) 2, "errorNo"),
        ERROR_MSG((short) 3, "errorMsg"),
        PAGE((short) 4, "page");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
            for (_Fields field : EnumSet.allOf(_Fields.class)) {
                byName.put(field.getFieldName(), field);
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch (fieldId) {
                case 1: // DATA
                    return DATA;
                case 2: // ERROR_NO
                    return ERROR_NO;
                case 3: // ERROR_MSG
                    return ERROR_MSG;
                case 4: // PAGE
                    return PAGE;
                default:
                    return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
            _Fields fields = findByThriftId(fieldId);
            if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
            return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
            return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
            _thriftId = thriftId;
            _fieldName = fieldName;
        }

        public short getThriftFieldId() {
            return _thriftId;
        }

        public String getFieldName() {
            return _fieldName;
        }
    }

    // isset id assignments
    private static final int __ERRORNO_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;

    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.DATA, new org.mi.thrift.meta_data.FieldMetaData("data", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        tmpMap.put(_Fields.ERROR_NO, new org.mi.thrift.meta_data.FieldMetaData("errorNo", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
        tmpMap.put(_Fields.ERROR_MSG, new org.mi.thrift.meta_data.FieldMetaData("errorMsg", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        tmpMap.put(_Fields.PAGE, new org.mi.thrift.meta_data.FieldMetaData("page", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRUCT, "Page")));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(Response.class, metaDataMap);
    }

    public Response() {
    }

    public Response(
            String data,
            int errorNo,
            String errorMsg,
            Page page) {
        this();
        this.data = data;
        this.errorNo = errorNo;
        setErrorNoIsSet(true);
        this.errorMsg = errorMsg;
        this.page = page;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public Response(Response other) {
        __isset_bitfield = other.__isset_bitfield;
        if (other.isSetData()) {
            this.data = other.data;
        }
        this.errorNo = other.errorNo;
        if (other.isSetErrorMsg()) {
            this.errorMsg = other.errorMsg;
        }
        if (other.isSetPage()) {
            this.page = other.page;
        }
    }

    public Response deepCopy() {
        return new Response(this);
    }

    @Override
    public void clear() {
        this.data = null;
        setErrorNoIsSet(false);
        this.errorNo = 0;
        this.errorMsg = null;
        this.page = null;
    }

    public String getData() {
        return this.data;
    }

    public Response setData(String data) {
        this.data = data;
        return this;
    }

    public void unsetData() {
        this.data = null;
    }

    /**
     * Returns true if field data is set (has been assigned a value) and false otherwise
     */
    public boolean isSetData() {
        return this.data != null;
    }

    public void setDataIsSet(boolean value) {
        if (!value) {
            this.data = null;
        }
    }

    public int getErrorNo() {
        return this.errorNo;
    }

    public Response setErrorNo(int errorNo) {
        this.errorNo = errorNo;
        setErrorNoIsSet(true);
        return this;
    }

    public void unsetErrorNo() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ERRORNO_ISSET_ID);
    }

    /**
     * Returns true if field errorNo is set (has been assigned a value) and false otherwise
     */
    public boolean isSetErrorNo() {
        return EncodingUtils.testBit(__isset_bitfield, __ERRORNO_ISSET_ID);
    }

    public void setErrorNoIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ERRORNO_ISSET_ID, value);
    }

    public String getErrorMsg() {
        return this.errorMsg;
    }

    public Response setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
        return this;
    }

    public void unsetErrorMsg() {
        this.errorMsg = null;
    }

    /**
     * Returns true if field errorMsg is set (has been assigned a value) and false otherwise
     */
    public boolean isSetErrorMsg() {
        return this.errorMsg != null;
    }

    public void setErrorMsgIsSet(boolean value) {
        if (!value) {
            this.errorMsg = null;
        }
    }

    public Page getPage() {
        return this.page;
    }

    public Response setPage(Page page) {
        this.page = page;
        return this;
    }

    public void unsetPage() {
        this.page = null;
    }

    /**
     * Returns true if field page is set (has been assigned a value) and false otherwise
     */
    public boolean isSetPage() {
        return this.page != null;
    }

    public void setPageIsSet(boolean value) {
        if (!value) {
            this.page = null;
        }
    }

    public void setFieldValue(_Fields field, Object value) {
        switch (field) {
            case DATA:
                if (value == null) {
                    unsetData();
                } else {
                    setData((String) value);
                }
                break;

            case ERROR_NO:
                if (value == null) {
                    unsetErrorNo();
                } else {
                    setErrorNo((Integer) value);
                }
                break;

            case ERROR_MSG:
                if (value == null) {
                    unsetErrorMsg();
                } else {
                    setErrorMsg((String) value);
                }
                break;

            case PAGE:
                if (value == null) {
                    unsetPage();
                } else {
                    setPage((Page) value);
                }
                break;

        }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
            case DATA:
                return getData();

            case ERROR_NO:
                return Integer.valueOf(getErrorNo());

            case ERROR_MSG:
                return getErrorMsg();

            case PAGE:
                return getPage();

        }
        throw new IllegalStateException();
    }

    /**
     * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
     */
    public boolean isSet(_Fields field) {
        if (field == null) {
            throw new IllegalArgumentException();
        }

        switch (field) {
            case DATA:
                return isSetData();
            case ERROR_NO:
                return isSetErrorNo();
            case ERROR_MSG:
                return isSetErrorMsg();
            case PAGE:
                return isSetPage();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
            return false;
        if (that instanceof Response)
            return this.equals((Response) that);
        return false;
    }

    public boolean equals(Response that) {
        if (that == null)
            return false;

        boolean this_present_data = true && this.isSetData();
        boolean that_present_data = true && that.isSetData();
        if (this_present_data || that_present_data) {
            if (!(this_present_data && that_present_data))
                return false;
            if (!this.data.equals(that.data))
                return false;
        }

        boolean this_present_errorNo = true;
        boolean that_present_errorNo = true;
        if (this_present_errorNo || that_present_errorNo) {
            if (!(this_present_errorNo && that_present_errorNo))
                return false;
            if (this.errorNo != that.errorNo)
                return false;
        }

        boolean this_present_errorMsg = true && this.isSetErrorMsg();
        boolean that_present_errorMsg = true && that.isSetErrorMsg();
        if (this_present_errorMsg || that_present_errorMsg) {
            if (!(this_present_errorMsg && that_present_errorMsg))
                return false;
            if (!this.errorMsg.equals(that.errorMsg))
                return false;
        }

        boolean this_present_page = true && this.isSetPage();
        boolean that_present_page = true && that.isSetPage();
        if (this_present_page || that_present_page) {
            if (!(this_present_page && that_present_page))
                return false;
            if (!this.page.equals(that.page))
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_data = true && (isSetData());
        list.add(present_data);
        if (present_data)
            list.add(data);

        boolean present_errorNo = true;
        list.add(present_errorNo);
        if (present_errorNo)
            list.add(errorNo);

        boolean present_errorMsg = true && (isSetErrorMsg());
        list.add(present_errorMsg);
        if (present_errorMsg)
            list.add(errorMsg);

        boolean present_page = true && (isSetPage());
        list.add(present_page);
        if (present_page)
            list.add(page);

        return list.hashCode();
    }

    @Override
    public int compareTo(Response other) {
        if (!getClass().equals(other.getClass())) {
            return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetData()).compareTo(other.isSetData());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetData()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.data, other.data);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetErrorNo()).compareTo(other.isSetErrorNo());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetErrorNo()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.errorNo, other.errorNo);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetErrorMsg()).compareTo(other.isSetErrorMsg());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetErrorMsg()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.errorMsg, other.errorMsg);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetPage()).compareTo(other.isSetPage());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetPage()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.page, other.page);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
        return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Response(");
        boolean first = true;

        sb.append("data:");
        if (this.data == null) {
            sb.append("null");
        } else {
            sb.append(this.data);
        }
        first = false;
        if (!first) sb.append(", ");
        sb.append("errorNo:");
        sb.append(this.errorNo);
        first = false;
        if (!first) sb.append(", ");
        sb.append("errorMsg:");
        if (this.errorMsg == null) {
            sb.append("null");
        } else {
            sb.append(this.errorMsg);
        }
        first = false;
        if (!first) sb.append(", ");
        sb.append("page:");
        if (this.page == null) {
            sb.append("null");
        } else {
            sb.append(this.page);
        }
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        try {
            write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        try {
            // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
            __isset_bitfield = 0;
            read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private static class ResponseStandardSchemeFactory implements SchemeFactory {
        public ResponseStandardScheme getScheme() {
            return new ResponseStandardScheme();
        }
    }

    private static class ResponseStandardScheme extends StandardScheme<Response> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, Response struct) throws TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true) {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                    break;
                }
                switch (schemeField.id) {
                    case 1: // DATA
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.data = iprot.readString();
                            struct.setDataIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // ERROR_NO
                        if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                            struct.errorNo = iprot.readI32();
                            struct.setErrorNoIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 3: // ERROR_MSG
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.errorMsg = iprot.readString();
                            struct.setErrorMsgIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 4: // PAGE
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                            struct.page = new Page();
                            struct.page.read(iprot);
                            struct.setPageIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                        org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, Response struct) throws TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            if (struct.data != null) {
                oprot.writeFieldBegin(DATA_FIELD_DESC);
                oprot.writeString(struct.data);
                oprot.writeFieldEnd();
            }
            oprot.writeFieldBegin(ERROR_NO_FIELD_DESC);
            oprot.writeI32(struct.errorNo);
            oprot.writeFieldEnd();
            if (struct.errorMsg != null) {
                oprot.writeFieldBegin(ERROR_MSG_FIELD_DESC);
                oprot.writeString(struct.errorMsg);
                oprot.writeFieldEnd();
            }
            if (struct.page != null) {
                oprot.writeFieldBegin(PAGE_FIELD_DESC);
                struct.page.write(oprot);
                oprot.writeFieldEnd();
            }
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class ResponseTupleSchemeFactory implements SchemeFactory {
        public ResponseTupleScheme getScheme() {
            return new ResponseTupleScheme();
        }
    }

    private static class ResponseTupleScheme extends TupleScheme<Response> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, Response struct) throws TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetData()) {
                optionals.set(0);
            }
            if (struct.isSetErrorNo()) {
                optionals.set(1);
            }
            if (struct.isSetErrorMsg()) {
                optionals.set(2);
            }
            if (struct.isSetPage()) {
                optionals.set(3);
            }
            oprot.writeBitSet(optionals, 4);
            if (struct.isSetData()) {
                oprot.writeString(struct.data);
            }
            if (struct.isSetErrorNo()) {
                oprot.writeI32(struct.errorNo);
            }
            if (struct.isSetErrorMsg()) {
                oprot.writeString(struct.errorMsg);
            }
            if (struct.isSetPage()) {
                struct.page.write(oprot);
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, Response struct) throws TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(4);
            if (incoming.get(0)) {
                struct.data = iprot.readString();
                struct.setDataIsSet(true);
            }
            if (incoming.get(1)) {
                struct.errorNo = iprot.readI32();
                struct.setErrorNoIsSet(true);
            }
            if (incoming.get(2)) {
                struct.errorMsg = iprot.readString();
                struct.setErrorMsgIsSet(true);
            }
            if (incoming.get(3)) {
                struct.page = new Page();
                struct.page.read(iprot);
                struct.setPageIsSet(true);
            }
        }
    }

}

