package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.assetcategory.entity.AssetCategory;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetCategoryPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:47
 */

@Mapper(componentModel = "spring")
public interface AssetCategoryRepoConverter extends CommonConverter {

    @Mapping(source = "id", target = "cateId")
    AssetCategory toAssetCategory(AssetCategoryPo source);

    List<AssetCategory> toAssetCategories(List<AssetCategoryPo> source);


    @Mapping(source = "cateId", target = "id")
    AssetCategoryPo toAssetCategoryPo(AssetCategory source);

    List<AssetCategoryPo> toAssetCategoryPos(List<AssetCategory> source);
}
