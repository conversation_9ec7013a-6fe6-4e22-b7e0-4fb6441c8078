package com.mi.oa.asset.commons.config.infra.repository.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.project.entity.ProjectCfg;
import com.mi.oa.asset.commons.config.domain.project.repository.ProjectCfgRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.ProjectCfgPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.ProjectCfgPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.ProjectCfgConvertor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 项目配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
@Slf4j
public class ProjectCfgRepoImpl extends ServiceImpl<ProjectCfgPoMapper, ProjectCfgPo> implements ProjectCfgRepo {

    @Resource
    private ProjectCfgConvertor convertor;

    @Override
    public ProjectCfg getProjectCfg(String businessLine, String projectCode) {
        if (StringUtils.isBlank(businessLine) || StringUtils.isBlank(projectCode)) return null;
        ProjectCfgPo projectCfgPo = this.baseMapper.selectOne(Wrappers.<ProjectCfgPo>lambdaQuery()
                .eq(ProjectCfgPo::getBusinessLine, businessLine).eq(ProjectCfgPo::getProjectCode, projectCode));
        return convertor.po2do(projectCfgPo);
    }

    @Override
    public ProjectCfg getProjectCfg(Integer projectId) {
        if (Objects.isNull(projectId)) return null;
        ProjectCfgPo projectCfgPo = this.baseMapper.selectById(projectId);
        return convertor.po2do(projectCfgPo);
    }

    @Override
    public List<ProjectCfg> listProjectCfg(String businessLine) {
        if (StringUtils.isBlank(businessLine)) return Collections.emptyList();
        List<ProjectCfgPo> projectCfgPos = this.baseMapper.selectList(Wrappers.<ProjectCfgPo>lambdaQuery()
                .eq(ProjectCfgPo::getBusinessLine, businessLine));
        return convertor.po2do(projectCfgPos);
    }

    @Override
    public void saveProjectCfg(ProjectCfg entity) {
        ProjectCfgPo projectCfgPo = convertor.do2po(entity);
        if (Objects.isNull(projectCfgPo.getId())) {
            baseMapper.insert(projectCfgPo);
            entity.setId(projectCfgPo.getId());
        } else {
            baseMapper.updateById(convertor.do2po(entity));
        }
    }

    @Override
    public void updateStatus(List<Integer> ids, String recordStatus) {
        if (CollectionUtils.isEmpty(ids) || StringUtils.isBlank(recordStatus)) return;
        baseMapper.update(null, Wrappers.<ProjectCfgPo>lambdaUpdate()
                .set(ProjectCfgPo::getRecordStatus, recordStatus)
                .in(ProjectCfgPo::getId, ids));
    }

}