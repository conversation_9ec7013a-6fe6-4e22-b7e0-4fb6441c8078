package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.commonfunc.entity.MyCommonFunc;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.MyCommonFuncPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13 14:21
 * @description
 */
@Mapper(componentModel = "spring")
public interface MyCommonFuncRepoConverter extends CommonConverter {
    MyCommonFunc poToDo(MyCommonFuncPo myCommonFuncPo);

    List<MyCommonFunc> poToDoList(List<MyCommonFuncPo> myCommonFuncPos);

    MyCommonFuncPo doToPo(MyCommonFunc myCommonFunc);

    List<MyCommonFuncPo> doToPoList(List<MyCommonFunc> myCommonFuncDos);
}
