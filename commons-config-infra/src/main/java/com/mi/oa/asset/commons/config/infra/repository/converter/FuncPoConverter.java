package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.function.entity.FuncForm;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FuncFormPo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface FuncPoConverter extends CommonConverter {


    FuncFormPo toPo(FuncForm funcForm);

    List<FuncFormPo> toPoList(List<FuncForm> funcForm);

    FuncForm toDo(FuncFormPo funcForm);

    List<FuncForm> toDoList(List<FuncFormPo> funcForm);

}
