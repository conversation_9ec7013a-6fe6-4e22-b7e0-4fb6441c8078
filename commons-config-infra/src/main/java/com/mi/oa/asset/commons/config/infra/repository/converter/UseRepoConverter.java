package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.use.entity.Use;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.UsePo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UseRepoConverter extends CommonConverter {

    Use toUse(UsePo source);

    UsePo toUsePo(Use source);

    List<Use> toUses(List<UsePo> source);
}