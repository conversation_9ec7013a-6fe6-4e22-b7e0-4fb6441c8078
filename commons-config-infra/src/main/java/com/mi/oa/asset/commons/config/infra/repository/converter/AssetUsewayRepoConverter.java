package com.mi.oa.asset.commons.config.infra.repository.converter;


import com.mi.oa.asset.commons.config.domain.assetuseway.entity.AssetUseway;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetUsewayPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 办公用途 converter 转换器
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@Mapper(componentModel = "spring")
public interface AssetUsewayRepoConverter {

    AssetUseway toAssetUseway(AssetUsewayPo source);

    AssetUsewayPo toAssetUsewayPo(AssetUseway source);

    List<AssetUseway> toAssetUseways(List<AssetUsewayPo> source);
}