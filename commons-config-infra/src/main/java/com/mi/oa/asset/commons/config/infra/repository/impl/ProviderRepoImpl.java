package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.common.repository.ProviderRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.Provider;
import com.mi.oa.asset.commons.config.infra.database.dataobject.ProviderPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.ProviderPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.CommonDataRepoConverter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/9/30 14:56
 */
@Service
@RequiredArgsConstructor
public class ProviderRepoImpl extends ServiceImpl<ProviderPoMapper, ProviderPo> implements ProviderRepo {

    private final CommonDataRepoConverter commonDataRepoConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveProvider(List<Provider> providers) {
        if (CollectionUtils.isEmpty(providers)) return;

        List<ProviderPo> providerPos = commonDataRepoConverter.toProviderPo(providers);
        List<String> providerCodes = providerPos.stream()
                .map(ProviderPo::getProviderCode).collect(Collectors.toList());
        List<ProviderPo> list = this.list(Wrappers.lambdaQuery(ProviderPo.class)
                .in(ProviderPo::getProviderCode, providerCodes));
        List<ProviderPo> saveList = new ArrayList<>();
        List<ProviderPo> updateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            providerPos.forEach(i -> {
                ProviderPo exist = list.stream()
                        .filter(j -> j.getProviderCode().equals(i.getProviderCode()))
                        .findFirst().orElse(null);
                if (exist != null) {
                    i.setId(exist.getId());
                    i.setUpdateTime(new Date());
                    updateList.add(i);
                } else {
                    saveList.add(i);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(saveList)) {
            this.saveBatch(saveList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
    }
}
