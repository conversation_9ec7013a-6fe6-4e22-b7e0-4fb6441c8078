package com.mi.oa.asset.commons.config.infra.rpc.mdm;

import com.google.gson.*;

import java.lang.reflect.Type;

public class BooleanTo01Adapter implements JsonSerializer<Boolean>, JsonDeserializer {

    @Override
    public JsonElement serialize(Boolean value, Type type, JsonSerializationContext jsonSerializationContext) {
        return new JsonPrimitive(value ? "1" : "0");
    }

    @Override
    public Object deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
        String v = jsonElement.getAsString();
        return "1".equals(v);
    }
}
