/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.TException;
import org.mi.thrift.async.AsyncMethodCallback;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;
import org.mi.thrift.server.AbstractNonblockingServer.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Generated;
import java.util.*;

/**
 * 　　* @description: TODO
 * 　　* @date 2021/8/9 下午1:09
 */
@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2020-5-26")
public class AddressService {
    /**
     * 　　* @description: TODO
     * 　　* @date 2021/8/9 下午1:12
     */
    public interface Iface {

        Response getCountry(CountryRequest request) throws TException;

        Response getProvince(ProvinceRequest request) throws TException;

        Response getCity(CityRequest request) throws TException;

        Response getDistrict(DistrictRequest request) throws TException;

        Response getStreet(StreetRequest request) throws TException;

        Response syncData(SyncRequest request) throws TException;

        Response getInfoByIds(IdRequest request) throws TException;

        Response getInfoByNames(NameRequest request) throws TException;

        Response getSubset(SubsetRequest request) throws TException;

        Response getRegionTag(RegionTagRequest request) throws TException;

        Response getRegionTagByIds(TagIdRequest request) throws TException;

        Response getIndRegionTag(RegionTagRequest request) throws TException;

        List<Integer> getCountryNum(CountryNumRequest request) throws TException;

    }

    public interface AsyncIface {

        void getCountry(CountryRequest request, AsyncMethodCallback resultHandler) throws TException;

        void getProvince(ProvinceRequest request, AsyncMethodCallback resultHandler) throws TException;

        void getCity(CityRequest request, AsyncMethodCallback resultHandler) throws TException;

        void getDistrict(DistrictRequest request, AsyncMethodCallback resultHandler) throws TException;

        void getStreet(StreetRequest request, AsyncMethodCallback resultHandler) throws TException;

        void syncData(SyncRequest request, AsyncMethodCallback resultHandler) throws TException;

        void getInfoByIds(IdRequest request, AsyncMethodCallback resultHandler) throws TException;

        void getInfoByNames(NameRequest request, AsyncMethodCallback resultHandler) throws TException;

        void getSubset(SubsetRequest request, AsyncMethodCallback resultHandler) throws TException;

        void getRegionTag(RegionTagRequest request, AsyncMethodCallback resultHandler) throws TException;

        void getRegionTagByIds(TagIdRequest request, AsyncMethodCallback resultHandler) throws TException;

        void getIndRegionTag(RegionTagRequest request, AsyncMethodCallback resultHandler) throws TException;

        void getCountryNum(CountryNumRequest request, AsyncMethodCallback resultHandler) throws TException;

    }

    /**
     * 　　* @description: TODO
     * 　　* @date 2021/8/9 下午1:10
     */
    public static class Client extends org.mi.thrift.TServiceClient implements Iface {
        private org.mi.thrift.transport.XContext context = null;

        /**
        　　* @description: TODO
        　　* @date 2021/8/9 下午1:21
        　　*/
        public static class Factory implements org.mi.thrift.TServiceClientFactory<Client> {
            public Factory() {
            }

            public Client getClient(org.mi.thrift.protocol.TProtocol prot) {
                return new Client(prot);
            }

            public Client getClient(org.mi.thrift.protocol.TProtocol iprot, org.mi.thrift.protocol.TProtocol oprot) {
                return new Client(iprot, oprot);
            }
        }

        public void setContext(org.mi.thrift.transport.XContext context) {
            context = context;
        }


        public Client(org.mi.thrift.protocol.TProtocol prot) {
            super(prot, prot);
        }

        public Client(org.mi.thrift.protocol.TProtocol iprot, org.mi.thrift.protocol.TProtocol oprot) {
            super(iprot, oprot);
        }

        public Response getCountry(CountryRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendGetCountry(request);
            return recvGetCountry();
        }

        public void sendGetCountry(CountryRequest request) throws TException {
            GetCountryArgs args = new GetCountryArgs();
            args.setRequest(request);
            sendBase("GetCountry", args);
        }

        public Response recvGetCountry() throws TException {
            GetCountryResult result = new GetCountryResult();
            receiveBase(result, "GetCountry");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "GetCountry failed: unknown result");
        }

        public Response getProvince(ProvinceRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendGetProvince(request);
            return recvGetProvince();
        }

        public void sendGetProvince(ProvinceRequest request) throws TException {
            GetProvinceArgs args = new GetProvinceArgs();
            args.setRequest(request);
            sendBase("GetProvince", args);
        }

        public Response recvGetProvince() throws TException {
            GetProvinceResult result = new GetProvinceResult();
            receiveBase(result, "GetProvince");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "GetProvince failed: unknown result");
        }

        public Response getCity(CityRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendGetCity(request);
            return recvGetCity();
        }

        public void sendGetCity(CityRequest request) throws TException {
            GetCityArgs args = new GetCityArgs();
            args.setRequest(request);
            sendBase("GetCity", args);
        }

        public Response recvGetCity() throws TException {
            GetCityResult result = new GetCityResult();
            receiveBase(result, "GetCity");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "GetCity failed: unknown result");
        }

        public Response getDistrict(DistrictRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendGetDistrict(request);
            return recvGetDistrict();
        }

        public void sendGetDistrict(DistrictRequest request) throws TException {
            GetDistrictArgs args = new GetDistrictArgs();
            args.setRequest(request);
            sendBase("GetDistrict", args);
        }

        public Response recvGetDistrict() throws TException {
            GetDistrictResult result = new GetDistrictResult();
            receiveBase(result, "GetDistrict");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "GetDistrict failed: unknown result");
        }

        public Response getStreet(StreetRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendGetStreet(request);
            return recvGetStreet();
        }

        public void sendGetStreet(StreetRequest request) throws TException {
            GetStreetArgs args = new GetStreetArgs();
            args.setRequest(request);
            sendBase("GetStreet", args);
        }

        public Response recvGetStreet() throws TException {
            GetStreetResult result = new GetStreetResult();
            receiveBase(result, "GetStreet");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "GetStreet failed: unknown result");
        }

        public Response syncData(SyncRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendSyncData(request);
            return recvSyncData();
        }

        public void sendSyncData(SyncRequest request) throws TException {
            SyncDataArgs args = new SyncDataArgs();
            args.setRequest(request);
            sendBase("SyncData", args);
        }

        public Response recvSyncData() throws TException {
            SyncDataResult result = new SyncDataResult();
            receiveBase(result, "SyncData");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "SyncData failed: unknown result");
        }

        public Response getInfoByIds(IdRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendGetInfoByIds(request);
            return recvGetInfoByIds();
        }

        public void sendGetInfoByIds(IdRequest request) throws TException {
            GetInfoByIdsArgs args = new GetInfoByIdsArgs();
            args.setRequest(request);
            sendBase("GetInfoByIds", args);
        }

        public Response recvGetInfoByIds() throws TException {
            GetInfoByIdsResult result = new GetInfoByIdsResult();
            receiveBase(result, "GetInfoByIds");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "GetInfoByIds failed: unknown result");
        }

        public Response getInfoByNames(NameRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendgetInfoByNames(request);
            return recvgetInfoByNames();
        }

        public void sendgetInfoByNames(NameRequest request) throws TException {
            GetInfoByNamesArge args = new GetInfoByNamesArge();
            args.setRequest(request);
            sendBase("getInfoByNames", args);
        }

        public Response recvgetInfoByNames() throws TException {
            GetInfoByNamesResult result = new GetInfoByNamesResult();
            receiveBase(result, "getInfoByNames");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "getInfoByNames failed: unknown result");
        }

        public Response getSubset(SubsetRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendGetSubset(request);
            return recvGetSubset();
        }

        public void sendGetSubset(SubsetRequest request) throws TException {
            GetSubsetArgs args = new GetSubsetArgs();
            args.setRequest(request);
            sendBase("GetSubset", args);
        }

        public Response recvGetSubset() throws TException {
            GetSubsetResult result = new GetSubsetResult();
            receiveBase(result, "GetSubset");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "GetSubset failed: unknown result");
        }

        public Response getRegionTag(RegionTagRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendGetRegionTag(request);
            return recvGetRegionTag();
        }

        public void sendGetRegionTag(RegionTagRequest request) throws TException {
            GetRegionTagArgs args = new GetRegionTagArgs();
            args.setRequest(request);
            sendBase("GetRegionTag", args);
        }

        public Response recvGetRegionTag() throws TException {
            GetRegionTagResult result = new GetRegionTagResult();
            receiveBase(result, "GetRegionTag");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "GetRegionTag failed: unknown result");
        }

        public Response getRegionTagByIds(TagIdRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendGetRegionTagByIds(request);
            return recvGetRegionTagByIds();
        }

        public void sendGetRegionTagByIds(TagIdRequest request) throws TException {
            GetRegionTagByIdsArgs args = new GetRegionTagByIdsArgs();
            args.setRequest(request);
            sendBase("GetRegionTagByIds", args);
        }

        public Response recvGetRegionTagByIds() throws TException {
            GetRegionTagByIdsResult result = new GetRegionTagByIdsResult();
            receiveBase(result, "GetRegionTagByIds");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "GetRegionTagByIds failed: unknown result");
        }

        public Response getIndRegionTag(RegionTagRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendGetIndRegionTag(request);
            return recvGetIndRegionTag();
        }

        public void sendGetIndRegionTag(RegionTagRequest request) throws TException {
            GetIndRegionTagArgs args = new GetIndRegionTagArgs();
            args.setRequest(request);
            sendBase("GetIndRegionTag", args);
        }

        public Response recvGetIndRegionTag() throws TException {
            GetIndRegionTagResult result = new GetIndRegionTagResult();
            receiveBase(result, "GetIndRegionTag");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "GetIndRegionTag failed: unknown result");
        }

        public List<Integer> getCountryNum(CountryNumRequest request) throws TException {
            org.mi.thrift.protocol.TProtocol proto = super.getOutputProtocol();
            org.mi.thrift.transport.TTransport trans = proto.getTransport();
            try {
                org.mi.thrift.transport.TXmHeaderTransport xmHeader = (org.mi.thrift.transport.TXmHeaderTransport) trans;
                if (context != null) {
                    xmHeader.setAppId(context.getAppId());
                    xmHeader.setLogId(context.getLogId());
                    xmHeader.setRpcId(context.getRpcId());
                }
            } catch (Exception e) {
            }
            sendGetCountryNum(request);
            return recvGetCountryNum();
        }

        public void sendGetCountryNum(CountryNumRequest request) throws TException {
            GetCountryNumArgs args = new GetCountryNumArgs();
            args.setRequest(request);
            sendBase("GetCountryNum", args);
        }

        public List<Integer> recvGetCountryNum() throws TException {
            GetCountryNumResult result = new GetCountryNumResult();
            receiveBase(result, "GetCountryNum");
            if (result.isSetSuccess()) {
                return result.success;
            }
            throw new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.MISSING_RESULT, "GetCountryNum failed: unknown result");
        }

    }

    public static class AsyncClient extends org.mi.thrift.async.TAsyncClient implements AsyncIface {
        public static class Factory implements org.mi.thrift.async.TAsyncClientFactory<AsyncClient> {
            private org.mi.thrift.async.TAsyncClientManager clientManager;
            private org.mi.thrift.protocol.TProtocolFactory protocolFactory;

            public Factory(org.mi.thrift.async.TAsyncClientManager clientManager, org.mi.thrift.protocol.TProtocolFactory protocolFactory) {
                this.clientManager = clientManager;
                this.protocolFactory = protocolFactory;
            }

            public AsyncClient getAsyncClient(org.mi.thrift.transport.TNonblockingTransport transport) {
                return new AsyncClient(protocolFactory, clientManager, transport);
            }
        }

        public AsyncClient(org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.async.TAsyncClientManager clientManager, org.mi.thrift.transport.TNonblockingTransport transport) {
            super(protocolFactory, clientManager, transport);
        }

        public void getCountry(CountryRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            GetCountryCall methodCall = new GetCountryCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class GetCountryCall extends org.mi.thrift.async.TAsyncMethodCall {
            private CountryRequest request;

            public GetCountryCall(CountryRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("GetCountry", org.mi.thrift.protocol.TMessageType.CALL, 0));
                GetCountryArgs args = new GetCountryArgs();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public Response getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvGetCountry();
            }
        }

        public void getProvince(ProvinceRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            GetProvinceCall methodCall = new GetProvinceCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class GetProvinceCall extends org.mi.thrift.async.TAsyncMethodCall {
            private ProvinceRequest request;

            public GetProvinceCall(ProvinceRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("GetProvince", org.mi.thrift.protocol.TMessageType.CALL, 0));
                GetProvinceArgs args = new GetProvinceArgs();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public Response getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvGetProvince();
            }
        }

        public void getCity(CityRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            GetCityCall methodCall = new GetCityCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class GetCityCall extends org.mi.thrift.async.TAsyncMethodCall {
            private CityRequest request;

            public GetCityCall(CityRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("GetCity", org.mi.thrift.protocol.TMessageType.CALL, 0));
                GetCityArgs args = new GetCityArgs();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public Response getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvGetCity();
            }
        }

        public void getDistrict(DistrictRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            GetDistrictCall methodCall = new GetDistrictCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class GetDistrictCall extends org.mi.thrift.async.TAsyncMethodCall {
            private DistrictRequest request;

            public GetDistrictCall(DistrictRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("GetDistrict", org.mi.thrift.protocol.TMessageType.CALL, 0));
                GetDistrictArgs args = new GetDistrictArgs();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public Response getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvGetDistrict();
            }
        }

        public void getStreet(StreetRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            GetStreetCall methodCall = new GetStreetCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class GetStreetCall extends org.mi.thrift.async.TAsyncMethodCall {
            private StreetRequest request;

            public GetStreetCall(StreetRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("GetStreet", org.mi.thrift.protocol.TMessageType.CALL, 0));
                GetStreetArgs args = new GetStreetArgs();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public Response getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvGetStreet();
            }
        }

        public void syncData(SyncRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            SyncDataCall methodCall = new SyncDataCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class SyncDataCall extends org.mi.thrift.async.TAsyncMethodCall {
            private SyncRequest request;

            public SyncDataCall(SyncRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("SyncData", org.mi.thrift.protocol.TMessageType.CALL, 0));
                SyncDataArgs args = new SyncDataArgs();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public Response getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvSyncData();
            }
        }

        public void getInfoByIds(IdRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            GetInfoByIdsCall methodCall = new GetInfoByIdsCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class GetInfoByIdsCall extends org.mi.thrift.async.TAsyncMethodCall {
            private IdRequest request;

            public GetInfoByIdsCall(IdRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("GetInfoByIds", org.mi.thrift.protocol.TMessageType.CALL, 0));
                GetInfoByIdsArgs args = new GetInfoByIdsArgs();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public Response getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvGetInfoByIds();
            }
        }

        public void getInfoByNames(NameRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            getInfoByNamesCall methodCall = new getInfoByNamesCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class getInfoByNamesCall extends org.mi.thrift.async.TAsyncMethodCall {
            private NameRequest request;

            public getInfoByNamesCall(NameRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("getInfoByNames", org.mi.thrift.protocol.TMessageType.CALL, 0));
                GetInfoByNamesArge args = new GetInfoByNamesArge();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public Response getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvgetInfoByNames();
            }
        }

        public void getSubset(SubsetRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            GetSubsetCall methodCall = new GetSubsetCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class GetSubsetCall extends org.mi.thrift.async.TAsyncMethodCall {
            private SubsetRequest request;

            public GetSubsetCall(SubsetRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("GetSubset", org.mi.thrift.protocol.TMessageType.CALL, 0));
                GetSubsetArgs args = new GetSubsetArgs();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public Response getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvGetSubset();
            }
        }

        public void getRegionTag(RegionTagRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            GetRegionTagCall methodCall = new GetRegionTagCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class GetRegionTagCall extends org.mi.thrift.async.TAsyncMethodCall {
            private RegionTagRequest request;

            public GetRegionTagCall(RegionTagRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("GetRegionTag", org.mi.thrift.protocol.TMessageType.CALL, 0));
                GetRegionTagArgs args = new GetRegionTagArgs();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public Response getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvGetRegionTag();
            }
        }

        public void getRegionTagByIds(TagIdRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            GetRegionTagByIdsCall methodCall = new GetRegionTagByIdsCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class GetRegionTagByIdsCall extends org.mi.thrift.async.TAsyncMethodCall {
            private TagIdRequest request;

            public GetRegionTagByIdsCall(TagIdRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("GetRegionTagByIds", org.mi.thrift.protocol.TMessageType.CALL, 0));
                GetRegionTagByIdsArgs args = new GetRegionTagByIdsArgs();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public Response getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvGetRegionTagByIds();
            }
        }


        public void getIndRegionTag(RegionTagRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            GetIndRegionTagCall methodCall = new GetIndRegionTagCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class GetIndRegionTagCall extends org.mi.thrift.async.TAsyncMethodCall {
            private RegionTagRequest request;

            public GetIndRegionTagCall(RegionTagRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("GetIndRegionTag", org.mi.thrift.protocol.TMessageType.CALL, 0));
                GetIndRegionTagArgs args = new GetIndRegionTagArgs();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public Response getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvGetIndRegionTag();
            }
        }

        public void getCountryNum(CountryNumRequest request, AsyncMethodCallback resultHandler) throws TException {
            checkReady();
            GetCountryNumCall methodCall = new GetCountryNumCall(request, resultHandler, this, ___protocolFactory, ___transport);
            this.___currentMethod = methodCall;
            ___manager.call(methodCall);
        }

        public static class GetCountryNumCall extends org.mi.thrift.async.TAsyncMethodCall {
            private CountryNumRequest request;

            public GetCountryNumCall(CountryNumRequest request, AsyncMethodCallback resultHandler, org.mi.thrift.async.TAsyncClient client, org.mi.thrift.protocol.TProtocolFactory protocolFactory, org.mi.thrift.transport.TNonblockingTransport transport) throws TException {
                super(client, protocolFactory, transport, resultHandler, false);
                this.request = request;
            }

            public void write_args(org.mi.thrift.protocol.TProtocol prot) throws TException {
                prot.writeMessageBegin(new org.mi.thrift.protocol.TMessage("GetCountryNum", org.mi.thrift.protocol.TMessageType.CALL, 0));
                GetCountryNumArgs args = new GetCountryNumArgs();
                args.setRequest(request);
                args.write(prot);
                prot.writeMessageEnd();
            }

            public List<Integer> getResult() throws TException {
                if (getState() != State.RESPONSE_READ) {
                    throw new IllegalStateException("Method call not finished!");
                }
                org.mi.thrift.transport.TMemoryInputTransport memoryTransport = new org.mi.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
                org.mi.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
                return (new Client(prot)).recvGetCountryNum();
            }
        }

    }

    public static class Processor<I extends Iface> extends org.mi.thrift.TBaseProcessor<I> implements org.mi.thrift.TProcessor {
        private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());

        public Processor(I iface) {
            super(iface, getProcessMap(new HashMap<String, org.mi.thrift.ProcessFunction<I, ? extends org.mi.thrift.TBase>>()));
        }

        protected Processor(I iface, Map<String, org.mi.thrift.ProcessFunction<I, ? extends org.mi.thrift.TBase>> processMap) {
            super(iface, getProcessMap(processMap));
        }

        private static <I extends Iface> Map<String, org.mi.thrift.ProcessFunction<I, ? extends org.mi.thrift.TBase>> getProcessMap(Map<String, org.mi.thrift.ProcessFunction<I, ? extends org.mi.thrift.TBase>> processMap) {
            processMap.put("GetCountry", new GetCountry());
            processMap.put("GetProvince", new GetProvince());
            processMap.put("GetCity", new GetCity());
            processMap.put("GetDistrict", new GetDistrict());
            processMap.put("GetStreet", new GetStreet());
            processMap.put("SyncData", new SyncData());
            processMap.put("GetInfoByIds", new GetInfoByIds());
            processMap.put("GetInfoByNames", new GetInfoByNames());
            processMap.put("GetSubset", new GetSubset());
            processMap.put("GetRegionTag", new GetRegionTag());
            processMap.put("GetRegionTagByIds", new GetRegionTagByIds());
            processMap.put("GetIndRegionTag", new GetIndRegionTag());
            processMap.put("GetCountryNum", new GetCountryNum());
            return processMap;
        }

        /**
         * 　　* @description: TODO
         * 　　* @date 2021/8/9 下午1:20
         */
        public static class GetCountry<I extends Iface> extends org.mi.thrift.ProcessFunction<I, GetCountryArgs> {
            public GetCountry() {
                super("GetCountry");
            }

            public GetCountryArgs getEmptyArgsInstance() {
                return new GetCountryArgs();
            }

            protected boolean isOneway() {
                return false;
            }

            public GetCountryResult getResult(I iface, GetCountryArgs args) throws TException {
                GetCountryResult result = new GetCountryResult();
                result.success = iface.getCountry(args.request);
                return result;
            }
        }
/**
　　* @description: TODO 
　　* @date 2021/8/9 下午1:26 
　　*/
        public static class GetProvince<I extends Iface> extends org.mi.thrift.ProcessFunction<I, GetProvinceArgs> {
            public GetProvince() {
                super("GetProvince");
            }

            public GetProvinceArgs getEmptyArgsInstance() {
                return new GetProvinceArgs();
            }

            protected boolean isOneway() {
                return false;
            }

            public GetProvinceResult getResult(I iface, GetProvinceArgs args) throws TException {
                GetProvinceResult result = new GetProvinceResult();
                result.success = iface.getProvince(args.request);
                return result;
            }
        }

        /**
        　　* @description: TODO 
        　　* @date 2021/8/9 下午1:26 
        　　*/
        public static class GetCity<I extends Iface> extends org.mi.thrift.ProcessFunction<I, GetCityArgs> {
            public GetCity() {
                super("GetCity");
            }

            public GetCityArgs getEmptyArgsInstance() {
                return new GetCityArgs();
            }

            protected boolean isOneway() {
                return false;
            }

            public GetCityResult getResult(I iface, GetCityArgs args) throws TException {
                GetCityResult result = new GetCityResult();
                result.success = iface.getCity(args.request);
                return result;
            }
        }

        /**
        　　* @description: TODO 
        　　* @date 2021/8/9 下午1:27 
        　　*/
        public static class GetDistrict<I extends Iface> extends org.mi.thrift.ProcessFunction<I, GetDistrictArgs> {
            public GetDistrict() {
                super("GetDistrict");
            }

            public GetDistrictArgs getEmptyArgsInstance() {
                return new GetDistrictArgs();
            }

            protected boolean isOneway() {
                return false;
            }

            public GetDistrictResult getResult(I iface, GetDistrictArgs args) throws TException {
                GetDistrictResult result = new GetDistrictResult();
                result.success = iface.getDistrict(args.request);
                return result;
            }
        }

        /**
        　　* @description: TODO 
        　　* @date 2021/8/9 下午1:27 
        　　*/
        public static class GetStreet<I extends Iface> extends org.mi.thrift.ProcessFunction<I, GetStreetArgs> {
            public GetStreet() {
                super("GetStreet");
            }

            public GetStreetArgs getEmptyArgsInstance() {
                return new GetStreetArgs();
            }

            protected boolean isOneway() {
                return false;
            }

            public GetStreetResult getResult(I iface, GetStreetArgs args) throws TException {
                GetStreetResult result = new GetStreetResult();
                result.success = iface.getStreet(args.request);
                return result;
            }
        }

        /**
        　　* @description: TODO 
        　　* @date 2021/8/9 下午1:27 
        　　*/
        public static class SyncData<I extends Iface> extends org.mi.thrift.ProcessFunction<I, SyncDataArgs> {
            public SyncData() {
                super("SyncData");
            }

            public SyncDataArgs getEmptyArgsInstance() {
                return new SyncDataArgs();
            }

            protected boolean isOneway() {
                return false;
            }

            public SyncDataResult getResult(I iface, SyncDataArgs args) throws TException {
                SyncDataResult result = new SyncDataResult();
                result.success = iface.syncData(args.request);
                return result;
            }
        }

        public static class GetInfoByIds<I extends Iface> extends org.mi.thrift.ProcessFunction<I, GetInfoByIdsArgs> {
            public GetInfoByIds() {
                super("GetInfoByIds");
            }

            public GetInfoByIdsArgs getEmptyArgsInstance() {
                return new GetInfoByIdsArgs();
            }

            protected boolean isOneway() {
                return false;
            }

            public GetInfoByIdsResult getResult(I iface, GetInfoByIdsArgs args) throws TException {
                GetInfoByIdsResult result = new GetInfoByIdsResult();
                result.success = iface.getInfoByIds(args.request);
                return result;
            }
        }

        /**
        　　* @description: TODO 
        　　* @date 2021/8/9 下午1:27 
        　　*/
        public static class GetInfoByNames<I extends Iface> extends org.mi.thrift.ProcessFunction<I, GetInfoByNamesArge> {
            public GetInfoByNames() {
                super("GetInfoByNames");
            }

            public GetInfoByNamesArge getEmptyArgsInstance() {
                return new GetInfoByNamesArge();
            }

            protected boolean isOneway() {
                return false;
            }

            public GetInfoByNamesResult getResult(I iface, GetInfoByNamesArge args) throws TException {
                GetInfoByNamesResult result = new GetInfoByNamesResult();
                result.success = iface.getInfoByNames(args.request);
                return result;
            }
        }

        /**
        　　* @description: TODO 
        　　* @date 2021/8/9 下午1:27 
        　　*/
        public static class GetSubset<I extends Iface> extends org.mi.thrift.ProcessFunction<I, GetSubsetArgs> {
            public GetSubset() {
                super("GetSubset");
            }

            public GetSubsetArgs getEmptyArgsInstance() {
                return new GetSubsetArgs();
            }

            protected boolean isOneway() {
                return false;
            }

            public GetSubsetResult getResult(I iface, GetSubsetArgs args) throws TException {
                GetSubsetResult result = new GetSubsetResult();
                result.success = iface.getSubset(args.request);
                return result;
            }
        }

        /**
         　　* @description: TODO 
         　　* @date 2021/8/9 下午1:27 
         　　*/
        public static class GetRegionTag<I extends Iface> extends org.mi.thrift.ProcessFunction<I, GetRegionTagArgs> {
            public GetRegionTag() {
                super("GetRegionTag");
            }

            public GetRegionTagArgs getEmptyArgsInstance() {
                return new GetRegionTagArgs();
            }

            protected boolean isOneway() {
                return false;
            }

            public GetRegionTagResult getResult(I iface, GetRegionTagArgs args) throws TException {
                GetRegionTagResult result = new GetRegionTagResult();
                result.success = iface.getRegionTag(args.request);
                return result;
            }
        }
        /**
         　　* @description: TODO
         　　* @date 2021/8/9 下午1:21
         　　*/
        public static class GetRegionTagByIds<I extends Iface> extends org.mi.thrift.ProcessFunction<I, GetRegionTagByIdsArgs> {
            public GetRegionTagByIds() {
                super("GetRegionTagByIds");
            }

            public GetRegionTagByIdsArgs getEmptyArgsInstance() {
                return new GetRegionTagByIdsArgs();
            }

            protected boolean isOneway() {
                return false;
            }

            public GetRegionTagByIdsResult getResult(I iface, GetRegionTagByIdsArgs args) throws TException {
                GetRegionTagByIdsResult result = new GetRegionTagByIdsResult();
                result.success = iface.getRegionTagByIds(args.request);
                return result;
            }
        }

        /**
         　　* @description: TODO 
         　　* @date 2021/8/9 下午1:27 
         　　*/
        public static class GetIndRegionTag<I extends Iface> extends org.mi.thrift.ProcessFunction<I, GetIndRegionTagArgs> {
            public GetIndRegionTag() {
                super("GetIndRegionTag");
            }

            public GetIndRegionTagArgs getEmptyArgsInstance() {
                return new GetIndRegionTagArgs();
            }

            protected boolean isOneway() {
                return false;
            }

            public GetIndRegionTagResult getResult(I iface, GetIndRegionTagArgs args) throws TException {
                GetIndRegionTagResult result = new GetIndRegionTagResult();
                result.success = iface.getIndRegionTag(args.request);
                return result;
            }
        }

        /**
         　　* @description: TODO 
         　　* @date 2021/8/9 下午1:27 
         　　*/
        public static class GetCountryNum<I extends Iface> extends org.mi.thrift.ProcessFunction<I, GetCountryNumArgs> {
            public GetCountryNum() {
                super("GetCountryNum");
            }

            public GetCountryNumArgs getEmptyArgsInstance() {
                return new GetCountryNumArgs();
            }

            protected boolean isOneway() {
                return false;
            }

            public GetCountryNumResult getResult(I iface, GetCountryNumArgs args) throws TException {
                GetCountryNumResult result = new GetCountryNumResult();
                result.success = iface.getCountryNum(args.request);
                return result;
            }
        }

    }

    /**
     　　* @description: TODO 
     　　* @date 2021/8/9 下午1:27 
     　　*/
    public static class AsyncProcessor<I extends AsyncIface> extends org.mi.thrift.TBaseAsyncProcessor<I> {
        private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());

        public AsyncProcessor(I iface) {
            super(iface, getProcessMap(new HashMap<String, org.mi.thrift.AsyncProcessFunction<I, ? extends org.mi.thrift.TBase, ?>>()));
        }

        protected AsyncProcessor(I iface, Map<String, org.mi.thrift.AsyncProcessFunction<I, ? extends org.mi.thrift.TBase, ?>> processMap) {
            super(iface, getProcessMap(processMap));
        }

        /**
         　　* @description: TODO 
         　　* @date 2021/8/9 下午1:27 
         　　*/
        private static <I extends AsyncIface> Map<String, org.mi.thrift.AsyncProcessFunction<I, ? extends org.mi.thrift.TBase, ?>> getProcessMap(Map<String, org.mi.thrift.AsyncProcessFunction<I, ? extends org.mi.thrift.TBase, ?>> processMap) {
            processMap.put("GetCountry", new GetCountry());
            processMap.put("GetProvince", new GetProvince());
            processMap.put("GetCity", new GetCity());
            processMap.put("GetDistrict", new GetDistrict());
            processMap.put("GetStreet", new GetStreet());
            processMap.put("SyncData", new SyncData());
            processMap.put("GetInfoByIds", new GetInfoByIds());
            processMap.put("getInfoByNames", new getInfoByNames());
            processMap.put("GetSubset", new GetSubset());
            processMap.put("GetRegionTag", new GetRegionTag());
            processMap.put("GetRegionTagByIds", new GetRegionTagByIds());
            processMap.put("GetIndRegionTag", new GetIndRegionTag());
            processMap.put("GetCountryNum", new GetCountryNum());
            return processMap;
        }

        /**
         　　* @description: TODO 
         　　* @date 2021/8/9 下午1:27 
         　　*/
        public static class GetCountry<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, GetCountryArgs, Response> {
            public GetCountry() {
                super("GetCountry");
            }

            public GetCountryArgs getEmptyArgsInstance() {
                return new GetCountryArgs();
            }

            public AsyncMethodCallback<Response> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<Response>() {
                    public void onComplete(Response o) {
                        GetCountryResult result = new GetCountryResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        GetCountryResult result = new GetCountryResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, GetCountryArgs args, AsyncMethodCallback<Response> resultHandler) throws TException {
                iface.getCountry(args.request, resultHandler);
            }
        }

        /**
         　　* @description: TODO 
         　　* @date 2021/8/9 下午1:27 
         　　*/
        public static class GetProvince<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, GetProvinceArgs, Response> {
            public GetProvince() {
                super("GetProvince");
            }

            public GetProvinceArgs getEmptyArgsInstance() {
                return new GetProvinceArgs();
            }

            public AsyncMethodCallback<Response> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<Response>() {
                    public void onComplete(Response o) {
                        GetProvinceResult result = new GetProvinceResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        GetProvinceResult result = new GetProvinceResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, GetProvinceArgs args, AsyncMethodCallback<Response> resultHandler) throws TException {
                iface.getProvince(args.request, resultHandler);
            }
        }

        /**
         　　* @description: TODO 
         　　* @date 2021/8/9 下午1:27 
         　　*/
        public static class GetCity<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, GetCityArgs, Response> {
            public GetCity() {
                super("GetCity");
            }

            public GetCityArgs getEmptyArgsInstance() {
                return new GetCityArgs();
            }

            public AsyncMethodCallback<Response> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<Response>() {
                    public void onComplete(Response o) {
                        GetCityResult result = new GetCityResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        GetCityResult result = new GetCityResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, GetCityArgs args, AsyncMethodCallback<Response> resultHandler) throws TException {
                iface.getCity(args.request, resultHandler);
            }
        }
        /**
         　　* @description: TODO
         　　* @date 2021/8/9 下午1:21
         　　*/
        public static class GetDistrict<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, GetDistrictArgs, Response> {
            public GetDistrict() {
                super("GetDistrict");
            }

            public GetDistrictArgs getEmptyArgsInstance() {
                return new GetDistrictArgs();
            }

            public AsyncMethodCallback<Response> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<Response>() {
                    public void onComplete(Response o) {
                        GetDistrictResult result = new GetDistrictResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        GetDistrictResult result = new GetDistrictResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, GetDistrictArgs args, AsyncMethodCallback<Response> resultHandler) throws TException {
                iface.getDistrict(args.request, resultHandler);
            }
        }

        /**
         　　* @description: TODO
         　　* @date 2021/8/9 下午1:21
         　　*/
        public static class GetStreet<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, GetStreetArgs, Response> {
            public GetStreet() {
                super("GetStreet");
            }

            public GetStreetArgs getEmptyArgsInstance() {
                return new GetStreetArgs();
            }

            public AsyncMethodCallback<Response> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<Response>() {
                    public void onComplete(Response o) {
                        GetStreetResult result = new GetStreetResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        GetStreetResult result = new GetStreetResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, GetStreetArgs args, AsyncMethodCallback<Response> resultHandler) throws TException {
                iface.getStreet(args.request, resultHandler);
            }
        }

        /**
         　　* @description: TODO
         　　* @date 2021/8/9 下午1:21
         　　*/
        public static class SyncData<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, SyncDataArgs, Response> {
            public SyncData() {
                super("SyncData");
            }

            public SyncDataArgs getEmptyArgsInstance() {
                return new SyncDataArgs();
            }

            public AsyncMethodCallback<Response> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<Response>() {
                    public void onComplete(Response o) {
                        SyncDataResult result = new SyncDataResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        SyncDataResult result = new SyncDataResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, SyncDataArgs args, AsyncMethodCallback<Response> resultHandler) throws TException {
                iface.syncData(args.request, resultHandler);
            }
        }

        /**
         　　* @description: TODO
         　　* @date 2021/8/9 下午1:21
         　　*/
        public static class GetInfoByIds<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, GetInfoByIdsArgs, Response> {
            public GetInfoByIds() {
                super("GetInfoByIds");
            }

            public GetInfoByIdsArgs getEmptyArgsInstance() {
                return new GetInfoByIdsArgs();
            }

            public AsyncMethodCallback<Response> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<Response>() {
                    public void onComplete(Response o) {
                        GetInfoByIdsResult result = new GetInfoByIdsResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        GetInfoByIdsResult result = new GetInfoByIdsResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, GetInfoByIdsArgs args, AsyncMethodCallback<Response> resultHandler) throws TException {
                iface.getInfoByIds(args.request, resultHandler);
            }
        }

        /**
         　　* @description: TODO
         　　* @date 2021/8/9 下午1:21
         　　*/
        public static class getInfoByNames<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, GetInfoByNamesArge, Response> {
            public getInfoByNames() {
                super("GetInfoByNames");
            }

            public GetInfoByNamesArge getEmptyArgsInstance() {
                return new GetInfoByNamesArge();
            }

            public AsyncMethodCallback<Response> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<Response>() {
                    public void onComplete(Response o) {
                        GetInfoByNamesResult result = new GetInfoByNamesResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        GetInfoByNamesResult result = new GetInfoByNamesResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, GetInfoByNamesArge args, AsyncMethodCallback<Response> resultHandler) throws TException {
                iface.getInfoByNames(args.request, resultHandler);
            }
        }

        /**
         　　* @description: TODO
         　　* @date 2021/8/9 下午1:21
         　　*/
        public static class GetSubset<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, GetSubsetArgs, Response> {
            public GetSubset() {
                super("GetSubset");
            }

            public GetSubsetArgs getEmptyArgsInstance() {
                return new GetSubsetArgs();
            }

            public AsyncMethodCallback<Response> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<Response>() {
                    public void onComplete(Response o) {
                        GetSubsetResult result = new GetSubsetResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        GetSubsetResult result = new GetSubsetResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, GetSubsetArgs args, AsyncMethodCallback<Response> resultHandler) throws TException {
                iface.getSubset(args.request, resultHandler);
            }
        }

        /**
         　　* @description: TODO
         　　* @date 2021/8/9 下午1:21
         　　*/
        public static class GetRegionTag<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, GetRegionTagArgs, Response> {
            public GetRegionTag() {
                super("GetRegionTag");
            }

            public GetRegionTagArgs getEmptyArgsInstance() {
                return new GetRegionTagArgs();
            }

            public AsyncMethodCallback<Response> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<Response>() {
                    public void onComplete(Response o) {
                        GetRegionTagResult result = new GetRegionTagResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        GetRegionTagResult result = new GetRegionTagResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, GetRegionTagArgs args, AsyncMethodCallback<Response> resultHandler) throws TException {
                iface.getRegionTag(args.request, resultHandler);
            }
        }

        /**
         　　* @description: TODO
         　　* @date 2021/8/9 下午1:21
         　　*/
        public static class GetRegionTagByIds<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, GetRegionTagByIdsArgs, Response> {
            public GetRegionTagByIds() {
                super("GetRegionTagByIds");
            }

            public GetRegionTagByIdsArgs getEmptyArgsInstance() {
                return new GetRegionTagByIdsArgs();
            }

            public AsyncMethodCallback<Response> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<Response>() {
                    public void onComplete(Response o) {
                        GetRegionTagByIdsResult result = new GetRegionTagByIdsResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        GetRegionTagByIdsResult result = new GetRegionTagByIdsResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, GetRegionTagByIdsArgs args, AsyncMethodCallback<Response> resultHandler) throws TException {
                iface.getRegionTagByIds(args.request, resultHandler);
            }
        }

        /**
         　　* @description: TODO
         　　* @date 2021/8/9 下午1:21
         　　*/
        public static class GetIndRegionTag<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, GetIndRegionTagArgs, Response> {
            public GetIndRegionTag() {
                super("GetIndRegionTag");
            }

            public GetIndRegionTagArgs getEmptyArgsInstance() {
                return new GetIndRegionTagArgs();
            }

            public AsyncMethodCallback<Response> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<Response>() {
                    public void onComplete(Response o) {
                        GetIndRegionTagResult result = new GetIndRegionTagResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        GetIndRegionTagResult result = new GetIndRegionTagResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, GetIndRegionTagArgs args, AsyncMethodCallback<Response> resultHandler) throws TException {
                iface.getIndRegionTag(args.request, resultHandler);
            }
        }

        /**
         　　* @description: TODO
         　　* @date 2021/8/9 下午1:21
         　　*/
        public static class GetCountryNum<I extends AsyncIface> extends org.mi.thrift.AsyncProcessFunction<I, GetCountryNumArgs, List<Integer>> {
            public GetCountryNum() {
                super("GetCountryNum");
            }

            public GetCountryNumArgs getEmptyArgsInstance() {
                return new GetCountryNumArgs();
            }

            public AsyncMethodCallback<List<Integer>> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
                final org.mi.thrift.AsyncProcessFunction fcall = this;
                return new AsyncMethodCallback<List<Integer>>() {
                    public void onComplete(List<Integer> o) {
                        GetCountryNumResult result = new GetCountryNumResult();
                        result.success = o;
                        try {
                            fcall.sendResponse(fb, result, org.mi.thrift.protocol.TMessageType.REPLY, seqid);
                            return;
                        } catch (Exception e) {
                            LOGGER.error("Exception writing to internal frame buffer", e);
                        }
                        fb.close();
                    }

                    public void onError(Exception e) {
                        byte msgType = org.mi.thrift.protocol.TMessageType.REPLY;
                        org.mi.thrift.TBase msg;
                        GetCountryNumResult result = new GetCountryNumResult();
                        {
                            msgType = org.mi.thrift.protocol.TMessageType.EXCEPTION;
                            msg = (org.mi.thrift.TBase) new org.mi.thrift.TApplicationException(org.mi.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
                        }
                        try {
                            fcall.sendResponse(fb, msg, msgType, seqid);
                            return;
                        } catch (Exception ex) {
                            LOGGER.error("Exception writing to internal frame buffer", ex);
                        }
                        fb.close();
                    }
                };
            }

            protected boolean isOneway() {
                return false;
            }

            public void start(I iface, GetCountryNumArgs args, AsyncMethodCallback<List<Integer>> resultHandler) throws TException {
                iface.getCountryNum(args.request, resultHandler);
            }
        }

    }

    /**
     　　* @description: TODO
     　　* @date 2021/8/9 下午1:21
     　　*/
    public static class GetCountryArgs implements org.mi.thrift.TBase<GetCountryArgs, GetCountryArgs.Fields>, java.io.Serializable, Cloneable, Comparable<GetCountryArgs> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetCountryArgs");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetCountryArgsStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetCountryArgsTupleSchemeFactory());
        }

        public CountryRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, CountryRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetCountryArgs.class, METADATAMAP);
        }

        public GetCountryArgs() {
        }

        public GetCountryArgs(
                CountryRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetCountryArgs(GetCountryArgs other) {
            if (other.isSetRequest()) {
                this.request = new CountryRequest(other.request);
            }
        }

        public GetCountryArgs deepCopy() {
            return new GetCountryArgs(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public CountryRequest getRequest() {
            return this.request;
        }

        public GetCountryArgs setRequest(CountryRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((CountryRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetCountryArgs)
                return this.equals((GetCountryArgs) that);
            return false;
        }

        public boolean equals(GetCountryArgs that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetCountryArgs other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetCountryArgs(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetCountryArgsStandardSchemeFactory implements SchemeFactory {
            public GetCountryArgsStandardScheme getScheme() {
                return new GetCountryArgsStandardScheme();
            }
        }

        private static class GetCountryArgsStandardScheme extends StandardScheme<GetCountryArgs> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetCountryArgs struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new CountryRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetCountryArgs struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetCountryArgsTupleSchemeFactory implements SchemeFactory {
            public GetCountryArgsTupleScheme getScheme() {
                return new GetCountryArgsTupleScheme();
            }
        }

        private static class GetCountryArgsTupleScheme extends TupleScheme<GetCountryArgs> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetCountryArgs struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetCountryArgs struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new CountryRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class GetCountryResult implements org.mi.thrift.TBase<GetCountryResult, GetCountryResult.Fields>, java.io.Serializable, Cloneable, Comparable<GetCountryResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetCountryResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetCountryResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetCountryResultTupleSchemeFactory());
        }

        public Response success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private  String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Response.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetCountryResult.class, METADATAMAP);
        }

        public GetCountryResult() {
        }

        public GetCountryResult(
                Response success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetCountryResult(GetCountryResult other) {
            if (other.isSetSuccess()) {
                this.success = new Response(other.success);
            }
        }

        public GetCountryResult deepCopy() {
            return new GetCountryResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public Response getSuccess() {
            return this.success;
        }

        public GetCountryResult setSuccess(Response success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((Response) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetCountryResult)
                return this.equals((GetCountryResult) that);
            return false;
        }

        public boolean equals(GetCountryResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentsuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentsuccess) {
                if (!(thisPresentSuccess && thatPresentsuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetCountryResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetCountryResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (success != null) {
                success.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetCountryResultStandardSchemeFactory implements SchemeFactory {
            public GetCountryResultStandardScheme getScheme() {
                return new GetCountryResultStandardScheme();
            }
        }

        private static class GetCountryResultStandardScheme extends StandardScheme<GetCountryResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetCountryResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.success = new Response();
                                struct.success.read(iprot);
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetCountryResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    struct.success.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetCountryResultTupleSchemeFactory implements SchemeFactory {
            public GetCountryResultTupleScheme getScheme() {
                return new GetCountryResultTupleScheme();
            }
        }

        private static class GetCountryResultTupleScheme extends TupleScheme<GetCountryResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetCountryResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    struct.success.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetCountryResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.success = new Response();
                    struct.success.read(iprot);
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

    public static class GetProvinceArgs implements org.mi.thrift.TBase<GetProvinceArgs, GetProvinceArgs.Fields>, java.io.Serializable, Cloneable, Comparable<GetProvinceArgs> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetProvinceArgs");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetProvinceArgsStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetProvinceArgsTupleSchemeFactory());
        }

        public ProvinceRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, ProvinceRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetProvinceArgs.class, METADATAMAP);
        }

        public GetProvinceArgs() {
        }

        public GetProvinceArgs(
                ProvinceRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetProvinceArgs(GetProvinceArgs other) {
            if (other.isSetRequest()) {
                this.request = new ProvinceRequest(other.request);
            }
        }

        public GetProvinceArgs deepCopy() {
            return new GetProvinceArgs(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public ProvinceRequest getRequest() {
            return this.request;
        }

        public GetProvinceArgs setRequest(ProvinceRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((ProvinceRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetProvinceArgs)
                return this.equals((GetProvinceArgs) that);
            return false;
        }

        public boolean equals(GetProvinceArgs that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetProvinceArgs other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetProvinceArgs(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetProvinceArgsStandardSchemeFactory implements SchemeFactory {
            public GetProvinceArgsStandardScheme getScheme() {
                return new GetProvinceArgsStandardScheme();
            }
        }

        private static class GetProvinceArgsStandardScheme extends StandardScheme<GetProvinceArgs> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetProvinceArgs struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new ProvinceRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetProvinceArgs struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetProvinceArgsTupleSchemeFactory implements SchemeFactory {
            public GetProvinceArgsTupleScheme getScheme() {
                return new GetProvinceArgsTupleScheme();
            }
        }

        private static class GetProvinceArgsTupleScheme extends TupleScheme<GetProvinceArgs> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetProvinceArgs struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetProvinceArgs struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new ProvinceRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class GetProvinceResult implements org.mi.thrift.TBase<GetProvinceResult, GetProvinceResult.Fields>, java.io.Serializable, Cloneable, Comparable<GetProvinceResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetProvinceResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetProvinceResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetProvinceResultTupleSchemeFactory());
        }

        public Response success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Response.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetProvinceResult.class, METADATAMAP);
        }

        public GetProvinceResult() {
        }

        public GetProvinceResult(
                Response success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetProvinceResult(GetProvinceResult other) {
            if (other.isSetSuccess()) {
                this.success = new Response(other.success);
            }
        }

        public GetProvinceResult deepCopy() {
            return new GetProvinceResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public Response getSuccess() {
            return this.success;
        }

        public GetProvinceResult setSuccess(Response success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((Response) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetProvinceResult)
                return this.equals((GetProvinceResult) that);
            return false;
        }

        public boolean equals(GetProvinceResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentSuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentSuccess) {
                if (!(thisPresentSuccess && thatPresentSuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetProvinceResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetProvinceResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (success != null) {
                success.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetProvinceResultStandardSchemeFactory implements SchemeFactory {
            public GetProvinceResultStandardScheme getScheme() {
                return new GetProvinceResultStandardScheme();
            }
        }

        private static class GetProvinceResultStandardScheme extends StandardScheme<GetProvinceResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetProvinceResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.success = new Response();
                                struct.success.read(iprot);
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetProvinceResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    struct.success.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetProvinceResultTupleSchemeFactory implements SchemeFactory {
            public GetProvinceResultTupleScheme getScheme() {
                return new GetProvinceResultTupleScheme();
            }
        }

        private static class GetProvinceResultTupleScheme extends TupleScheme<GetProvinceResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetProvinceResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    struct.success.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetProvinceResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.success = new Response();
                    struct.success.read(iprot);
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

    public static class GetCityArgs implements org.mi.thrift.TBase<GetCityArgs, GetCityArgs.Fields>, java.io.Serializable, Cloneable, Comparable<GetCityArgs> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetCityArgs");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetCityArgsStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetCityArgsTupleSchemeFactory());
        }

        public CityRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private  String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, CityRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetCityArgs.class, METADATAMAP);
        }

        public GetCityArgs() {
        }

        public GetCityArgs(
                CityRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetCityArgs(GetCityArgs other) {
            if (other.isSetRequest()) {
                this.request = new CityRequest(other.request);
            }
        }

        public GetCityArgs deepCopy() {
            return new GetCityArgs(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public CityRequest getRequest() {
            return this.request;
        }

        public GetCityArgs setRequest(CityRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((CityRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetCityArgs)
                return this.equals((GetCityArgs) that);
            return false;
        }

        public boolean equals(GetCityArgs that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetCityArgs other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetCityArgs(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetCityArgsStandardSchemeFactory implements SchemeFactory {
            public GetCityArgsStandardScheme getScheme() {
                return new GetCityArgsStandardScheme();
            }
        }

        private static class GetCityArgsStandardScheme extends StandardScheme<GetCityArgs> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetCityArgs struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new CityRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetCityArgs struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetCityArgsTupleSchemeFactory implements SchemeFactory {
            public GetCityArgsTupleScheme getScheme() {
                return new GetCityArgsTupleScheme();
            }
        }

        private static class GetCityArgsTupleScheme extends TupleScheme<GetCityArgs> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetCityArgs struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetCityArgs struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new CityRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class GetCityResult implements org.mi.thrift.TBase<GetCityResult, GetCityResult.Fields>, java.io.Serializable, Cloneable, Comparable<GetCityResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetCityResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetCityResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetCityResultTupleSchemeFactory());
        }

        public Response success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private  String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Response.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetCityResult.class, METADATAMAP);
        }

        public GetCityResult() {
        }

        public GetCityResult(
                Response success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetCityResult(GetCityResult other) {
            if (other.isSetSuccess()) {
                this.success = new Response(other.success);
            }
        }

        public GetCityResult deepCopy() {
            return new GetCityResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public Response getSuccess() {
            return this.success;
        }

        public GetCityResult setSuccess(Response success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((Response) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetCityResult)
                return this.equals((GetCityResult) that);
            return false;
        }

        public boolean equals(GetCityResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentSuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentSuccess) {
                if (!(thisPresentSuccess && thatPresentSuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetCityResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetCityResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (success != null) {
                success.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetCityResultStandardSchemeFactory implements SchemeFactory {
            public GetCityResultStandardScheme getScheme() {
                return new GetCityResultStandardScheme();
            }
        }

        private static class GetCityResultStandardScheme extends StandardScheme<GetCityResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetCityResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.success = new Response();
                                struct.success.read(iprot);
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetCityResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    struct.success.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetCityResultTupleSchemeFactory implements SchemeFactory {
            public GetCityResultTupleScheme getScheme() {
                return new GetCityResultTupleScheme();
            }
        }

        private static class GetCityResultTupleScheme extends TupleScheme<GetCityResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetCityResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    struct.success.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetCityResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.success = new Response();
                    struct.success.read(iprot);
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

    public static class GetDistrictArgs implements org.mi.thrift.TBase<GetDistrictArgs, GetDistrictArgs.Fields>, java.io.Serializable, Cloneable, Comparable<GetDistrictArgs> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetDistrictArgs");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetDistrictArgsStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetDistrictArgsTupleSchemeFactory());
        }

        public DistrictRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, DistrictRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetDistrictArgs.class, METADATAMAP);
        }

        public GetDistrictArgs() {
        }

        public GetDistrictArgs(
                DistrictRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetDistrictArgs(GetDistrictArgs other) {
            if (other.isSetRequest()) {
                this.request = new DistrictRequest(other.request);
            }
        }

        public GetDistrictArgs deepCopy() {
            return new GetDistrictArgs(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public DistrictRequest getRequest() {
            return this.request;
        }

        public GetDistrictArgs setRequest(DistrictRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((DistrictRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetDistrictArgs)
                return this.equals((GetDistrictArgs) that);
            return false;
        }

        public boolean equals(GetDistrictArgs that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetDistrictArgs other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetDistrictArgs(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetDistrictArgsStandardSchemeFactory implements SchemeFactory {
            public GetDistrictArgsStandardScheme getScheme() {
                return new GetDistrictArgsStandardScheme();
            }
        }

        private static class GetDistrictArgsStandardScheme extends StandardScheme<GetDistrictArgs> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetDistrictArgs struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new DistrictRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetDistrictArgs struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetDistrictArgsTupleSchemeFactory implements SchemeFactory {
            public GetDistrictArgsTupleScheme getScheme() {
                return new GetDistrictArgsTupleScheme();
            }
        }

        private static class GetDistrictArgsTupleScheme extends TupleScheme<GetDistrictArgs> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetDistrictArgs struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetDistrictArgs struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new DistrictRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class GetDistrictResult implements org.mi.thrift.TBase<GetDistrictResult, GetDistrictResult.Fields>, java.io.Serializable, Cloneable, Comparable<GetDistrictResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetDistrictResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetDistrictResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetDistrictResultTupleSchemeFactory());
        }

        public Response success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Response.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetDistrictResult.class, METADATAMAP);
        }

        public GetDistrictResult() {
        }

        public GetDistrictResult(
                Response success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetDistrictResult(GetDistrictResult other) {
            if (other.isSetSuccess()) {
                this.success = new Response(other.success);
            }
        }

        public GetDistrictResult deepCopy() {
            return new GetDistrictResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public Response getSuccess() {
            return this.success;
        }

        public GetDistrictResult setSuccess(Response success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((Response) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetDistrictResult)
                return this.equals((GetDistrictResult) that);
            return false;
        }

        public boolean equals(GetDistrictResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentSuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentSuccess) {
                if (!(thisPresentSuccess && thatPresentSuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetDistrictResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetDistrictResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (success != null) {
                success.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetDistrictResultStandardSchemeFactory implements SchemeFactory {
            public GetDistrictResultStandardScheme getScheme() {
                return new GetDistrictResultStandardScheme();
            }
        }

        private static class GetDistrictResultStandardScheme extends StandardScheme<GetDistrictResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetDistrictResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.success = new Response();
                                struct.success.read(iprot);
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetDistrictResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    struct.success.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetDistrictResultTupleSchemeFactory implements SchemeFactory {
            public GetDistrictResultTupleScheme getScheme() {
                return new GetDistrictResultTupleScheme();
            }
        }

        private static class GetDistrictResultTupleScheme extends TupleScheme<GetDistrictResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetDistrictResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    struct.success.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetDistrictResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.success = new Response();
                    struct.success.read(iprot);
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

    public static class GetStreetArgs implements org.mi.thrift.TBase<GetStreetArgs, GetStreetArgs.Fields>, java.io.Serializable, Cloneable, Comparable<GetStreetArgs> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetStreetArgs");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetStreetArgsStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetStreetArgsTupleSchemeFactory());
        }

        public StreetRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, StreetRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetStreetArgs.class, METADATAMAP);
        }

        public GetStreetArgs() {
        }

        public GetStreetArgs(
                StreetRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetStreetArgs(GetStreetArgs other) {
            if (other.isSetRequest()) {
                this.request = new StreetRequest(other.request);
            }
        }

        public GetStreetArgs deepCopy() {
            return new GetStreetArgs(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public StreetRequest getRequest() {
            return this.request;
        }

        public GetStreetArgs setRequest(StreetRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((StreetRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetStreetArgs)
                return this.equals((GetStreetArgs) that);
            return false;
        }

        public boolean equals(GetStreetArgs that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetStreetArgs other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetStreetArgs(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetStreetArgsStandardSchemeFactory implements SchemeFactory {
            public GetStreetArgsStandardScheme getScheme() {
                return new GetStreetArgsStandardScheme();
            }
        }

        private static class GetStreetArgsStandardScheme extends StandardScheme<GetStreetArgs> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetStreetArgs struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new StreetRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetStreetArgs struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetStreetArgsTupleSchemeFactory implements SchemeFactory {
            public GetStreetArgsTupleScheme getScheme() {
                return new GetStreetArgsTupleScheme();
            }
        }

        private static class GetStreetArgsTupleScheme extends TupleScheme<GetStreetArgs> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetStreetArgs struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetStreetArgs struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new StreetRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class GetStreetResult implements org.mi.thrift.TBase<GetStreetResult, GetStreetResult.Fields>, java.io.Serializable, Cloneable, Comparable<GetStreetResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetStreetResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetStreetResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetStreetResultTupleSchemeFactory());
        }

        public Response success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Response.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetStreetResult.class, METADATAMAP);
        }

        public GetStreetResult() {
        }

        public GetStreetResult(
                Response success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetStreetResult(GetStreetResult other) {
            if (other.isSetSuccess()) {
                this.success = new Response(other.success);
            }
        }

        public GetStreetResult deepCopy() {
            return new GetStreetResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public Response getSuccess() {
            return this.success;
        }

        public GetStreetResult setSuccess(Response success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((Response) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetStreetResult)
                return this.equals((GetStreetResult) that);
            return false;
        }

        public boolean equals(GetStreetResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentSuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentSuccess) {
                if (!(thisPresentSuccess && thatPresentSuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetStreetResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetStreetResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (success != null) {
                success.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetStreetResultStandardSchemeFactory implements SchemeFactory {
            public GetStreetResultStandardScheme getScheme() {
                return new GetStreetResultStandardScheme();
            }
        }

        private static class GetStreetResultStandardScheme extends StandardScheme<GetStreetResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetStreetResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.success = new Response();
                                struct.success.read(iprot);
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetStreetResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    struct.success.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetStreetResultTupleSchemeFactory implements SchemeFactory {
            public GetStreetResultTupleScheme getScheme() {
                return new GetStreetResultTupleScheme();
            }
        }

        private static class GetStreetResultTupleScheme extends TupleScheme<GetStreetResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetStreetResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    struct.success.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetStreetResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.success = new Response();
                    struct.success.read(iprot);
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

    public static class SyncDataArgs implements org.mi.thrift.TBase<SyncDataArgs, SyncDataArgs.Fields>, java.io.Serializable, Cloneable, Comparable<SyncDataArgs> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("SyncDataArgs");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new SyncDataArgsStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new SyncDataArgsTupleSchemeFactory());
        }

        public SyncRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private  String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, SyncRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SyncDataArgs.class, METADATAMAP);
        }

        public SyncDataArgs() {
        }

        public SyncDataArgs(
                SyncRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public SyncDataArgs(SyncDataArgs other) {
            if (other.isSetRequest()) {
                this.request = new SyncRequest(other.request);
            }
        }

        public SyncDataArgs deepCopy() {
            return new SyncDataArgs(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public SyncRequest getRequest() {
            return this.request;
        }

        public SyncDataArgs setRequest(SyncRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((SyncRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof SyncDataArgs)
                return this.equals((SyncDataArgs) that);
            return false;
        }

        public boolean equals(SyncDataArgs that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(SyncDataArgs other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("SyncDataArgs(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class SyncDataArgsStandardSchemeFactory implements SchemeFactory {
            public SyncDataArgsStandardScheme getScheme() {
                return new SyncDataArgsStandardScheme();
            }
        }

        private static class SyncDataArgsStandardScheme extends StandardScheme<SyncDataArgs> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, SyncDataArgs struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new SyncRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, SyncDataArgs struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class SyncDataArgsTupleSchemeFactory implements SchemeFactory {
            public SyncDataArgsTupleScheme getScheme() {
                return new SyncDataArgsTupleScheme();
            }
        }

        private static class SyncDataArgsTupleScheme extends TupleScheme<SyncDataArgs> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, SyncDataArgs struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, SyncDataArgs struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new SyncRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class SyncDataResult implements org.mi.thrift.TBase<SyncDataResult, SyncDataResult.Fields>, java.io.Serializable, Cloneable, Comparable<SyncDataResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("SyncDataResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new SyncDataResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new SyncDataResultTupleSchemeFactory());
        }

        public Response success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Response.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SyncDataResult.class, METADATAMAP);
        }

        public SyncDataResult() {
        }

        public SyncDataResult(
                Response success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public SyncDataResult(SyncDataResult other) {
            if (other.isSetSuccess()) {
                this.success = new Response(other.success);
            }
        }

        public SyncDataResult deepCopy() {
            return new SyncDataResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public Response getSuccess() {
            return this.success;
        }

        public SyncDataResult setSuccess(Response success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((Response) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof SyncDataResult)
                return this.equals((SyncDataResult) that);
            return false;
        }

        public boolean equals(SyncDataResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentSuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentSuccess) {
                if (!(thisPresentSuccess && thatPresentSuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(SyncDataResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("SyncDataResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (success != null) {
                success.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class SyncDataResultStandardSchemeFactory implements SchemeFactory {
            public SyncDataResultStandardScheme getScheme() {
                return new SyncDataResultStandardScheme();
            }
        }

        private static class SyncDataResultStandardScheme extends StandardScheme<SyncDataResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, SyncDataResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.success = new Response();
                                struct.success.read(iprot);
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, SyncDataResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    struct.success.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class SyncDataResultTupleSchemeFactory implements SchemeFactory {
            public SyncDataResultTupleScheme getScheme() {
                return new SyncDataResultTupleScheme();
            }
        }

        private static class SyncDataResultTupleScheme extends TupleScheme<SyncDataResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, SyncDataResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    struct.success.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, SyncDataResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.success = new Response();
                    struct.success.read(iprot);
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

    public static class GetInfoByIdsArgs implements org.mi.thrift.TBase<GetInfoByIdsArgs, GetInfoByIdsArgs.Fields>, java.io.Serializable, Cloneable, Comparable<GetInfoByIdsArgs> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetInfoByIdsArgs");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetInfoByIdsArgsStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetInfoByIdsArgsTupleSchemeFactory());
        }

        public IdRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, IdRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetInfoByIdsArgs.class, METADATAMAP);
        }

        public GetInfoByIdsArgs() {
        }

        public GetInfoByIdsArgs(
                IdRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetInfoByIdsArgs(GetInfoByIdsArgs other) {
            if (other.isSetRequest()) {
                this.request = new IdRequest(other.request);
            }
        }

        public GetInfoByIdsArgs deepCopy() {
            return new GetInfoByIdsArgs(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public IdRequest getRequest() {
            return this.request;
        }

        public GetInfoByIdsArgs setRequest(IdRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((IdRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetInfoByIdsArgs)
                return this.equals((GetInfoByIdsArgs) that);
            return false;
        }

        public boolean equals(GetInfoByIdsArgs that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetInfoByIdsArgs other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetInfoByIdsArgs(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetInfoByIdsArgsStandardSchemeFactory implements SchemeFactory {
            public GetInfoByIdsArgsStandardScheme getScheme() {
                return new GetInfoByIdsArgsStandardScheme();
            }
        }

        private static class GetInfoByIdsArgsStandardScheme extends StandardScheme<GetInfoByIdsArgs> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetInfoByIdsArgs struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new IdRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetInfoByIdsArgs struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetInfoByIdsArgsTupleSchemeFactory implements SchemeFactory {
            public GetInfoByIdsArgsTupleScheme getScheme() {
                return new GetInfoByIdsArgsTupleScheme();
            }
        }

        private static class GetInfoByIdsArgsTupleScheme extends TupleScheme<GetInfoByIdsArgs> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetInfoByIdsArgs struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetInfoByIdsArgs struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new IdRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class GetInfoByIdsResult implements org.mi.thrift.TBase<GetInfoByIdsResult, GetInfoByIdsResult.Fields>, java.io.Serializable, Cloneable, Comparable<GetInfoByIdsResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetInfoByIdsResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetInfoByIdsResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetInfoByIdsResultTupleSchemeFactory());
        }

        public Response success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Response.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetInfoByIdsResult.class, METADATAMAP);
        }

        public GetInfoByIdsResult() {
        }

        public GetInfoByIdsResult(
                Response success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetInfoByIdsResult(GetInfoByIdsResult other) {
            if (other.isSetSuccess()) {
                this.success = new Response(other.success);
            }
        }

        public GetInfoByIdsResult deepCopy() {
            return new GetInfoByIdsResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public Response getSuccess() {
            return this.success;
        }

        public GetInfoByIdsResult setSuccess(Response success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((Response) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetInfoByIdsResult)
                return this.equals((GetInfoByIdsResult) that);
            return false;
        }

        public boolean equals(GetInfoByIdsResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentSuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentSuccess) {
                if (!(thisPresentSuccess && thatPresentSuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetInfoByIdsResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetInfoByIdsResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (success != null) {
                success.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetInfoByIdsResultStandardSchemeFactory implements SchemeFactory {
            public GetInfoByIdsResultStandardScheme getScheme() {
                return new GetInfoByIdsResultStandardScheme();
            }
        }

        private static class GetInfoByIdsResultStandardScheme extends StandardScheme<GetInfoByIdsResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetInfoByIdsResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.success = new Response();
                                struct.success.read(iprot);
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetInfoByIdsResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    struct.success.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetInfoByIdsResultTupleSchemeFactory implements SchemeFactory {
            public GetInfoByIdsResultTupleScheme getScheme() {
                return new GetInfoByIdsResultTupleScheme();
            }
        }

        private static class GetInfoByIdsResultTupleScheme extends TupleScheme<GetInfoByIdsResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetInfoByIdsResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    struct.success.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetInfoByIdsResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.success = new Response();
                    struct.success.read(iprot);
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

    public static class GetInfoByNamesArge implements org.mi.thrift.TBase<GetInfoByNamesArge, GetInfoByNamesArge.Fields>, java.io.Serializable, Cloneable, Comparable<GetInfoByNamesArge> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetInfoByNamesArge");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetInfoByNamesArgeStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetInfoByNamesArgeTupleSchemeFactory());
        }

        public NameRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, NameRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetInfoByNamesArge.class, METADATAMAP);
        }

        public GetInfoByNamesArge() {
        }

        public GetInfoByNamesArge(
                NameRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetInfoByNamesArge(GetInfoByNamesArge other) {
            if (other.isSetRequest()) {
                this.request = new NameRequest(other.request);
            }
        }

        public GetInfoByNamesArge deepCopy() {
            return new GetInfoByNamesArge(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public NameRequest getRequest() {
            return this.request;
        }

        public GetInfoByNamesArge setRequest(NameRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((NameRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetInfoByNamesArge)
                return this.equals((GetInfoByNamesArge) that);
            return false;
        }

        public boolean equals(GetInfoByNamesArge that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetInfoByNamesArge other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetInfoByNamesArge(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetInfoByNamesArgeStandardSchemeFactory implements SchemeFactory {
            public GetInfoByNamesArgeStandardScheme getScheme() {
                return new GetInfoByNamesArgeStandardScheme();
            }
        }

        private static class GetInfoByNamesArgeStandardScheme extends StandardScheme<GetInfoByNamesArge> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetInfoByNamesArge struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new NameRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetInfoByNamesArge struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetInfoByNamesArgeTupleSchemeFactory implements SchemeFactory {
            public GetInfoByNamesArgeTupleScheme getScheme() {
                return new GetInfoByNamesArgeTupleScheme();
            }
        }

        private static class GetInfoByNamesArgeTupleScheme extends TupleScheme<GetInfoByNamesArge> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetInfoByNamesArge struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetInfoByNamesArge struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new NameRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class GetInfoByNamesResult implements org.mi.thrift.TBase<GetInfoByNamesResult, GetInfoByNamesResult.Fields>, java.io.Serializable, Cloneable, Comparable<GetInfoByNamesResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetInfoByNamesResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetInfoByNamesResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetInfoByNamesResultTupleSchemeFactory());
        }

        public Response success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Response.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetInfoByNamesResult.class, METADATAMAP);
        }

        public GetInfoByNamesResult() {
        }

        public GetInfoByNamesResult(
                Response success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetInfoByNamesResult(GetInfoByNamesResult other) {
            if (other.isSetSuccess()) {
                this.success = new Response(other.success);
            }
        }

        public GetInfoByNamesResult deepCopy() {
            return new GetInfoByNamesResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public Response getSuccess() {
            return this.success;
        }

        public GetInfoByNamesResult setSuccess(Response success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((Response) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetInfoByNamesResult)
                return this.equals((GetInfoByNamesResult) that);
            return false;
        }

        public boolean equals(GetInfoByNamesResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentSuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentSuccess) {
                if (!(thisPresentSuccess && thatPresentSuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetInfoByNamesResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetInfoByNamesResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (success != null) {
                success.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetInfoByNamesResultStandardSchemeFactory implements SchemeFactory {
            public GetInfoByNamesResultStandardScheme getScheme() {
                return new GetInfoByNamesResultStandardScheme();
            }
        }

        private static class GetInfoByNamesResultStandardScheme extends StandardScheme<GetInfoByNamesResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetInfoByNamesResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.success = new Response();
                                struct.success.read(iprot);
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetInfoByNamesResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    struct.success.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetInfoByNamesResultTupleSchemeFactory implements SchemeFactory {
            public GetInfoByNamesResultTupleScheme getScheme() {
                return new GetInfoByNamesResultTupleScheme();
            }
        }

        private static class GetInfoByNamesResultTupleScheme extends TupleScheme<GetInfoByNamesResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetInfoByNamesResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    struct.success.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetInfoByNamesResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.success = new Response();
                    struct.success.read(iprot);
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

    public static class GetSubsetArgs implements org.mi.thrift.TBase<GetSubsetArgs, GetSubsetArgs.Fields>, java.io.Serializable, Cloneable, Comparable<GetSubsetArgs> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetSubsetArgs");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetSubsetArgsStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetSubsetArgsTupleSchemeFactory());
        }

        public SubsetRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, SubsetRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetSubsetArgs.class, METADATAMAP);
        }

        public GetSubsetArgs() {
        }

        public GetSubsetArgs(
                SubsetRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetSubsetArgs(GetSubsetArgs other) {
            if (other.isSetRequest()) {
                this.request = new SubsetRequest(other.request);
            }
        }

        public GetSubsetArgs deepCopy() {
            return new GetSubsetArgs(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public SubsetRequest getRequest() {
            return this.request;
        }

        public GetSubsetArgs setRequest(SubsetRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((SubsetRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetSubsetArgs)
                return this.equals((GetSubsetArgs) that);
            return false;
        }

        public boolean equals(GetSubsetArgs that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetSubsetArgs other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetSubsetArgs(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetSubsetArgsStandardSchemeFactory implements SchemeFactory {
            public GetSubsetArgsStandardScheme getScheme() {
                return new GetSubsetArgsStandardScheme();
            }
        }

        private static class GetSubsetArgsStandardScheme extends StandardScheme<GetSubsetArgs> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetSubsetArgs struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new SubsetRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetSubsetArgs struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetSubsetArgsTupleSchemeFactory implements SchemeFactory {
            public GetSubsetArgsTupleScheme getScheme() {
                return new GetSubsetArgsTupleScheme();
            }
        }

        private static class GetSubsetArgsTupleScheme extends TupleScheme<GetSubsetArgs> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetSubsetArgs struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetSubsetArgs struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new SubsetRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class GetSubsetResult implements org.mi.thrift.TBase<GetSubsetResult, GetSubsetResult.Fields>, java.io.Serializable, Cloneable, Comparable<GetSubsetResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetSubsetResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetSubsetResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetSubsetResultTupleSchemeFactory());
        }

        public Response success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Response.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetSubsetResult.class, METADATAMAP);
        }

        public GetSubsetResult() {
        }

        public GetSubsetResult(
                Response success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetSubsetResult(GetSubsetResult other) {
            if (other.isSetSuccess()) {
                this.success = new Response(other.success);
            }
        }

        public GetSubsetResult deepCopy() {
            return new GetSubsetResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public Response getSuccess() {
            return this.success;
        }

        public GetSubsetResult setSuccess(Response success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((Response) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetSubsetResult)
                return this.equals((GetSubsetResult) that);
            return false;
        }

        public boolean equals(GetSubsetResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentSuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentSuccess) {
                if (!(thisPresentSuccess && thatPresentSuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetSubsetResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetSubsetResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (success != null) {
                success.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetSubsetResultStandardSchemeFactory implements SchemeFactory {
            public GetSubsetResultStandardScheme getScheme() {
                return new GetSubsetResultStandardScheme();
            }
        }

        private static class GetSubsetResultStandardScheme extends StandardScheme<GetSubsetResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetSubsetResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.success = new Response();
                                struct.success.read(iprot);
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetSubsetResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    struct.success.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetSubsetResultTupleSchemeFactory implements SchemeFactory {
            public GetSubsetResultTupleScheme getScheme() {
                return new GetSubsetResultTupleScheme();
            }
        }

        private static class GetSubsetResultTupleScheme extends TupleScheme<GetSubsetResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetSubsetResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    struct.success.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetSubsetResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.success = new Response();
                    struct.success.read(iprot);
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

    public static class GetRegionTagArgs implements org.mi.thrift.TBase<GetRegionTagArgs, GetRegionTagArgs.Fields>, java.io.Serializable, Cloneable, Comparable<GetRegionTagArgs> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetRegionTagArgs");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetRegionTagArgsStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetRegionTagArgsTupleSchemeFactory());
        }

        public RegionTagRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, RegionTagRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetRegionTagArgs.class, METADATAMAP);
        }

        public GetRegionTagArgs() {
        }

        public GetRegionTagArgs(
                RegionTagRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetRegionTagArgs(GetRegionTagArgs other) {
            if (other.isSetRequest()) {
                this.request = new RegionTagRequest(other.request);
            }
        }

        public GetRegionTagArgs deepCopy() {
            return new GetRegionTagArgs(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public RegionTagRequest getRequest() {
            return this.request;
        }

        public GetRegionTagArgs setRequest(RegionTagRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((RegionTagRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetRegionTagArgs)
                return this.equals((GetRegionTagArgs) that);
            return false;
        }

        public boolean equals(GetRegionTagArgs that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetRegionTagArgs other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetRegionTagArgs(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetRegionTagArgsStandardSchemeFactory implements SchemeFactory {
            public GetRegionTagArgsStandardScheme getScheme() {
                return new GetRegionTagArgsStandardScheme();
            }
        }

        private static class GetRegionTagArgsStandardScheme extends StandardScheme<GetRegionTagArgs> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetRegionTagArgs struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new RegionTagRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetRegionTagArgs struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetRegionTagArgsTupleSchemeFactory implements SchemeFactory {
            public GetRegionTagArgsTupleScheme getScheme() {
                return new GetRegionTagArgsTupleScheme();
            }
        }

        private static class GetRegionTagArgsTupleScheme extends TupleScheme<GetRegionTagArgs> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetRegionTagArgs struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetRegionTagArgs struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new RegionTagRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class GetRegionTagResult implements org.mi.thrift.TBase<GetRegionTagResult, GetRegionTagResult.Fields>, java.io.Serializable, Cloneable, Comparable<GetRegionTagResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetRegionTagResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetRegionTagResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetRegionTagResultTupleSchemeFactory());
        }

        public Response success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Response.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetRegionTagResult.class, METADATAMAP);
        }

        public GetRegionTagResult() {
        }

        public GetRegionTagResult(
                Response success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetRegionTagResult(GetRegionTagResult other) {
            if (other.isSetSuccess()) {
                this.success = new Response(other.success);
            }
        }

        public GetRegionTagResult deepCopy() {
            return new GetRegionTagResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public Response getSuccess() {
            return this.success;
        }

        public GetRegionTagResult setSuccess(Response success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((Response) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetRegionTagResult)
                return this.equals((GetRegionTagResult) that);
            return false;
        }

        public boolean equals(GetRegionTagResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentSuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentSuccess) {
                if (!(thisPresentSuccess && thatPresentSuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetRegionTagResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetRegionTagResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (success != null) {
                success.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetRegionTagResultStandardSchemeFactory implements SchemeFactory {
            public GetRegionTagResultStandardScheme getScheme() {
                return new GetRegionTagResultStandardScheme();
            }
        }

        private static class GetRegionTagResultStandardScheme extends StandardScheme<GetRegionTagResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetRegionTagResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.success = new Response();
                                struct.success.read(iprot);
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetRegionTagResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    struct.success.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetRegionTagResultTupleSchemeFactory implements SchemeFactory {
            public GetRegionTagResultTupleScheme getScheme() {
                return new GetRegionTagResultTupleScheme();
            }
        }

        private static class GetRegionTagResultTupleScheme extends TupleScheme<GetRegionTagResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetRegionTagResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    struct.success.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetRegionTagResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.success = new Response();
                    struct.success.read(iprot);
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

    public static class GetRegionTagByIdsArgs implements org.mi.thrift.TBase<GetRegionTagByIdsArgs, GetRegionTagByIdsArgs.Fields>, java.io.Serializable, Cloneable, Comparable<GetRegionTagByIdsArgs> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetRegionTagByIdsArgs");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetRegionTagByIdsArgsStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetRegionTagByIdsArgsTupleSchemeFactory());
        }

        public TagIdRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, TagIdRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetRegionTagByIdsArgs.class, METADATAMAP);
        }

        public GetRegionTagByIdsArgs() {
        }

        public GetRegionTagByIdsArgs(
                TagIdRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetRegionTagByIdsArgs(GetRegionTagByIdsArgs other) {
            if (other.isSetRequest()) {
                this.request = new TagIdRequest(other.request);
            }
        }

        public GetRegionTagByIdsArgs deepCopy() {
            return new GetRegionTagByIdsArgs(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public TagIdRequest getRequest() {
            return this.request;
        }

        public GetRegionTagByIdsArgs setRequest(TagIdRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((TagIdRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetRegionTagByIdsArgs)
                return this.equals((GetRegionTagByIdsArgs) that);
            return false;
        }

        public boolean equals(GetRegionTagByIdsArgs that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetRegionTagByIdsArgs other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetRegionTagByIdsArgs(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetRegionTagByIdsArgsStandardSchemeFactory implements SchemeFactory {
            public GetRegionTagByIdsArgsStandardScheme getScheme() {
                return new GetRegionTagByIdsArgsStandardScheme();
            }
        }

        private static class GetRegionTagByIdsArgsStandardScheme extends StandardScheme<GetRegionTagByIdsArgs> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetRegionTagByIdsArgs struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new TagIdRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetRegionTagByIdsArgs struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetRegionTagByIdsArgsTupleSchemeFactory implements SchemeFactory {
            public GetRegionTagByIdsArgsTupleScheme getScheme() {
                return new GetRegionTagByIdsArgsTupleScheme();
            }
        }

        private static class GetRegionTagByIdsArgsTupleScheme extends TupleScheme<GetRegionTagByIdsArgs> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetRegionTagByIdsArgs struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetRegionTagByIdsArgs struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new TagIdRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class GetRegionTagByIdsResult implements org.mi.thrift.TBase<GetRegionTagByIdsResult, GetRegionTagByIdsResult.Fields>, java.io.Serializable, Cloneable, Comparable<GetRegionTagByIdsResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetRegionTagByIdsResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetRegionTagByIdsResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetRegionTagByIdsResultTupleSchemeFactory());
        }

        public Response success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Response.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetRegionTagByIdsResult.class, METADATAMAP);
        }

        public GetRegionTagByIdsResult() {
        }

        public GetRegionTagByIdsResult(
                Response success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetRegionTagByIdsResult(GetRegionTagByIdsResult other) {
            if (other.isSetSuccess()) {
                this.success = new Response(other.success);
            }
        }

        public GetRegionTagByIdsResult deepCopy() {
            return new GetRegionTagByIdsResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public Response getSuccess() {
            return this.success;
        }

        public GetRegionTagByIdsResult setSuccess(Response success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((Response) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetRegionTagByIdsResult)
                return this.equals((GetRegionTagByIdsResult) that);
            return false;
        }

        public boolean equals(GetRegionTagByIdsResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentSuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentSuccess) {
                if (!(thisPresentSuccess && thatPresentSuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetRegionTagByIdsResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetRegionTagByIdsResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (success != null) {
                success.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetRegionTagByIdsResultStandardSchemeFactory implements SchemeFactory {
            public GetRegionTagByIdsResultStandardScheme getScheme() {
                return new GetRegionTagByIdsResultStandardScheme();
            }
        }

        private static class GetRegionTagByIdsResultStandardScheme extends StandardScheme<GetRegionTagByIdsResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetRegionTagByIdsResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.success = new Response();
                                struct.success.read(iprot);
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetRegionTagByIdsResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    struct.success.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetRegionTagByIdsResultTupleSchemeFactory implements SchemeFactory {
            public GetRegionTagByIdsResultTupleScheme getScheme() {
                return new GetRegionTagByIdsResultTupleScheme();
            }
        }

        private static class GetRegionTagByIdsResultTupleScheme extends TupleScheme<GetRegionTagByIdsResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetRegionTagByIdsResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    struct.success.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetRegionTagByIdsResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.success = new Response();
                    struct.success.read(iprot);
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

    public static class GetIndRegionTagArgs implements org.mi.thrift.TBase<GetIndRegionTagArgs, GetIndRegionTagArgs.Fields>, java.io.Serializable, Cloneable, Comparable<GetIndRegionTagArgs> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetIndRegionTagArgs");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetIndRegionTagArgsStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetIndRegionTagArgsTupleSchemeFactory());
        }

        public RegionTagRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private  String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, RegionTagRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetIndRegionTagArgs.class, METADATAMAP);
        }

        public GetIndRegionTagArgs() {
        }

        public GetIndRegionTagArgs(
                RegionTagRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetIndRegionTagArgs(GetIndRegionTagArgs other) {
            if (other.isSetRequest()) {
                this.request = new RegionTagRequest(other.request);
            }
        }

        public GetIndRegionTagArgs deepCopy() {
            return new GetIndRegionTagArgs(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public RegionTagRequest getRequest() {
            return this.request;
        }

        public GetIndRegionTagArgs setRequest(RegionTagRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((RegionTagRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetIndRegionTagArgs)
                return this.equals((GetIndRegionTagArgs) that);
            return false;
        }

        public boolean equals(GetIndRegionTagArgs that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetIndRegionTagArgs other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetIndRegionTagArgs(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetIndRegionTagArgsStandardSchemeFactory implements SchemeFactory {
            public GetIndRegionTagArgsStandardScheme getScheme() {
                return new GetIndRegionTagArgsStandardScheme();
            }
        }

        private static class GetIndRegionTagArgsStandardScheme extends StandardScheme<GetIndRegionTagArgs> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetIndRegionTagArgs struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new RegionTagRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetIndRegionTagArgs struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetIndRegionTagArgsTupleSchemeFactory implements SchemeFactory {
            public GetIndRegionTagArgsTupleScheme getScheme() {
                return new GetIndRegionTagArgsTupleScheme();
            }
        }

        private static class GetIndRegionTagArgsTupleScheme extends TupleScheme<GetIndRegionTagArgs> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetIndRegionTagArgs struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetIndRegionTagArgs struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new RegionTagRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class GetIndRegionTagResult implements org.mi.thrift.TBase<GetIndRegionTagResult, GetIndRegionTagResult.Fields>, java.io.Serializable, Cloneable, Comparable<GetIndRegionTagResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetIndRegionTagResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.STRUCT, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetIndRegionTagResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetIndRegionTagResultTupleSchemeFactory());
        }

        public Response success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private  String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, Response.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetIndRegionTagResult.class, METADATAMAP);
        }

        public GetIndRegionTagResult() {
        }

        public GetIndRegionTagResult(
                Response success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetIndRegionTagResult(GetIndRegionTagResult other) {
            if (other.isSetSuccess()) {
                this.success = new Response(other.success);
            }
        }

        public GetIndRegionTagResult deepCopy() {
            return new GetIndRegionTagResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public Response getSuccess() {
            return this.success;
        }

        public GetIndRegionTagResult setSuccess(Response success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((Response) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetIndRegionTagResult)
                return this.equals((GetIndRegionTagResult) that);
            return false;
        }

        public boolean equals(GetIndRegionTagResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentSuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentSuccess) {
                if (!(thisPresentSuccess && thatPresentSuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetIndRegionTagResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetIndRegionTagResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (success != null) {
                success.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetIndRegionTagResultStandardSchemeFactory implements SchemeFactory {
            public GetIndRegionTagResultStandardScheme getScheme() {
                return new GetIndRegionTagResultStandardScheme();
            }
        }

        private static class GetIndRegionTagResultStandardScheme extends StandardScheme<GetIndRegionTagResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetIndRegionTagResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.success = new Response();
                                struct.success.read(iprot);
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetIndRegionTagResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    struct.success.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetIndRegionTagResultTupleSchemeFactory implements SchemeFactory {
            public GetIndRegionTagResultTupleScheme getScheme() {
                return new GetIndRegionTagResultTupleScheme();
            }
        }

        private static class GetIndRegionTagResultTupleScheme extends TupleScheme<GetIndRegionTagResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetIndRegionTagResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    struct.success.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetIndRegionTagResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.success = new Response();
                    struct.success.read(iprot);
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

    public static class GetCountryNumArgs implements org.mi.thrift.TBase<GetCountryNumArgs, GetCountryNumArgs.Fields>, java.io.Serializable, Cloneable, Comparable<GetCountryNumArgs> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetCountryNumArgs");

        private static final org.mi.thrift.protocol.TField REQUEST_FIELD_DESC = new org.mi.thrift.protocol.TField("request", org.mi.thrift.protocol.TType.STRUCT, (short) 1);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetCountryNumArgsStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetCountryNumArgsTupleSchemeFactory());
        }

        public CountryNumRequest request; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            REQUEST((short) 1, "request");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 1: // REQUEST
                        return REQUEST;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.REQUEST, new org.mi.thrift.meta_data.FieldMetaData("request", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.StructMetaData(org.mi.thrift.protocol.TType.STRUCT, CountryNumRequest.class)));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetCountryNumArgs.class, METADATAMAP);
        }

        public GetCountryNumArgs() {
        }

        public GetCountryNumArgs(
                CountryNumRequest request) {
            this();
            this.request = request;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetCountryNumArgs(GetCountryNumArgs other) {
            if (other.isSetRequest()) {
                this.request = new CountryNumRequest(other.request);
            }
        }

        public GetCountryNumArgs deepCopy() {
            return new GetCountryNumArgs(this);
        }

        @Override
        public void clear() {
            this.request = null;
        }

        public CountryNumRequest getRequest() {
            return this.request;
        }

        public GetCountryNumArgs setRequest(CountryNumRequest request) {
            this.request = request;
            return this;
        }

        public void unsetRequest() {
            this.request = null;
        }

        /**
         * Returns true if field request is set (has been assigned a value) and false otherwise
         */
        public boolean isSetRequest() {
            return this.request != null;
        }

        public void setRequestIsSet(boolean value) {
            if (!value) {
                this.request = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case REQUEST:
                    if (value == null) {
                        unsetRequest();
                    } else {
                        setRequest((CountryNumRequest) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case REQUEST:
                    return getRequest();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case REQUEST:
                    return isSetRequest();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetCountryNumArgs)
                return this.equals((GetCountryNumArgs) that);
            return false;
        }

        public boolean equals(GetCountryNumArgs that) {
            if (that == null)
                return false;

            boolean thisPresentRequest = true && this.isSetRequest();
            boolean thatPresentRequest = true && that.isSetRequest();
            if (thisPresentRequest || thatPresentRequest) {
                if (!(thisPresentRequest && thatPresentRequest))
                    return false;
                if (!this.request.equals(that.request))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentRequest = true && (isSetRequest());
            list.add(presentRequest);
            if (presentRequest)
                list.add(request);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetCountryNumArgs other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetRequest()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.request, other.request);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetCountryNumArgs(");
            boolean first = true;

            sb.append("request:");
            if (this.request == null) {
                sb.append("null");
            } else {
                sb.append(this.request);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
            if (request != null) {
                request.validate();
            }
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetCountryNumArgsStandardSchemeFactory implements SchemeFactory {
            public GetCountryNumArgsStandardScheme getScheme() {
                return new GetCountryNumArgsStandardScheme();
            }
        }

        private static class GetCountryNumArgsStandardScheme extends StandardScheme<GetCountryNumArgs> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetCountryNumArgs struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 1: // REQUEST
                            if (schemeField.type == org.mi.thrift.protocol.TType.STRUCT) {
                                struct.request = new CountryNumRequest();
                                struct.request.read(iprot);
                                struct.setRequestIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetCountryNumArgs struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.request != null) {
                    oprot.writeFieldBegin(REQUEST_FIELD_DESC);
                    struct.request.write(oprot);
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetCountryNumArgsTupleSchemeFactory implements SchemeFactory {
            public GetCountryNumArgsTupleScheme getScheme() {
                return new GetCountryNumArgsTupleScheme();
            }
        }

        private static class GetCountryNumArgsTupleScheme extends TupleScheme<GetCountryNumArgs> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetCountryNumArgs struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetRequest()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetRequest()) {
                    struct.request.write(oprot);
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetCountryNumArgs struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    struct.request = new CountryNumRequest();
                    struct.request.read(iprot);
                    struct.setRequestIsSet(true);
                }
            }
        }

    }

    public static class GetCountryNumResult implements org.mi.thrift.TBase<GetCountryNumResult, GetCountryNumResult.Fields>, java.io.Serializable, Cloneable, Comparable<GetCountryNumResult> {
        private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("GetCountryNumResult");

        private static final org.mi.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.mi.thrift.protocol.TField("success", org.mi.thrift.protocol.TType.LIST, (short) 0);

        private static final Map<Class<? extends IScheme>, SchemeFactory> SCHEMES = new HashMap<Class<? extends IScheme>, SchemeFactory>();

        static {
            SCHEMES.put(StandardScheme.class, new GetCountryNumResultStandardSchemeFactory());
            SCHEMES.put(TupleScheme.class, new GetCountryNumResultTupleSchemeFactory());
        }

        public List<Integer> success; // required

        /**
         * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
         */
        public enum Fields implements org.mi.thrift.TFieldIdEnum {
            SUCCESS((short) 0, "success");

            private static final Map<String, Fields> BY_NAME = new HashMap<String, Fields>();

            static {
                for (Fields field : EnumSet.allOf(Fields.class)) {
                    BY_NAME.put(field.getFieldName(), field);
                }
            }

            /**
             * Find the Fields constant that matches fieldId, or null if its not found.
             */
            public static Fields findBythriftId(int fieldId) {
                switch (fieldId) {
                    case 0: // SUCCESS
                        return SUCCESS;
                    default:
                        return null;
                }
            }

            /**
             * Find the Fields constant that matches fieldId, throwing an exception
             * if it is not found.
             */
            public static Fields findBythriftIdOrThrow(int fieldId) {
                Fields fields = findBythriftId(fieldId);
                if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
                return fields;
            }

            /**
             * Find the Fields constant that matches name, or null if its not found.
             */
            public static Fields findByName(String name) {
                return BY_NAME.get(name);
            }

            private  short thriftId;
            private String fieldName;

            Fields(short thriftId, String fieldName) {
                thriftId = thriftId;
                fieldName = fieldName;
            }

            public short getThriftFieldId() {
                return thriftId;
            }

            public String getFieldName() {
                return fieldName;
            }
        }

        // isset id assignments
        public static final Map<Fields, org.mi.thrift.meta_data.FieldMetaData> METADATAMAP;

        static {
            Map<Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<Fields, org.mi.thrift.meta_data.FieldMetaData>(Fields.class);
            tmpMap.put(Fields.SUCCESS, new org.mi.thrift.meta_data.FieldMetaData("success", org.mi.thrift.TFieldRequirementType.DEFAULT,
                    new org.mi.thrift.meta_data.ListMetaData(org.mi.thrift.protocol.TType.LIST,
                            new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32))));
            METADATAMAP = Collections.unmodifiableMap(tmpMap);
            org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetCountryNumResult.class, METADATAMAP);
        }

        public GetCountryNumResult() {
        }

        public GetCountryNumResult(
                List<Integer> success) {
            this();
            this.success = success;
        }

        /**
         * Performs a deep copy on <i>other</i>.
         */
        public GetCountryNumResult(GetCountryNumResult other) {
            if (other.isSetSuccess()) {
                List<Integer> __this__success = new ArrayList<Integer>(other.success);
                this.success = __this__success;
            }
        }

        public GetCountryNumResult deepCopy() {
            return new GetCountryNumResult(this);
        }

        @Override
        public void clear() {
            this.success = null;
        }

        public int getSuccessSize() {
            return (this.success == null) ? 0 : this.success.size();
        }

        public Iterator<Integer> getSuccessIterator() {
            return (this.success == null) ? null : this.success.iterator();
        }

        public void addToSuccess(int elem) {
            if (this.success == null) {
                this.success = new ArrayList<Integer>();
            }
            this.success.add(elem);
        }

        public List<Integer> getSuccess() {
            return this.success;
        }

        public GetCountryNumResult setSuccess(List<Integer> success) {
            this.success = success;
            return this;
        }

        public void unsetSuccess() {
            this.success = null;
        }

        /**
         * Returns true if field success is set (has been assigned a value) and false otherwise
         */
        public boolean isSetSuccess() {
            return this.success != null;
        }

        public void setSuccessIsSet(boolean value) {
            if (!value) {
                this.success = null;
            }
        }

        public void setFieldValue(Fields field, Object value) {
            switch (field) {
                case SUCCESS:
                    if (value == null) {
                        unsetSuccess();
                    } else {
                        setSuccess((List<Integer>) value);
                    }
                    break;

            }
        }

        public Object getFieldValue(Fields field) {
            switch (field) {
                case SUCCESS:
                    return getSuccess();

            }
            throw new IllegalStateException();
        }

        /**
         * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
         */
        public boolean isSet(Fields field) {
            if (field == null) {
                throw new IllegalArgumentException();
            }

            switch (field) {
                case SUCCESS:
                    return isSetSuccess();
            }
            throw new IllegalStateException();
        }

        @Override
        public boolean equals(Object that) {
            if (that == null)
                return false;
            if (that instanceof GetCountryNumResult)
                return this.equals((GetCountryNumResult) that);
            return false;
        }

        public boolean equals(GetCountryNumResult that) {
            if (that == null)
                return false;

            boolean thisPresentSuccess = true && this.isSetSuccess();
            boolean thatPresentSuccess = true && that.isSetSuccess();
            if (thisPresentSuccess || thatPresentSuccess) {
                if (!(thisPresentSuccess && thatPresentSuccess))
                    return false;
                if (!this.success.equals(that.success))
                    return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            List<Object> list = new ArrayList<Object>();

            boolean presentSuccess = true && (isSetSuccess());
            list.add(presentSuccess);
            if (presentSuccess)
                list.add(success);

            return list.hashCode();
        }

        @Override
        public int compareTo(GetCountryNumResult other) {
            if (!getClass().equals(other.getClass())) {
                return getClass().getName().compareTo(other.getClass().getName());
            }

            int lastComparison = 0;

            lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
            if (lastComparison != 0) {
                return lastComparison;
            }
            if (isSetSuccess()) {
                lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.success, other.success);
                if (lastComparison != 0) {
                    return lastComparison;
                }
            }
            return 0;
        }

        public Fields fieldForId(int fieldId) {
            return Fields.findBythriftId(fieldId);
        }

        public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
            SCHEMES.get(iprot.getScheme()).getScheme().read(iprot, this);
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
            SCHEMES.get(oprot.getScheme()).getScheme().write(oprot, this);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("GetCountryNumResult(");
            boolean first = true;

            sb.append("success:");
            if (this.success == null) {
                sb.append("null");
            } else {
                sb.append(this.success);
            }
            first = false;
            sb.append(")");
            return sb.toString();
        }

        public void validate() throws TException {
            // check for required fields
            // check for sub-struct validity
        }

        private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
            try {
                write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
            try {
                read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
            } catch (TException te) {
                throw new java.io.IOException(te);
            }
        }

        private static class GetCountryNumResultStandardSchemeFactory implements SchemeFactory {
            public GetCountryNumResultStandardScheme getScheme() {
                return new GetCountryNumResultStandardScheme();
            }
        }

        private static class GetCountryNumResultStandardScheme extends StandardScheme<GetCountryNumResult> {

            public void read(org.mi.thrift.protocol.TProtocol iprot, GetCountryNumResult struct) throws TException {
                org.mi.thrift.protocol.TField schemeField;
                iprot.readStructBegin();
                while (true) {
                    schemeField = iprot.readFieldBegin();
                    if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                        break;
                    }
                    switch (schemeField.id) {
                        case 0: // SUCCESS
                            if (schemeField.type == org.mi.thrift.protocol.TType.LIST) {
                                {
                                    org.mi.thrift.protocol.TList list24 = iprot.readListBegin();
                                    struct.success = new ArrayList<Integer>(list24.size);
                                    int elem25;
                                    for (int i26 = 0; i26 < list24.size; ++i26) {
                                        elem25 = iprot.readI32();
                                        struct.success.add(elem25);
                                    }
                                    iprot.readListEnd();
                                }
                                struct.setSuccessIsSet(true);
                            } else {
                                org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                            }
                            break;
                        default:
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                    }
                    iprot.readFieldEnd();
                }
                iprot.readStructEnd();

                // check for required fields of primitive type, which can't be checked in the validate method
                struct.validate();
            }

            public void write(org.mi.thrift.protocol.TProtocol oprot, GetCountryNumResult struct) throws TException {
                struct.validate();

                oprot.writeStructBegin(STRUCT_DESC);
                if (struct.success != null) {
                    oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
                    {
                        oprot.writeListBegin(new org.mi.thrift.protocol.TList(org.mi.thrift.protocol.TType.I32, struct.success.size()));
                        for (int iter27 : struct.success) {
                            oprot.writeI32(iter27);
                        }
                        oprot.writeListEnd();
                    }
                    oprot.writeFieldEnd();
                }
                oprot.writeFieldStop();
                oprot.writeStructEnd();
            }

        }

        private static class GetCountryNumResultTupleSchemeFactory implements SchemeFactory {
            public GetCountryNumResultTupleScheme getScheme() {
                return new GetCountryNumResultTupleScheme();
            }
        }

        private static class GetCountryNumResultTupleScheme extends TupleScheme<GetCountryNumResult> {

            @Override
            public void write(org.mi.thrift.protocol.TProtocol prot, GetCountryNumResult struct) throws TException {
                TTupleProtocol oprot = (TTupleProtocol) prot;
                BitSet optionals = new BitSet();
                if (struct.isSetSuccess()) {
                    optionals.set(0);
                }
                oprot.writeBitSet(optionals, 1);
                if (struct.isSetSuccess()) {
                    {
                        oprot.writeI32(struct.success.size());
                        for (int iter28 : struct.success) {
                            oprot.writeI32(iter28);
                        }
                    }
                }
            }

            @Override
            public void read(org.mi.thrift.protocol.TProtocol prot, GetCountryNumResult struct) throws TException {
                TTupleProtocol iprot = (TTupleProtocol) prot;
                BitSet incoming = iprot.readBitSet(1);
                if (incoming.get(0)) {
                    {
                        org.mi.thrift.protocol.TList list29 = new org.mi.thrift.protocol.TList(org.mi.thrift.protocol.TType.I32, iprot.readI32());
                        struct.success = new ArrayList<Integer>(list29.size);
                        int elem30;
                        for (int i31 = 0; i31 < list29.size; ++i31) {
                            elem30 = iprot.readI32();
                            struct.success.add(elem30);
                        }
                    }
                    struct.setSuccessIsSet(true);
                }
            }
        }

    }

}
