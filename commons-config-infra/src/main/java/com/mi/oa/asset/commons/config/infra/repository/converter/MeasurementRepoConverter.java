package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.mesurement.entity.MeasurementUnit;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.MeasurementUnitPo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/15 18:14
 */

@Mapper(
        componentModel = "spring",
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL
)
public interface MeasurementRepoConverter extends CommonConverter {

    MeasurementUnit toMeasurementUnit(MeasurementUnitPo source);

    List<MeasurementUnit> toMeasurementUnits(List<MeasurementUnitPo> source);

    MeasurementUnitPo toMeasurementUnitPo(MeasurementUnit source);

    List<MeasurementUnitPo> toMeasurementUnitPos(List<MeasurementUnit> source);
}
