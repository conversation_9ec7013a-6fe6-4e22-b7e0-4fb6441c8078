package com.mi.oa.asset.commons.config.infra.common;

import com.mi.oa.asset.common.BaseConverter;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.DeptDisplayWay;
import com.mi.oa.asset.common.enums.IEnum;
import com.mi.oa.asset.common.enums.YesNo;
import com.mi.oa.asset.common.enums.YesNoEnum;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgType;
import com.mi.oa.asset.commons.config.api.assetsku.ManageModel;
import com.mi.oa.asset.commons.config.api.assetsku.SnTypeEnum;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigDataRage;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigField;
import com.mi.oa.asset.commons.config.api.common.enums.AsyncTaskType;
import com.mi.oa.asset.commons.config.api.common.enums.ExecutionStatus;
import com.mi.oa.asset.commons.config.api.warehouse.enums.WarehouseType;
import com.mi.oa.asset.commons.config.domain.assetsku.enums.StockCostEnum;
import com.mi.oa.asset.commons.config.domain.assetuseway.enums.UsewayReasonType;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import com.mi.oa.asset.commons.config.domain.use.enums.UseReasonEnum;
import com.mi.oa.asset.commons.config.domain.use.enums.UseScope;
import com.mi.oa.asset.commons.config.domain.use.enums.UseType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:50
 */

@SuppressWarnings("all")
public interface CommonConverter extends BaseConverter {

    default AssetOrgType assetOrgTypeMapping(String code) { return IEnum.getByCode(AssetOrgType.class, code); }

    default String assetOrgTypeMapping(AssetOrgType orgType) { return null != orgType ? orgType.getCode() : null; }

    default DataCreateSource dataCreateSourceMapping(String code) { return DataCreateSource.getByCode(code); }

    default String dataCreateSourceMapping(DataCreateSource dataCreateSource) { return null != dataCreateSource ? dataCreateSource.getCode() : null; }

    default String usewayReasonMapping(UsewayReasonType source) {
        return source != null ? source.getDesc() : null;
    }
    default UsewayReasonType usewayReasonMapping(String source) {
        return UsewayReasonType.getByCode(source);
    }

    default YesNoEnum yesNoEnumMapping(String code) { return YesNoEnum.getByCode(code); }

    default String yesNoEnumMapping(YesNoEnum yesNoEnum) { return null != yesNoEnum ? yesNoEnum.getCode() : null; }

    default YesNo yesNoMapping(Integer code) { return YesNo.getByCode(code); }

    default Integer yesNoMapping(YesNo yesNo) { return null != yesNo ? yesNo.getCode() : null; }

    default ExecutionStatus executionStatusMapping(String code) { return ExecutionStatus.getByCode(code); }

    default String executionStatusMapping(ExecutionStatus executionStatus) { return null != executionStatus ? executionStatus.getCode() : null; }

    default AsyncTaskType asyncTaskTypeMapping(String code) { return AsyncTaskType.getByCode(code); }

    default String asyncTaskTypeMapping(AsyncTaskType asyncTaskType) { return null != asyncTaskType ? asyncTaskType.getCode() : null; }

    default AllotConfigField allotConfigFieldMapping(String code) { return AllotConfigField.getByCode(code); }

    default String allotConfigFieldMapping(AllotConfigField allotConfigField) { return null != allotConfigField ? allotConfigField.getCode() : null; }

    default AllotConfigDataRage allotConfigDataRageMapping(String code) { return AllotConfigDataRage.getByCode(code); }

    default String allotConfigDataRageMapping(AllotConfigDataRage allotConfigField) { return null != allotConfigField ? allotConfigField.getCode() : null; }

    default ManageModel manageModelMapping(String source){
        return ManageModel.getByCode(source);
    }
    default String manageModelMapping(ManageModel source){
        return source != null ? source.getCode() : null;
    }
    default SnTypeEnum snTypeMapping(String source){
        return SnTypeEnum.getByCode(source);
    }
    default String snTypeMapping(SnTypeEnum source){
        return source != null ? source.getCode() : null;
    }
    default String stockCostEnumMapping(StockCostEnum source) {
        return source != null ? source.getCode() : null;
    }
    default StockCostEnum stockCostEnumMapping(String source) {
        return StockCostEnum.getByCode(source);
    }

    default String warehouseTypeMapping(WarehouseType warehouseType) {
        return warehouseType != null ? warehouseType.getCode() : null;
    }
    default WarehouseType warehouseTypeMapping(String warehouseType) {
        return WarehouseType.getByCode(warehouseType);
    }

    default String useReasonMapping(UseReasonEnum source) {
        return source != null ? source.getDesc() : null;
    }

    default UseReasonEnum useReasonMapping(String source) {
        return UseReasonEnum.getByCode(source);
    }

    default Integer useScopMapping(UseScope source) {
        return source != null ? source.getCode() : null;
    }
    default UseScope useScopMapping(Integer source) {
        return UseScope.getByCode(source);
    }

    default Integer useTypeMapping(UseType source) {
        return source != null ? source.getCode() : null;
    }
    default UseType useTypeMapping(Integer source) {
        return UseType.getByCode(source);
    }

    default DeptDisplayWay deptDisplayWayMapping(String code) { return DeptDisplayWay.getByCode(code); }

    default String deptDisplayWayMapping(DeptDisplayWay deptDisplayWay) { return null != deptDisplayWay ? deptDisplayWay.getCode() : null; }

    default List<String> mapBusinessLines(List<BusinessLine> businessLines) {
        if (businessLines == null) {
            return null;
        }
        return businessLines.stream()
                .map(BusinessLine::getCode)
                .collect(Collectors.toList());
    }


}
