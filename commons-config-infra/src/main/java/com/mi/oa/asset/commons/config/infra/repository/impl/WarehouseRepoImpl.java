package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.Warehouse;
import com.mi.oa.asset.commons.config.domain.warehouse.repository.WarehouseRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.WarehousePo;
import com.mi.oa.asset.commons.config.infra.database.mapper.WarehouseMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.WarehouseConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Service
public class WarehouseRepoImpl extends ServiceImpl<WarehouseMapper, WarehousePo> implements WarehouseRepo {
    @Resource
    private WarehouseConverter converter;

    /**
     * 保存并修改
     *
     * @param warehouse
     */
    @Override
    public void saveWarehouse(Warehouse warehouse) {
        WarehousePo po = converter.toPo(warehouse);
        this.saveOrUpdate(po);
        warehouse.setId(po.getId());
    }

    @Override
    public void saveWarehouse(List<Warehouse> warehouses) {
        this.saveOrUpdateBatch(converter.toPoList(warehouses));
    }

    @Override
    public void deleteWarehouse(List<Integer> ids) {
        this.baseMapper.deleteBatchIds(ids);
    }

    @Override
    public List<Warehouse> getByCode(String code) {
        List<WarehousePo> list = this.baseMapper.selectList(Wrappers.lambdaQuery(WarehousePo.class).eq(WarehousePo::getHouseCode, code));
        return converter.toDoList(list);
    }

    @Override
    public List<Warehouse> findByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();
        List<WarehousePo> pos = this.list(Wrappers.lambdaQuery(WarehousePo.class).in(WarehousePo::getHouseCode, codes)
                .eq(WarehousePo::getIsDeleted, 0));
        return converter.toDoList(pos);
    }

    @Override
    public Warehouse findByCode(String code) {
        List<Warehouse> list = this.getByCode(code);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<Warehouse> getBatchByServiceType(String businessLine, String serviceType, String key) {
        List<String> businessLineList = new ArrayList<>(Arrays.asList(businessLine.split(",")));
        List<WarehousePo> list = this.baseMapper.selectList(
                Wrappers.lambdaQuery(WarehousePo.class)
                        .in(WarehousePo::getBusinessLine, businessLineList)
                        .like(WarehousePo::getServices, serviceType)
                        .eq(WarehousePo::getHouseStatus, 1)
                        .nested(StringUtils.isNotEmpty(key), q -> q.like(WarehousePo::getHouseCode, key).or()
                                .like(WarehousePo::getHouseName, key)).orderByDesc(WarehousePo::getCreateTime)
        );
        return converter.toDoList(list);
    }

    @Override
    public List<Warehouse> getByBusinessLine(String businessLine) {
        List<WarehousePo> list = this.baseMapper.selectList(
                Wrappers.lambdaQuery(WarehousePo.class)
                        .eq(WarehousePo::getBusinessLine, businessLine)
                        .eq(WarehousePo::getIsDeleted, 0)
        );
        return converter.toDoList(list);
    }
}
