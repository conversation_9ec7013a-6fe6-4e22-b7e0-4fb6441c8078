package com.mi.oa.asset.commons.config.infra.config;

import com.mi.oa.infra.oaucf.core.exception.InfraErrorCode;

/**
 * by roger
 */
public enum InfraErrorCodeEnum implements InfraErrorCode {

    INFRA_UNKNOWN_ERROR(1, "基础设施层未知错误"),
    INFRA_UTILS_ERROR(2, "基础设施层工具错误"),
    INFRA_X5_INIT_ERROR(3, "X5 请求客户端初始化错误"),
    INFRA_X5_EMPTY_ID_OR_KEY(4, "X5 appId 或 appKey 为空"),
    INFRA_X5_RESPONSE_ERROR(5, "X5 返回错误: %s"),

    INFRA_HROD_MISSING_API_DESCRIBE(100, "找不到接口定义: %s")
    ;


    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }

    /**
     * 构造方法
     *
     * @param errCode
     * @param errDesc
     */
    InfraErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }


}
