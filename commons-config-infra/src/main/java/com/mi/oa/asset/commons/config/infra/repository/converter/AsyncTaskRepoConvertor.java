package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.common.entity.AsyncTask;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AsyncTaskPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @date 2024/7/12 10:06
 */

@Mapper(componentModel = "spring")
public interface AsyncTaskRepoConvertor extends CommonConverter {

    @Mapping(source = "taskId", target = "id")
    AsyncTaskPo doToPo(AsyncTask asyncTask);

    @Mapping(source = "id", target = "taskId")
    AsyncTask poToDo(AsyncTaskPo asyncTaskPo);
}
