package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-01-06 15:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "amg_share_record")
public class ShareRecordPo extends BasePo {

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 单据标题
     */
    private String recordTitle;

    /**
     * 单据编号
     */
    private String recordNo;

    /**
     * 单据状态：draft-草稿,submitted-已提交
     */
    private String recordStatus;

    /**
     * 共享状态：1-生效,0-失效
     */
    private Integer isValid;

    /**
     * 有效期类型：start_stop-起止时间,permanent-永久有效
     */
    private String validityType;

    /**
     * 共享开始时间
     */
    private Date shareStartTime;

    /**
     * 共享结束时间
     */
    private Date shareEndTime;

    /**
     * 共享场景
     */
    private String shareScene;

    /**
     * 应用的客户端
     */
    private String shareClient;
    /**
     * 通知范围
     */
    private String notifyRange;

    /**
     * 共享说明
     */
    private String memo;

    /**
     * 业务线范围编码
     */
    private String businessLineCode;

    /**
     * 业务线范围名称
     */
    private String businessLineName;

    /**
     * 部门范围编码
     */
    private String shareDeptCode;

    /**
     * 部门范围名称
     */
    private String shareDeptName;

    /**
     * 人员范围账号
     */
    private String shareUserCode;

    /**
     * 人员范围
     */
    private String shareUserName;

    /**
     * 使用部门范围编码
     */
    private String useDeptCode;

    /**
     * 使用部门范围名称
     */
    private String useDeptName;

    /**
     * 管理部门范围编码
     */
    private String manageDeptCode;

    /**
     * 管理部门范围名称
     */
    private String manageDeptName;

    /**
     * 资产所属位置编码
     */
    private String locationCode;

    /**
     * 资产所属位置名称
     */
    private String locationName;

    /**
     * 资产所属位置ID
     */
    private String locationId;

    /**
     * 资产分类编码
     */
    private String assetCateCode;

    /**
     * 资产分类名称
     */
    private String assetCateName;

    /**
     * 资产分类Id
     */
    private String assetCateId;

    /**
     * 资产类型范围
     */
    private String assetType;

    /**
     * 使用状态范围
     */
    private String useStatus;

    /**
     * 验收状态范围
     */
    private String checkStatus;

    /**
     * 是否勾选自定义共享清单
     */
    private Integer isCheckCustom;

    /**
     * 编制部门编码
     */
    private String createDeptCode;

    /**
     * 编制部门名称
     */
    private String createDeptName;
}

