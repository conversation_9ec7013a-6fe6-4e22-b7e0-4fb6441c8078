package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.common.BaseConverter;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRole;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRoleUser;
import com.mi.oa.asset.commons.config.infra.database.dataobject.BusinessRolePo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.BusinessRoleUserPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/15 17:17
 */

@Mapper(componentModel = "spring")
public interface BusinessRoleRepoConverter extends BaseConverter {

    BusinessRole poToDo(BusinessRolePo businessRoleUserPo);

    List<BusinessRole> toBusinessRoleList(List<BusinessRolePo> source);

    BusinessRoleUser poToDo(BusinessRoleUserPo businessRoleUserPo);

    List<BusinessRoleUser> poToList(List<BusinessRoleUserPo> source);

    List<BusinessRoleUserPo> toBusinessRoleUserPos(List<BusinessRoleUser> source);
}
