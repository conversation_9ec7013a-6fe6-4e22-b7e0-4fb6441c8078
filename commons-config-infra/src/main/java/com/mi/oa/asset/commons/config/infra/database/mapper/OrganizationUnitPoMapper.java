package com.mi.oa.asset.commons.config.infra.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.commons.config.infra.database.dataobject.OrganizationUnitPo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/22 16:26
 */

public interface OrganizationUnitPoMapper extends BaseMapper<OrganizationUnitPo> {
    List<OrganizationUnitPo> getUseOrgDataRange(String userName, List<String> businessLine);

    List<OrganizationUnitPo> getManageOrgDataRange(String userName, List<String> businessLine);
}