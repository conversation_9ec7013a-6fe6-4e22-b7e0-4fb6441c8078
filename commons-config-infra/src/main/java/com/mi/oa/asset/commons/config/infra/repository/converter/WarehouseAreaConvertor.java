package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.warehouse.entity.WarehouseArea;
import com.mi.oa.asset.commons.config.infra.database.dataobject.WarehouseAreaPo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface WarehouseAreaConvertor {

    List<WarehouseAreaPo> toPoList(List<WarehouseArea> source);

    List<WarehouseArea> toDoList(List<WarehouseAreaPo> source);

    WarehouseAreaPo toIndiaWarehousePo(WarehouseArea source);

    WarehouseArea toIndiaWarehouse(WarehouseAreaPo source);
}