package com.mi.oa.asset.commons.config.infra.database.dataobject.old;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date    2023/6/27 17:19
 */

/**
    * 展示分类
    */
@Data
@TableName(value = "asset_show")
public class DeviceCategoryPO {
    /**
     * 主键
     */
    @TableId(value = "show_id", type = IdType.INPUT)
    private String showId;

    /**
     * 分类编码
     */
    @TableField(value = "show_code")
    private String showCode;

    /**
     * 级别
     */
    @TableField(value = "show_level")
    private BigDecimal showLevel;

    /**
     * 分类名称
     */
    @TableField(value = "show_name")
    private String showName;

    /**
     * 添加人ID
     */
    @TableField(value = "add_userid")
    private String addUserid;

    /**
     * 添加时间
     */
    @TableField(value = "add_date")
    private Date addDate;

    /**
     * 修改人ID
     */
    @TableField(value = "modify_userid")
    private String modifyUserid;

    /**
     * 修改时间
     */
    @TableField(value = "modify_date")
    private Date modifyDate;

    /**
     * 系统租户ID
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 是否生效
     */
    @TableField(value = "is_valid")
    private String isValid;

    /**
     * 父ID
     */
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * SAP资产分类编码
     */
    @TableField(value = "acc_code")
    private String accCode;

    /**
     * 排序字段
     */
    @TableField(value = "asset_sort")
    private BigDecimal assetSort;

    /**
     * 业务渠道
     */
    @TableField(value = "bus_type")
    private String busType;

    /**
     * 国家编码
     */
    @TableField(value = "country")
    private String country;

    /**
     * 国家名称
     */
    @TableField(value = "country_name")
    private String countryName;

    /**
     * 分类名称(英文)
     */
    @TableField(value = "show_name_en")
    private String showNameEn;

    /**
     * SAP资产分类ID
     */
    @TableField(value = "acc_id")
    private String accId;

    /**
     * 是否叶子节点
     */
    @TableField(value = "is_leaf")
    private String isLeaf;

    /**
     * 数据来源
     */
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * SAP资产分类名称
     */
    @TableField(value = "acc_name")
    private String accName;

    /**
     * 使用期间
     */
    @TableField(value = "use_month")
    private String useMonth;

    /**
     * 使用年限
     */
    @TableField(value = "use_year")
    private String useYear;
}