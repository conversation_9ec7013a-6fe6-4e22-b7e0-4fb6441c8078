package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.api.auth.ResourceDto;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/24 18:58
 * @description
 */
@Mapper(componentModel = "spring", collectionMappingStrategy = CollectionMappingStrategy.TARGET_IMMUTABLE)
public interface DimensionResourceConverter extends CommonConverter {
    @Mapping(source = "code", target = "resourceCode")
    @Mapping(source = "name", target = "resourceName")
    @Mapping(target = "resourceType", constant = "1")
    @Mapping(source = "dimensionCode", target = "dimensionCode")
    @Mapping(target = "appCode", constant = "lqisPdAVHqCn")
    ResourceDto toResourceDto(String code,String name,String dimensionCode);

//    @Mapping(source = "source.bizFuncCode", target = "resourceCode")
//    @Mapping(source = "source.bizFuncName", target = "resourceName")
//    @Mapping(target = "resourceType", constant = "1")
//    @Mapping(target = "dimensionCode", constant = "asset_account_model")
//    @Mapping(target = "appCode", constant = "lqisPdAVHqCn")
//    ResourceDto toResourceDto(AssetBizFunc source);
//
//    List<ResourceDto> toResourceDtoList(AssetBizFunc[] source);
//
//    default List<ResourceDto> toResourceDtoList(AssetAccountModelGroupMeta[] source, String bizFuncCode) {
//        List<ResourceDto> resourceDtoList = new ArrayList<>();
//        for (AssetAccountModelGroupMeta meta : source) {
//            ResourceDto dto = toResourceTreeDto(meta);
//            StringBuilder builder = new StringBuilder();
//            builder.append(bizFuncCode).append(":").append(meta.getGroupCode());
//            dto.setResourceCode(builder.toString());
//            resourceDtoList.add(dto);
//        }
//        return resourceDtoList;
//    }
}
