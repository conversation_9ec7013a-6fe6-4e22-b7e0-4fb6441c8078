package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgStructure;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.OrganizationStructurePo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.OrganizationUnitPo;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/15 18:14
 */

@Mapper(
        componentModel = "spring",
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL
)
public interface AssetOrgRepoConverter extends CommonConverter {

    @Mapping(source = "id", target = "structureId")
    AssetOrgStructure toAssetOrgStructure(OrganizationStructurePo source);

    List<AssetOrgStructure> toOrganizationStructures(List<OrganizationStructurePo> source);

    @Mapping(source = "structureId", target = "id")
    OrganizationStructurePo toOrganizationStructurePo(AssetOrgStructure source);

    List<OrganizationStructurePo> toOrganizationStructurePos(List<AssetOrgStructure> source);

    @Mapping(source = "id", target = "orgId")
    AssetOrgUnit toAssetOrgUnit(OrganizationUnitPo source);

    List<AssetOrgUnit> toAssetOrgUnits(List<OrganizationUnitPo> source);

    @Mapping(source = "orgId", target = "id")
    OrganizationUnitPo toOrganizationUnitPo(AssetOrgUnit source);

    List<OrganizationUnitPo> toOrganizationUnitPoList(List<AssetOrgUnit> orgUnits);
}
