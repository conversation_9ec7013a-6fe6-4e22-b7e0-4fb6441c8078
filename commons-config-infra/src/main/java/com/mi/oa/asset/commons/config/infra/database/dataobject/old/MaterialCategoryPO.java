package com.mi.oa.asset.commons.config.infra.database.dataobject.old;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/12/21/03:59
 */
@Data
@TableName("asset_show_type")
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class MaterialCategoryPO implements Serializable {
    /**
     * 物料分类编码 type_code
     */
    private String typeCode;

    /**
     * 物料分类名称 type_name
     */
    private String typeName;

    /**
     * 展示分类ID show_id
     */
    private String showId;

    /**
     * 主键 typedet_id
     */
    private String typedetId;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 分类ID type_id
     */
    private String typeId;

    /**
     * SAP资产分类（中国大陆） acc_name
     */
    private String accName;

    /**
     * SAP资产分类编码（中国大陆） acc_code
     */
    private String accCode;
}