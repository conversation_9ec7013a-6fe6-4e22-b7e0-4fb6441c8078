package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.use.repository.UseUserRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.UseUserPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.UseUserMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【amg_use_user(用途用户)】的数据库操作Service实现
* @createDate 2024-08-23 16:44:30
*/
@Service
public class UseUserRepoImpl extends ServiceImpl<UseUserMapper, UseUserPo> implements UseUserRepo {

}




