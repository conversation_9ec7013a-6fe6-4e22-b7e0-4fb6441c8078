package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.position.entity.Position;
import com.mi.oa.asset.commons.config.domain.position.entity.PositionSpacePark;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.PositionPo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.PositionSpaceParkPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/16 10:42
 */

@Mapper(componentModel = "spring")
public interface PositionRepoConverter extends CommonConverter {

    @Mapping(source = "positionId", target = "id")
    PositionPo toPositionPo(Position position);

    List<PositionPo> toPositionPo(List<Position> positionList);

    @Mapping(source = "id", target = "positionId")
    Position toPosition(PositionPo positionPo);

    List<Position> toPosition(List<PositionPo> positionPoList);

    PositionSpaceParkPo toPositionSpaceParkPo(PositionSpacePark positionSpacePark);

    List<PositionSpaceParkPo> toPositionSpaceParkPoList(List<PositionSpacePark> positionSpaceParks);

    PositionSpacePark toPositionSpacePark(PositionSpaceParkPo positionSpaceParkPo);

    List<PositionSpacePark> toPositionSpaceParkList(List<PositionSpaceParkPo> positionSpaceParkPoList);
}
