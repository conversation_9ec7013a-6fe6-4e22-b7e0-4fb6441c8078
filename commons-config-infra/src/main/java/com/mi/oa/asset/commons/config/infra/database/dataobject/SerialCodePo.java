package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/9/27 18:22
 */

@Data
@TableName(value = "amg_serial_code")
public class SerialCodePo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * 编码前缀
     */
    @TableField(value = "code_prefix")
    private String codePrefix;

    /**
     * 开始值
     */
    @TableField(value = "start_num")
    private Integer startNum;

    /**
     * 索引
     */
    @TableField(value = "index_code")
    private String indexCode;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}