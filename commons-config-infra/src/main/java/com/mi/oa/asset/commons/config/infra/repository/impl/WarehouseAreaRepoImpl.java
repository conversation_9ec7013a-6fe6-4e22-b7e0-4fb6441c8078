package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.WarehouseArea;
import com.mi.oa.asset.commons.config.domain.warehouse.repository.WarehouseAreaRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.WarehouseAreaPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.WarehouseAreaMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.WarehouseAreaConvertor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
public class WarehouseAreaRepoImpl extends ServiceImpl<WarehouseAreaMapper, WarehouseAreaPo> implements WarehouseAreaRepo {
    @Resource
    private WarehouseAreaConvertor converter;

    @Override
    public Integer saveWarehouse(WarehouseArea warehouseArea) {
        String warehouseCode = warehouseArea.getWarehouseCode();
        LambdaQueryWrapper<WarehouseAreaPo> queryWrapper = Wrappers.lambdaQuery(WarehouseAreaPo.class)
                .eq(WarehouseAreaPo::getWarehouseCode, warehouseCode)
                .eq(warehouseArea.getCountryId() != null, WarehouseAreaPo::getCountryId, warehouseArea.getCountryId())
                .eq(warehouseArea.getProvinceId() != null, WarehouseAreaPo::getProvinceId, warehouseArea.getProvinceId())
                .eq(warehouseArea.getCityId() != null, WarehouseAreaPo::getCityId, warehouseArea.getCityId())
                .eq(warehouseArea.getAreaId() != null, WarehouseAreaPo::getAreaId, warehouseArea.getAreaId())
                .eq(warehouseArea.getStreetId() != null, WarehouseAreaPo::getStreetId, warehouseArea.getStreetId());
        WarehouseAreaPo existingPo = this.getOne(queryWrapper);
        if (existingPo == null) {
            WarehouseAreaPo po = converter.toIndiaWarehousePo(warehouseArea);
            this.save(po);
            warehouseArea.setId(po.getId());
            return po.getId();
        } else {
            WarehouseAreaPo po = converter.toIndiaWarehousePo(warehouseArea);
            po.setId(existingPo.getId());
            this.updateById(po);
            warehouseArea.setId(existingPo.getId());
            return existingPo.getId();
        }
    }

    @Override
    public void removeWarehouse(String warehouseCode, String countryId, String provinceId, String cityId, String areaId, String streetId) {
        LambdaQueryWrapper<WarehouseAreaPo> queryWrapper = Wrappers.lambdaQuery(WarehouseAreaPo.class)
                .eq(WarehouseAreaPo::getWarehouseCode, warehouseCode);
        if (countryId != null && !countryId.isEmpty()) {
            queryWrapper.eq(WarehouseAreaPo::getCountryId, countryId);
        }
        if (provinceId != null && !provinceId.isEmpty()) {
            queryWrapper.eq(WarehouseAreaPo::getProvinceId, provinceId);
        }
        if (cityId != null && !cityId.isEmpty()) {
            queryWrapper.eq(WarehouseAreaPo::getCityId, cityId);
        }
        if (areaId != null && !areaId.isEmpty()) {
            queryWrapper.eq(WarehouseAreaPo::getAreaId, areaId);
        }
        if (streetId != null && !streetId.isEmpty()) {
            queryWrapper.eq(WarehouseAreaPo::getStreetId, streetId);
        }
        this.remove(queryWrapper);
    }
    /**
     * 获取仓库区域列表
     * @param businessLines 业务线，必传
     * @param countryId 国家id，可选
     * @param provinceId 省id，可选
     * @param cityId 市id，可选
     * @param areaId 区id，可选
     * @param streetId 街道id，可选
     * @param areaName 区域名称，可选
     * @return
     */
    @Override
    public List<WarehouseArea> listByBizAndRegion(List<String> businessLines,String countryId, String provinceId, String cityId, String areaId, String streetId, String areaName) {
        LambdaQueryWrapper<WarehouseAreaPo> queryWrapper = Wrappers.lambdaQuery(WarehouseAreaPo.class)
                .in(WarehouseAreaPo::getBusinessLine, businessLines);
        if (countryId != null && !countryId.isEmpty()) {
            queryWrapper.eq(WarehouseAreaPo::getCountryId, countryId);
        }
        if (provinceId != null && !provinceId.isEmpty()) {
            queryWrapper.eq(WarehouseAreaPo::getProvinceId, provinceId);
        }
        if (cityId != null && !cityId.isEmpty()) {
            queryWrapper.eq(WarehouseAreaPo::getCityId, cityId);
        }
        if (areaId != null && !areaId.isEmpty()) {
            queryWrapper.eq(WarehouseAreaPo::getAreaId, areaId);
        }
        if (streetId != null && !streetId.isEmpty()) {
            queryWrapper.eq(WarehouseAreaPo::getStreetId, streetId);
        }
        if (areaName != null && !areaName.isEmpty()) {
            queryWrapper.like(WarehouseAreaPo::getAreaName, areaName);
        }
        List<WarehouseAreaPo> warehouseAreaPos = this.list(queryWrapper);
        return  converter.toDoList(warehouseAreaPos);
    }

    /**
     * 根据关键词（仓编码或仓名称）模糊查询仓库区域，支持业务线过滤
     */
    @Override
    public List<WarehouseArea> searchWarehouseArea(String keyword, List<String> businessLines) {
        LambdaQueryWrapper<WarehouseAreaPo> queryWrapper = Wrappers.lambdaQuery(WarehouseAreaPo.class);
        if (businessLines != null && !businessLines.isEmpty()) {
            queryWrapper.in(WarehouseAreaPo::getBusinessLine, businessLines);
        }
        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.and(qw -> qw.like(WarehouseAreaPo::getWarehouseCode, keyword)
                    .or()
                    .like(WarehouseAreaPo::getWarehouseName, keyword));
        }
        List<WarehouseAreaPo> warehouseAreaPos = this.list(queryWrapper);
        return converter.toDoList(warehouseAreaPos);
    }

}
