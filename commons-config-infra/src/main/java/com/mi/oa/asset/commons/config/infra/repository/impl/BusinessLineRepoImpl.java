package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.domain.common.repository.BusinessLineRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.BusinessLinePo;
import com.mi.oa.asset.commons.config.infra.database.mapper.BusinessLineMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.BusinessLinePoConvertor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/31 19:32
 */

@Service
public class BusinessLineRepoImpl extends ServiceImpl<BusinessLineMapper, BusinessLinePo> implements BusinessLineRepo {


    @Autowired
    private BusinessLinePoConvertor businessLinePoConvertor;

    @Override
    public BusinessLineDo searchByBusinessLine(String businessLine) {
        BusinessLinePo businessLinePo = baseMapper.selectOne(new LambdaQueryWrapper<BusinessLinePo>()
                .eq(BusinessLinePo::getBusinessLine, businessLine));
        return businessLinePoConvertor.poToDo(businessLinePo);
    }

    @Override
    public List<BusinessLineDo> batchSearchByBusinessLine(List<String> businessLine) {
        // 如果businessLine 为空就返回空，否则查询
        if (CollectionUtils.isEmpty(businessLine)) {
            return Collections.emptyList();
        }
        List<BusinessLinePo> businessLinePos = baseMapper.selectList(new LambdaQueryWrapper<BusinessLinePo>()
                .in(BusinessLinePo::getBusinessLine, businessLine));
        return businessLinePoConvertor.poToDoList(businessLinePos);
    }

    @Override
    public BusinessLineDo searchById(Integer businessLineId) {
        BusinessLinePo businessLinePo = baseMapper.selectById(businessLineId);
        return businessLinePoConvertor.poToDo(businessLinePo);
    }

    @Override
    public Integer save(BusinessLineDo businessLineDo) {
        BusinessLinePo businessLinePo = businessLinePoConvertor.doToPo(businessLineDo);
        this.saveOrUpdate(businessLinePo);
        return businessLinePo.getId();
    }

    // 查询全部业务线
    @Override
    public List<BusinessLineDo> searchAll() {
        List<BusinessLinePo> businessLinePos = baseMapper.selectList(new LambdaQueryWrapper<>());
        return businessLinePoConvertor.poToDoList(businessLinePos);
    }

    @Override
    public List<BusinessLineDo> getBusinessLine(List<String> manageLines) {
        if (CollectionUtils.isEmpty(manageLines)) return Collections.emptyList();
        List<BusinessLinePo> businessLinePos = baseMapper.selectList(Wrappers.lambdaQuery(BusinessLinePo.class)
                .in(BusinessLinePo::getManageLineCode, manageLines));
        return businessLinePoConvertor.poToDoList(businessLinePos);
    }
}
