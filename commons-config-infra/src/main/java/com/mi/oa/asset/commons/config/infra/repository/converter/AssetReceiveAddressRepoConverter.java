package com.mi.oa.asset.commons.config.infra.repository.converter;


import com.mi.oa.asset.commons.config.domain.address.entity.AssetReceiveAddress;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetReceiveAddressPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 *  converter 转换器
 * <AUTHOR>
 * @date 2024-04-08 11:14:07
 */
@Mapper(componentModel = "spring")
public interface AssetReceiveAddressRepoConverter extends CommonConverter {

    AssetReceiveAddress toAssetReceiveAddress(AssetReceiveAddressPo source);

    AssetReceiveAddressPo toAssetReceiveAddressPo(AssetReceiveAddress source);

    List<AssetReceiveAddress> toAssetReceiveAddresss(List<AssetReceiveAddressPo> source);
}