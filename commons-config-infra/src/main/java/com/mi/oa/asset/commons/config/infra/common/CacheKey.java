package com.mi.oa.asset.commons.config.infra.common;

import com.mi.oa.infra.oaucf.redis.key.OACacheKey;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/9/11 16:50
 */

@Getter
public enum <PERSON>acheKey implements OACacheKey {

    ALL_PURCHASE_CATALOG("ALL_PURCHASE_CATALOG", 12 * 3600, "全量采购目录"),
    ALL_ASSET_CATEGORY("ALL_PURCHASE_CATALOG:{0}", 3600, "资产分类"),
    ALL_SAP_ASSET_CATEGORY("ALL_SAP_ASSET_CATEGORY", 24 * 3600, "全部SAP资产分类"),
    ALL_SAP_ASSET_CATEGORY_EAM("ALL_SAP_ASSET_CATEGORY_EAM", 24 * 3600, "全部SAP资产分类EAM"),
    ALL_DEPARTMENT("ALL_DEPARTMENT", 12 * 3600, "全部部门"),
    ORGANIZATION_STRUCTURE_LEVEL("ORGANIZATION_STRUCTURE_LEVEL_{0}", 12 * 3600, "组织架构-按层级"),
    ORGANIZATION_STRUCTURE_LEVEL_MAX("ORGANIZATION_STRUCTURE_LEVEL_MAX", 0, "组织架构-最大层级"),
    ORGANIZATION_STRUCTURE_FULL_NAME("ORGANIZATION_STRUCTURE_FULL_NAME:{0}", 0, "组织架构-全路径名"),
    ORGANIZATION_STRUCTURE_FULL_NAME_EN("ORGANIZATION_STRUCTURE_FULL_NAME_EN:{0}", 0, "组织架构-英文全路径名"),
    ORGANIZATION_STRUCTURE_FULL_NAME_BY_NAME("ORGANIZATION_STRUCTURE_FULL_NAME_BY_NAME:{0}", 0, "组织架构-全路径名(名字为key)"),
    KEY_DEPARTMENT("KEY_DEPARTMENT_{0}", 3600, "部分部门查询"),
    USE_DEPT_NAME_SHOW_WAY("UDP_SHOW_WAY_{0}", 3600, "每个业务线使用部门名称的展示方式"),
    MANAGE_DEPT_NAME_SHOW_WAY("MDP_SHOW_WAY_{0}", 3600, "每个业务线管理部门名称的展示方式"),
    ;

    private String key;

    private int second;

    private String comment;

     CacheKey(String key, int second, String comment) {
        this.key = key;
        this.second = second;
        this.comment = comment;
    }

    @Override
    public String getKey() {
        return key;
    }

    @Override
    public void setKey(String key) {
        this.key = key;
    }

    @Override
    public int getSecond() {
        return second;
    }

    @Override
    public void setSecond(int second) {
        this.second = second;
    }

    @Override
    public String getComment() {
        return comment;
    }

    @Override
    public void setComment(String comment) {
        this.comment = comment;
    }
}
