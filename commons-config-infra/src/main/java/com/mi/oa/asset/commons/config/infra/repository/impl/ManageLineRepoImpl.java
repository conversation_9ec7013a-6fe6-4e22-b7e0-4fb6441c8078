package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.common.entity.ManageLineDo;
import com.mi.oa.asset.commons.config.domain.common.repository.ManageLineRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.ManageLinePo;
import com.mi.oa.asset.commons.config.infra.database.mapper.ManageLinePoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.ManageLinePoConvertor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/16 17:19
 * @desciption: 管理线仓储层实现
 */

@Service
public class ManageLineRepoImpl extends ServiceImpl<ManageLinePoMapper, ManageLinePo> implements ManageLineRepo {

    @Resource
    private ManageLinePoMapper manageLinePoMapper;

    @Resource
    private ManageLinePoConvertor manageLinePoConvertor;


    @Override
    public List<ManageLineDo> getManageLine() {
        // 查询全部管理线
        return getManageLine(null);
    }

    @Override
    public List<ManageLineDo> getManageLine(List<String> manageLines) {
        List<ManageLinePo> manageLinePos = manageLinePoMapper.selectList(Wrappers.lambdaQuery(ManageLinePo.class)
                .in(CollectionUtils.isNotEmpty(manageLines), ManageLinePo::getManageLine, manageLines));
        return manageLinePoConvertor.poToDoList(manageLinePos);
    }
}
