package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/4/07 14:14
 */

@Data
@TableName(value = "amg_global_variable")
public class GlobalValPo extends BasePo {

    @TableField(value = "use_way")
    private Integer useWay;

    @TableField(value = "type")
    private Integer type;

    @TableField(value = "code")
    private String code;

    @TableField(value = "value")
    private String value;

    @TableField(value = "en_value")
    private String enValue;

    @TableField(value = "group_code")
    private String groupCode;

    @TableField(value = "group_name")
    private String groupName;

    @TableField(value = "manage_type")
    private Integer manageType;

    @TableField(value = "business_line")
    private String businessLine;

    @TableField(value = "remark")
    private String remark;


}
