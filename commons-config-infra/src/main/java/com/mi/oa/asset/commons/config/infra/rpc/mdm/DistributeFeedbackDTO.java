package com.mi.oa.asset.commons.config.infra.rpc.mdm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.JsonAdapter;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * @Desc MDM 数据分发后回调接口数据定义
 */

@Data
@Builder
public class DistributeFeedbackDTO {

    private String modelId;

    private String modelName;

    @Setter(AccessLevel.NONE)
    @Builder.Default
    @JsonProperty("systemName")
    private String sys = "EAM2";

    private List<Item> data;

    @Data
    @Builder
    public static class Item {

        private String masterId;

        private String bussId;

        @JsonAdapter(BooleanTo01Adapter.class)
        @JsonProperty("status")
        private boolean success;

        @Builder.Default
        private String message = "SUCCESS";

        @Builder.Default
        @JsonProperty("sendtime")
        private Date sendTime = new Date();
    }
}
