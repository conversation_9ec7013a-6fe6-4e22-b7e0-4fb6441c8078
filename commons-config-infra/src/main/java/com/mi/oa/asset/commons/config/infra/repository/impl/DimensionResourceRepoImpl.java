package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.dimension.entity.DimensionResource;
import com.mi.oa.asset.commons.config.domain.dimension.repository.DimensionResourceRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.DimensionResourcePo;
import com.mi.oa.asset.commons.config.infra.database.mapper.DimensionResourceMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.DimensionResourceRepoConvertor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/24 16:03
 * @description
 */
@Service
public class DimensionResourceRepoImpl extends ServiceImpl<DimensionResourceMapper, DimensionResourcePo> implements DimensionResourceRepo {
    @Resource
    private DimensionResourceRepoConvertor resourceRepoConvertor;
    @Override
    public DimensionResource getByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        DimensionResourcePo dimensionResourcePo = this.getOne(new LambdaQueryWrapper<DimensionResourcePo>()
                .eq(DimensionResourcePo::getDimensionCode,code));
        return resourceRepoConvertor.poToDo(dimensionResourcePo);
    }
}
