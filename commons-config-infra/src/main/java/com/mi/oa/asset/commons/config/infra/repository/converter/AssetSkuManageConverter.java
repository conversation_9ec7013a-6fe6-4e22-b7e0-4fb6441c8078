package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSkuManage;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetSkuManagePo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AssetSkuManageConverter extends CommonConverter {
    AssetSkuManagePo toManagePo(AssetSkuManage source);

    List<AssetSkuManagePo> toManagePoList(List<AssetSkuManage> source);

    AssetSkuManage toManageDo(AssetSkuManagePo source);

    List<AssetSkuManage> toManageDoList(List<AssetSkuManagePo> source);
}
