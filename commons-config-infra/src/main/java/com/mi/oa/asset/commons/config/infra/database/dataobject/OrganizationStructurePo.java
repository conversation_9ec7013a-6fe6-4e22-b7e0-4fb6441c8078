package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2023/9/18 15:13
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "amg_organization_structure")
public class OrganizationStructurePo extends BasePo {
    /**
     * 组织单元编码
     */
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 组织单元名称
     */
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 组织单元英文名称
     */
    @TableField(value = "org_name_en")
    private String orgNameEn;

    /**
     * 上级编码
     */
    @TableField(value = "parent_code")
    private String parentCode;

    /**
     * 默认成本中心
     */
    @TableField(value = "default_cost_center")
    private String defaultCostCenter;

    /**
     * 是否虚拟组织
     */
    @TableField(value = "is_virtual")
    private Integer isVirtual;

    /**
     * 级别
     */
    @TableField(value = "level")
    private Integer level;

    /**
     * 业务线
     */
    @TableField(value = "business_line")
    private String businessLine;

    /**
     * 禁用
     */
    @TableField(value = "disabled")
    private Integer disabled;

    /**
     * 数据来源, 默认手动录入
     */
    @TableField(value = "data_source")
    private String dataSource;
}