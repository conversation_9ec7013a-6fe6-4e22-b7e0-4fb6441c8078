package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 国家和业务线关系表
 * @TableName amg_country_business_line
 */
@TableName(value ="amg_country_business_line")
@Data
public class CountryBusinessLinePo {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 国家/地区代码（3位）
     */
    private String countryCodeAlphaThree;

    /**
     * 国家/地区代码（2位）
     */
    private String countryCodeAlphaTwo;

    /**
     * 国家/地区名称
     */
    private String countryName;

    /**
     * 国家/地区名称（英文）
     */
    private String countryEnglishName;

    /**
     * 地址库id（地址库中国家/地区 一级地区 对应的id
     */
    private Integer provinceId;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 国家数据表id
     */
    private Integer countryConfigId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 默认语言
     */
    private String defaultLanguage;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建人用户名
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新人用户名
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}