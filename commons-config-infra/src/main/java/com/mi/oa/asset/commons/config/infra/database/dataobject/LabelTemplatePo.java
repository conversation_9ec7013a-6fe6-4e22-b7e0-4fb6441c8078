package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description 标签模版表
 * <AUTHOR>
 * @date 2025-02-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_label_template")
public class LabelTemplatePo extends BasePo implements Serializable {

    /**
     * 模版类型 业务模板：business、系统模板：system
     */
    private String templateType;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 是否默认模版，0-否，1-是
     */
    private Integer isDefault;

    /**
     * 是否启用，0-否，1-是
     */
    private Integer isActive;

    /**
     * 扫码展示字段
     */
    private String scanDisplayField;

    /**
     * 是否展示编码
     */
    private Integer isShow;

    /**
     * 扩展配置
     */
    private String extConf;

    /**
     * 数据来源
     */
    private String dataSource;
}