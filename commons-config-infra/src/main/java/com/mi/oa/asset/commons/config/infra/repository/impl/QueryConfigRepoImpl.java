package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.queryfield.entity.QueryConfig;
import com.mi.oa.asset.commons.config.domain.queryfield.repository.QueryConfigRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.QueryConfigPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.QueryConfigPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.QueryFieldRepoConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant.COMMA;

/**
 * <AUTHOR>
 * @date 2024/1/8 11:17
 */

@Service
public class QueryConfigRepoImpl extends ServiceImpl<QueryConfigPoMapper, QueryConfigPo> implements QueryConfigRepo {

    @Resource
    QueryFieldRepoConverter queryFieldConverter;

    @Override
    public List<QueryConfig> queryConfigList(String manageLine, String funId, String userName) {
        List<String> manageLineList = StringUtils.isEmpty(manageLine) ? Collections.emptyList() : Arrays.asList(manageLine.split(COMMA));
        List<QueryConfigPo> queryConfigPos = list(new LambdaQueryWrapper<QueryConfigPo>()
                .in(!CollectionUtils.isEmpty(manageLineList), QueryConfigPo::getManageLine, manageLineList)
                .eq(QueryConfigPo::getFunId, funId)
                .eq(QueryConfigPo::getCreateUser, userName));
        return queryFieldConverter.queryConfigPoToDoList(queryConfigPos);
    }

    @Override
    public QueryConfig queryConfigByName(String manageLine, String funId, String userName, String queryName) {
        List<String> manageLineList = StringUtils.isEmpty(manageLine) ? Collections.emptyList() : Arrays.asList(manageLine.split(COMMA));
        QueryConfigPo queryConfigPo = getOne(new LambdaQueryWrapper<QueryConfigPo>()
                .in(!CollectionUtils.isEmpty(manageLineList), QueryConfigPo::getManageLine, manageLineList)
                .eq(QueryConfigPo::getFunId, funId)
                .eq(QueryConfigPo::getCreateUser, userName).eq(QueryConfigPo::getQueryName, queryName)
                .orderByDesc(QueryConfigPo::getUpdateTime).last(" limit 1"));

        return queryFieldConverter.queryConfigPoToDo(queryConfigPo);
    }

    @Override
    public void saveQueryConfig(QueryConfig queryConfig) {
        QueryConfigPo queryConfigPo = queryFieldConverter.queryConfigToPo(queryConfig);
        saveOrUpdate(queryConfigPo);
        queryConfig.setQueryId(queryConfigPo.getId());
    }

    @Override
    public void deleteQueryConfig(Integer queryId) {
        removeById(queryId);
    }
}
