package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/7/10 13:26
 * 
 */
/**
    * 资产调拨配置表
    */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "amg_allot_config")
public class AllotConfigPo extends BasePo {
    /**
     * 业务线Id
     */
    @TableField(value = "business_line_id")
    private Integer businessLineId;

    /**
     * 字段编码
     */
    @TableField(value = "field_code")
    private String fieldCode;

    /**
     * 字段属性
     */
    @TableField(value = "field_property")
    private String fieldProperty;

    /**
     * 数据范围
     */
    @TableField(value = "data_rage")
    private String dataRage;

    /**
     * 限定内容
     */
    @TableField(value = "limit_biz_data")
    private String limitBizData;

    /**
     * 排序
     */
    private Integer sort;
}