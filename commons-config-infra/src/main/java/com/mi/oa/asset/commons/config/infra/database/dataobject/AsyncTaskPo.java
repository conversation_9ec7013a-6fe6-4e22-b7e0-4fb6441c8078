package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/7/18 10:52
 * 
 */
/**
    * 异步任务管理表
    */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "amg_async_task")
public class AsyncTaskPo extends BasePo {
    /**
     * 任务执行状态
     */
    @TableField(value = "execution_status")
    private String executionStatus;

    /**
     * 任务类型
     */
    @TableField(value = "task_type")
    private String taskType;

    /**
     * 业务线
     */
    @TableField(value = "business_line")
    private String businessLine;

    /**
     * 任务编号
     */
    @TableField(value = "record_no")
    private String recordNo;

    /**
     * 执行结果
     */
    @TableField(value = "execution_result")
    private String executionResult;

    /**
     * 原始文件链接
     */
    @TableField(value = "origin_file_url")
    private String originFileUrl;

    /**
     * 错误结果链接
     */
    @TableField(value = "result_file_url")
    private String resultFileUrl;
}