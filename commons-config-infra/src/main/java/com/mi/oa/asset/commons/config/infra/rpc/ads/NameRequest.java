/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.EncodingUtils;
import org.mi.thrift.TException;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2020-5-26")
public class NameRequest implements org.mi.thrift.TBase<NameRequest, NameRequest._Fields>, java.io.Serializable, Cloneable, Comparable<NameRequest> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("NameRequest");

    private static final org.mi.thrift.protocol.TField LEVEL_FIELD_DESC = new org.mi.thrift.protocol.TField("level", org.mi.thrift.protocol.TType.I32, (short) 1);
    private static final org.mi.thrift.protocol.TField NAMES_FIELD_DESC = new org.mi.thrift.protocol.TField("names", org.mi.thrift.protocol.TType.LIST, (short) 2);
    private static final org.mi.thrift.protocol.TField ENABLE_FIELD_DESC = new org.mi.thrift.protocol.TField("enable", org.mi.thrift.protocol.TType.STRING, (short) 3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();

    static {
        schemes.put(StandardScheme.class, new NameRequestStandardSchemeFactory());
        schemes.put(TupleScheme.class, new NameRequestTupleSchemeFactory());
    }

    public int level; // required
    public List<String> names; // required
    public String enable; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        LEVEL((short) 1, "level"),
        NAMES((short) 2, "names"),
        ENABLE((short) 3, "enable");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
            for (_Fields field : EnumSet.allOf(_Fields.class)) {
                byName.put(field.getFieldName(), field);
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch (fieldId) {
                case 1: // LEVEL
                    return LEVEL;
                case 2: // NAMES
                    return NAMES;
                case 3: // ENABLE
                    return ENABLE;
                default:
                    return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
            _Fields fields = findByThriftId(fieldId);
            if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
            return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
            return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
            _thriftId = thriftId;
            _fieldName = fieldName;
        }

        public short getThriftFieldId() {
            return _thriftId;
        }

        public String getFieldName() {
            return _fieldName;
        }
    }

    // isset id assignments
    private static final int __LEVEL_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;

    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.LEVEL, new org.mi.thrift.meta_data.FieldMetaData("level", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
        tmpMap.put(_Fields.NAMES, new org.mi.thrift.meta_data.FieldMetaData("names", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.ListMetaData(org.mi.thrift.protocol.TType.LIST,
                        new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING))));
        tmpMap.put(_Fields.ENABLE, new org.mi.thrift.meta_data.FieldMetaData("enable", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(NameRequest.class, metaDataMap);
    }

    public NameRequest() {
    }

    public NameRequest(
            int level,
            List<String> names,
            String enable) {
        this();
        this.level = level;
        setLevelIsSet(true);
        this.names = names;
        this.enable = enable;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public NameRequest(NameRequest other) {
        __isset_bitfield = other.__isset_bitfield;
        this.level = other.level;
        if (other.isSetNames()) {
            List<String> __this__names = new ArrayList<String>(other.names);
            this.names = __this__names;
        }
        if (other.isSetEnable()) {
            this.enable = other.enable;
        }
    }

    public NameRequest deepCopy() {
        return new NameRequest(this);
    }

    @Override
    public void clear() {
        setLevelIsSet(false);
        this.level = 0;
        this.names = null;
        this.enable = null;
    }

    public int getLevel() {
        return this.level;
    }

    public NameRequest setLevel(int level) {
        this.level = level;
        setLevelIsSet(true);
        return this;
    }

    public void unsetLevel() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LEVEL_ISSET_ID);
    }

    /** Returns true if field level is set (has been assigned a value) and false otherwise */
    public boolean isSetLevel() {
        return EncodingUtils.testBit(__isset_bitfield, __LEVEL_ISSET_ID);
    }

    public void setLevelIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LEVEL_ISSET_ID, value);
    }

    public int getNamesSize() {
        return (this.names == null) ? 0 : this.names.size();
    }

    public Iterator<String> getNamesIterator() {
        return (this.names == null) ? null : this.names.iterator();
    }

    public void addToNames(String elem) {
        if (this.names == null) {
            this.names = new ArrayList<String>();
        }
        this.names.add(elem);
    }

    public List<String> getNames() {
        return this.names;
    }

    public NameRequest setNames(List<String> names) {
        this.names = names;
        return this;
    }

    public void unsetNames() {
        this.names = null;
    }

    /** Returns true if field names is set (has been assigned a value) and false otherwise */
    public boolean isSetNames() {
        return this.names != null;
    }

    public void setNamesIsSet(boolean value) {
        if (!value) {
            this.names = null;
        }
    }

    public String getEnable() {
        return this.enable;
    }

    public NameRequest setEnable(String enable) {
        this.enable = enable;
        return this;
    }

    public void unsetEnable() {
        this.enable = null;
    }

    /** Returns true if field enable is set (has been assigned a value) and false otherwise */
    public boolean isSetEnable() {
        return this.enable != null;
    }

    public void setEnableIsSet(boolean value) {
        if (!value) {
            this.enable = null;
        }
    }

    public void setFieldValue(_Fields field, Object value) {
        switch (field) {
            case LEVEL:
                if (value == null) {
                    unsetLevel();
                } else {
                    setLevel((Integer) value);
                }
                break;

            case NAMES:
                if (value == null) {
                    unsetNames();
                } else {
                    setNames((List<String>) value);
                }
                break;

            case ENABLE:
                if (value == null) {
                    unsetEnable();
                } else {
                    setEnable((String) value);
                }
                break;

        }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
            case LEVEL:
                return Integer.valueOf(getLevel());

            case NAMES:
                return getNames();

            case ENABLE:
                return getEnable();

        }
        throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
        if (field == null) {
            throw new IllegalArgumentException();
        }

        switch (field) {
            case LEVEL:
                return isSetLevel();
            case NAMES:
                return isSetNames();
            case ENABLE:
                return isSetEnable();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
            return false;
        if (that instanceof NameRequest)
            return this.equals((NameRequest) that);
        return false;
    }

    public boolean equals(NameRequest that) {
        if (that == null)
            return false;

        boolean this_present_level = true;
        boolean that_present_level = true;
        if (this_present_level || that_present_level) {
            if (!(this_present_level && that_present_level))
                return false;
            if (this.level != that.level)
                return false;
        }

        boolean this_present_names = true && this.isSetNames();
        boolean that_present_names = true && that.isSetNames();
        if (this_present_names || that_present_names) {
            if (!(this_present_names && that_present_names))
                return false;
            if (!this.names.equals(that.names))
                return false;
        }

        boolean this_present_enable = true && this.isSetEnable();
        boolean that_present_enable = true && that.isSetEnable();
        if (this_present_enable || that_present_enable) {
            if (!(this_present_enable && that_present_enable))
                return false;
            if (!this.enable.equals(that.enable))
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_level = true;
        list.add(present_level);
        if (present_level)
            list.add(level);

        boolean present_names = true && (isSetNames());
        list.add(present_names);
        if (present_names)
            list.add(names);

        boolean present_enable = true && (isSetEnable());
        list.add(present_enable);
        if (present_enable)
            list.add(enable);

        return list.hashCode();
    }

    @Override
    public int compareTo(NameRequest other) {
        if (!getClass().equals(other.getClass())) {
            return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetLevel()).compareTo(other.isSetLevel());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetLevel()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.level, other.level);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetNames()).compareTo(other.isSetNames());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetNames()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.names, other.names);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetEnable()).compareTo(other.isSetEnable());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetEnable()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.enable, other.enable);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
        return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("NameRequest(");
        boolean first = true;

        sb.append("level:");
        sb.append(this.level);
        first = false;
        if (!first) sb.append(", ");
        sb.append("names:");
        if (this.names == null) {
            sb.append("null");
        } else {
            sb.append(this.names);
        }
        first = false;
        if (!first) sb.append(", ");
        sb.append("enable:");
        if (this.enable == null) {
            sb.append("null");
        } else {
            sb.append(this.enable);
        }
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        try {
            write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        try {
            // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
            __isset_bitfield = 0;
            read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private static class NameRequestStandardSchemeFactory implements SchemeFactory {
        public NameRequestStandardScheme getScheme() {
            return new NameRequestStandardScheme();
        }
    }

    private static class NameRequestStandardScheme extends StandardScheme<NameRequest> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, NameRequest struct) throws TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true) {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                    break;
                }
                switch (schemeField.id) {
                    case 1: // LEVEL
                        if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                            struct.level = iprot.readI32();
                            struct.setLevelIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // NAMES
                        if (schemeField.type == org.mi.thrift.protocol.TType.LIST) {
                            {
                                org.mi.thrift.protocol.TList _list8 = iprot.readListBegin();
                                struct.names = new ArrayList<String>(_list8.size);
                                String _elem9;
                                for (int _i10 = 0; _i10 < _list8.size; ++_i10) {
                                    _elem9 = iprot.readString();
                                    struct.names.add(_elem9);
                                }
                                iprot.readListEnd();
                            }
                            struct.setNamesIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 3: // ENABLE
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.enable = iprot.readString();
                            struct.setEnableIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                        org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, NameRequest struct) throws TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            oprot.writeFieldBegin(LEVEL_FIELD_DESC);
            oprot.writeI32(struct.level);
            oprot.writeFieldEnd();
            if (struct.names != null) {
                oprot.writeFieldBegin(NAMES_FIELD_DESC);
                {
                    oprot.writeListBegin(new org.mi.thrift.protocol.TList(org.mi.thrift.protocol.TType.STRING, struct.names.size()));
                    for (String _iter11 : struct.names) {
                        oprot.writeString(_iter11);
                    }
                    oprot.writeListEnd();
                }
                oprot.writeFieldEnd();
            }
            if (struct.enable != null) {
                oprot.writeFieldBegin(ENABLE_FIELD_DESC);
                oprot.writeString(struct.enable);
                oprot.writeFieldEnd();
            }
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class NameRequestTupleSchemeFactory implements SchemeFactory {
        public NameRequestTupleScheme getScheme() {
            return new NameRequestTupleScheme();
        }
    }

    private static class NameRequestTupleScheme extends TupleScheme<NameRequest> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, NameRequest struct) throws TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetLevel()) {
                optionals.set(0);
            }
            if (struct.isSetNames()) {
                optionals.set(1);
            }
            if (struct.isSetEnable()) {
                optionals.set(2);
            }
            oprot.writeBitSet(optionals, 3);
            if (struct.isSetLevel()) {
                oprot.writeI32(struct.level);
            }
            if (struct.isSetNames()) {
                {
                    oprot.writeI32(struct.names.size());
                    for (String _iter12 : struct.names) {
                        oprot.writeString(_iter12);
                    }
                }
            }
            if (struct.isSetEnable()) {
                oprot.writeString(struct.enable);
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, NameRequest struct) throws TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(3);
            if (incoming.get(0)) {
                struct.level = iprot.readI32();
                struct.setLevelIsSet(true);
            }
            if (incoming.get(1)) {
                {
                    org.mi.thrift.protocol.TList _list13 = new org.mi.thrift.protocol.TList(org.mi.thrift.protocol.TType.STRING, iprot.readI32());
                    struct.names = new ArrayList<String>(_list13.size);
                    String _elem14;
                    for (int _i15 = 0; _i15 < _list13.size; ++_i15) {
                        _elem14 = iprot.readString();
                        struct.names.add(_elem14);
                    }
                }
                struct.setNamesIsSet(true);
            }
            if (incoming.get(2)) {
                struct.enable = iprot.readString();
                struct.setEnableIsSet(true);
            }
        }
    }

}

