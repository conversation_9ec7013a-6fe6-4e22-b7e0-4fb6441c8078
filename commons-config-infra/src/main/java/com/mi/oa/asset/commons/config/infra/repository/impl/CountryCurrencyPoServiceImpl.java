package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryCurrencyDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryCurrencyRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.CountryCurrencyPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.CountryCurrencyPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.CountryCurrencyPoConvertor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 【amg_country_currency(货币配置表)】
* @createDate 2025-04-29 19:34:38
*/
@Service
public class CountryCurrencyPoServiceImpl extends ServiceImpl<CountryCurrencyPoMapper, CountryCurrencyPo>
    implements CountryCurrencyRepo {

    @Resource
    private CountryCurrencyPoConvertor converter;

    @Resource
    private CountryCurrencyPoMapper countryCurrencyPoMapper;
    @Override
    public List<CountryCurrencyDo> searchAll() {
        List<CountryCurrencyPo> countryCurrencyPoList = countryCurrencyPoMapper.selectList(new LambdaQueryWrapper<>());
        return converter.poToDoList(countryCurrencyPoList);
    }

    @Override
    public CountryCurrencyDo getById(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        CountryCurrencyPo po = this.baseMapper.selectById(id);
        return converter.poToDo(po);
    }

    @Override
    public Integer save(CountryCurrencyDo countryCurrencyDo) {
        CountryCurrencyPo po = converter.doToPo(countryCurrencyDo);
        this.save(po);
        return po.getId();
    }

    @Override
    public void updateById(CountryCurrencyDo entity) {
        CountryCurrencyPo po = converter.doToPo(entity);
        updateById(po);
    }

    @Override
    public void deleteByIds(List<Integer> idList) {
        this.baseMapper.deleteBatchIds(idList);
    }

    @Override
    public List<CountryCurrencyDo> getByCountryId(Integer countryId) {
        List<CountryCurrencyPo> countryCurrencyPoList = countryCurrencyPoMapper.selectList(new LambdaQueryWrapper<CountryCurrencyPo>()
                .eq(CountryCurrencyPo::getCountryConfigId, countryId));
        return converter.poToDoList(countryCurrencyPoList);
    }

    @Override
    public CountryCurrencyDo getCountryDefaultCurrency(Integer countryId, int defaultCurrency) {
        CountryCurrencyPo countryCurrencyPo = countryCurrencyPoMapper.selectOne(new LambdaQueryWrapper<CountryCurrencyPo>()
                .eq(CountryCurrencyPo::getCountryConfigId, countryId)
                .eq(CountryCurrencyPo::getDefaultCurrency, defaultCurrency));
        return converter.poToDo(countryCurrencyPo);
    }

    @Override
    public List<CountryCurrencyDo> listCountryDefaultCurrency(List<Integer> countryIdList, Integer defaultCurrency) {
        List<CountryCurrencyPo> countryCurrencyPoList = countryCurrencyPoMapper.selectList(new LambdaQueryWrapper<CountryCurrencyPo>()
                .in(CountryCurrencyPo::getCountryConfigId, countryIdList)
                .eq(CountryCurrencyPo::getDefaultCurrency, defaultCurrency));
        return converter.poToDoList(countryCurrencyPoList);
    }

    @Override
    public List<CountryCurrencyDo> getByCountryCode(String countryCode) {
        List<CountryCurrencyPo> countryCurrencyPoList = countryCurrencyPoMapper.selectList(new LambdaQueryWrapper<CountryCurrencyPo>()
                .eq(CountryCurrencyPo::getCountryCodeAlphaThree, countryCode)
                .or()
                .eq(CountryCurrencyPo::getCountryCodeAlphaTwo, countryCode));
        return converter.poToDoList(countryCurrencyPoList);
    }
}




