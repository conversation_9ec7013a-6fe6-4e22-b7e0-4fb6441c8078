package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.common.repository.AllotConfigRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.AllotConfig;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AllotConfigPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.AllotConfigPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AllotConfigPoConvertor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/10 13:34
 */

@Service
public class AllotConfigRepoImpl extends ServiceImpl<AllotConfigPoMapper, AllotConfigPo> implements AllotConfigRepo {

    @Resource
    private AllotConfigPoMapper allotConfigPoMapper;

    @Resource
    private AllotConfigPoConvertor allocConfigPoConvertor;

    @Override
    public List<AllotConfig> allotConfigList(Integer businessLineId) {
        List<AllotConfigPo> allotConfigPos = allotConfigPoMapper.selectList(new LambdaQueryWrapper<AllotConfigPo>()
                .eq(AllotConfigPo::getBusinessLineId, businessLineId)
                .orderBy(true, true, AllotConfigPo::getSort));
        return allocConfigPoConvertor.poToDoList(allotConfigPos);
    }

    @Override
    public void save(List<AllotConfig> allotConfigList, Integer businessLineId, boolean isCreate) {
        List<AllotConfigPo> allotConfigPos = allotConfigPoMapper.selectList(new LambdaQueryWrapper<AllotConfigPo>().eq(AllotConfigPo::getBusinessLineId, businessLineId));
        Map<String, AllotConfigPo> allotConfigPoMap = allotConfigPos.stream().collect(Collectors.toMap(AllotConfigPo::getFieldCode, Function.identity()));

        for (AllotConfig allotConfig : allotConfigList) {
            if (isCreate) allotConfig.setConfigId(null);
            allotConfig.setBusinessLineId(businessLineId);
            AllotConfigPo allotConfigPo = allotConfigPoMap.get(allotConfig.getFieldCode().getCode());
            if (Objects.nonNull(allotConfigPo)) {
                allotConfig.setConfigId(allotConfigPo.getId());
            }
        }

        this.saveOrUpdateBatch(allocConfigPoConvertor.doToPoList(allotConfigList));
    }


}
