package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2023/9/22 16:26
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "amg_organization_unit")
public class OrganizationUnitPo extends BasePo {
    /**
     * 组织单元编码
     */
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 组织单元名称
     */
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 组织单元名称
     */
    @TableField(value = "org_name_en")
    private String orgNameEn;

    /**
     * 别名
     */
    @TableField(value = "alias_name")
    private String aliasName;

    /**
     * 默认成本中心
     */
    @TableField(value = "default_cost_center")
    private String defaultCostCenter;

    /**
     * 编码全路径，-分隔
     */
    @TableField(value = "org_code_path")
    private String orgCodePath;

    /**
     * 名称全路径，-分隔
     */
    @TableField(value = "org_name_path")
    private String orgNamePath;

    /**
     * 名称全路径，-分隔
     */
    @TableField(value = "org_name_path_en")
    private String orgNamePathEn;

    /**
     * 上级编码
     */
    @TableField(value = "parent_code")
    private String parentCode;

    /**
     * 上级部门名称
     */
    @TableField(value = "parent_name")
    private String parentName;

    /**
     * 级别
     */
    @TableField(value = "level")
    private Integer level;

    /**
     * 是否虚拟组织
     */
    @TableField(value = "is_virtual")
    private Integer isVirtual;

    /**
     * 业务线
     */
    @TableField(value = "business_line")
    private String businessLine;

    /**
     * 是否资产管理组织
     */
    @TableField(value = "is_asset_manage_org")
    private Integer isAssetManageOrg;

    /**
     * 是否资产使用组织
     */
    @TableField(value = "is_asset_use_org")
    private Integer isAssetUseOrg;

    /**
     * 组织类型
     */
    @TableField(value = "org_type")
    private String orgType;

    /**
     * 委托记账主体编码
     */
    @TableField(value = "company_code")
    private String companyCode;

    /**
     * 委托记账主体名称
     */
    @TableField(value = "company_name")
    private String companyName;

    /**
     * 委托记账成本中心
     */
    @TableField(value = "cost_center")
    private String costCenter;

    /**
     * 地址
     */
    @TableField(value = "address")
    private String address;
}