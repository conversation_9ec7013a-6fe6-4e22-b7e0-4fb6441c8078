package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 用途原因
 * @TableName amg_use_reason
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_use_reason")
public class UseReasonPo extends BasePo {

    /**
     * 用途ID
     */
    private Integer useId;

    /**
     * 用途理由
     */
    private String useReason;
}