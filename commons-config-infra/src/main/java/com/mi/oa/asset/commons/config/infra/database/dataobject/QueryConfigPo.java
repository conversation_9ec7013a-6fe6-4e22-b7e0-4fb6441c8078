package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/01/08/11:24
 * 查询方案配置表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_query_config")
public class QueryConfigPo extends BasePo {

    /**
     * 管理线 manage_line
     */
    private String manageLine;

    /**
     * 功能Id fun_id
     */
    private String funId;

    /**
     * 查询方案名称 query_name
     */
    private String queryName;

    /**
     * 查询条件 query_cond
     */
    private String queryCond;
}