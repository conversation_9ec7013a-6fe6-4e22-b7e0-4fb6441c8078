package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/10/13/10:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_business_line")
public class BusinessLinePo extends BasePo {

    /**
     * 业务线 business_line
     */
    private String businessLine;

    /**
     * 业务线名称 business_line_name
     */
    private String businessLineName;

    /**
     * 业务线英文名称 business_line_name_en
     */
    private String businessLineNameEn;

    /**
     * 父管理线主键 father_id
     */
    private Integer fatherId;

    /**
     * 管理线编码 manage_line_code
     */
    private String manageLineCode;

    /**
     * 管理线名称 manage_line_name
     */
    private String manageLineName;

    /**
     * 是否新业务线 is_new_line
     */
    private String isNewLine;

    /**
     * 使用部门名称展示方式 use_dept_name_show_way
     */
    private String useDeptNameShowWay;

    /**
     * 管理部门名称展示方式 manage_dept_name_show_way
     */
    private String manageDeptNameShowWay;

    /**
     * 是否生效 is_effective
     */
    private Integer isEffective;

    /**
     * 委托记账主体是否变动 is_modify_company
     */
    private Integer isModifyCompany;

    /**
     * 资产配置（库存）
     */
    private String configs;
}