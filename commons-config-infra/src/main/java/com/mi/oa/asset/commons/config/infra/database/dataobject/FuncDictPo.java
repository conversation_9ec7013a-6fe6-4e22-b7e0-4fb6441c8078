package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/07 14:14
 */

@Data
@TableName(value = "amg_func_dict")
public class FuncDictPo extends BasePo {

    @TableField(value = "code")
    private String code;

    @TableField(value = "parent_code")
    private String parentCode;

    @TableField(value = "manage_type")
    private Integer manageType;

    @TableField(value = "business_line")
    private String businessLine;

    @TableField(value = "valid")
    private Integer valid;

    @TableField(value = "auth")
    private Integer auth;

    @TableField(value = "func_code")
    private String funcCode;

    @TableField(value = "related_code")
    private String relatedCode;
}
