package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.common.enums.SubmitStatus;
import com.mi.oa.asset.common.enums.YesNo;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRecord;
import com.mi.oa.asset.commons.config.domain.assetshare.enums.ShareValidityType;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.ShareRecordRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.ShareRecordPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.ShareRecordPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.ShareRecordPoConverter;
import com.mi.oa.asset.eam.mybatis.BasePo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 共享记录数据访问接口
 *
 * <AUTHOR>
 * @date 2025-01-07 11:50
 */
@Service
public class ShareRecordRepoImpl extends ServiceImpl<ShareRecordPoMapper, ShareRecordPo> implements ShareRecordRepo {

    @Resource
    private ShareRecordPoConverter converter;

    @Override
    public ShareRecord getById(Integer id) {
        ShareRecordPo shareRecordPo = baseMapper.selectById(id);
        return converter.poToDo(shareRecordPo);
    }


    @Override
    public List<ShareRecord> queryList(List<String> businessLine, String userCode, List<String> orgCodes, String shareScene, String shareClient) {
        if (StringUtils.isBlank(userCode) || CollectionUtils.isEmpty(orgCodes)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ShareRecordPo> lqw = Wrappers.lambdaQuery(ShareRecordPo.class)
                .eq(ShareRecordPo::getIsValid, YesNo.YES.getCode())
                .eq(ShareRecordPo::getRecordStatus, SubmitStatus.SUBMITTED.getCode())
                .nested(q -> q.eq(ShareRecordPo::getValidityType, ShareValidityType.PERMANENT.getCode()).
                        or().gt(ShareRecordPo::getShareEndTime, new Date()))
                .nested(CollectionUtils.isNotEmpty(businessLine), q -> {
                    int size = businessLine.size();
                    for (int i = 0; i < size; i++) {
                        q.like(ShareRecordPo::getBusinessLineCode, businessLine.get(i)).or(i < size - 1);
                    }
                })
                .like(StringUtils.isNotBlank(shareScene), ShareRecordPo::getShareScene, shareScene)
                .like(StringUtils.isNotBlank(shareClient), ShareRecordPo::getShareClient, shareClient)
                .and(w -> w.like(ShareRecordPo::getShareUserCode, userCode).or()
                        .nested(q -> {
                            int size = orgCodes.size();
                            for (int i = 0; i < size; i++) {
                                q.apply(String.format("find_in_set('%s', replace(share_dept_code, ';', ',')) > 0 ", orgCodes.get(i))).or(i < size - 1);
                            }
                        }));
        List<ShareRecordPo> recordPos = baseMapper.selectList(lqw);
        return converter.poToDoList(recordPos);
    }

    @Override
    public ShareRecord saveShareRecord(ShareRecord shareRecord) {
        if (Objects.nonNull(shareRecord.getId()) && Objects.isNull(shareRecord.getShareStartTime()) && Objects.isNull(shareRecord.getShareEndTime())) {
            // 处理有效期为空时的更新
            this.update(Wrappers.lambdaUpdate(ShareRecordPo.class)
                    .set(ShareRecordPo::getShareStartTime, "0000-00-00 00:00:00")
                    .set(ShareRecordPo::getShareEndTime, "0000-00-00 00:00:00")
                    .eq(BasePo::getId, shareRecord.getId()));
        }
        ShareRecordPo shareRecordPo = converter.doToPo(shareRecord);
        this.saveOrUpdate(shareRecordPo);
        return converter.poToDo(shareRecordPo);
    }

    @Override
    public void removeShareRecord(Integer id) {
        this.removeById(id);
    }
}
