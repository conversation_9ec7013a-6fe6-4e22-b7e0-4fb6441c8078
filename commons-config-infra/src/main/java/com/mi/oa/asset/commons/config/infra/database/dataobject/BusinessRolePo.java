package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/01/15/03:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_business_role_list")
public class BusinessRolePo extends BasePo implements Serializable {
    /**
     * 角色编码 role_code
     */
    private String roleCode;

    /**
     * 角色名称 role_name
     */
    private String roleName;

    /**
     * 角色英文名称 role_name_en
     */
    private String roleNameEn;

    /**
     * 角色描述 role_desc
     */
    private String roleDesc;

    /**
     * 角色英文描述 role_desc_en
     */
    private String roleDescEn;
}