/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.EncodingUtils;
import org.mi.thrift.TException;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;
import org.mi.thrift.server.AbstractNonblockingServer.*;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2020-5-26")
public class StreetRequest implements org.mi.thrift.TBase<StreetRequest, StreetRequest._Fields>, java.io.Serializable, Cloneable, Comparable<StreetRequest> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("StreetRequest");

    private static final org.mi.thrift.protocol.TField STREET_NAME_FIELD_DESC = new org.mi.thrift.protocol.TField("street_name", org.mi.thrift.protocol.TType.STRING, (short) 1);
    private static final org.mi.thrift.protocol.TField ID_FIELD_DESC = new org.mi.thrift.protocol.TField("id", org.mi.thrift.protocol.TType.I64, (short) 2);
    private static final org.mi.thrift.protocol.TField ENABLE_FIELD_DESC = new org.mi.thrift.protocol.TField("enable", org.mi.thrift.protocol.TType.STRING, (short) 3);
    private static final org.mi.thrift.protocol.TField PARENT_ID_FIELD_DESC = new org.mi.thrift.protocol.TField("parent_id", org.mi.thrift.protocol.TType.I64, (short) 4);
    private static final org.mi.thrift.protocol.TField PAGE_FIELD_DESC = new org.mi.thrift.protocol.TField("page", org.mi.thrift.protocol.TType.I32, (short) 5);
    private static final org.mi.thrift.protocol.TField UPDATE_TIME_FIELD_DESC = new org.mi.thrift.protocol.TField("update_time", org.mi.thrift.protocol.TType.STRING, (short) 6);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();

    static {
        schemes.put(StandardScheme.class, new StreetRequestStandardSchemeFactory());
        schemes.put(TupleScheme.class, new StreetRequestTupleSchemeFactory());
    }

    public String street_name; // required
    public long id; // required
    public String enable; // required
    public long parent_id; // required
    public int page; // required
    public String update_time; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        STREET_NAME((short) 1, "street_name"),
        ID((short) 2, "id"),
        ENABLE((short) 3, "enable"),
        PARENT_ID((short) 4, "parent_id"),
        PAGE((short) 5, "page"),
        UPDATE_TIME((short) 6, "update_time");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
            for (_Fields field : EnumSet.allOf(_Fields.class)) {
                byName.put(field.getFieldName(), field);
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch (fieldId) {
                case 1: // STREET_NAME
                    return STREET_NAME;
                case 2: // ID
                    return ID;
                case 3: // ENABLE
                    return ENABLE;
                case 4: // PARENT_ID
                    return PARENT_ID;
                case 5: // PAGE
                    return PAGE;
                case 6: // UPDATE_TIME
                    return UPDATE_TIME;
                default:
                    return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
            _Fields fields = findByThriftId(fieldId);
            if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
            return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
            return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
            _thriftId = thriftId;
            _fieldName = fieldName;
        }

        public short getThriftFieldId() {
            return _thriftId;
        }

        public String getFieldName() {
            return _fieldName;
        }
    }

    // isset id assignments
    private static final int __ID_ISSET_ID = 0;
    private static final int __PARENT_ID_ISSET_ID = 1;
    private static final int __PAGE_ISSET_ID = 2;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;

    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.STREET_NAME, new org.mi.thrift.meta_data.FieldMetaData("street_name", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        tmpMap.put(_Fields.ID, new org.mi.thrift.meta_data.FieldMetaData("id", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I64)));
        tmpMap.put(_Fields.ENABLE, new org.mi.thrift.meta_data.FieldMetaData("enable", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        tmpMap.put(_Fields.PARENT_ID, new org.mi.thrift.meta_data.FieldMetaData("parent_id", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I64)));
        tmpMap.put(_Fields.PAGE, new org.mi.thrift.meta_data.FieldMetaData("page", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
        tmpMap.put(_Fields.UPDATE_TIME, new org.mi.thrift.meta_data.FieldMetaData("update_time", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(StreetRequest.class, metaDataMap);
    }

    public StreetRequest() {
    }

    public StreetRequest(
            String street_name,
            long id,
            String enable,
            long parent_id,
            int page,
            String update_time) {
        this();
        this.street_name = street_name;
        this.id = id;
        setIdIsSet(true);
        this.enable = enable;
        this.parent_id = parent_id;
        setParent_idIsSet(true);
        this.page = page;
        setPageIsSet(true);
        this.update_time = update_time;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public StreetRequest(StreetRequest other) {
        __isset_bitfield = other.__isset_bitfield;
        if (other.isSetStreet_name()) {
            this.street_name = other.street_name;
        }
        this.id = other.id;
        if (other.isSetEnable()) {
            this.enable = other.enable;
        }
        this.parent_id = other.parent_id;
        this.page = other.page;
        if (other.isSetUpdate_time()) {
            this.update_time = other.update_time;
        }
    }

    public StreetRequest deepCopy() {
        return new StreetRequest(this);
    }

    @Override
    public void clear() {
        this.street_name = null;
        setIdIsSet(false);
        this.id = 0;
        this.enable = null;
        setParent_idIsSet(false);
        this.parent_id = 0;
        setPageIsSet(false);
        this.page = 0;
        this.update_time = null;
    }

    public String getStreet_name() {
        return this.street_name;
    }

    public StreetRequest setStreet_name(String street_name) {
        this.street_name = street_name;
        return this;
    }

    public void unsetStreet_name() {
        this.street_name = null;
    }

    /** Returns true if field street_name is set (has been assigned a value) and false otherwise */
    public boolean isSetStreet_name() {
        return this.street_name != null;
    }

    public void setStreet_nameIsSet(boolean value) {
        if (!value) {
            this.street_name = null;
        }
    }

    public long getId() {
        return this.id;
    }

    public StreetRequest setId(long id) {
        this.id = id;
        setIdIsSet(true);
        return this;
    }

    public void unsetId() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ID_ISSET_ID);
    }

    /** Returns true if field id is set (has been assigned a value) and false otherwise */
    public boolean isSetId() {
        return EncodingUtils.testBit(__isset_bitfield, __ID_ISSET_ID);
    }

    public void setIdIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ID_ISSET_ID, value);
    }

    public String getEnable() {
        return this.enable;
    }

    public StreetRequest setEnable(String enable) {
        this.enable = enable;
        return this;
    }

    public void unsetEnable() {
        this.enable = null;
    }

    /** Returns true if field enable is set (has been assigned a value) and false otherwise */
    public boolean isSetEnable() {
        return this.enable != null;
    }

    public void setEnableIsSet(boolean value) {
        if (!value) {
            this.enable = null;
        }
    }

    public long getParent_id() {
        return this.parent_id;
    }

    public StreetRequest setParent_id(long parent_id) {
        this.parent_id = parent_id;
        setParent_idIsSet(true);
        return this;
    }

    public void unsetParent_id() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PARENT_ID_ISSET_ID);
    }

    /** Returns true if field parent_id is set (has been assigned a value) and false otherwise */
    public boolean isSetParent_id() {
        return EncodingUtils.testBit(__isset_bitfield, __PARENT_ID_ISSET_ID);
    }

    public void setParent_idIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PARENT_ID_ISSET_ID, value);
    }

    public int getPage() {
        return this.page;
    }

    public StreetRequest setPage(int page) {
        this.page = page;
        setPageIsSet(true);
        return this;
    }

    public void unsetPage() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGE_ISSET_ID);
    }

    /** Returns true if field page is set (has been assigned a value) and false otherwise */
    public boolean isSetPage() {
        return EncodingUtils.testBit(__isset_bitfield, __PAGE_ISSET_ID);
    }

    public void setPageIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGE_ISSET_ID, value);
    }

    public String getUpdate_time() {
        return this.update_time;
    }

    public StreetRequest setUpdate_time(String update_time) {
        this.update_time = update_time;
        return this;
    }

    public void unsetUpdate_time() {
        this.update_time = null;
    }

    /** Returns true if field update_time is set (has been assigned a value) and false otherwise */
    public boolean isSetUpdate_time() {
        return this.update_time != null;
    }

    public void setUpdate_timeIsSet(boolean value) {
        if (!value) {
            this.update_time = null;
        }
    }

    public void setFieldValue(_Fields field, Object value) {
        switch (field) {
            case STREET_NAME:
                if (value == null) {
                    unsetStreet_name();
                } else {
                    setStreet_name((String) value);
                }
                break;

            case ID:
                if (value == null) {
                    unsetId();
                } else {
                    setId((Long) value);
                }
                break;

            case ENABLE:
                if (value == null) {
                    unsetEnable();
                } else {
                    setEnable((String) value);
                }
                break;

            case PARENT_ID:
                if (value == null) {
                    unsetParent_id();
                } else {
                    setParent_id((Long) value);
                }
                break;

            case PAGE:
                if (value == null) {
                    unsetPage();
                } else {
                    setPage((Integer) value);
                }
                break;

            case UPDATE_TIME:
                if (value == null) {
                    unsetUpdate_time();
                } else {
                    setUpdate_time((String) value);
                }
                break;

        }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
            case STREET_NAME:
                return getStreet_name();

            case ID:
                return Long.valueOf(getId());

            case ENABLE:
                return getEnable();

            case PARENT_ID:
                return Long.valueOf(getParent_id());

            case PAGE:
                return Integer.valueOf(getPage());

            case UPDATE_TIME:
                return getUpdate_time();

        }
        throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
        if (field == null) {
            throw new IllegalArgumentException();
        }

        switch (field) {
            case STREET_NAME:
                return isSetStreet_name();
            case ID:
                return isSetId();
            case ENABLE:
                return isSetEnable();
            case PARENT_ID:
                return isSetParent_id();
            case PAGE:
                return isSetPage();
            case UPDATE_TIME:
                return isSetUpdate_time();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
            return false;
        if (that instanceof StreetRequest)
            return this.equals((StreetRequest) that);
        return false;
    }

    public boolean equals(StreetRequest that) {
        if (that == null)
            return false;

        boolean this_present_street_name = true && this.isSetStreet_name();
        boolean that_present_street_name = true && that.isSetStreet_name();
        if (this_present_street_name || that_present_street_name) {
            if (!(this_present_street_name && that_present_street_name))
                return false;
            if (!this.street_name.equals(that.street_name))
                return false;
        }

        boolean this_present_id = true;
        boolean that_present_id = true;
        if (this_present_id || that_present_id) {
            if (!(this_present_id && that_present_id))
                return false;
            if (this.id != that.id)
                return false;
        }

        boolean this_present_enable = true && this.isSetEnable();
        boolean that_present_enable = true && that.isSetEnable();
        if (this_present_enable || that_present_enable) {
            if (!(this_present_enable && that_present_enable))
                return false;
            if (!this.enable.equals(that.enable))
                return false;
        }

        boolean this_present_parent_id = true;
        boolean that_present_parent_id = true;
        if (this_present_parent_id || that_present_parent_id) {
            if (!(this_present_parent_id && that_present_parent_id))
                return false;
            if (this.parent_id != that.parent_id)
                return false;
        }

        boolean this_present_page = true;
        boolean that_present_page = true;
        if (this_present_page || that_present_page) {
            if (!(this_present_page && that_present_page))
                return false;
            if (this.page != that.page)
                return false;
        }

        boolean this_present_update_time = true && this.isSetUpdate_time();
        boolean that_present_update_time = true && that.isSetUpdate_time();
        if (this_present_update_time || that_present_update_time) {
            if (!(this_present_update_time && that_present_update_time))
                return false;
            if (!this.update_time.equals(that.update_time))
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_street_name = true && (isSetStreet_name());
        list.add(present_street_name);
        if (present_street_name)
            list.add(street_name);

        boolean present_id = true;
        list.add(present_id);
        if (present_id)
            list.add(id);

        boolean present_enable = true && (isSetEnable());
        list.add(present_enable);
        if (present_enable)
            list.add(enable);

        boolean present_parent_id = true;
        list.add(present_parent_id);
        if (present_parent_id)
            list.add(parent_id);

        boolean present_page = true;
        list.add(present_page);
        if (present_page)
            list.add(page);

        boolean present_update_time = true && (isSetUpdate_time());
        list.add(present_update_time);
        if (present_update_time)
            list.add(update_time);

        return list.hashCode();
    }

    @Override
    public int compareTo(StreetRequest other) {
        if (!getClass().equals(other.getClass())) {
            return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetStreet_name()).compareTo(other.isSetStreet_name());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetStreet_name()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.street_name, other.street_name);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetId()).compareTo(other.isSetId());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetId()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.id, other.id);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetEnable()).compareTo(other.isSetEnable());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetEnable()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.enable, other.enable);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetParent_id()).compareTo(other.isSetParent_id());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetParent_id()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.parent_id, other.parent_id);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetPage()).compareTo(other.isSetPage());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetPage()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.page, other.page);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetUpdate_time()).compareTo(other.isSetUpdate_time());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetUpdate_time()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.update_time, other.update_time);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
        return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("StreetRequest(");
        boolean first = true;

        sb.append("street_name:");
        if (this.street_name == null) {
            sb.append("null");
        } else {
            sb.append(this.street_name);
        }
        first = false;
        if (!first) sb.append(", ");
        sb.append("id:");
        sb.append(this.id);
        first = false;
        if (!first) sb.append(", ");
        sb.append("enable:");
        if (this.enable == null) {
            sb.append("null");
        } else {
            sb.append(this.enable);
        }
        first = false;
        if (!first) sb.append(", ");
        sb.append("parent_id:");
        sb.append(this.parent_id);
        first = false;
        if (!first) sb.append(", ");
        sb.append("page:");
        sb.append(this.page);
        first = false;
        if (!first) sb.append(", ");
        sb.append("update_time:");
        if (this.update_time == null) {
            sb.append("null");
        } else {
            sb.append(this.update_time);
        }
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        try {
            write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        try {
            // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
            __isset_bitfield = 0;
            read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private static class StreetRequestStandardSchemeFactory implements SchemeFactory {
        public StreetRequestStandardScheme getScheme() {
            return new StreetRequestStandardScheme();
        }
    }

    private static class StreetRequestStandardScheme extends StandardScheme<StreetRequest> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, StreetRequest struct) throws TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true) {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                    break;
                }
                switch (schemeField.id) {
                    case 1: // STREET_NAME
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.street_name = iprot.readString();
                            struct.setStreet_nameIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // ID
                        if (schemeField.type == org.mi.thrift.protocol.TType.I64) {
                            struct.id = iprot.readI64();
                            struct.setIdIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 3: // ENABLE
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.enable = iprot.readString();
                            struct.setEnableIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 4: // PARENT_ID
                        if (schemeField.type == org.mi.thrift.protocol.TType.I64) {
                            struct.parent_id = iprot.readI64();
                            struct.setParent_idIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 5: // PAGE
                        if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                            struct.page = iprot.readI32();
                            struct.setPageIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 6: // UPDATE_TIME
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.update_time = iprot.readString();
                            struct.setUpdate_timeIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                        org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, StreetRequest struct) throws TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            if (struct.street_name != null) {
                oprot.writeFieldBegin(STREET_NAME_FIELD_DESC);
                oprot.writeString(struct.street_name);
                oprot.writeFieldEnd();
            }
            oprot.writeFieldBegin(ID_FIELD_DESC);
            oprot.writeI64(struct.id);
            oprot.writeFieldEnd();
            if (struct.enable != null) {
                oprot.writeFieldBegin(ENABLE_FIELD_DESC);
                oprot.writeString(struct.enable);
                oprot.writeFieldEnd();
            }
            oprot.writeFieldBegin(PARENT_ID_FIELD_DESC);
            oprot.writeI64(struct.parent_id);
            oprot.writeFieldEnd();
            oprot.writeFieldBegin(PAGE_FIELD_DESC);
            oprot.writeI32(struct.page);
            oprot.writeFieldEnd();
            if (struct.update_time != null) {
                oprot.writeFieldBegin(UPDATE_TIME_FIELD_DESC);
                oprot.writeString(struct.update_time);
                oprot.writeFieldEnd();
            }
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class StreetRequestTupleSchemeFactory implements SchemeFactory {
        public StreetRequestTupleScheme getScheme() {
            return new StreetRequestTupleScheme();
        }
    }

    private static class StreetRequestTupleScheme extends TupleScheme<StreetRequest> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, StreetRequest struct) throws TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetStreet_name()) {
                optionals.set(0);
            }
            if (struct.isSetId()) {
                optionals.set(1);
            }
            if (struct.isSetEnable()) {
                optionals.set(2);
            }
            if (struct.isSetParent_id()) {
                optionals.set(3);
            }
            if (struct.isSetPage()) {
                optionals.set(4);
            }
            if (struct.isSetUpdate_time()) {
                optionals.set(5);
            }
            oprot.writeBitSet(optionals, 6);
            if (struct.isSetStreet_name()) {
                oprot.writeString(struct.street_name);
            }
            if (struct.isSetId()) {
                oprot.writeI64(struct.id);
            }
            if (struct.isSetEnable()) {
                oprot.writeString(struct.enable);
            }
            if (struct.isSetParent_id()) {
                oprot.writeI64(struct.parent_id);
            }
            if (struct.isSetPage()) {
                oprot.writeI32(struct.page);
            }
            if (struct.isSetUpdate_time()) {
                oprot.writeString(struct.update_time);
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, StreetRequest struct) throws TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(6);
            if (incoming.get(0)) {
                struct.street_name = iprot.readString();
                struct.setStreet_nameIsSet(true);
            }
            if (incoming.get(1)) {
                struct.id = iprot.readI64();
                struct.setIdIsSet(true);
            }
            if (incoming.get(2)) {
                struct.enable = iprot.readString();
                struct.setEnableIsSet(true);
            }
            if (incoming.get(3)) {
                struct.parent_id = iprot.readI64();
                struct.setParent_idIsSet(true);
            }
            if (incoming.get(4)) {
                struct.page = iprot.readI32();
                struct.setPageIsSet(true);
            }
            if (incoming.get(5)) {
                struct.update_time = iprot.readString();
                struct.setUpdate_timeIsSet(true);
            }
        }
    }

}

