package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.mi.oa.asset.commons.config.domain.common.repository.SerialCodeRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.SerialCode;
import com.mi.oa.asset.commons.config.infra.database.dataobject.SerialCodePo;
import com.mi.oa.asset.commons.config.infra.database.mapper.SerialCodePoMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/9/19 15:03
 */

@Service
public class SerialCodeRepoImpl implements SerialCodeRepo {

    @Resource
    private SerialCodePoMapper serialCodeMapper;

    @Override
    public boolean isSerialCodeExisted(String codePrefix, String indexCode) {
        return new LambdaQueryChainWrapper<>(serialCodeMapper)
                .eq(SerialCodePo::getCodePrefix, codePrefix)
                .eq(StringUtils.isNotBlank(indexCode), SerialCodePo::getIndexCode, indexCode)
                .exists();
    }

    @Override
    public SerialCode getSerialCode(String codePrefix, String indexCode) {
        SerialCodePo data = new LambdaQueryChainWrapper<>(serialCodeMapper)
                .eq(SerialCodePo::getCodePrefix, codePrefix)
                .eq(StringUtils.isNotBlank(indexCode), SerialCodePo::getIndexCode, indexCode)
                .last("for update")
                .one();

        return SerialCode.builder().codePrefix(data.getCodePrefix()).startNum(data.getStartNum()).build();
    }

    @Override
    public void createSerialCode(String codePrefix, String indexCode) {
        SerialCodePo data = new SerialCodePo();
        data.setCodePrefix(codePrefix);
        data.setStartNum(0);
        if(StringUtils.isNotBlank(indexCode)) {
            data.setIndexCode(indexCode);
        }

        serialCodeMapper.insert(data);
    }

    @Override
    public void updateSerialCode(SerialCode serialCode, String indexCode) {
        SerialCodePo data = new SerialCodePo();
        data.setStartNum(serialCode.getStartNum());

        serialCodeMapper.update(
                data,
                Wrappers.lambdaUpdate(SerialCodePo.class)
                        .eq(SerialCodePo::getCodePrefix, serialCode.getCodePrefix())
                        .eq(StringUtils.isNotBlank(indexCode), SerialCodePo::getIndexCode, indexCode)
        );
    }
}
