package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 办公用途
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11 11:29:51
 */
@Getter
@Setter
@TableName("asset_useway")
public class AssetUsewayPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "useway_id", type = IdType.INPUT)
    private String usewayId;
    /**
     * 用途代码
     */
    private String usewayCode;

    /**
     * 备注
     */
    private String usewayMemo;

    /**
     * 用途名称
     */
    private String usewayName;

    /**
     * 用途区别
     */
    private String usewayType;


    /**
     * 添加时间
     */
    private Date addDate;

    /**
     * 添加人ID
     */
    private String addUserid;

    /**
     * 修改时间
     */
    private Date modifyDate;

    /**
     * 修改人ID
     */
    private String modifyUserid;

    /**
     * 系统租户ID
     */
    private String tenantId;

    /**
     * 适用范围
     */
    private String usewayQualif;

    /**
     * 是否生效
     */
    private String isValid;

    /**
     * 排序字段
     */
    private BigDecimal id;

    /**
     * EAM编码
     */
    private String eamCode;

    /**
     * 用途名称英文
     */
    private String usewayNameEn;

    /**
     * 备注英文
     */
    private String usewayMemoEn;


}
