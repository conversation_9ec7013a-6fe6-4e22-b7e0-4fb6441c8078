package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfigDept;
import com.mi.oa.asset.commons.config.domain.common.repository.FunctionConfigDeptRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FunctionConfigDeptPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.FunctionConfigDeptMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.FunctionConfigConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/28 19:07
 **/
@Service
public class FunctionConfigDeptRepoImpl extends ServiceImpl<FunctionConfigDeptMapper, FunctionConfigDeptPo> implements FunctionConfigDeptRepo {

    @Resource
    private FunctionConfigConverter converter;

    @Override
    public void saveAuthDept(List<FunctionConfigDept> configDeptList) {
        if (CollectionUtils.isEmpty(configDeptList)) {
            return;
        }
        List<FunctionConfigDeptPo> deptPos = converter.toDeptPos(configDeptList);
        saveOrUpdateBatch(deptPos);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteAuthDeptByConfigId(Integer configId) {
        if (configId == null) return;
        baseMapper.delete(Wrappers.lambdaQuery(FunctionConfigDeptPo.class).eq(FunctionConfigDeptPo::getConfigId, configId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteAuthDeptByConfigIds(List<Integer> configIds) {
        if (CollectionUtils.isEmpty(configIds)) return;
        baseMapper.delete(Wrappers.lambdaQuery(FunctionConfigDeptPo.class).in(FunctionConfigDeptPo::getConfigId, configIds));
    }

    @Override
    public List<FunctionConfigDept> listByDeptCodes(List<String> allDeptCode) {
        return listByDeptCodeAndId(allDeptCode, null);
    }

    @Override
    public List<FunctionConfigDept> listByDeptCodeAndId(List<String> allDeptCode, List<Integer> configIds) {
        if (CollectionUtils.isEmpty(allDeptCode) && CollectionUtils.isEmpty(configIds)) return Collections.emptyList();
        List<FunctionConfigDeptPo> functionConfigDeptPos = baseMapper.selectList(Wrappers.lambdaQuery(FunctionConfigDeptPo.class)
                .in(CollectionUtils.isNotEmpty(allDeptCode), FunctionConfigDeptPo::getDeptCode, allDeptCode)
                .in(CollectionUtils.isNotEmpty(configIds), FunctionConfigDeptPo::getConfigId, configIds));
        return converter.poToDeptList(functionConfigDeptPos);
    }
}
