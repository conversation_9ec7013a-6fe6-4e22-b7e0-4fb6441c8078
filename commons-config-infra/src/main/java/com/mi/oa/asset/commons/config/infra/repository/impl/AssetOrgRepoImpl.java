package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgType;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.assetorganization.repository.AssetOrgRepo;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgStructure;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgUnitQuery;
import com.mi.oa.asset.commons.config.infra.common.CacheKey;
import com.mi.oa.asset.commons.config.infra.database.dataobject.OrganizationStructurePo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.OrganizationUnitPo;
import com.mi.oa.asset.commons.config.infra.repository.converter.AssetOrgRepoConverter;
import com.mi.oa.asset.commons.config.infra.repository.service.AssetOrgStructureService;
import com.mi.oa.asset.commons.config.infra.repository.service.AssetOrgUnitService;
import com.mi.oa.infra.oaucf.redis.annotation.OACacheSet;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/15 18:08
 */

@Service
public class AssetOrgRepoImpl implements AssetOrgRepo {

    @Resource
    private AssetOrgStructureService orgStructureService;

    @Resource
    private AssetOrgUnitService orgUnitService;

    @Resource
    private AssetOrgRepoConverter converter;

    @Override
    public AssetOrgStructure getOrgStructureByCode(String code, Boolean disabledIgnore) {
        LambdaQueryWrapper<OrganizationStructurePo> wrapper = Wrappers.lambdaQuery(OrganizationStructurePo.class);
        wrapper.eq(OrganizationStructurePo::getOrgCode, code);
        if (!disabledIgnore) {
            wrapper.eq(OrganizationStructurePo::getDisabled, 0);
        }
        wrapper.last("limit 1");

        return converter.toAssetOrgStructure(orgStructureService.getOne(wrapper));
    }

    @Override
    public AssetOrgStructure getOrgStructureByCode(String code) {
        return getOrgStructureByCode(code, false);
    }

    @Override
    public List<AssetOrgStructure> getOrgStructuresByCodes(List<String> orgCodes) {
        List<OrganizationStructurePo> list = orgStructureService.list(
                Wrappers.lambdaQuery(OrganizationStructurePo.class)
                        .in(OrganizationStructurePo::getOrgCode, orgCodes)
                        .eq(OrganizationStructurePo::getIsDeleted, 0)
        );

        return converter.toOrganizationStructures(list);
    }

    @Override
    public AssetOrgUnit getOrgUnitByCode(String code, BusinessLine businessLine) {
        OrganizationUnitPo data = orgUnitService.getOne(
                Wrappers.lambdaQuery(OrganizationUnitPo.class)
                        .eq(OrganizationUnitPo::getOrgCode, code)
                        .eq(null != businessLine, OrganizationUnitPo::getBusinessLine, businessLine.getCode())
                        .last("limit 1")
        );

        return converter.toAssetOrgUnit(data);
    }

    @Override
    public PageData<AssetOrgUnit> getOrgUnitPageByCode(BusinessLine businessLine, PageRequest pageRequest) {
        //分页查询组织结构
        Page<OrganizationUnitPo> page = orgUnitService.page(
                new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize()),
                Wrappers.lambdaQuery(OrganizationUnitPo.class)
                        .eq(OrganizationUnitPo::getBusinessLine, businessLine.getCode())
        );

        return converter.toPageData(page, converter::toAssetOrgUnits);
    }

    @Override
    public List<AssetOrgUnit> getBatchOrgUnitByCode(List<String> code, BusinessLine businessLine) {
        // 根据业务线和组织单元编码查询组织单元
        LambdaQueryWrapper<OrganizationUnitPo> wrapper = Wrappers.lambdaQuery(OrganizationUnitPo.class);
        wrapper.in(OrganizationUnitPo::getOrgCode, code);
        if (null != businessLine) {
            wrapper.eq(OrganizationUnitPo::getBusinessLine, businessLine.getCode());
        }
        return converter.toAssetOrgUnits(orgUnitService.list(wrapper));
    }

    @Override
    public AssetOrgUnit getOrgUnitById(Integer id) {
        return converter.toAssetOrgUnit(orgUnitService.getById(id));
    }

    private void loadParentName(AssetOrgUnit assetOrgUnit) {
        OrganizationUnitPo parentOrgUnit = orgUnitService.getOne(Wrappers.lambdaQuery(OrganizationUnitPo.class)
                .eq(OrganizationUnitPo::getOrgCode, assetOrgUnit.getParentCode())
                .eq(OrganizationUnitPo::getBusinessLine, assetOrgUnit.getBusinessLine().getCode()));
        assetOrgUnit.setParentName(Objects.isNull(parentOrgUnit) ? "" : parentOrgUnit.getOrgName());
    }

    @Override
    public void createOrgUnit(AssetOrgUnit unit) {
        unit.setOrgId(null); // 清空主键
        OrganizationUnitPo data = converter.toOrganizationUnitPo(unit);

        orgUnitService.save(data);
    }

    @Override
    public void updateOrgUnit(AssetOrgUnit unit) {
        orgUnitService.updateById(converter.toOrganizationUnitPo(unit));
    }

    @Override
    public void createOrgStructure(AssetOrgStructure structure) {
        orgStructureService.save(converter.toOrganizationStructurePo(structure));
    }

    @Override
    public void createOrgStructures(List<AssetOrgStructure> structures) {
        if (CollectionUtils.isEmpty(structures)) return;

        orgStructureService.saveBatch(converter.toOrganizationStructurePos(structures));
    }

    @Override
    public void updateOrgStructure(AssetOrgStructure structure) {
        orgStructureService.updateById(converter.toOrganizationStructurePo(structure));
    }

    @Override
    public void updateOrgStructures(List<AssetOrgStructure> structures) {
        if (CollectionUtils.isEmpty(structures)) return;

        orgStructureService.updateBatchById(converter.toOrganizationStructurePos(structures));
    }

    @Override
    public List<AssetOrgStructure> getAllOrgStructure(List<String> businessLineCodes) {
        List<OrganizationStructurePo> list = orgStructureService.list(
                Wrappers.lambdaQuery(OrganizationStructurePo.class)
                        .eq(OrganizationStructurePo::getDisabled, 0)
                        .in(CollectionUtils.isNotEmpty(businessLineCodes), OrganizationStructurePo::getBusinessLine, businessLineCodes)
                        .or(CollectionUtils.isEmpty(businessLineCodes), q -> q.eq(OrganizationStructurePo::getIsVirtual, 0))
        );

        return converter.toOrganizationStructures(list);
    }

    @Override
    public boolean hasSubList(String orgCode) {
        return orgStructureService.count(
                Wrappers.lambdaQuery(OrganizationStructurePo.class)
                        .eq(OrganizationStructurePo::getParentCode, orgCode)
                        .eq(OrganizationStructurePo::getDisabled, 0)
        ) > 0;
    }

    @Override
    public boolean hasSubOrgUnitList(String orgCode, BusinessLine businessLine) {
        return orgUnitService.count(
                Wrappers.lambdaQuery(OrganizationUnitPo.class)
                        .eq(OrganizationUnitPo::getParentCode, orgCode)
                        .eq(OrganizationUnitPo::getBusinessLine, businessLine.getCode())

        ) > 0;
    }

    @Override
    public void deleteOrgStructure(AssetOrgStructure orgStructure) {
        orgStructureService.remove(
                Wrappers.lambdaQuery(OrganizationStructurePo.class)
                        .eq(OrganizationStructurePo::getOrgCode, orgStructure.getOrgCode())
        );
    }

    @Override
    public void deleteOrgUnitByOrgCode(String orgCode) {
        orgUnitService.remove(
                Wrappers.lambdaQuery(OrganizationUnitPo.class)
                        .eq(OrganizationUnitPo::getOrgCode, orgCode)
        );
    }

    @Override
    public void deleteOrgStructures(List<AssetOrgStructure> orgStructures) {
        if (CollectionUtils.isEmpty(orgStructures)) return;

        orgStructureService.remove(
                Wrappers.lambdaQuery(OrganizationStructurePo.class)
                        .in(OrganizationStructurePo::getOrgCode, orgStructures.stream().map(AssetOrgStructure::getOrgCode).toArray())
        );
    }

    @Override
    public void deleteOrgUnitByIds(List<Integer> ids) {
        orgUnitService.removeBatchByIds(ids);
    }

    @Override
    public PageData<AssetOrgUnit> orgUnitPageData(AssetOrgUnitQuery params, PageRequest pageRequest) {

        Page<OrganizationUnitPo> page = orgUnitService.page(
                new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize()),
                commonQueryWrapper(params)
        );

        return converter.toPageData(page, this::toAssetOrgUnits);
    }

    List<AssetOrgUnit> toAssetOrgUnits(List<OrganizationUnitPo> source) {
        return converter.toAssetOrgUnits(source);
    }

    @Override
    public List<AssetOrgStructure> getOrgStructures(List<String> orgCodes, String orderSql) {
        List<OrganizationStructurePo> list = orgStructureService.list(
                Wrappers.lambdaQuery(OrganizationStructurePo.class)
                        .in(OrganizationStructurePo::getOrgCode, orgCodes)
                        .last(StringUtils.isNotBlank(orderSql), orderSql)
        );

        return converter.toOrganizationStructures(list);
    }

    @Override
    public AssetOrgUnit getOrgUnits(String keyword, String businessLine) {
        OrganizationUnitPo po = orgUnitService.getOne(
                Wrappers.lambdaQuery(OrganizationUnitPo.class)
                        .eq(OrganizationUnitPo::getBusinessLine, businessLine)
                        .nested(q -> q.eq(OrganizationUnitPo::getOrgCode, keyword)
                                .or().eq(OrganizationUnitPo::getOrgNamePath, keyword)
                        )
                        .last("limit 1")
        );
        return converter.toAssetOrgUnit(po);
    }

    @Override
    public List<AssetOrgUnit> getOrgUnits(AssetOrgUnitQuery params) {
        return this.toAssetOrgUnits(orgUnitService.list(commonQueryWrapper(params)));
    }

    @Override
    public PageData<AssetOrgStructure> orgStructurePageData(String businessLine, String keyword, PageRequest pageRequest) {
        Page<OrganizationStructurePo> page = orgStructureService.page(
                new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize()),
                Wrappers.lambdaQuery(OrganizationStructurePo.class)
                        .eq(OrganizationStructurePo::getDisabled, 0)
                        .nested(q -> q.eq(OrganizationStructurePo::getIsVirtual, 0)
                                .or(StringUtils.isNotBlank(businessLine), sq -> sq.eq(OrganizationStructurePo::getBusinessLine, businessLine))
                        )
                        .like(StringUtils.isNotBlank(keyword), OrganizationStructurePo::getOrgName, keyword)
        );

        return converter.toPageData(page, converter::toOrganizationStructures);
    }

    @Override
    @OACacheSet(cacheEnum = CacheKey.class, cacheEnumField = "ORGANIZATION_STRUCTURE_LEVEL", param = "level", refreshCacheTime = 3600 * 12)
    public List<AssetOrgStructure> getOrgStructuresByLevel(Integer level) {
        List<OrganizationStructurePo> list = orgStructureService.list(
                Wrappers.lambdaQuery(OrganizationStructurePo.class)
                        .eq(OrganizationStructurePo::getDisabled, 0)
                        .in(OrganizationStructurePo::getLevel, level)
        );

        return converter.toOrganizationStructures(list);
    }

    private LambdaQueryWrapper<OrganizationUnitPo> commonQueryWrapper(AssetOrgUnitQuery params) {
        LambdaQueryWrapper<OrganizationUnitPo> wrapper = Wrappers.lambdaQuery(OrganizationUnitPo.class);
        if (params.isAllEmpty()) {
            // 如果查询参数都为null，查询自增id不存在的数据，避免全表查询
            wrapper.eq(OrganizationUnitPo::getId, -10000);

            return wrapper;
        }

        wrapper.in(CollectionUtils.isNotEmpty(params.getOrgIds()), OrganizationUnitPo::getId, params.getOrgIds())
                .nested(
                        StringUtils.isNotBlank(params.getOrgCode()),
                        q -> q.eq(OrganizationUnitPo::getOrgCode, params.getOrgCode()).or()
                                .eq(OrganizationUnitPo::getParentCode, params.getOrgCode())
                )
                .in(CollectionUtils.isNotEmpty(params.getOrgCodes()), OrganizationUnitPo::getOrgCode, params.getOrgCodes())
                .nested(null != params.getBusinessLine(),
                        q -> q.eq(OrganizationUnitPo::getBusinessLine, params.getBusinessLine().getCode()))
                .nested(CollectionUtils.isNotEmpty(params.getBusinessLines()),
                        q -> q.in(OrganizationUnitPo::getBusinessLine, params.getBusinessLines().stream().map(BusinessLine::getCode).collect(Collectors.toList())))
                .eq(StringUtils.isNotBlank(params.getParentCode()), OrganizationUnitPo::getParentCode, params.getParentCode())
                .eq(null != params.getLevel(), OrganizationUnitPo::getLevel, params.getLevel())
                .eq(null != params.getIsVirtual(), OrganizationUnitPo::getIsVirtual, Boolean.TRUE.equals(params.getIsVirtual()) ? 1 : 0)
                .eq(null != params.getIsAssetManageOrg(), OrganizationUnitPo::getIsAssetManageOrg, Boolean.TRUE.equals(params.getIsAssetManageOrg()) ? 1 : 0)
                .eq(null != params.getIsAssetUseOrg(), OrganizationUnitPo::getIsAssetUseOrg, Boolean.TRUE.equals(params.getIsAssetUseOrg()) ? 1 : 0)
                .nested(null != params.getOrgType(),
                        q -> q.eq(OrganizationUnitPo::getOrgType, params.getOrgType().getCode()))
                .nested(CollectionUtils.isNotEmpty(params.getOrgTypes()),
                        q -> q.in(OrganizationUnitPo::getOrgType, params.getOrgTypes().stream().map(AssetOrgType::getCode).collect(Collectors.toList())))
                .nested( StringUtils.isNotBlank(params.getKeyword()),
                        q -> q.like(OrganizationUnitPo::getOrgCodePath, params.getKeyword()).or()
                                .like(OrganizationUnitPo::getOrgNamePath, params.getKeyword()).or()
                                .like(OrganizationUnitPo::getOrgNameEn, params.getKeyword()).or()
                                .like(OrganizationUnitPo::getOrgNamePathEn, params.getKeyword()))
                .orderByAsc(OrganizationUnitPo::getLevel);

        return wrapper;
    }

    @Override
    public void inactivateOrgStructure(List<String> orgCodes) {
        if (CollectionUtils.isEmpty(orgCodes)) return;

        orgStructureService.update(
                Wrappers.lambdaUpdate(OrganizationStructurePo.class)
                        .set(OrganizationStructurePo::getDisabled, 1)
                        .in(OrganizationStructurePo::getOrgCode, orgCodes)
                        .eq(OrganizationStructurePo::getIsVirtual, 0)
        );
    }

    @Override
    public void batchCreateOrgUnit(List<AssetOrgUnit> orgUnits) {
        List<OrganizationUnitPo> organizationUnitPos = converter.toOrganizationUnitPoList(orgUnits);
        orgUnitService.saveBatch(organizationUnitPos);
    }

    @Override
    public List<AssetOrgUnit> getLabTypeOrgList(String businessLine, String orgCode) {
        List<OrganizationUnitPo> unitPoList = orgUnitService.list(Wrappers.lambdaQuery(OrganizationUnitPo.class)
                .eq(OrganizationUnitPo::getBusinessLine, businessLine)
                .eq(BusinessLine.LAB.getCode().equals(businessLine), OrganizationUnitPo::getOrgType, AssetOrgType.LAB)
                .like(OrganizationUnitPo::getOrgCodePath, orgCode));
        return converter.toAssetOrgUnits(unitPoList);
    }

    @Override
    public List<AssetOrgStructure> getOrgStructuresByParentCodes(List<String> orgCodes) {
        List<OrganizationStructurePo> list = orgStructureService.list(
                Wrappers.lambdaQuery(OrganizationStructurePo.class)
                        .in(OrganizationStructurePo::getParentCode, orgCodes)
                        .eq(OrganizationStructurePo::getDisabled, 0)
        );
        return converter.toOrganizationStructures(list);
    }
}