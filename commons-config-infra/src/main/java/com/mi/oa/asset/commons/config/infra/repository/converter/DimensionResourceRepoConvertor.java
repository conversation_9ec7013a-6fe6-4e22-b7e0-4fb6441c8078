package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.dimension.entity.DimensionResource;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.DimensionResourcePo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/24 16:45
 * @description
 */
@Mapper(componentModel = "spring")
public interface DimensionResourceRepoConvertor extends CommonConverter {
    DimensionResource poToDo(DimensionResourcePo manageLinePo);

    List<DimensionResource> poToDoList(List<DimensionResourcePo> manageLinePos);

}
