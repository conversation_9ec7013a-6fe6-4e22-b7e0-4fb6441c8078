package com.mi.oa.asset.commons.config.infra.database.dataobject.old;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.old.DeviceCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.repository.old.DeviceCategoryRepo;
import com.mi.oa.asset.commons.config.infra.database.mapper.DeviceCategoryPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.DeviceCategoryRepoConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:39
 */

@Service
public class DeviceCategoryRepoImpl extends ServiceImpl<DeviceCategoryPoMapper, DeviceCategoryPO> implements DeviceCategoryRepo {

    @Resource
    private DeviceCategoryRepoConverter converter;




    @Override
    public List<DeviceCategory> getAllAssetCategory(String businessLine, Integer level) {
        return converter.toDeviceCategories(
                lambdaQuery().eq(StringUtils.isNotEmpty(businessLine), DeviceCategoryPO::getBusType, businessLine)
                        .eq(DeviceCategoryPO::getShowLevel, level)
                        .list()
        );
    }

    @Override
    public DeviceCategory getAssetCategory(String showId){
        return converter.toDeviceCategory(
                lambdaQuery().eq(StringUtils.isNotEmpty(showId), DeviceCategoryPO::getShowId, showId)
                        .one()
        );
    }

}
