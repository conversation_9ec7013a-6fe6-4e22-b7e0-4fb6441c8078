/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.EncodingUtils;
import org.mi.thrift.TException;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;
import org.mi.thrift.server.AbstractNonblockingServer.*;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2020-5-26")
public class ProvinceRequest implements org.mi.thrift.TBase<ProvinceRequest, ProvinceRequest._Fields>, java.io.Serializable, Cloneable, Comparable<ProvinceRequest> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("ProvinceRequest");

    private static final org.mi.thrift.protocol.TField PROVINCE_NAME_FIELD_DESC = new org.mi.thrift.protocol.TField("province_name", org.mi.thrift.protocol.TType.STRING, (short) 1);
    private static final org.mi.thrift.protocol.TField ID_FIELD_DESC = new org.mi.thrift.protocol.TField("id", org.mi.thrift.protocol.TType.I64, (short) 2);
    private static final org.mi.thrift.protocol.TField UPDATE_TIME_FIELD_DESC = new org.mi.thrift.protocol.TField("update_time", org.mi.thrift.protocol.TType.STRING, (short) 3);
    private static final org.mi.thrift.protocol.TField ENABLE_FIELD_DESC = new org.mi.thrift.protocol.TField("enable", org.mi.thrift.protocol.TType.STRING, (short) 4);
    private static final org.mi.thrift.protocol.TField IS_ALL_FIELD_DESC = new org.mi.thrift.protocol.TField("is_all", org.mi.thrift.protocol.TType.BOOL, (short) 5);
    private static final org.mi.thrift.protocol.TField PARENT_ID_FIELD_DESC = new org.mi.thrift.protocol.TField("parent_id", org.mi.thrift.protocol.TType.I64, (short) 6);
    private static final org.mi.thrift.protocol.TField PAGE_FIELD_DESC = new org.mi.thrift.protocol.TField("page", org.mi.thrift.protocol.TType.I32, (short) 7);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();

    static {
        schemes.put(StandardScheme.class, new ProvinceRequestStandardSchemeFactory());
        schemes.put(TupleScheme.class, new ProvinceRequestTupleSchemeFactory());
    }

    public String province_name; // required
    public long id; // required
    public String update_time; // required
    public String enable; // required
    public boolean is_all; // required
    public long parent_id; // required
    public int page; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        PROVINCE_NAME((short) 1, "province_name"),
        ID((short) 2, "id"),
        UPDATE_TIME((short) 3, "update_time"),
        ENABLE((short) 4, "enable"),
        IS_ALL((short) 5, "is_all"),
        PARENT_ID((short) 6, "parent_id"),
        PAGE((short) 7, "page");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
            for (_Fields field : EnumSet.allOf(_Fields.class)) {
                byName.put(field.getFieldName(), field);
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch (fieldId) {
                case 1: // PROVINCE_NAME
                    return PROVINCE_NAME;
                case 2: // ID
                    return ID;
                case 3: // UPDATE_TIME
                    return UPDATE_TIME;
                case 4: // ENABLE
                    return ENABLE;
                case 5: // IS_ALL
                    return IS_ALL;
                case 6: // PARENT_ID
                    return PARENT_ID;
                case 7: // PAGE
                    return PAGE;
                default:
                    return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
            _Fields fields = findByThriftId(fieldId);
            if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
            return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
            return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
            _thriftId = thriftId;
            _fieldName = fieldName;
        }

        public short getThriftFieldId() {
            return _thriftId;
        }

        public String getFieldName() {
            return _fieldName;
        }
    }

    // isset id assignments
    private static final int __ID_ISSET_ID = 0;
    private static final int __IS_ALL_ISSET_ID = 1;
    private static final int __PARENT_ID_ISSET_ID = 2;
    private static final int __PAGE_ISSET_ID = 3;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;

    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.PROVINCE_NAME, new org.mi.thrift.meta_data.FieldMetaData("province_name", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        tmpMap.put(_Fields.ID, new org.mi.thrift.meta_data.FieldMetaData("id", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I64)));
        tmpMap.put(_Fields.UPDATE_TIME, new org.mi.thrift.meta_data.FieldMetaData("update_time", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        tmpMap.put(_Fields.ENABLE, new org.mi.thrift.meta_data.FieldMetaData("enable", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.STRING)));
        tmpMap.put(_Fields.IS_ALL, new org.mi.thrift.meta_data.FieldMetaData("is_all", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.BOOL)));
        tmpMap.put(_Fields.PARENT_ID, new org.mi.thrift.meta_data.FieldMetaData("parent_id", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I64)));
        tmpMap.put(_Fields.PAGE, new org.mi.thrift.meta_data.FieldMetaData("page", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I32)));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ProvinceRequest.class, metaDataMap);
    }

    public ProvinceRequest() {
    }

    public ProvinceRequest(
            String province_name,
            long id,
            String update_time,
            String enable,
            boolean is_all,
            long parent_id,
            int page) {
        this();
        this.province_name = province_name;
        this.id = id;
        setIdIsSet(true);
        this.update_time = update_time;
        this.enable = enable;
        this.is_all = is_all;
        setIs_allIsSet(true);
        this.parent_id = parent_id;
        setParent_idIsSet(true);
        this.page = page;
        setPageIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public ProvinceRequest(ProvinceRequest other) {
        __isset_bitfield = other.__isset_bitfield;
        if (other.isSetProvince_name()) {
            this.province_name = other.province_name;
        }
        this.id = other.id;
        if (other.isSetUpdate_time()) {
            this.update_time = other.update_time;
        }
        if (other.isSetEnable()) {
            this.enable = other.enable;
        }
        this.is_all = other.is_all;
        this.parent_id = other.parent_id;
        this.page = other.page;
    }

    public ProvinceRequest deepCopy() {
        return new ProvinceRequest(this);
    }

    @Override
    public void clear() {
        this.province_name = null;
        setIdIsSet(false);
        this.id = 0;
        this.update_time = null;
        this.enable = null;
        setIs_allIsSet(false);
        this.is_all = false;
        setParent_idIsSet(false);
        this.parent_id = 0;
        setPageIsSet(false);
        this.page = 0;
    }

    public String getProvince_name() {
        return this.province_name;
    }

    public ProvinceRequest setProvince_name(String province_name) {
        this.province_name = province_name;
        return this;
    }

    public void unsetProvince_name() {
        this.province_name = null;
    }

    /** Returns true if field province_name is set (has been assigned a value) and false otherwise */
    public boolean isSetProvince_name() {
        return this.province_name != null;
    }

    public void setProvince_nameIsSet(boolean value) {
        if (!value) {
            this.province_name = null;
        }
    }

    public long getId() {
        return this.id;
    }

    public ProvinceRequest setId(long id) {
        this.id = id;
        setIdIsSet(true);
        return this;
    }

    public void unsetId() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ID_ISSET_ID);
    }

    /** Returns true if field id is set (has been assigned a value) and false otherwise */
    public boolean isSetId() {
        return EncodingUtils.testBit(__isset_bitfield, __ID_ISSET_ID);
    }

    public void setIdIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ID_ISSET_ID, value);
    }

    public String getUpdate_time() {
        return this.update_time;
    }

    public ProvinceRequest setUpdate_time(String update_time) {
        this.update_time = update_time;
        return this;
    }

    public void unsetUpdate_time() {
        this.update_time = null;
    }

    /** Returns true if field update_time is set (has been assigned a value) and false otherwise */
    public boolean isSetUpdate_time() {
        return this.update_time != null;
    }

    public void setUpdate_timeIsSet(boolean value) {
        if (!value) {
            this.update_time = null;
        }
    }

    public String getEnable() {
        return this.enable;
    }

    public ProvinceRequest setEnable(String enable) {
        this.enable = enable;
        return this;
    }

    public void unsetEnable() {
        this.enable = null;
    }

    /** Returns true if field enable is set (has been assigned a value) and false otherwise */
    public boolean isSetEnable() {
        return this.enable != null;
    }

    public void setEnableIsSet(boolean value) {
        if (!value) {
            this.enable = null;
        }
    }

    public boolean isIs_all() {
        return this.is_all;
    }

    public ProvinceRequest setIs_all(boolean is_all) {
        this.is_all = is_all;
        setIs_allIsSet(true);
        return this;
    }

    public void unsetIs_all() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __IS_ALL_ISSET_ID);
    }

    /** Returns true if field is_all is set (has been assigned a value) and false otherwise */
    public boolean isSetIs_all() {
        return EncodingUtils.testBit(__isset_bitfield, __IS_ALL_ISSET_ID);
    }

    public void setIs_allIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __IS_ALL_ISSET_ID, value);
    }

    public long getParent_id() {
        return this.parent_id;
    }

    public ProvinceRequest setParent_id(long parent_id) {
        this.parent_id = parent_id;
        setParent_idIsSet(true);
        return this;
    }

    public void unsetParent_id() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PARENT_ID_ISSET_ID);
    }

    /** Returns true if field parent_id is set (has been assigned a value) and false otherwise */
    public boolean isSetParent_id() {
        return EncodingUtils.testBit(__isset_bitfield, __PARENT_ID_ISSET_ID);
    }

    public void setParent_idIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PARENT_ID_ISSET_ID, value);
    }

    public int getPage() {
        return this.page;
    }

    public ProvinceRequest setPage(int page) {
        this.page = page;
        setPageIsSet(true);
        return this;
    }

    public void unsetPage() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGE_ISSET_ID);
    }

    /** Returns true if field page is set (has been assigned a value) and false otherwise */
    public boolean isSetPage() {
        return EncodingUtils.testBit(__isset_bitfield, __PAGE_ISSET_ID);
    }

    public void setPageIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGE_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
        switch (field) {
            case PROVINCE_NAME:
                if (value == null) {
                    unsetProvince_name();
                } else {
                    setProvince_name((String) value);
                }
                break;

            case ID:
                if (value == null) {
                    unsetId();
                } else {
                    setId((Long) value);
                }
                break;

            case UPDATE_TIME:
                if (value == null) {
                    unsetUpdate_time();
                } else {
                    setUpdate_time((String) value);
                }
                break;

            case ENABLE:
                if (value == null) {
                    unsetEnable();
                } else {
                    setEnable((String) value);
                }
                break;

            case IS_ALL:
                if (value == null) {
                    unsetIs_all();
                } else {
                    setIs_all((Boolean) value);
                }
                break;

            case PARENT_ID:
                if (value == null) {
                    unsetParent_id();
                } else {
                    setParent_id((Long) value);
                }
                break;

            case PAGE:
                if (value == null) {
                    unsetPage();
                } else {
                    setPage((Integer) value);
                }
                break;

        }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
            case PROVINCE_NAME:
                return getProvince_name();

            case ID:
                return Long.valueOf(getId());

            case UPDATE_TIME:
                return getUpdate_time();

            case ENABLE:
                return getEnable();

            case IS_ALL:
                return Boolean.valueOf(isIs_all());

            case PARENT_ID:
                return Long.valueOf(getParent_id());

            case PAGE:
                return Integer.valueOf(getPage());

        }
        throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
        if (field == null) {
            throw new IllegalArgumentException();
        }

        switch (field) {
            case PROVINCE_NAME:
                return isSetProvince_name();
            case ID:
                return isSetId();
            case UPDATE_TIME:
                return isSetUpdate_time();
            case ENABLE:
                return isSetEnable();
            case IS_ALL:
                return isSetIs_all();
            case PARENT_ID:
                return isSetParent_id();
            case PAGE:
                return isSetPage();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
            return false;
        if (that instanceof ProvinceRequest)
            return this.equals((ProvinceRequest) that);
        return false;
    }

    public boolean equals(ProvinceRequest that) {
        if (that == null)
            return false;

        boolean this_present_province_name = true && this.isSetProvince_name();
        boolean that_present_province_name = true && that.isSetProvince_name();
        if (this_present_province_name || that_present_province_name) {
            if (!(this_present_province_name && that_present_province_name))
                return false;
            if (!this.province_name.equals(that.province_name))
                return false;
        }

        boolean this_present_id = true;
        boolean that_present_id = true;
        if (this_present_id || that_present_id) {
            if (!(this_present_id && that_present_id))
                return false;
            if (this.id != that.id)
                return false;
        }

        boolean this_present_update_time = true && this.isSetUpdate_time();
        boolean that_present_update_time = true && that.isSetUpdate_time();
        if (this_present_update_time || that_present_update_time) {
            if (!(this_present_update_time && that_present_update_time))
                return false;
            if (!this.update_time.equals(that.update_time))
                return false;
        }

        boolean this_present_enable = true && this.isSetEnable();
        boolean that_present_enable = true && that.isSetEnable();
        if (this_present_enable || that_present_enable) {
            if (!(this_present_enable && that_present_enable))
                return false;
            if (!this.enable.equals(that.enable))
                return false;
        }

        boolean this_present_is_all = true;
        boolean that_present_is_all = true;
        if (this_present_is_all || that_present_is_all) {
            if (!(this_present_is_all && that_present_is_all))
                return false;
            if (this.is_all != that.is_all)
                return false;
        }

        boolean this_present_parent_id = true;
        boolean that_present_parent_id = true;
        if (this_present_parent_id || that_present_parent_id) {
            if (!(this_present_parent_id && that_present_parent_id))
                return false;
            if (this.parent_id != that.parent_id)
                return false;
        }

        boolean this_present_page = true;
        boolean that_present_page = true;
        if (this_present_page || that_present_page) {
            if (!(this_present_page && that_present_page))
                return false;
            if (this.page != that.page)
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_province_name = true && (isSetProvince_name());
        list.add(present_province_name);
        if (present_province_name)
            list.add(province_name);

        boolean present_id = true;
        list.add(present_id);
        if (present_id)
            list.add(id);

        boolean present_update_time = true && (isSetUpdate_time());
        list.add(present_update_time);
        if (present_update_time)
            list.add(update_time);

        boolean present_enable = true && (isSetEnable());
        list.add(present_enable);
        if (present_enable)
            list.add(enable);

        boolean present_is_all = true;
        list.add(present_is_all);
        if (present_is_all)
            list.add(is_all);

        boolean present_parent_id = true;
        list.add(present_parent_id);
        if (present_parent_id)
            list.add(parent_id);

        boolean present_page = true;
        list.add(present_page);
        if (present_page)
            list.add(page);

        return list.hashCode();
    }

    @Override
    public int compareTo(ProvinceRequest other) {
        if (!getClass().equals(other.getClass())) {
            return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetProvince_name()).compareTo(other.isSetProvince_name());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetProvince_name()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.province_name, other.province_name);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetId()).compareTo(other.isSetId());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetId()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.id, other.id);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetUpdate_time()).compareTo(other.isSetUpdate_time());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetUpdate_time()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.update_time, other.update_time);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetEnable()).compareTo(other.isSetEnable());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetEnable()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.enable, other.enable);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetIs_all()).compareTo(other.isSetIs_all());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetIs_all()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.is_all, other.is_all);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetParent_id()).compareTo(other.isSetParent_id());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetParent_id()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.parent_id, other.parent_id);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetPage()).compareTo(other.isSetPage());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetPage()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.page, other.page);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
        return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ProvinceRequest(");
        boolean first = true;

        sb.append("province_name:");
        if (this.province_name == null) {
            sb.append("null");
        } else {
            sb.append(this.province_name);
        }
        first = false;
        if (!first) sb.append(", ");
        sb.append("id:");
        sb.append(this.id);
        first = false;
        if (!first) sb.append(", ");
        sb.append("update_time:");
        if (this.update_time == null) {
            sb.append("null");
        } else {
            sb.append(this.update_time);
        }
        first = false;
        if (!first) sb.append(", ");
        sb.append("enable:");
        if (this.enable == null) {
            sb.append("null");
        } else {
            sb.append(this.enable);
        }
        first = false;
        if (!first) sb.append(", ");
        sb.append("is_all:");
        sb.append(this.is_all);
        first = false;
        if (!first) sb.append(", ");
        sb.append("parent_id:");
        sb.append(this.parent_id);
        first = false;
        if (!first) sb.append(", ");
        sb.append("page:");
        sb.append(this.page);
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        try {
            write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        try {
            // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
            __isset_bitfield = 0;
            read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private static class ProvinceRequestStandardSchemeFactory implements SchemeFactory {
        public ProvinceRequestStandardScheme getScheme() {
            return new ProvinceRequestStandardScheme();
        }
    }

    private static class ProvinceRequestStandardScheme extends StandardScheme<ProvinceRequest> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, ProvinceRequest struct) throws TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true) {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                    break;
                }
                switch (schemeField.id) {
                    case 1: // PROVINCE_NAME
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.province_name = iprot.readString();
                            struct.setProvince_nameIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // ID
                        if (schemeField.type == org.mi.thrift.protocol.TType.I64) {
                            struct.id = iprot.readI64();
                            struct.setIdIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 3: // UPDATE_TIME
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.update_time = iprot.readString();
                            struct.setUpdate_timeIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 4: // ENABLE
                        if (schemeField.type == org.mi.thrift.protocol.TType.STRING) {
                            struct.enable = iprot.readString();
                            struct.setEnableIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 5: // IS_ALL
                        if (schemeField.type == org.mi.thrift.protocol.TType.BOOL) {
                            struct.is_all = iprot.readBool();
                            struct.setIs_allIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 6: // PARENT_ID
                        if (schemeField.type == org.mi.thrift.protocol.TType.I64) {
                            struct.parent_id = iprot.readI64();
                            struct.setParent_idIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 7: // PAGE
                        if (schemeField.type == org.mi.thrift.protocol.TType.I32) {
                            struct.page = iprot.readI32();
                            struct.setPageIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                        org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, ProvinceRequest struct) throws TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            if (struct.province_name != null) {
                oprot.writeFieldBegin(PROVINCE_NAME_FIELD_DESC);
                oprot.writeString(struct.province_name);
                oprot.writeFieldEnd();
            }
            oprot.writeFieldBegin(ID_FIELD_DESC);
            oprot.writeI64(struct.id);
            oprot.writeFieldEnd();
            if (struct.update_time != null) {
                oprot.writeFieldBegin(UPDATE_TIME_FIELD_DESC);
                oprot.writeString(struct.update_time);
                oprot.writeFieldEnd();
            }
            if (struct.enable != null) {
                oprot.writeFieldBegin(ENABLE_FIELD_DESC);
                oprot.writeString(struct.enable);
                oprot.writeFieldEnd();
            }
            oprot.writeFieldBegin(IS_ALL_FIELD_DESC);
            oprot.writeBool(struct.is_all);
            oprot.writeFieldEnd();
            oprot.writeFieldBegin(PARENT_ID_FIELD_DESC);
            oprot.writeI64(struct.parent_id);
            oprot.writeFieldEnd();
            oprot.writeFieldBegin(PAGE_FIELD_DESC);
            oprot.writeI32(struct.page);
            oprot.writeFieldEnd();
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class ProvinceRequestTupleSchemeFactory implements SchemeFactory {
        public ProvinceRequestTupleScheme getScheme() {
            return new ProvinceRequestTupleScheme();
        }
    }

    private static class ProvinceRequestTupleScheme extends TupleScheme<ProvinceRequest> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, ProvinceRequest struct) throws TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetProvince_name()) {
                optionals.set(0);
            }
            if (struct.isSetId()) {
                optionals.set(1);
            }
            if (struct.isSetUpdate_time()) {
                optionals.set(2);
            }
            if (struct.isSetEnable()) {
                optionals.set(3);
            }
            if (struct.isSetIs_all()) {
                optionals.set(4);
            }
            if (struct.isSetParent_id()) {
                optionals.set(5);
            }
            if (struct.isSetPage()) {
                optionals.set(6);
            }
            oprot.writeBitSet(optionals, 7);
            if (struct.isSetProvince_name()) {
                oprot.writeString(struct.province_name);
            }
            if (struct.isSetId()) {
                oprot.writeI64(struct.id);
            }
            if (struct.isSetUpdate_time()) {
                oprot.writeString(struct.update_time);
            }
            if (struct.isSetEnable()) {
                oprot.writeString(struct.enable);
            }
            if (struct.isSetIs_all()) {
                oprot.writeBool(struct.is_all);
            }
            if (struct.isSetParent_id()) {
                oprot.writeI64(struct.parent_id);
            }
            if (struct.isSetPage()) {
                oprot.writeI32(struct.page);
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, ProvinceRequest struct) throws TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(7);
            if (incoming.get(0)) {
                struct.province_name = iprot.readString();
                struct.setProvince_nameIsSet(true);
            }
            if (incoming.get(1)) {
                struct.id = iprot.readI64();
                struct.setIdIsSet(true);
            }
            if (incoming.get(2)) {
                struct.update_time = iprot.readString();
                struct.setUpdate_timeIsSet(true);
            }
            if (incoming.get(3)) {
                struct.enable = iprot.readString();
                struct.setEnableIsSet(true);
            }
            if (incoming.get(4)) {
                struct.is_all = iprot.readBool();
                struct.setIs_allIsSet(true);
            }
            if (incoming.get(5)) {
                struct.parent_id = iprot.readI64();
                struct.setParent_idIsSet(true);
            }
            if (incoming.get(6)) {
                struct.page = iprot.readI32();
                struct.setPageIsSet(true);
            }
        }
    }

}

