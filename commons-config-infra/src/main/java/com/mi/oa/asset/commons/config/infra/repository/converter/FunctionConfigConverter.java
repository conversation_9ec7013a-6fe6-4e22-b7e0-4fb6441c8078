package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.myfunctions.*;
import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfig;
import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfigDept;
import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfigItem;
import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfigUser;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FunctionConfigDeptPo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FunctionConfigItemPo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FunctionConfigPo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FunctionConfigUserPo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface FunctionConfigConverter extends CommonConverter {

    FunctionConfig reqToConfig(SaveFunctionConfigReq source);

    @Mapping(source = "configId", target = "id")
    FunctionConfigPo toPo(FunctionConfig config);

    @Mapping(source = "id", target = "configId")
    FunctionConfig toConfig(FunctionConfigPo config);

    List<FunctionConfig> toConfigs(List<FunctionConfigPo> configs);

    FunctionConfigDeptPo toDeptPo(FunctionConfigDept configDept);

    List<FunctionConfigDeptPo> toDeptPos(List<FunctionConfigDept> configDeptList);

    FunctionConfigDept poToDept(FunctionConfigDeptPo configDeptPo);

    List<FunctionConfigDept> poToDeptList(List<FunctionConfigDeptPo> configDeptPos);

    @Mappings({
            @Mapping(source = "itemId", target = "id"),
            @Mapping(target = "country", expression = "java(converterCountries(item.getCountries()))"),
    })
    FunctionConfigItemPo toItemPo(FunctionConfigItem item);

    List<FunctionConfigItemPo> toItemPos(List<FunctionConfigItem> items);

    @Mappings({
            @Mapping(source = "id", target = "itemId"),
            @Mapping(target = "countries", expression = "java(converterCountry(itemPo.getCountry()))"),
    })
    FunctionConfigItem poToItem(FunctionConfigItemPo itemPo);

    List<FunctionConfigItem> poToItems(List<FunctionConfigItemPo> items);

    default String converterCountries(List<Country> countries) {
        if (CollectionUtils.isNotEmpty(countries)) {
            return countries.stream().map(Country::getCountry).collect(Collectors.joining(";"));
        }
        return null;
    }

    default List<Country> converterCountry(String countries) {
        if (StringUtils.isNotBlank(countries)) {
            String[] split = countries.split(";");
            List<Country> countryList = new ArrayList<>(split.length);
            for (String s : split) {
                Country country = new Country();
                country.setCountry(s);
                countryList.add(country);
            }
            return countryList;
        }
        return Collections.emptyList();
    }

    FunctionConfigUserPo toConfigUserPo(FunctionConfigUser configUser);

    List<FunctionConfigUserPo> toConfigUserPos(List<FunctionConfigUser> configUsers);

    FunctionConfigUser poToConfigUser(FunctionConfigUserPo configUserPo);

    List<FunctionConfigUser> poToConfigUsers(List<FunctionConfigUserPo> configUserPos);

    FunctionConfigUser reqToConfigUser(SaveFunctionConfigUserReq configUserReq);

    default FunctionConfigUser reqToConfigUser(SaveFunctionConfigUserReq configUserReq, User user) {
        if (configUserReq == null || user == null) {
            return null;
        }
        FunctionConfigUser functionConfigUser = new FunctionConfigUser();
        functionConfigUser.setManageLine(configUserReq.getManageLine());
        functionConfigUser.setManageLineName(configUserReq.getManageLineName());
        functionConfigUser.setSort(configUserReq.getSort());
        functionConfigUser.setUserCode(user.getUserName());
        functionConfigUser.setUserName(user.getDisplayName());
        return functionConfigUser;
    }

    default FunctionConfigUser updateConfigUser(SaveFunctionConfigUserReq configUserReq, User user, FunctionConfigUser exist) {
        exist.setManageLineName(configUserReq.getManageLineName());
        exist.setUserCode(user.getUserName());
        exist.setUserName(user.getDisplayName());
        exist.setSort(configUserReq.getSort());
        return exist;
    }

    @Mapping(target = "items", ignore = true)
    FunctionConfigRes toConfigRes(FunctionConfig config);

    FunctionConfigItemRes toItemRes(FunctionConfigItem item);

    FunctionConfigDeptRes toDeptRes(FunctionConfigDept dept);

    List<FunctionConfigDeptRes> toDeptResList(List<FunctionConfigDept> deptList);
}
