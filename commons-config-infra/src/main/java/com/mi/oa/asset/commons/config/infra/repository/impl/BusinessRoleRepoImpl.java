package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRole;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRoleUser;
import com.mi.oa.asset.commons.config.domain.businessrole.repository.BusinessRoleRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.BusinessRolePo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.BusinessRoleUserPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.BusinessRoleMapper;
import com.mi.oa.asset.commons.config.infra.database.mapper.BusinessRoleUserPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.BusinessRoleRepoConverter;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/01/15 19:43
 */

@Service
public class BusinessRoleRepoImpl implements BusinessRoleRepo {

    @Resource
    private BusinessRoleMapper roleMapper;

    @Resource
    private BusinessRoleUserPoMapper roleUserMapper;

    @Resource
    private BusinessRoleRepoConverter roleRepoConverter;

    @Override
    public List<BusinessRole> getRoleList() {
        return roleRepoConverter.toBusinessRoleList(roleMapper.selectList(new QueryWrapper<>()));
    }

    @Override
    public BusinessRole findByCode(String code) {
        if (StringUtils.isEmpty(code)) return null;

        return roleRepoConverter.poToDo(roleMapper.selectOne(new LambdaQueryWrapper<BusinessRolePo>().eq(BusinessRolePo::getRoleCode, code)));
    }

    @Override
    public List<BusinessRole> findByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return new ArrayList<>();
        }

        return roleRepoConverter.toBusinessRoleList(roleMapper.selectList(new LambdaQueryWrapper<BusinessRolePo>().in(BusinessRolePo::getRoleCode, codes)));
    }

    @Override
    public BusinessRoleUser getRoleByOrgCode(String roleCode, String orgCode, BusinessLine businessLine) {
        LambdaQueryWrapper<BusinessRoleUserPo> queryWrapper = new LambdaQueryWrapper<BusinessRoleUserPo>()
                .eq(BusinessRoleUserPo::getRoleCode, roleCode)
                .eq(!StringUtils.isEmpty(orgCode), BusinessRoleUserPo::getOrgCode, orgCode);
        if (Objects.nonNull(businessLine)) {
            queryWrapper.eq(BusinessRoleUserPo::getBusinessLine, businessLine.getCode());
        }
        queryWrapper.last("limit 1");

        BusinessRoleUserPo businessRoleUserPo = roleUserMapper.selectOne(queryWrapper);
        return roleRepoConverter.poToDo(businessRoleUserPo);
    }

    @Override
    public List<BusinessRoleUser> getRolesByOrgCode(String roleCode, String orgCode, BusinessLine businessLine) {
        LambdaQueryWrapper<BusinessRoleUserPo> queryWrapper = new LambdaQueryWrapper<BusinessRoleUserPo>()
                .eq(BusinessRoleUserPo::getRoleCode, roleCode)
                .eq(!StringUtils.isEmpty(orgCode), BusinessRoleUserPo::getOrgCode, orgCode);
        if (Objects.nonNull(businessLine)) {
            queryWrapper.eq(BusinessRoleUserPo::getBusinessLine, businessLine.getCode());
        }
        List<BusinessRoleUserPo> businessRoleUsers = roleUserMapper.selectList(queryWrapper);
        return roleRepoConverter.poToList(businessRoleUsers);
    }

    @Override
    public List<BusinessRoleUser> getRolesByRoleCodes(List<String> roleCodes, String userName, Integer limit, String businessLine) {
        LambdaQueryWrapper<BusinessRoleUserPo> queryWrapper = new LambdaQueryWrapper<BusinessRoleUserPo>()
                .select(BusinessRoleUserPo::getUserCode)
                .eq(BusinessRoleUserPo::getBusinessLine, businessLine)
                .in(BusinessRoleUserPo::getRoleCode, roleCodes);
        if (StringUtils.isNotBlank(userName)) {
            queryWrapper.and(o -> o.like(BusinessRoleUserPo::getUserCode, userName).or().like(BusinessRoleUserPo::getUserName, userName));
        }
        String lastSql = " group by user_code ";
        if (Objects.nonNull(limit)) {
            lastSql += (" limit " + limit);
        }
        queryWrapper.last(lastSql);

        List<BusinessRoleUserPo> businessRoleUsers = roleUserMapper.selectList(queryWrapper);
        return roleRepoConverter.poToList(businessRoleUsers);
    }

    @Override
    public List<BusinessRoleUser> getRolesByUserCode(String userCode, List<String> roleCodes, BusinessLine businessLine) {
        List<BusinessRoleUserPo> roleUserPoList = roleUserMapper.selectList(new LambdaQueryWrapper<BusinessRoleUserPo>()
                .eq(BusinessRoleUserPo::getUserCode, userCode)
                .eq(BusinessRoleUserPo::getBusinessLine, businessLine.getCode())
                .in(CollectionUtils.isNotEmpty(roleCodes), BusinessRoleUserPo::getRoleCode, roleCodes));
        return roleRepoConverter.poToList(roleUserPoList);
    }

    @Override
    public void deleteByIds(List<Integer> ids) {
        if(CollectionUtils.isEmpty(ids)) return;

        roleUserMapper.deleteBatchIds(ids);
    }

    @Override
    public void createRoleUser(List<BusinessRoleUser> list) {
        List<BusinessRoleUserPo> poList = roleRepoConverter.toBusinessRoleUserPos(list);
        poList.forEach(p -> roleUserMapper.insert(p));
    }

    @Override
    public List<BusinessRoleUser> getRoleByOrgCodeAndBusiness(String orgCode, BusinessLine businessLine) {
        List<BusinessRoleUserPo> businessRoleUsers = roleUserMapper.selectList(new LambdaQueryWrapper<BusinessRoleUserPo>()
                .eq(null != businessLine, BusinessRoleUserPo::getBusinessLine, businessLine.getCode())
                .eq(BusinessRoleUserPo::getOrgCode, orgCode));

        return roleRepoConverter.poToList(businessRoleUsers);
    }

    @Override
    public void deleteRoles(String orgCode, BusinessLine businessLine) {
        roleUserMapper.delete(
                Wrappers.lambdaQuery(BusinessRoleUserPo.class)
                .eq(BusinessRoleUserPo::getOrgCode, orgCode)
                .eq(BusinessRoleUserPo::getBusinessLine, businessLine.getCode())
        );
    }

    @Override
    public void deleteRolesByOrgCodes(List<String> orgCode, BusinessLine businessLine) {
        roleUserMapper.delete(Wrappers.lambdaQuery(BusinessRoleUserPo.class)
                .in(BusinessRoleUserPo::getOrgCode, orgCode)
                .eq(BusinessRoleUserPo::getBusinessLine, businessLine.getCode())
        );
    }
}
