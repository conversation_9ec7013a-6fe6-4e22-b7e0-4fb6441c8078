package com.mi.oa.asset.commons.config.infra.rpc.mdm;

import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSku;
import com.mi.oa.asset.commons.config.domain.assetsku.repository.AssetSkuRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("MdmService")
@Slf4j
public class MdmServiceImpl implements MdmService {

    @Resource
    private AssetSkuRepo assetSkuRepo;

    private static final String DEFAULT_CREATE_SYSTEM = "EAM2";

    @Override
    public List<DistributeFeedbackDTO.Item> savePurchaseItemFromDistribute(List<MdmDistributePurchaseItemVO> list) {
        List<DistributeFeedbackDTO.Item> feedbackItems = new ArrayList<>(list.size());

        List<AssetSku> assetSkuList = new ArrayList<>();
        List<Integer> indexMap = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            MdmDistributePurchaseItemVO item = list.get(i);
            if (DEFAULT_CREATE_SYSTEM.equals(item.getSys())) {
                AssetSku assetSku = new AssetSku();
                assetSku.setSkuCode(item.getSeq());
                assetSku.setMiSkuCode(item.getMiSkuCode());
                assetSku.setMdmCreateStatus(item.isActive() ? 1 : 0);
                assetSkuList.add(assetSku);
                indexMap.add(i);
            }
        }

        List<Boolean> updateResults = assetSkuRepo.updateFromDistribute(assetSkuList);

        for (int i = 0; i < list.size(); i++) {
            MdmDistributePurchaseItemVO item = list.get(i);
            boolean rst = true;
            if (DEFAULT_CREATE_SYSTEM.equals(item.getSys())) {
                int idx = indexMap.indexOf(i);
                rst = (idx >= 0 && idx < updateResults.size()) ? updateResults.get(idx) : false;
                log.info("批量updateFromDistribute结果: {}, skuCode: {}", rst, item.getSeq());
            } else {
                log.info("非EAM2系统, 跳过更新, sys: {}, skuCode: {}", item.getSys(), item.getSeq());
            }

            DistributeFeedbackDTO.Item feedbackItem = DistributeFeedbackDTO.Item.builder()
                    .bussId(item.getBussId())
                    .masterId(item.getMasterId())
                    .success(rst)
                    .message(rst ? "SUCCESS" : "数据更新失败: " + item.getSeq())
                    .build();

            feedbackItems.add(feedbackItem);
        }

        return feedbackItems;
    }
}
