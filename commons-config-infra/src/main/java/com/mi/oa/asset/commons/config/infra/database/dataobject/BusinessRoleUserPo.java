package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date    2024/3/12 14:18
 */

/**
 * 角色配置明细
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "amg_business_role_user")
public class BusinessRoleUserPo extends BasePo {
    /**
     * 角色编码
     */
    @TableField(value = "role_code")
    private String roleCode;

    /**
     * 角色名称
     */
    @TableField(value = "role_name")
    private String roleName;

    /**
     * 角色描述
     */
    @TableField(value = "role_desc")
    private String roleDesc;

    /**
     * 账号
     */
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 姓名
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 用户部门名称
     */
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 用户头像
     */
    @TableField(value = "avatar")
    private String avatar;

    /**
     * 组织结构编码
     */
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 业务线
     */
    @TableField(value = "business_line")
    private String businessLine;
}