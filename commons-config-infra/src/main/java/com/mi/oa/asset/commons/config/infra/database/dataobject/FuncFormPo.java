package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/2/25 14:14
 */

@Data
@TableName(value = "amg_func_form")
public class FuncFormPo extends BasePo {

    /**
     * 字段名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 字段编码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 功能id
     */
    @TableField(value = "func_id")
    private String funcId;

    /**
     * 功能编码
     */
    @TableField(value = "func_code")
    private String funcCode;

    @TableField(value = "en_code")
    private String enCode;

    @TableField(value = "show_type")
    private String showType;

    @TableField(value = "default_value")
    private String defaultValue;

    @TableField(value = "view")
    private Boolean view;

    @TableField(value = "edit")
    private String edit;

    @TableField(value = "business_line")
    private String businessLine;
}
