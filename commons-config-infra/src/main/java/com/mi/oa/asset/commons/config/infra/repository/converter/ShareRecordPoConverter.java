package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.common.BaseConverter;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRecord;
import com.mi.oa.asset.commons.config.infra.database.dataobject.ShareRecordPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-07 11:52
 */
@Mapper(componentModel = "spring")
public interface ShareRecordPoConverter extends BaseConverter {
    ShareRecordPo doToPo(ShareRecord shareRecord);

    ShareRecord poToDo(ShareRecordPo shareRecordPo);

    List<ShareRecord> poToDoList(List<ShareRecordPo> shareRecordPoList);
}
