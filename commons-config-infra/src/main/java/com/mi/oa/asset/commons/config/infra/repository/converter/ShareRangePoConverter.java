package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRange;
import com.mi.oa.asset.commons.config.infra.database.dataobject.ShareRangePo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-07 14:51
 */
@Mapper(componentModel = "spring")
public interface ShareRangePoConverter {
    ShareRange poToDo(ShareRangePo shareRangePo);

    List<ShareRange> poToDoList(List<ShareRangePo> shareRangeList);

    ShareRangePo doToPo(ShareRange shareRange);

    List<ShareRangePo> doToPoList(List<ShareRange> shareRangeList);
}
