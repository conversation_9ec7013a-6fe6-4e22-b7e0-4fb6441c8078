package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.function.repository.FunColumnRepo;
import com.mi.oa.asset.commons.config.domain.function.repository.FuncDictRelRepo;
import com.mi.oa.asset.eam.mybatis.FuncColumnMapper;
import com.mi.oa.asset.eam.mybatis.FuncColumnPo;
import com.mi.oa.asset.eam.mybatis.FuncDictRelPo;
import com.mi.oa.asset.eam.mybatis.FunctionDictMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class FuncDictRelRepoImpl extends ServiceImpl<FunctionDictMapper, FuncDictRelPo> implements FuncDictRelRepo {
    @Override
    public void batchSave(List<FuncDictRelPo> list) {
        if (CollectionUtils.isEmpty(list)) return;
        this.saveBatch(list);
    }

    @Override
    public void batchUpdate(List<FuncDictRelPo> list) {
        if (CollectionUtils.isEmpty(list)) return;
        this.updateBatchById(list);
    }
}
