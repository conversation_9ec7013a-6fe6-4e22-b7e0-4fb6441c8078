package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/4/07 14:14
 */

@Data
@TableName(value = "amg_dict")
public class DictPo extends BasePo {

    @TableField(value = "parent_code")
    private String parentCode;

    @TableField(value = "type")
    private Integer type;

    @TableField(value = "code")
    private String code;

    @TableField(value = "en_name")
    private String enName;

    @TableField(value = "name")
    private String name;

    @TableField(value = "module_code")
    private String moduleCode;

    @TableField(value = "module_name")
    private String moduleName;

    @TableField(value = "valid")
    private Integer valid;

    @TableField(value = "sort")
    private Integer sort;

}
