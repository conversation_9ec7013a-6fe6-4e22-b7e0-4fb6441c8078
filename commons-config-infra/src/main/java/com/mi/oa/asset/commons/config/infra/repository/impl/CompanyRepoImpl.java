package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.common.repository.CompanyRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.Company;
import com.mi.oa.asset.commons.config.infra.database.dataobject.CompanyPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.CompanyPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.CommonDataRepoConverter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/9/30 14:56
 */
@Service
@RequiredArgsConstructor
public class CompanyRepoImpl extends ServiceImpl<CompanyPoMapper, CompanyPo> implements CompanyRepo {

    private final CommonDataRepoConverter commonDataRepoConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveCompany(List<Company> companies) {
        if (CollectionUtils.isEmpty(companies)) return;
        List<CompanyPo> companyPos = commonDataRepoConverter.toCompanyPo(companies);
        List<String> companyCodes = companyPos.stream()
                .map(CompanyPo::getCompanyCode).collect(Collectors.toList());
        List<CompanyPo> list = this.list(Wrappers.lambdaQuery(CompanyPo.class)
                .in(CompanyPo::getCompanyCode, companyCodes));
        if (CollectionUtils.isNotEmpty(list)) {
            companyPos.forEach(i -> list.stream()
                    .filter(j -> j.getCompanyCode().equals(i.getCompanyCode())).findFirst().ifPresent(companyPo -> i.setId(companyPo.getId())));
        }
        this.saveOrUpdateBatch(companyPos);
    }
}
