package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;

import java.util.Date;

/**
 * 区域配置表
 * @TableName amg_region_config
 */
@TableName(value ="amg_region_config")
@Data
public class RegionConfigPo extends BasePo {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 区域名称（英文）
     */
    private String regionEnglishName;

    /**
     * 排序序号
     */
    private Integer regionSortOrder;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建人用户名
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新人用户名
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}