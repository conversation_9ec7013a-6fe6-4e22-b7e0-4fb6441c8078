package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description 用户模版默认表
 * <AUTHOR>
 * @date 2025-02-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_label_template_user_default")
public class LabelTemplateUserDefaultPo extends BasePo {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 默认模版id
     */
    private Integer templateId;

    /**
     * 最近使用模版id
     */
    private Integer recentlyUsedTemplateId;


}
