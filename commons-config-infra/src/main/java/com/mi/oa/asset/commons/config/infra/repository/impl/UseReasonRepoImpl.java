package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.use.entity.UseReason;
import com.mi.oa.asset.commons.config.domain.use.repository.UseReasonRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.UseReasonPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.UseReasonMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.UseReasonRepoConverter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【amg_use_reason(用途原因)】的数据库操作Service实现
* @createDate 2024-07-26 20:37:46
*/
@Service
public class UseReasonRepoImpl extends ServiceImpl<UseReasonMapper, UseReasonPo> implements UseReasonRepo {


    @Resource
    private UseReasonRepoConverter converter;

    private LambdaQueryWrapper<UseReasonPo> getWrapper(UseReason entity) {
        UseReasonPo po = converter.toUseReasonPo(entity);
        return Wrappers.lambdaQuery(UseReasonPo.class)
                .eq(Objects.nonNull(po.getUseId()), UseReasonPo::getUseId, po.getUseId());
    }

    @Override
    public List<UseReason> list(UseReason entity) {
        List<UseReasonPo> list = list(getWrapper(entity));
        return converter.toUseReasons(list);
    }
}




