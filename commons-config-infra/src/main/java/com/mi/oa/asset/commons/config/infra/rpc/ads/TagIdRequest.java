/**
 * Autogenerated by Thrift Compiler (0.9.2)
 * <p>
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *
 * @generated
 */
package com.mi.oa.asset.commons.config.infra.rpc.ads;

import org.mi.thrift.EncodingUtils;
import org.mi.thrift.TException;
import org.mi.thrift.protocol.TTupleProtocol;
import org.mi.thrift.scheme.IScheme;
import org.mi.thrift.scheme.SchemeFactory;
import org.mi.thrift.scheme.StandardScheme;
import org.mi.thrift.scheme.TupleScheme;
import org.mi.thrift.server.AbstractNonblockingServer.*;

import javax.annotation.Generated;
import java.util.*;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.2)", date = "2020-5-26")
public class TagIdRequest implements org.mi.thrift.TBase<TagIdRequest, TagIdRequest._Fields>, java.io.Serializable, Cloneable, Comparable<TagIdRequest> {
    private static final org.mi.thrift.protocol.TStruct STRUCT_DESC = new org.mi.thrift.protocol.TStruct("TagIdRequest");

    private static final org.mi.thrift.protocol.TField TYPE_ID_FIELD_DESC = new org.mi.thrift.protocol.TField("type_id", org.mi.thrift.protocol.TType.I16, (short) 1);
    private static final org.mi.thrift.protocol.TField IDS_FIELD_DESC = new org.mi.thrift.protocol.TField("ids", org.mi.thrift.protocol.TType.LIST, (short) 2);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();

    static {
        schemes.put(StandardScheme.class, new TagIdRequestStandardSchemeFactory());
        schemes.put(TupleScheme.class, new TagIdRequestTupleSchemeFactory());
    }

    public short type_id; // required
    public List<Long> ids; // required

    /**
     * The set of fields this struct contains, along with convenience methods for finding and manipulating them.
     */
    public enum _Fields implements org.mi.thrift.TFieldIdEnum {
        TYPE_ID((short) 1, "type_id"),
        IDS((short) 2, "ids");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
            for (_Fields field : EnumSet.allOf(_Fields.class)) {
                byName.put(field.getFieldName(), field);
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch (fieldId) {
                case 1: // TYPE_ID
                    return TYPE_ID;
                case 2: // IDS
                    return IDS;
                default:
                    return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
            _Fields fields = findByThriftId(fieldId);
            if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
            return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
            return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
            _thriftId = thriftId;
            _fieldName = fieldName;
        }

        public short getThriftFieldId() {
            return _thriftId;
        }

        public String getFieldName() {
            return _fieldName;
        }
    }

    // isset id assignments
    private static final int __TYPE_ID_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> metaDataMap;

    static {
        Map<_Fields, org.mi.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.mi.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.TYPE_ID, new org.mi.thrift.meta_data.FieldMetaData("type_id", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I16)));
        tmpMap.put(_Fields.IDS, new org.mi.thrift.meta_data.FieldMetaData("ids", org.mi.thrift.TFieldRequirementType.DEFAULT,
                new org.mi.thrift.meta_data.ListMetaData(org.mi.thrift.protocol.TType.LIST,
                        new org.mi.thrift.meta_data.FieldValueMetaData(org.mi.thrift.protocol.TType.I64))));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.mi.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TagIdRequest.class, metaDataMap);
    }

    public TagIdRequest() {
    }

    public TagIdRequest(
            short type_id,
            List<Long> ids) {
        this();
        this.type_id = type_id;
        setType_idIsSet(true);
        this.ids = ids;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public TagIdRequest(TagIdRequest other) {
        __isset_bitfield = other.__isset_bitfield;
        this.type_id = other.type_id;
        if (other.isSetIds()) {
            List<Long> __this__ids = new ArrayList<Long>(other.ids);
            this.ids = __this__ids;
        }
    }

    public TagIdRequest deepCopy() {
        return new TagIdRequest(this);
    }

    @Override
    public void clear() {
        setType_idIsSet(false);
        this.type_id = 0;
        this.ids = null;
    }

    public short getType_id() {
        return this.type_id;
    }

    public TagIdRequest setType_id(short type_id) {
        this.type_id = type_id;
        setType_idIsSet(true);
        return this;
    }

    public void unsetType_id() {
        __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TYPE_ID_ISSET_ID);
    }

    /**
     * Returns true if field type_id is set (has been assigned a value) and false otherwise
     */
    public boolean isSetType_id() {
        return EncodingUtils.testBit(__isset_bitfield, __TYPE_ID_ISSET_ID);
    }

    public void setType_idIsSet(boolean value) {
        __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TYPE_ID_ISSET_ID, value);
    }

    public int getIdsSize() {
        return (this.ids == null) ? 0 : this.ids.size();
    }

    public Iterator<Long> getIdsIterator() {
        return (this.ids == null) ? null : this.ids.iterator();
    }

    public void addToIds(long elem) {
        if (this.ids == null) {
            this.ids = new ArrayList<Long>();
        }
        this.ids.add(elem);
    }

    public List<Long> getIds() {
        return this.ids;
    }

    public TagIdRequest setIds(List<Long> ids) {
        this.ids = ids;
        return this;
    }

    public void unsetIds() {
        this.ids = null;
    }

    /**
     * Returns true if field ids is set (has been assigned a value) and false otherwise
     */
    public boolean isSetIds() {
        return this.ids != null;
    }

    public void setIdsIsSet(boolean value) {
        if (!value) {
            this.ids = null;
        }
    }

    public void setFieldValue(_Fields field, Object value) {
        switch (field) {
            case TYPE_ID:
                if (value == null) {
                    unsetType_id();
                } else {
                    setType_id((Short) value);
                }
                break;

            case IDS:
                if (value == null) {
                    unsetIds();
                } else {
                    setIds((List<Long>) value);
                }
                break;

        }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
            case TYPE_ID:
                return Short.valueOf(getType_id());

            case IDS:
                return getIds();

        }
        throw new IllegalStateException();
    }

    /**
     * Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise
     */
    public boolean isSet(_Fields field) {
        if (field == null) {
            throw new IllegalArgumentException();
        }

        switch (field) {
            case TYPE_ID:
                return isSetType_id();
            case IDS:
                return isSetIds();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
            return false;
        if (that instanceof TagIdRequest)
            return this.equals((TagIdRequest) that);
        return false;
    }

    public boolean equals(TagIdRequest that) {
        if (that == null)
            return false;

        boolean this_present_type_id = true;
        boolean that_present_type_id = true;
        if (this_present_type_id || that_present_type_id) {
            if (!(this_present_type_id && that_present_type_id))
                return false;
            if (this.type_id != that.type_id)
                return false;
        }

        boolean this_present_ids = true && this.isSetIds();
        boolean that_present_ids = true && that.isSetIds();
        if (this_present_ids || that_present_ids) {
            if (!(this_present_ids && that_present_ids))
                return false;
            if (!this.ids.equals(that.ids))
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> list = new ArrayList<Object>();

        boolean present_type_id = true;
        list.add(present_type_id);
        if (present_type_id)
            list.add(type_id);

        boolean present_ids = true && (isSetIds());
        list.add(present_ids);
        if (present_ids)
            list.add(ids);

        return list.hashCode();
    }

    @Override
    public int compareTo(TagIdRequest other) {
        if (!getClass().equals(other.getClass())) {
            return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetType_id()).compareTo(other.isSetType_id());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetType_id()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.type_id, other.type_id);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetIds()).compareTo(other.isSetIds());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetIds()) {
            lastComparison = org.mi.thrift.TBaseHelper.compareTo(this.ids, other.ids);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
        return _Fields.findByThriftId(fieldId);
    }

    public void read(org.mi.thrift.protocol.TProtocol iprot) throws TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.mi.thrift.protocol.TProtocol oprot) throws TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("TagIdRequest(");
        boolean first = true;

        sb.append("type_id:");
        sb.append(this.type_id);
        first = false;
        if (!first) sb.append(", ");
        sb.append("ids:");
        if (this.ids == null) {
            sb.append("null");
        } else {
            sb.append(this.ids);
        }
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        try {
            write(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(out)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        try {
            // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
            __isset_bitfield = 0;
            read(new org.mi.thrift.protocol.TCompactProtocol(new org.mi.thrift.transport.TIOStreamTransport(in)));
        } catch (TException te) {
            throw new java.io.IOException(te);
        }
    }

    private static class TagIdRequestStandardSchemeFactory implements SchemeFactory {
        public TagIdRequestStandardScheme getScheme() {
            return new TagIdRequestStandardScheme();
        }
    }

    private static class TagIdRequestStandardScheme extends StandardScheme<TagIdRequest> {

        public void read(org.mi.thrift.protocol.TProtocol iprot, TagIdRequest struct) throws TException {
            org.mi.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true) {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.mi.thrift.protocol.TType.STOP) {
                    break;
                }
                switch (schemeField.id) {
                    case 1: // TYPE_ID
                        if (schemeField.type == org.mi.thrift.protocol.TType.I16) {
                            struct.type_id = iprot.readI16();
                            struct.setType_idIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // IDS
                        if (schemeField.type == org.mi.thrift.protocol.TType.LIST) {
                            {
                                org.mi.thrift.protocol.TList _list16 = iprot.readListBegin();
                                struct.ids = new ArrayList<Long>(_list16.size);
                                long _elem17;
                                for (int _i18 = 0; _i18 < _list16.size; ++_i18) {
                                    _elem17 = iprot.readI64();
                                    struct.ids.add(_elem17);
                                }
                                iprot.readListEnd();
                            }
                            struct.setIdsIsSet(true);
                        } else {
                            org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                        org.mi.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.mi.thrift.protocol.TProtocol oprot, TagIdRequest struct) throws TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            oprot.writeFieldBegin(TYPE_ID_FIELD_DESC);
            oprot.writeI16(struct.type_id);
            oprot.writeFieldEnd();
            if (struct.ids != null) {
                oprot.writeFieldBegin(IDS_FIELD_DESC);
                {
                    oprot.writeListBegin(new org.mi.thrift.protocol.TList(org.mi.thrift.protocol.TType.I64, struct.ids.size()));
                    for (long _iter19 : struct.ids) {
                        oprot.writeI64(_iter19);
                    }
                    oprot.writeListEnd();
                }
                oprot.writeFieldEnd();
            }
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class TagIdRequestTupleSchemeFactory implements SchemeFactory {
        public TagIdRequestTupleScheme getScheme() {
            return new TagIdRequestTupleScheme();
        }
    }

    private static class TagIdRequestTupleScheme extends TupleScheme<TagIdRequest> {

        @Override
        public void write(org.mi.thrift.protocol.TProtocol prot, TagIdRequest struct) throws TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetType_id()) {
                optionals.set(0);
            }
            if (struct.isSetIds()) {
                optionals.set(1);
            }
            oprot.writeBitSet(optionals, 2);
            if (struct.isSetType_id()) {
                oprot.writeI16(struct.type_id);
            }
            if (struct.isSetIds()) {
                {
                    oprot.writeI32(struct.ids.size());
                    for (long _iter20 : struct.ids) {
                        oprot.writeI64(_iter20);
                    }
                }
            }
        }

        @Override
        public void read(org.mi.thrift.protocol.TProtocol prot, TagIdRequest struct) throws TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(2);
            if (incoming.get(0)) {
                struct.type_id = iprot.readI16();
                struct.setType_idIsSet(true);
            }
            if (incoming.get(1)) {
                {
                    org.mi.thrift.protocol.TList _list21 = new org.mi.thrift.protocol.TList(org.mi.thrift.protocol.TType.I64, iprot.readI32());
                    struct.ids = new ArrayList<Long>(_list21.size);
                    long _elem22;
                    for (int _i23 = 0; _i23 < _list21.size; ++_i23) {
                        _elem22 = iprot.readI64();
                        struct.ids.add(_elem22);
                    }
                }
                struct.setIdsIsSet(true);
            }
        }
    }

}

