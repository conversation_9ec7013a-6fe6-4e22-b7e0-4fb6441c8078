package com.mi.oa.asset.commons.config.infra.rpc.utils;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 */
public class X5HttpUtil {


    private static final String SOCKET_TIMEOUT = "http.socket.timeout";
    private static final int TIME_OUT = 1000 * 59;
    private static final String CHAR_SET = "UTF-8";


    public static String doX5Post(String url, String data) throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("data", data);
        DefaultHttpClient httpclient = new DefaultHttpClient();
        HttpRequestBase httpPost = new HttpPost();
        String content = "";
        // 开始添加参数
        List<NameValuePair> baluePairs = new ArrayList<NameValuePair>();
        Set<String> keys = params.keySet();
        for (Iterator<String> it = keys.iterator(); it.hasNext(); ) {
            String k = it.next();
            baluePairs.add(new BasicNameValuePair(k, params.get(k)));
        }
        UrlEncodedFormEntity uefEntity;
        try {
            httpclient.getParams().setIntParameter(SOCKET_TIMEOUT, TIME_OUT);
            uefEntity = new UrlEncodedFormEntity(baluePairs, CHAR_SET);
            httpPost.setURI(new java.net.URI(url));
            ((HttpPost) httpPost).setEntity(uefEntity);
            HttpResponse response = httpclient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            content = EntityUtils.toString(entity, CHAR_SET);
        } catch (Exception e) {
            throw new Exception(e);
        } finally {
            httpclient.getConnectionManager().shutdown();
            httpPost.abort();
        }
        return content;
    }

}
