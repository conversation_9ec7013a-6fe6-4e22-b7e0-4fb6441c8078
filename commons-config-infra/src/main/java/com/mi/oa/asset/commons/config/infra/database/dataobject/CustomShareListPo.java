package com.mi.oa.asset.commons.config.infra.database.dataobject;


import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025-01-06 15:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "amg_custom_share_list")
public class CustomShareListPo extends BasePo {
    /**
     * 共享记录主键
     */
    private Integer shareId;

    /**
     * 资产台账主键
     */
    private Integer assetId;

    /**
     * 台账数量
     */
    private Integer assetQuantity;

    /**
     * 共享数量
     */
    private Integer shareQuantity;
}

