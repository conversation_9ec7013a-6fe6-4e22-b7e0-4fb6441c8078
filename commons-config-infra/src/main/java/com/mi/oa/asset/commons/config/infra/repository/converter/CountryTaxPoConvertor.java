package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.international.entity.CountryTaxDo;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.CountryTaxPo;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 *
 */
@Mapper(componentModel = "spring")
public interface CountryTaxPoConvertor extends CommonConverter {

    CountryTaxDo poToDo(CountryTaxPo countryTaxPo);

    CountryTaxPo doToPo(CountryTaxDo countryTaxDo);

    List<CountryTaxDo> poToDoList(List<CountryTaxPo> countryTaxPoList);

    @Named("toJsonString")
    default String toJsonString(Map<String, String> source) {
        if (Objects.isNull(source) || source.isEmpty()) return StringUtils.EMPTY;
        return JacksonUtils.bean2Json(source);
    }

    @Named("toMap")
    default Map<String, String> toMap(String source) {
        if (StringUtils.isEmpty(source)) return null;
        return JacksonUtils.json2Bean(source, Map.class);
    }
}
