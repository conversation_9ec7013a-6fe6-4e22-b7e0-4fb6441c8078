package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSku;
import com.mi.oa.asset.commons.config.domain.assetsku.repository.AssetSkuRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetSkuPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.AssetSkuPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AssetSkuRepoConverter;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/10/9 14:44
 */

@Service
public class AssetSkuRepoImpl extends ServiceImpl<AssetSkuPoMapper, AssetSkuPo> implements AssetSkuRepo {

    @Resource
    private AssetSkuRepoConverter converter;
    @Resource
    private AssetSkuPoMapper assetSkuPoMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveAssetSku(AssetSku assetSku) {
        AssetSkuPo data = converter.toAssetSkuPo(assetSku);
        this.saveOrUpdate(data);
        assetSku.setSkuId(data.getId());
    }

    @Override
    public void batchSaveAssetSku(List<AssetSku> assetSkus) {
        if (CollectionUtils.isEmpty(assetSkus)) return;
        List<AssetSkuPo> assetSkuPos = converter.toAssetSkuPos(assetSkus);
        this.saveBatch(assetSkuPos);

        // 根据SkuCode， 将assetSkuPos 的主键返回给assetSkus
        // assetSkuPos 转换为skuCode 的map
        Map<String, Integer> skuCodeToIdMap = assetSkuPos.stream()
                .collect(Collectors.toMap(AssetSkuPo::getSkuCode, AssetSkuPo::getId));

        for (AssetSku assetSku : assetSkus) {
            assetSku.setSkuId(skuCodeToIdMap.get(assetSku.getSkuCode()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchAssetSku(List<AssetSku> assetSkus) {
        if (CollectionUtils.isEmpty(assetSkus)) return;
        List<AssetSkuPo> assetSkuPos = converter.toAssetSkuPos(assetSkus);
        updateBatchById(assetSkuPos);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteAssetSku(List<Integer> ids) {
        removeByIds(ids);
    }

    @Override
    public boolean isExists(String skuCode) {
        if (StringUtils.isBlank(skuCode)) {
            return false;
        }
        return new LambdaQueryChainWrapper<>(assetSkuPoMapper)
                .eq(AssetSkuPo::getSkuCode, skuCode)
                .eq(AssetSkuPo::getIsDeleted, 0)
                .exists();
    }

    @Override
    public PageData<AssetSku> getAssetSkuPageData(List<Integer> relationCateIds, String keyword, PageRequest pageRequest, Boolean disabled, Boolean containMiGoods) {
        if (CollectionUtils.isEmpty(relationCateIds)) {
            return PageData.of(new ArrayList<>(), pageRequest.getPageSize(), pageRequest.getPageNum(), 0);
        }

        IPage<AssetSkuPo> page = page(
                new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize()),
                Wrappers.lambdaQuery(AssetSkuPo.class)
                        .eq(disabled != null, AssetSkuPo::getDisabled, disabled)
                        .in(CollectionUtils.isNotEmpty(relationCateIds), AssetSkuPo::getCateId, relationCateIds)
                        .eq(!containMiGoods, AssetSkuPo::getMiGoodsId, "")
                        .eq(!containMiGoods, AssetSkuPo::getMiSkuCode, "")
                        .nested(StringUtils.isNotBlank(keyword),
                                q -> q.like(AssetSkuPo::getCateCode, keyword)
                                        .or().like(AssetSkuPo::getSkuName, keyword)
                                        .or().like(AssetSkuPo::getSkuCode, keyword)
                        )
        );

        return converter.toPageData(page, converter::toAssetSkus);
    }

    @Override
    public PageData<AssetSku> getAssetSkuPageDataV1(List<Integer> relationCateIds, List<String> businessLines, String keyword,
                                                    PageRequest pageRequest, Boolean disabled, Boolean containMiGoods) {
        IPage<AssetSkuPo> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        IPage<AssetSkuPo> assetSkuPoIPage = assetSkuPoMapper.pageSku(relationCateIds, businessLines, keyword,
                disabled, containMiGoods, page);
        return converter.toPageData(assetSkuPoIPage, converter::toAssetSkus);
    }

    @Override
    public List<AssetSku> listAssetSkuByParams(List<Integer> relationCateIds, List<String> businessLines, String keyword) {
        if (CollectionUtils.isEmpty(relationCateIds)) {
            return Collections.emptyList();
        }
        List<AssetSkuPo> list = list(Wrappers.lambdaQuery(AssetSkuPo.class)
                .in(AssetSkuPo::getBusinessLine, businessLines)
                .in(AssetSkuPo::getCateId, relationCateIds)
                .nested(StringUtils.isNotBlank(keyword),
                        q -> q.like(AssetSkuPo::getCateCode, keyword)
                                .or().like(AssetSkuPo::getSkuName, keyword)
                                .or().like(AssetSkuPo::getSkuCode, keyword)
                ));
        return converter.toAssetSkus(list);
    }

    @Override
    public AssetSku getAssetSku(Integer skuId) {
        return converter.toAssetSku(getById(skuId));
    }

    @Override
    public List<AssetSku> getAssetSkus(List<Integer> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) return new ArrayList<>();

        return converter.toAssetSkus(listByIds(skuIds));
    }

    @Override
    public List<AssetSku> getAssetSkuCodes(List<String> skuCodes) {
        if (CollectionUtils.isEmpty(skuCodes)) return Collections.emptyList();
        return converter.toAssetSkus(list(Wrappers.lambdaQuery(AssetSkuPo.class)
                .in(AssetSkuPo::getSkuCode, skuCodes)
                .eq(AssetSkuPo::getIsDeleted, 0)));
    }

    @Override
    public AssetSku getAssetSkuBySkuCode(String skuCode) {
        AssetSkuPo data = new LambdaQueryChainWrapper<>(assetSkuPoMapper)
                .eq(AssetSkuPo::getSkuCode, skuCode)
                .eq(AssetSkuPo::getIsDeleted, 0)
                .one();
        return converter.toAssetSku(data);
    }

    @Override
    public long getAssetSkuCountsByCategory(List<Integer> cateIds) {
        return this.count(Wrappers.lambdaQuery(AssetSkuPo.class)
                .in(AssetSkuPo::getCateId, cateIds)
                .eq(AssetSkuPo::getIsDeleted, 0));
    }

    @Override
    public List<Boolean> updateFromDistribute(List<AssetSku> assetSkus) {
        if (CollectionUtils.isEmpty(assetSkus)) {
            return Collections.emptyList();
        }
        List<Boolean> results = new ArrayList<>(assetSkus.size());
        for (AssetSku assetSku : assetSkus) {
            if (assetSku == null || assetSku.getSkuCode() == null) {
                results.add(false);
                continue;
            }
            AssetSkuPo data = converter.toAssetSkuPo(assetSku);
            boolean updated = this.update(data, Wrappers.lambdaUpdate(AssetSkuPo.class)
                    .eq(AssetSkuPo::getSkuCode, assetSku.getSkuCode())
                    .eq(AssetSkuPo::getIsDeleted, 0));
            results.add(updated);
        }
        return results;
    }
}
