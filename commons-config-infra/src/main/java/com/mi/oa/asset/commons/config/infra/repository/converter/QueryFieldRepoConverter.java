package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.queryfield.entity.FieldConfig;
import com.mi.oa.asset.commons.config.domain.queryfield.entity.QueryConfig;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FieldConfigPo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.QueryConfigPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/8 11:16
 */

@Mapper(componentModel = "spring")
public interface QueryFieldRepoConverter extends CommonConverter {

    @Mapping(target = "id", source = "queryId")
    QueryConfigPo queryConfigToPo(QueryConfig queryConfig);

    @Mapping(target = "queryId", source = "id")
    QueryConfig queryConfigPoToDo(QueryConfigPo queryConfigPo);

    List<QueryConfig> queryConfigPoToDoList(List<QueryConfigPo> queryConfigPos);

    @Mapping(target = "id", source = "fieldId")
    FieldConfigPo fieldConfigToPo(FieldConfig fieldConfig);

    @Mapping(target = "fieldId", source = "id")
    FieldConfig fieldConfigPoToDo(FieldConfigPo fieldConfigPo);

}
