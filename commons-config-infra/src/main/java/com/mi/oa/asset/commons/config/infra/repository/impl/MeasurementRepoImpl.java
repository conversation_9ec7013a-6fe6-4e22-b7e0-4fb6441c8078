package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.MeasurementUnitQuery;
import com.mi.oa.asset.commons.config.domain.mesurement.entity.MeasurementUnit;
import com.mi.oa.asset.commons.config.domain.mesurement.repository.MeasurementRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.MeasurementUnitPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.MeasurementUnitPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.MeasurementRepoConverter;
import com.mi.oa.asset.commons.config.infra.repository.service.MeasurementService;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class MeasurementRepoImpl implements MeasurementRepo {

    @Autowired
    private MeasurementService measurementService;

    @Autowired
    private MeasurementUnitPoMapper measurementUnitPoMapper;

    @Autowired
    private MeasurementRepoConverter converter;

    @Override
    public List<MeasurementUnit> queryListByMeasureCode(String measureCode) {
        return converter.toMeasurementUnits(measurementUnitPoMapper.selectList(Wrappers.lambdaQuery(MeasurementUnitPo.class)
                .like(MeasurementUnitPo::getMeasureCode, measureCode)));
    }

    @Override
    public List<MeasurementUnit> queryListByMeasureCodes(List<String> measureCodes) {
        if (CollectionUtils.isEmpty(measureCodes)) {
            return Collections.emptyList();
        }
        return converter.toMeasurementUnits(measurementUnitPoMapper.selectList(Wrappers.lambdaQuery(MeasurementUnitPo.class)
                .in(MeasurementUnitPo::getMeasureCode, measureCodes)));
    }

    @Override
    public List<MeasurementUnit> queryListByMeasureNames(List<String> measureNames) {
        if (CollectionUtils.isEmpty(measureNames)) {
            return Collections.emptyList();
        }
        return converter.toMeasurementUnits(measurementUnitPoMapper.selectList(Wrappers.lambdaQuery(MeasurementUnitPo.class)
                .in(MeasurementUnitPo::getMeasureName, measureNames)));
    }

    @Override
    public List<MeasurementUnit> getMeasurementUnits(List<String> muIds) {
        if (CollectionUtils.isEmpty(muIds)) {
            return Collections.emptyList();
        }
        return converter.toMeasurementUnits(measurementService.list(Wrappers.lambdaQuery(MeasurementUnitPo.class)
                .in(MeasurementUnitPo::getMuId, muIds)));
    }

    @Override
    public List<MeasurementUnit> queryAllMeasurementUnits() {
        return converter.toMeasurementUnits(measurementUnitPoMapper.selectList(Wrappers.lambdaQuery(MeasurementUnitPo.class)));
    }

    @Override
    public void createMeasurements(List<MeasurementUnit> creates) {
        if (CollectionUtils.isEmpty(creates)) {
            return;
        }
        measurementService.saveBatch(converter.toMeasurementUnitPos(creates));
    }

    @Override
    public void updateMeasurements(List<MeasurementUnit> updates) {
        if (CollectionUtils.isEmpty(updates)) {
            return;
        }
        measurementService.updateBatchById(converter.toMeasurementUnitPos(updates));
    }

    @Override
    public void inactivateMeasurement(List<String> muIds) {
        if (CollectionUtils.isEmpty(muIds)) {
            return;
        }
        measurementService.update(Wrappers.lambdaUpdate(MeasurementUnitPo.class)
                .set(MeasurementUnitPo::getDisabled, 1)
                .in(MeasurementUnitPo::getMuId, muIds));
    }

    @Override
    public PageData<MeasurementUnit> measurementUnitPageData(MeasurementUnitQuery params, PageRequest pageRequest) {
        Page<MeasurementUnitPo> page = measurementService.page(
                new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize()),
                commonQueryWrapper(params)
        );

        return converter.toPageData(page, converter::toMeasurementUnits);
    }

    private LambdaQueryWrapper<MeasurementUnitPo> commonQueryWrapper(MeasurementUnitQuery params) {
        LambdaQueryWrapper<MeasurementUnitPo> wrapper = Wrappers.lambdaQuery(MeasurementUnitPo.class);
        if (StringUtils.isBlank(params.getKeyword())) {
            // 如果查询参数都为null，查询自增id不存在的数据，避免全表查询
            wrapper.eq(MeasurementUnitPo::getId, -10000);

            return wrapper;
        }

        wrapper.nested(StringUtils.isNotBlank(params.getKeyword()),
                        q -> q.like(MeasurementUnitPo::getMeasureCode, params.getKeyword()).or()
                                .like(MeasurementUnitPo::getMeasureName, params.getKeyword()))
                .orderByAsc(MeasurementUnitPo::getId);

        return wrapper;
    }
}
