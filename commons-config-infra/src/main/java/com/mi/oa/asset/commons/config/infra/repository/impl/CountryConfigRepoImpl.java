package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryConfigRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.CountryConfigPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.CountryConfigPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.CountryConfigPoConvertor;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 【amg_country_config(国家地区配置表)】
* @createDate 2025-04-29 15:01:49
*/
@Service
public class CountryConfigRepoImpl extends ServiceImpl<CountryConfigPoMapper, CountryConfigPo>
    implements CountryConfigRepo {

    @Resource
    private CountryConfigPoConvertor converter;

    @Resource
    private CountryConfigPoMapper countryConfigPoMapper;
    @Override
    public List<CountryConfigDo> searchAll() {
        List<CountryConfigPo> countryConfigPoList = countryConfigPoMapper.selectList(new LambdaQueryWrapper<>());
        return converter.poToDoList(countryConfigPoList);
    }

    @Override
    public List<CountryConfigDo> getByRegionId(Integer regionId) {
        List<CountryConfigPo> countryConfigPoList = countryConfigPoMapper.selectList(new LambdaQueryWrapper<CountryConfigPo>()
                .eq(CountryConfigPo::getRegionId, regionId));
        return converter.poToDoList(countryConfigPoList);
    }

    @Override
    public List<CountryConfigDo> getByThreeCode(List<String> threeCode) {
        if (Objects.isNull(threeCode) || threeCode.isEmpty()) {
            return Collections.emptyList();
        }
        List<CountryConfigPo> countryConfigPoList = countryConfigPoMapper.selectList(new LambdaQueryWrapper<CountryConfigPo>()
                .in(CountryConfigPo::getCountryCodeAlphaThree, threeCode));
        return converter.poToDoList(countryConfigPoList);
    }

    @Override
    public List<CountryConfigDo> getByRegionIds(List<Integer> idList) {
        List<CountryConfigPo> countryConfigPoList = countryConfigPoMapper.selectList(new LambdaQueryWrapper<CountryConfigPo>()
                .in(CountryConfigPo::getRegionId, idList));
        return converter.poToDoList(countryConfigPoList);
    }

    @Override
    public CountryConfigDo getByCountryCodeAlphaThree(String countryCodeAlphaThree) {
        if (StringUtil.isEmpty(countryCodeAlphaThree)) return null;
        CountryConfigPo countryConfigPo = countryConfigPoMapper.selectOne(new LambdaQueryWrapper<CountryConfigPo>()
                .eq(CountryConfigPo::getCountryCodeAlphaThree, countryCodeAlphaThree));
        return converter.poToDo(countryConfigPo);
    }

    @Override
    public CountryConfigDo getById(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        CountryConfigPo po = this.baseMapper.selectById(id);
        return converter.poToDo(po);
    }

    @Override
    public List<CountryConfigDo> getByIds(List<Integer> countryIdList) {
        if (CollectionUtils.isEmpty(countryIdList)) {
            return null;
        }
        List<CountryConfigPo> countryConfigPoList = this.baseMapper.selectBatchIds(countryIdList);
        return converter.poToDoList(countryConfigPoList);
    }

    @Override
    public Integer save(CountryConfigDo countryConfigDo) {
        CountryConfigPo po = converter.doToPo(countryConfigDo);
        this.save(po);
        return po.getId();
    }

    @Override
    public Integer updateById(CountryConfigDo entity) {
        CountryConfigPo po = converter.doToPo(entity);
       return this.baseMapper.updateById(po);
    }

    @Override
    public void updateBatchById(List<CountryConfigDo> doList) {
        List<CountryConfigPo> countryConfigPos = converter.listDoToPo(doList);
        updateBatchById(countryConfigPos);
    }

    @Override
    public void deleteByIds(List<Integer> idList) {
        this.baseMapper.deleteBatchIds(idList);
    }

}




