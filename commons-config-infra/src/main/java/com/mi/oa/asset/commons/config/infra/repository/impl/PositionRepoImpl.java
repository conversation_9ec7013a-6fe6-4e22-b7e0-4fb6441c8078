package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import com.mi.oa.asset.commons.config.domain.position.entity.Position;
import com.mi.oa.asset.commons.config.domain.position.repository.PositionRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.PositionPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.PositionPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.PositionRepoConverter;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/16 10:32
 */

@Service
public class PositionRepoImpl extends ServiceImpl<PositionPoMapper, PositionPo> implements PositionRepo {

    @Resource
    private PositionRepoConverter converter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePosition(Position position) {
        PositionPo positionPo = converter.toPositionPo(position);
        saveOrUpdate(positionPo);
        position.setPositionId(positionPo.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePositions(List<Position> positions) {
        List<PositionPo> positionPos = converter.toPositionPo(positions);
        saveOrUpdateBatch(positionPos);
        positions.forEach(c -> positionPos.stream()
                .filter(d -> d.getPositionCode().equals(c.getPositionCode()))
                .findFirst()
                .ifPresent(d -> c.setPositionId(d.getId())));
    }

    @Override
    public void updatePositiions(List<Position> positions) {
        if(CollectionUtils.isEmpty(positions)) return;
        List<PositionPo> positionPos = converter.toPositionPo(positions);
        updateBatchById(positionPos);
    }

    public void updatePositionPath(Position position) {
        position.setPositionPath(position.getPositionPath() + "-" + position.getPositionId());
        updateById(converter.toPositionPo(position));
    }

    @Override
    public Position getPosition(Integer positionId) {
        return converter.toPosition(getById(positionId));
    }

    @Override
    public List<Position> getPositions(List<Integer> positionIds) {
        return converter.toPosition(listByIds(positionIds));
    }

    @Override
    public Position getPositionByCode(String positionCode, BusinessLine businessLine) {
        return converter.toPosition(
                lambdaQuery().eq(PositionPo::getPositionCode, positionCode)
                        .eq(PositionPo::getBusinessLine, null != businessLine ? businessLine.getCode() : "null")
                        .one()
        );
    }

    @Override
    public List<Position> getPositionByCodes(List<String> positionCodes, BusinessLine businessLine) {
        return converter.toPosition(
                lambdaQuery().in(PositionPo::getPositionCode, positionCodes)
                        .eq(PositionPo::getBusinessLine, null != businessLine ? businessLine.getCode() : "null")
                        .list()
        );
    }

    @Override
    public PageData<Position> getPositionPageData(String positionPath, String keyword, PageRequest pageRequest) {
        Page<PositionPo> page = this.page(
                new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize()),
                Wrappers.lambdaQuery(PositionPo.class)
                        .nested(StringUtils.isNotBlank(positionPath),
                                q -> q.eq(PositionPo::getPositionPath, positionPath)
                                        .or().likeRight(PositionPo::getPositionPath, positionPath + "-")

                        )
                        .nested(StringUtils.isNotBlank(keyword),
                                q -> q.like(PositionPo::getPositionCode, keyword)
                                        .or().like(PositionPo::getPositionName, keyword).or().like(PositionPo::getPositionNameEn, keyword)
                        )
        );

        return converter.toPageData(page, converter::toPosition);
    }

    @Override
    public PageData<Position> getPositionPageData(List<String> businessLineCodes, String keyword, PageRequest pageRequest) {
        Page<PositionPo> page = this.page(
                new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize()),
                Wrappers.lambdaQuery(PositionPo.class)
                        .in(CollectionUtils.isNotEmpty(businessLineCodes), PositionPo::getBusinessLine, businessLineCodes)
                        .nested(StringUtils.isNotBlank(keyword),
                                q -> q.like(PositionPo::getPositionCode, keyword)
                                        .or().like(PositionPo::getPositionName, keyword)
                        )
        );

        return converter.toPageData(page, converter::toPosition);
    }

    @Override
    public void deleteByPaths(List<String> paths) {
        if(CollectionUtils.isEmpty(paths)) return;

        lambdaUpdate().in(PositionPo::getPositionPath, paths)
                .or().nested(q -> paths.forEach(p -> q.or().likeRight(PositionPo::getPositionPath, p + "-")))
                .remove();
    }

    @Override
    public List<Position> getAllPosition(List<String> businessLineCodes) {
        if(CollectionUtils.isEmpty(businessLineCodes)) return new ArrayList<>();

        return converter.toPosition(lambdaQuery().in(PositionPo::getBusinessLine, businessLineCodes).list());
    }

    @Override
    public List<Position> getPositionByBusinessLine(BusinessLine businessLine, DataCreateSource source) {
        if (null == businessLine || source == null) return Collections.emptyList();
        return converter.toPosition(lambdaQuery()
                .in(PositionPo::getBusinessLine, businessLine.getCode())
                .eq(PositionPo::getDataSource, source.getCode())
                .list());
    }


    @Override
    public List<Position> getPositionByPaths(List<String> positionPaths) {
        if(CollectionUtils.isEmpty(positionPaths)) return new ArrayList<>();

        return converter.toPosition(
                lambdaQuery().in(PositionPo::getPositionPath, positionPaths)
                        .or().nested(q -> positionPaths.forEach(p -> q.or().likeRight(PositionPo::getPositionPath, p + "-")))
                        .list()
        );
    }
}
