package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.api.assetcode.FieldRuleConfigRes;
import com.mi.oa.asset.commons.config.domain.systemvar.valobj.FieldRuleConfig;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-07-08 14:27
 */
@Mapper(componentModel = "spring")
public interface AssetCodeRuleConverter {

    FieldRuleConfigRes voToRes(FieldRuleConfig vo);

    List<FieldRuleConfigRes> voToRes(List<FieldRuleConfig> voList);
}
