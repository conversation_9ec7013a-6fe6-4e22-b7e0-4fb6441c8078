package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfigUser;
import com.mi.oa.asset.commons.config.domain.common.repository.FunctionConfigUserRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FunctionConfigUserPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.FunctionConfigUserMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.FunctionConfigConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/6 21:15
 **/
@Service
public class FunctionConfigUserRepoImpl extends ServiceImpl<FunctionConfigUserMapper, FunctionConfigUserPo> implements FunctionConfigUserRepo {

    @Resource
    private FunctionConfigConverter converter;

    @Override
    public void saveConfigUser(List<FunctionConfigUser> configUsers) {
        List<FunctionConfigUserPo> configUserPos = converter.toConfigUserPos(configUsers);
        saveOrUpdateBatch(configUserPos);
    }

    @Override
    public List<FunctionConfigUser> listByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) return Collections.emptyList();
        List<FunctionConfigUserPo> functionConfigUserPos = baseMapper.selectList(Wrappers.lambdaQuery(FunctionConfigUserPo.class)
                .eq(FunctionConfigUserPo::getUserCode, userCode));
        return converter.poToConfigUsers(functionConfigUserPos);
    }
}
