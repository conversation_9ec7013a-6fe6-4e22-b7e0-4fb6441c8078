package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date    2023/9/27 17:19
 */

/**
 * 资产分类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "amg_asset_category")
public class AssetCategoryPo extends BasePo {
    /**
     * 业务线
     */
    @TableField(value = "business_line")
    private String businessLine;

    /**
     * 分类编码
     */
    @TableField(value = "cate_code")
    private String cateCode;

    /**
     * 分类名称
     */
    @TableField(value = "cate_name")
    private String cateName;

    /**
     * 分类名称（英文）
     */
    @TableField(value = "cate_name_en")
    private String cateNameEn;

    /**
     * 级别
     */
    @TableField(value = "`level`")
    private Integer level;

    /**
     * 上级编码
     */
    @TableField(value = "parent_cate_code")
    private String parentCateCode;

    /**
     * 分类路径
     */
    @TableField(value = "cate_path")
    private String catePath;
    /**
     * 国家编码 默认CHN
     */
    @TableField(value = "country")
    private String country;
    /**
     * 国家
     */
    @TableField(value = "country_name")
    private String countryName;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 禁用
     */
    @TableField(value = "disabled")
    private Integer disabled;

    /**
     * sap资产分类编码
     */
    @TableField(value = "sap_cate_code")
    private String sapCateCode;

    /**
     * sap资产分类名称
     */
    @TableField(value = "sap_cate_name")
    private String sapCateName;

    /**
     * 使用年限，默认使用采购目录对应的折旧年限
     */
    @TableField(value = "use_year")
    private Integer useYear;

    /**
     * 使用期间
     */
    @TableField(value = "use_month")
    private Integer useMonth;

    /**
     * 是否唯一码管理
     */
    @TableField(value = "is_serial_code_manage")
    private Integer isSerialCodeManage;

    /**
     * 关联采购目录编码
     */
    @TableField(value = "purchase_catalog_code")
    private String purchaseCatalogCode;

    /**
     * 关联采购目录编码（全路径）
     */
    @TableField(value = "purchase_catalog_code_path")
    private String purchaseCatalogCodePath;

    /**
     * 关联采购目录名称
     */
    @TableField(value = "purchase_catalog_name")
    private String purchaseCatalogName;

    /**
     * 关联采购目录名称（全路径）
     */
    @TableField(value = "purchase_catalog_name_path")
    private String purchaseCatalogNamePath;

    /**
     * 数据来源, 默认手动录入
     */
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * 是否一卡多物管理
     */
    @TableField(value = "is_multiple_manage")
    private Integer isMultipleManage;

    /**
     * 物料类型
     */
    @TableField(value = "material_type")
    private String materialType;
    /**
     * 管理模式
     */
    @TableField(value = "mg_model")
    private String mgModel;
    /**
     * 品类经理账号
     */
    @TableField(value = "manager_code")
    private String managerCode;
    /**
     * 品类经理
     */
    @TableField(value = "manager_name")
    private String managerName;
    /**
     * 交期(天)
     */
    @TableField(value = "delivery_days")
    private Integer deliveryDays;
    /**
     * 编码生成方式
     */
    @TableField(value = "code_gen_type")
    private Integer codeGenType;
}