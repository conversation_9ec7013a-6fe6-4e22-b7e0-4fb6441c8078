package com.mi.oa.asset.commons.config.infra.repository.converter;


import com.mi.oa.asset.commons.config.domain.use.entity.UseReason;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.UseReasonPo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UseReasonRepoConverter extends CommonConverter {

    UseReason toUseReason(UseReasonPo source);

    UseReasonPo toUseReasonPo(UseReason source);

    List<UseReason> toUseReasons(List<UseReasonPo> source);
}