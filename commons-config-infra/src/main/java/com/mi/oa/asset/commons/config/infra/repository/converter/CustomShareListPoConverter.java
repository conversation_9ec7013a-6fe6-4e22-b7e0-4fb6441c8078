package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.assetshare.entity.CustomShareList;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.CustomShareListPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-07 15:17
 */
@Mapper(componentModel = "spring")
public interface CustomShareListPoConverter extends CommonConverter {
    CustomShareListPo doToPo(CustomShareList customShareList);

    List<CustomShareListPo> doToPoList(List<CustomShareList> shareLists);

    CustomShareList poToDo(CustomShareListPo customShareListPo);

    List<CustomShareList> poToDoList(List<CustomShareListPo> shareListPos);
}
