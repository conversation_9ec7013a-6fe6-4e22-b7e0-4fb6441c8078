package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.CustomShareList;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.CustomShareListRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.CustomShareListPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.CustomShareListPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.CustomShareListPoConverter;
import com.xiaomi.mit.api.PageData;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-07 15:12
 */
@Service
public class CustomShareListRepoImpl extends ServiceImpl<CustomShareListPoMapper, CustomShareListPo> implements CustomShareListRepo {

    @Resource
    private CustomShareListPoConverter converter;

    @Override
    public List<CustomShareList> getByShareId(Integer shareId) {
        List<CustomShareListPo> shareListPos = baseMapper.selectList(Wrappers.lambdaQuery(CustomShareListPo.class).eq(CustomShareListPo::getShareId, shareId));
        return converter.poToDoList(shareListPos);
    }

    @Override
    public List<CustomShareList> getByShareIds(List<Integer> shareIds) {
        List<CustomShareListPo> shareListPos = baseMapper.selectList(Wrappers.lambdaQuery(CustomShareListPo.class).in(CustomShareListPo::getShareId, shareIds));
        return converter.poToDoList(shareListPos);
    }

    @Override
    public PageData<CustomShareList> pageQuery(Integer shareId, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<CustomShareListPo> lqw = Wrappers.lambdaQuery(CustomShareListPo.class).eq(CustomShareListPo::getShareId, shareId);
        Page<CustomShareListPo> page = baseMapper.selectPage(new Page<>(pageNum, pageSize), lqw);
        return converter.toPageData(page, converter::poToDoList);
    }

    @Override
    public void batchSaveShareList(List<CustomShareList> shareLists) {
        List<CustomShareListPo> customShareListPos = converter.doToPoList(shareLists);
        this.saveOrUpdateBatch(customShareListPos);
    }

    @Override
    public void batchRemove(List<Integer> ids) {
        baseMapper.deleteBatchIds(ids);
    }
}
