package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.commons.config.domain.common.entity.AssetDisposeType;
import com.mi.oa.asset.commons.config.domain.common.repository.AssetDisposeTypeRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetDisposeTypePO;
import com.mi.oa.asset.commons.config.infra.database.mapper.AssetDisposeTypeMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AssetDisposeTypeConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/11 11:20
 * @description mi
 */
@Service
public class AssetDisposeTypeRepoImpl extends ServiceImpl<AssetDisposeTypeMapper, AssetDisposeTypePO>  implements AssetDisposeTypeRepo {

    @Resource
    private AssetDisposeTypeConverter typeConverter;
    @Override
    public Integer saveOrUpdateDisposeType(AssetDisposeType disposeType) {
        AssetDisposeTypePO typePO = typeConverter.assetDisposeTypeToPo(disposeType);
        this.saveOrUpdate(typePO);
        disposeType.setId(typePO.getId());
        return disposeType.getId();
    }

    @Override
    public void batchDeleteDisposeType(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        this.baseMapper.deleteBatchIds(ids);
    }

    @Override
    public Integer getDisposeTypeNum(String recordNo) {
        if (StringUtils.isEmpty(recordNo)) {
            return null;
        }
        AssetDisposeTypePO disposeType = this.baseMapper.selectOne(Wrappers.lambdaQuery(AssetDisposeTypePO.class)
                .eq(AssetDisposeTypePO::getRecordNo, recordNo));
        return disposeType == null ? null : disposeType.getId();
    }

    @Override
    public AssetDisposeType getDisposeTypeNum(Integer id) {
        if (id == null) {
            return null;
        }
        AssetDisposeTypePO disposeType = this.baseMapper.selectOne(Wrappers.lambdaQuery(AssetDisposeTypePO.class)
                .eq(AssetDisposeTypePO::getId, id));
        return typeConverter.assetDisposeTypePoToDo(disposeType);
    }
}
