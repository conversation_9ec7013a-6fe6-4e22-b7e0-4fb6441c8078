package com.mi.oa.asset.commons.config.infra.rpc.role;

import com.mi.oa.asset.eam.feign.x5.X5Response;
import com.mi.oa.asset.eam.feign.x5.X5ResponseHeader;
import com.mi.oa.asset.eam.utils.HttpClientUtil;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/13 10:30
 **/
@Component
public class SysRoleClient {

    @Value("${eam.url}")
    private String url;

    @Value("${eam.x5.appId}")
    private String appId;

    @Value("${eam.x5.appKey}")
    private String appKey;

    @Value("${eam.x5.method}")
    private String method;

    /**
     * BPM内置角色前缀
     */
    private static String BPM_ROLE_PREFIX = "__";

    public boolean registerRole(String userCode, List<String> roleNos) {
        if (CollectionUtils.isNotEmpty(roleNos)) {
            roleNos = roleNos.stream().filter(roleNo -> StringUtils.isNotBlank(roleNo) && !roleNo.startsWith(BPM_ROLE_PREFIX)).collect(Collectors.toList());
        }
        X5Response x5Response = HttpClientUtil.doX5Post(url + method, appId, appKey, UserCodeReq.builder().userCode(userCode)
                .roleNos(roleNos).build(), null);
        X5ResponseHeader header = x5Response.getHeader();
        // 请求失败
        if (!X5ResponseHeader.SUCCESS_CODE.equals(header.getCode())) {
            throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, "登录自动注册角色数据权限失败");
        } else {
            return true;
        }
    }


}
