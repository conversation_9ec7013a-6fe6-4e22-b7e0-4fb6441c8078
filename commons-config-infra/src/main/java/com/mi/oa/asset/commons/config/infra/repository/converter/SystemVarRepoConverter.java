package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.common.BaseConverter;
import com.mi.oa.asset.commons.config.domain.systemvar.entity.SystemVar;
import com.mi.oa.asset.commons.config.infra.database.dataobject.SystemVarPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/19 15:11
 */

@Mapper(componentModel = "spring")
public interface SystemVarRepoConverter extends BaseConverter {

     SystemVarPo toSystemVarPo(SystemVar source);

     SystemVar toSystemVar(SystemVarPo source);

     List<SystemVar> toSystemVars(List<SystemVarPo> source);
}
