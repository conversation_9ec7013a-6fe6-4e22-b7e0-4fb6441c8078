package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "amg_warehouse_area")
public class WarehouseAreaPo extends BasePo {

    /**
     * 仓库编码
     */
    @TableField(value = "warehouse_code")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @TableField(value = "warehouse_name")
    private String warehouseName;

    /**
     * 业务线
     */
    @TableField(value = "business_line")
    private String businessLine;

    /**
     * 优先级，正整数 必填
     */
    @TableField(value = "priority")
    private Integer priority;

    /**
     * 地区名称
     */
    @TableField(value = "area_name")
    private String areaName;

    /**
     * 国家id
     */
    @TableField(value = "country_id")
    private String countryId;

    /**
     * 省id
     */
    @TableField(value = "province_id")
    private String provinceId;

    /**
     * 城市id
     */
    @TableField(value = "city_id")
    private String cityId;

    /**
     * 区域id
     */
    @TableField(value = "area_id")
    private String areaId;

    /**
     * 街道id
     */
    @TableField(value = "street_id")
    private String streetId;


}
