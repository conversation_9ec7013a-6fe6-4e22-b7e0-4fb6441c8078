package com.mi.oa.asset.commons.config.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.asset.eam.mybatis.BasePo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/04/29/06:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("amg_function_config_item")
public class FunctionConfigItemPo extends BasePo {

    /**
     * 配置id config_id
     */
    private Integer configId;

    /**
     * 业务线 business_line
     */
    private String businessLine;

    /**
     * 业务线名称 business_line_name
     */
    private String businessLineName;

    /**
     * 授权范围 auth_type
     */
    private Integer authType;

    /**
     * 启用的国家或地区 country
     */
    private String country;

}