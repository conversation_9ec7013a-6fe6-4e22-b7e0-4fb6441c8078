package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.commons.config.domain.function.entity.Dict;
import com.mi.oa.asset.commons.config.domain.function.entity.FuncDict;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.DictPo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FuncDictPo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DictPoConverter extends CommonConverter {


    DictPo toPo(Dict source);

    List<DictPo> toPoList(List<Dict> source);

    Dict toDo(DictPo source);

    List<Dict> toDoList(List<DictPo> source);


    FuncDictPo toFuncDictPo(FuncDict source);

    List<FuncDictPo> toFuncDictPoList(List<FuncDict> source);

    FuncDict toFuncDictDo(FuncDictPo source);

    List<FuncDict> toFuncDictDoList(List<FuncDictPo> source);

}
