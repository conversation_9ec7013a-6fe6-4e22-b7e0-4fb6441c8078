package com.mi.oa.asset.commons.config.infra.repository.converter;

import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.myfunctions.Country;
import com.mi.oa.asset.commons.config.api.myfunctions.SaveFunctionConfigUserReq;
import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfigUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class FunctionConfigConverterTest {

    private FunctionConfigConverter converter;

    @BeforeEach
    void setUp() {
        converter = Mappers.getMapper(FunctionConfigConverter.class);
    }

    @Test
    void testConverterCountries() {
        // 测试空列表
        assertNull(converter.converterCountries(null));
        assertNull(converter.converterCountries(Collections.emptyList()));

        // 测试正常列表
        List<Country> countries = Arrays.asList(
            createCountry("CN"),
            createCountry("US"),
            createCountry("JP")
        );
        String result = converter.converterCountries(countries);
        assertEquals("CN;US;JP", result);
    }

    @Test
    void testConverterCountry() {
        // 测试空字符串
        assertTrue(converter.converterCountry(null).isEmpty());
        assertTrue(converter.converterCountry("").isEmpty());

        // 测试正常字符串
        String countries = "CN;US;JP";
        List<Country> result = converter.converterCountry(countries);
        assertEquals(3, result.size());
        assertEquals("CN", result.get(0).getCountry());
        assertEquals("US", result.get(1).getCountry());
        assertEquals("JP", result.get(2).getCountry());
    }

    @Test
    void testReqToConfigUser() {
        // 测试空参数
        assertNull(converter.reqToConfigUser(null, null));

        // 测试正常参数
        SaveFunctionConfigUserReq req = new SaveFunctionConfigUserReq();
        req.setManageLine("line1");
        req.setManageLineName("Line 1");
        req.setSort(1);

        User user = new User();
        user.setUserName("testUser");
        user.setDisplayName("Test User");

        FunctionConfigUser result = converter.reqToConfigUser(req, user);
        assertNotNull(result);
        assertEquals("line1", result.getManageLine());
        assertEquals("Line 1", result.getManageLineName());
        assertEquals(1, result.getSort());
        assertEquals("testUser", result.getUserCode());
        assertEquals("Test User", result.getUserName());
    }

    @Test
    void testUpdateConfigUser() {
        // 准备测试数据
        SaveFunctionConfigUserReq req = new SaveFunctionConfigUserReq();
        req.setManageLineName("New Line Name");
        req.setSort(2);

        User user = new User();
        user.setUserName("newUser");
        user.setDisplayName("New User");

        FunctionConfigUser exist = new FunctionConfigUser();
        exist.setManageLine("oldLine");
        exist.setManageLineName("Old Line Name");
        exist.setUserCode("oldUser");
        exist.setUserName("Old User");
        exist.setSort(1);

        // 执行更新
        FunctionConfigUser result = converter.updateConfigUser(req, user, exist);

        // 验证结果
        assertNotNull(result);
        assertEquals("New Line Name", result.getManageLineName());
        assertEquals("newUser", result.getUserCode());
        assertEquals("New User", result.getUserName());
        assertEquals(2, result.getSort());
        // 验证未修改的字段保持不变
        assertEquals("oldLine", result.getManageLine());
    }

    private Country createCountry(String countryCode) {
        Country country = new Country();
        country.setCountry(countryCode);
        return country;
    }
}
