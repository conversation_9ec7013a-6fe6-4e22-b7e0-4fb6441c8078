package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfig;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FunctionConfigPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.FunctionConfigMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.FunctionConfigConverter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FunctionConfigRepoImplTest {

    @Mock
    private FunctionConfigMapper functionConfigMapper;

    @Mock
    private FunctionConfigConverter converter;

    @Spy
    private FunctionConfigRepoImpl functionConfigRepo;

    private FunctionConfig functionConfig;
    private FunctionConfigPo functionConfigPo;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化测试数据
        functionConfig = new FunctionConfig();
        functionConfig.setConfigId(1);
        functionConfig.setManageLine("testLine");
        functionConfig.setName("testName");
        functionConfig.setNameEn("testNameEn");

        functionConfigPo = new FunctionConfigPo();
        functionConfigPo.setId(1);
        functionConfigPo.setManageLine("testLine");
        functionConfigPo.setName("testName");
        functionConfigPo.setNameEn("testNameEn");

        // 使用反射设置baseMapper
        Field baseMapperField = FunctionConfigRepoImpl.class.getSuperclass().getDeclaredField("baseMapper");
        baseMapperField.setAccessible(true);
        baseMapperField.set(functionConfigRepo, functionConfigMapper);

        // 使用反射设置converter
        Field converterField = FunctionConfigRepoImpl.class.getDeclaredField("converter");
        converterField.setAccessible(true);
        converterField.set(functionConfigRepo, converter);
    }

    @Test
    void saveFunctionConfig_ShouldSaveAndReturnId() {
        // 准备测试数据
        when(converter.toPo(any(FunctionConfig.class))).thenReturn(functionConfigPo);
        
        // 模拟saveOrUpdate方法
        doReturn(true).when(functionConfigRepo).saveOrUpdate(any(FunctionConfigPo.class));

        // 执行测试
        int result = functionConfigRepo.saveFunctionConfig(functionConfig);

        // 验证结果
        assertEquals(1, result);
        verify(converter).toPo(functionConfig);
        verify(functionConfigRepo).saveOrUpdate(any(FunctionConfigPo.class));
    }

    @Test
    void getConfig_WithValidParams_ShouldReturnConfig() {
        // 准备测试数据
        when(functionConfigMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(functionConfigPo);
        when(converter.toConfig(any(FunctionConfigPo.class))).thenReturn(functionConfig);

        // 执行测试
        FunctionConfig result = functionConfigRepo.getConfig("testLine", "testName", "testNameEn");

        // 验证结果
        assertNotNull(result);
        assertEquals(functionConfig, result);
        verify(functionConfigMapper).selectOne(any(LambdaQueryWrapper.class));
        verify(converter).toConfig(any(FunctionConfigPo.class));
    }

    @Test
    void getConfig_WithInvalidParams_ShouldReturnNull() {
        // 测试空参数
        assertNull(functionConfigRepo.getConfig(null, null, null));
        assertNull(functionConfigRepo.getConfig("", "", ""));
        assertNull(functionConfigRepo.getConfig("testLine", null, null));
    }

    @Test
    void listByIds_WithValidIds_ShouldReturnConfigList() {
        // 准备测试数据
        List<Integer> ids = Arrays.asList(1, 2, 3);
        List<FunctionConfigPo> pos = Arrays.asList(functionConfigPo);
        when(functionConfigMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(pos);
        when(converter.toConfigs(anyList())).thenReturn(Collections.singletonList(functionConfig));

        // 执行测试
        List<FunctionConfig> result = functionConfigRepo.listByIds(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(functionConfig, result.get(0));
        verify(functionConfigMapper).selectList(any(LambdaQueryWrapper.class));
        verify(converter).toConfigs(anyList());
    }

    @Test
    void listByIds_WithEmptyIds_ShouldReturnEmptyList() {
        // 测试空列表
        List<FunctionConfig> result = functionConfigRepo.listByIds(Collections.emptyList());
        assertTrue(result.isEmpty());
    }

    @Test
    void listByManageLine_WithValidLine_ShouldReturnConfigList() {
        // 准备测试数据
        List<FunctionConfigPo> pos = Arrays.asList(functionConfigPo);
        when(functionConfigMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(pos);
        when(converter.toConfigs(anyList())).thenReturn(Collections.singletonList(functionConfig));

        // 执行测试
        List<FunctionConfig> result = functionConfigRepo.listByManageLine("testLine");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(functionConfig, result.get(0));
        verify(functionConfigMapper).selectList(any(LambdaQueryWrapper.class));
        verify(converter).toConfigs(anyList());
    }

    @Test
    void listByManageLine_WithEmptyLine_ShouldReturnEmptyList() {
        // 测试空管理线
        List<FunctionConfig> result = functionConfigRepo.listByManageLine("");
        assertTrue(result.isEmpty());
    }

    @Test
    void deleteByConfigIds_WithValidIds_ShouldDeleteConfigs() {
        // 准备测试数据
        List<Integer> ids = Arrays.asList(1, 2, 3);

        // 执行测试
        functionConfigRepo.deleteByConfigIds(ids);

        // 验证结果
        verify(functionConfigMapper).deleteBatchIds(ids);
    }

    @Test
    void deleteByConfigIds_WithEmptyIds_ShouldDoNothing() {
        // 测试空列表
        functionConfigRepo.deleteByConfigIds(Collections.emptyList());
        verify(functionConfigMapper, never()).deleteBatchIds(anyList());
    }
} 