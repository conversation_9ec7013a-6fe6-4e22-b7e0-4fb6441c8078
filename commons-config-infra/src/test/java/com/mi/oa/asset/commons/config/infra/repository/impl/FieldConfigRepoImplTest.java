package com.mi.oa.asset.commons.config.infra.repository.impl;

import com.mi.oa.asset.commons.config.domain.queryfield.entity.FieldConfig;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FieldConfigPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.FieldConfigPoMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.QueryFieldRepoConverter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FieldConfigRepoImplTest {

    @Mock
    private FieldConfigPoMapper fieldConfigPoMapper;

    @Mock
    private QueryFieldRepoConverter queryFieldConverter;

    @Spy
    private FieldConfigRepoImpl fieldConfigRepo;

    private FieldConfig fieldConfig;
    private FieldConfigPo fieldConfigPo;
    private final String testUserName = "testUser";

    @BeforeEach
    void setUp() throws Exception {
        // 初始化测试数据
        fieldConfig = FieldConfig.builder()
                .fieldId(null)
                .manageLine("testLine")
                .funId("testFunId")
                .fieldConfig("testFieldConfig")
                .extra("testExtra")
                .build();

        fieldConfigPo = new FieldConfigPo();
        fieldConfigPo.setId(1);
        fieldConfigPo.setManageLine("testLine");
        fieldConfigPo.setFunId("testFunId");
        fieldConfigPo.setFieldConfig("testFieldConfig");
        fieldConfigPo.setExtra("testExtra");

        // 使用反射设置baseMapper
        Field baseMapperField = FieldConfigRepoImpl.class.getSuperclass().getDeclaredField("baseMapper");
        baseMapperField.setAccessible(true);
        baseMapperField.set(fieldConfigRepo, fieldConfigPoMapper);

        // 使用反射设置converter
        Field converterField = FieldConfigRepoImpl.class.getDeclaredField("queryFieldConverter");
        converterField.setAccessible(true);
        converterField.set(fieldConfigRepo, queryFieldConverter);
    }

    @Test
    void saveFieldConfig_WhenConfigDoesNotExist_ShouldCreateNewConfig() {
        // 准备测试数据 - 模拟不存在配置的情况
        doReturn(null).when(fieldConfigRepo).searchFieldConfig(anyString(), anyString(), anyString());
        when(queryFieldConverter.fieldConfigToPo(any(FieldConfig.class))).thenReturn(fieldConfigPo);
        doReturn(true).when(fieldConfigRepo).saveOrUpdate(any(FieldConfigPo.class));
        
        // 执行测试 - 使用反射调用saveFieldConfig方法的私有实现
        callSaveFieldConfigImpl(fieldConfig);
        
        // 验证结果
        verify(fieldConfigRepo).searchFieldConfig(fieldConfig.getManageLine(), fieldConfig.getFunId(), testUserName);
        verify(queryFieldConverter).fieldConfigToPo(fieldConfig);
        verify(fieldConfigRepo).saveOrUpdate(fieldConfigPo);
        assertEquals(1, fieldConfig.getFieldId()); // 创建后fieldId应被设置
    }
    
    @Test
    void saveFieldConfig_WhenConfigExists_ShouldUpdateConfig() {
        // 准备测试数据 - 模拟已存在配置的情况
        // 创建已存在的配置
        FieldConfig existingConfig = FieldConfig.builder()
                .fieldId(1)
                .manageLine("testLine")
                .funId("testFunId")
                .fieldConfig("oldFieldConfig")
                .extra("oldExtra")
                .build();
                
        doReturn(existingConfig).when(fieldConfigRepo).searchFieldConfig(
                fieldConfig.getManageLine(), fieldConfig.getFunId(), testUserName);
        when(queryFieldConverter.fieldConfigToPo(any(FieldConfig.class))).thenReturn(fieldConfigPo);
        doReturn(true).when(fieldConfigRepo).saveOrUpdate(any(FieldConfigPo.class));
        
        // 执行测试 - 使用反射调用saveFieldConfig方法的私有实现
        callSaveFieldConfigImpl(fieldConfig);
        
        // 验证结果
        verify(fieldConfigRepo).searchFieldConfig(fieldConfig.getManageLine(), fieldConfig.getFunId(), testUserName);
        assertEquals(existingConfig.getFieldId(), fieldConfig.getFieldId()); // 应继承现有配置的ID
        verify(queryFieldConverter).fieldConfigToPo(fieldConfig);
        verify(fieldConfigRepo).saveOrUpdate(fieldConfigPo);
    }
    
    @Test
    void saveFieldConfig_ShouldSetFieldIdAfterSave() {
        // 准备测试数据
        doReturn(null).when(fieldConfigRepo).searchFieldConfig(anyString(), anyString(), anyString());
        when(queryFieldConverter.fieldConfigToPo(any(FieldConfig.class))).thenReturn(fieldConfigPo);
        
        // 设置保存后的ID
        doAnswer(invocation -> {
            FieldConfigPo po = invocation.getArgument(0);
            po.setId(1); // 模拟数据库自增ID
            return true;
        }).when(fieldConfigRepo).saveOrUpdate(any(FieldConfigPo.class));
        
        // 执行测试 - 使用反射调用saveFieldConfig方法的私有实现
        callSaveFieldConfigImpl(fieldConfig);
        
        // 验证结果
        assertEquals(1, fieldConfig.getFieldId()); // 保存后ID应被设置
    }
    
    /**
     * 通过反射直接调用saveFieldConfig方法，并提供testUserName作为参数，避免静态方法mock
     */
    private void callSaveFieldConfigImpl(FieldConfig config) {
        try {
            Method saveFieldConfigImpl = FieldConfigRepoImpl.class.getDeclaredMethod("saveFieldConfigImpl", FieldConfig.class, String.class);
            saveFieldConfigImpl.setAccessible(true);
            saveFieldConfigImpl.invoke(fieldConfigRepo, config, testUserName);
        } catch (Exception e) {
            fail("Failed to call saveFieldConfigImpl: " + e.getMessage());
        }
    }
} 