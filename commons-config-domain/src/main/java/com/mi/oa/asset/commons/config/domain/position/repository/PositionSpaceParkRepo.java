package com.mi.oa.asset.commons.config.domain.position.repository;

import com.mi.oa.asset.commons.config.domain.position.entity.PositionSpacePark;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/16 10:32
 */

public interface PositionSpaceParkRepo {

    void savePositionSpaceParks(List<PositionSpacePark> positions);

    List<PositionSpacePark> listPositionSpaceParks(List<String> positionCodes);
}
