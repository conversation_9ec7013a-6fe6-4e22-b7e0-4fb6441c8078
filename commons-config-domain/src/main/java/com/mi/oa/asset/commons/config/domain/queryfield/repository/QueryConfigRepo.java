package com.mi.oa.asset.commons.config.domain.queryfield.repository;

import com.mi.oa.asset.commons.config.domain.queryfield.entity.QueryConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/8 11:12
 */
public interface QueryConfigRepo {

    List<QueryConfig> queryConfigList(String manageLine, String funId, String userName);

    QueryConfig queryConfigByName(String manageLine, String funId, String userName, String queryName);

    void saveQueryConfig(QueryConfig queryConfig);

    void deleteQueryConfig(Integer queryId);

}
