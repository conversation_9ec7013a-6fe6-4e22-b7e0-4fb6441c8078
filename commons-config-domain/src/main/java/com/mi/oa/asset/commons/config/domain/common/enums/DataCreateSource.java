package com.mi.oa.asset.commons.config.domain.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:07
 */

@Getter
public enum DataCreateSource {

    MANUAL("0", "手动录入"),
    SYS_SPACE("sys_space", "空间系统"),
    SYS_MDM("sys_mdm", "MDM"),
    SYS_TPM("sys_tpm", "TPM")
    ;

    private final String code;

    private final String name;

    DataCreateSource(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DataCreateSource getByCode(String code) {
        for (DataCreateSource value : DataCreateSource.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
