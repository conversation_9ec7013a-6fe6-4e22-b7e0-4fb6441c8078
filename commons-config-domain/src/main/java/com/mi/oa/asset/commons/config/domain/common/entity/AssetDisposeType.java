package com.mi.oa.asset.commons.config.domain.common.entity;

import com.mi.oa.asset.common.enums.BusinessLine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/14 12:50
 * @description 处置类型
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssetDisposeType {
    /**
     * 处置类型主键
     */
    private Integer id;
    /**
     * 处置类型编码 record_no
     */
    private String recordNo;

    /**
     * 类型名称 record_name
     */
    private String recordName;

    /**
     * 记录状态：生效-1，失效-0 record_status
     */
    private Integer recordStatus;

    /**
     * 业务分类，业务侧根据业务场景自定义的细分的处置类型 business_cate
     */
    private String businessCate;

    /**
     * 处置类型：transfer-转让处置，normal-正常损毁报废，disasters-自然灾害损毁报废，scan_lose-盘亏，lost-丢失，other-其他 dispose_type
     */
    private String disposeType;

    /**
     * 备注 remark
     */
    private String remark;

    /**
     * 业务线 business_line
     */
    private BusinessLine businessLine;
    /**
     * 业务线 business_line
     */
    private String createUser;
    /**
     * 业务线 business_line
     */
    private String createUserName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新人姓名
     */
    private String updateUserName;
    /**
     * 更新时间
     */
    private Date updateTime;


}
