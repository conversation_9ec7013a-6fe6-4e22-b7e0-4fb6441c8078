package com.mi.oa.asset.commons.config.domain.use.entity;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.YesNo;
import com.mi.oa.asset.commons.config.domain.use.enums.UseScope;
import com.mi.oa.asset.commons.config.domain.use.enums.UseType;
import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.*;

import java.util.Date;

/**
 * 用途
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class Use extends DTO {
    private static final long serialVersionUID = -7042766259527417922L;
    /**
     * 主键
     */
    private Integer id;

    /**
     * 业务线
     */
    private BusinessLine businessLine;

    /**
     * 用途编码
     */
    private String useCode;

    /**
     * 用途名称
     */
    private String useName;

    /**
     * 用途名称（英文）
     */
    private String useNameEn;

    /**
     * 用途类别
     */
    private UseType useType;

    /**
     * 适用范围
     */
    private UseScope scope;

    /**
     * 是否禁用，1-是，0-否
     */
    private YesNo disabled;

    /**
     * 排序
     */
    private Integer sorted;

    /**
     * 备注
     */
    private String remark;

    /**
     * 备注（英文）
     */
    private String remarkEn;

    /**
     * 创建人账号
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人账号
     */
    private String updateUser;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Integer isDeleted;
}