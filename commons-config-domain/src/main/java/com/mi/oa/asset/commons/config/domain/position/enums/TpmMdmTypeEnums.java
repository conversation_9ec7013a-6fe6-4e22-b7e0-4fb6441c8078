package com.mi.oa.asset.commons.config.domain.position.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TpmMdmTypeEnums {
    /**
     * tpm只同步分厂 线体（产线） 工位有层级关联的位置数据，工段和工序暂时不考虑
     * 分厂挂在位置编码M5（M5空调工厂下） 层级关系小米集团-M5空调工厂-分厂-产线-工位
     * "workshop - 分厂
     * line - 线体  EAM 产线
     * workstation - 工位
     * processSection - 工段
     * process - 工序
     */
    WORKSHOP("workshop", "分厂"),
    LINE("line", "产线"),
    WORKSTATION("workstation", "工位");
    private final String code;
    private final String desc;

    public static TpmMdmTypeEnums getEnumByCode(String code) {
        for (TpmMdmTypeEnums e : TpmMdmTypeEnums.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
