package com.mi.oa.asset.commons.config.domain.address.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/8/27 21:27
 */
@Getter
@AllArgsConstructor
public enum SubsetLevelEnum {
    COUNTRY(1, "country"),
    PROVINCE(2, "province"),
    CITY(3, "city"),
    DISTRICT(4, "district"),
    STREET(5, "street"),
    ;

    private final int lv;
    private final String name;
    public boolean enumEquals(String name) {
        return this.name.equals(name);
    }
}
