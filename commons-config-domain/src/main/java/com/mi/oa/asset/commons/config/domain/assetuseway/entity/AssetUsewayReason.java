package com.mi.oa.asset.commons.config.domain.assetuseway.entity;

import com.mi.oa.asset.commons.config.domain.assetuseway.enums.UsewayReasonType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 申请理由 领域实体类
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssetUsewayReason implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 申请理由
     */
    private UsewayReasonType usewayReason;
    /**
     * 添加人ID
     */
    private String addUserid;
    /**
     * 添加时间
     */
    private Date addDate;
    /**
     * 修改人ID
     */
    private String modifyUserid;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 系统租户ID
     */
    private String tenantId;
    /**
     * 申请用途id
     */
    private String usewayId;
}
