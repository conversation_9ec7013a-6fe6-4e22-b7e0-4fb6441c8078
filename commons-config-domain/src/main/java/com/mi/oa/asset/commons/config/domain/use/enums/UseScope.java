package com.mi.oa.asset.commons.config.domain.use.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 * 申请用途
 */

@Getter
@AllArgsConstructor
public enum UseScope {

    ALL_STAFF(0, "所有员工"),
    AUTHORIZED_STAFF(1, "授权员工");

    @JsonValue
    private Integer code;

    private String desc;



    public static UseScope getByCode(Integer code) {
        if(null == code) {
            return null;
        }

        for (UseScope useScope: UseScope.values()) {
            if (useScope.code.equals(code)) {
                return useScope;
            }
        }
        return null;
    }

    public static UseScope getByDesc(String desc) {
        if(StringUtils.isBlank(desc)) {
            return null;
        }

        for (UseScope useScope: UseScope.values()) {
            if (useScope.desc.equals(desc)) {
                return useScope;
            }
        }
        return null;
    }
}
