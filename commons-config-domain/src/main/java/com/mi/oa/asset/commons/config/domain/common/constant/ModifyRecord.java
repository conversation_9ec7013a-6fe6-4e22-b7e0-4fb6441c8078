package com.mi.oa.asset.commons.config.domain.common.constant;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ModifyRecord {

    String value() default "";

    boolean ignore() default false;

    String dateFormat() default "yyyy-MM-dd HH:mm:ss";
}
