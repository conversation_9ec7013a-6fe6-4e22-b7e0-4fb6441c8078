package com.mi.oa.asset.commons.config.domain.common.repository;

import com.mi.oa.asset.commons.config.domain.common.valobj.AllotConfig;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/10/31 19:31
 */
public interface AllotConfigRepo {

    List<AllotConfig> allotConfigList(Integer businessLineId);

    void save(List<AllotConfig> allotConfigList, Integer businessLineId, boolean isCreate);

}
