package com.mi.oa.asset.commons.config.domain.common.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 首页功能配置
 * @Date 2025/4/28 17:14
 **/
@Data
public class FunctionConfig {

    /**
     * 配置id
     */
    private Integer configId;

    /**
     * 管理线 manage_line
     */
    private String manageLine;

    /**
     * 管理线名称 manage_line_name
     */
    private String manageLineName;

    /**
     * 功能名称 name
     */
    private String name;

    /**
     * 功能英文名称 name_en
     */
    private String nameEn;

    /**
     * icon url icon_url
     */
    private String iconUrl;

    /**
     * 功能url function_url
     */
    private String functionUrl;

    /**
     * 功能描述 description
     */
    private String description;

    /**
     * 功能英文描述 description_en
     */
    private String descriptionEn;

    /**
     * 排序值 sort
     */
    private Integer sort;

    /**
     * 配置明细
     */
    private List<FunctionConfigItem> items;

}
