package com.mi.oa.asset.commons.config.domain.common.entity;

import com.mi.oa.asset.common.enums.DeptDisplayWay;
import com.mi.oa.asset.common.enums.YesNo;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/31 19:33
 */

@Data
public class BusinessLineDo {

    /**
     * 主键
     */
    private Integer businessLineId;

    /**
     * 业务线 business_line
     */
    private String businessLine;

    /**
     * 业务线名称 business_line_name
     */
    private String businessLineName;

    /**
     * 业务线英文名称 business_line_name_en
     */
    private String businessLineNameEn;

    /**
     * 父管理线主键 father_id
     */
    private Integer fatherId;

    /**
     * 管理线编码 manage_line_code
     */
    private String manageLineCode;

    /**
     * 管理线名称 manage_line_name
     */
    private String manageLineName;

    /**
     * 是否新业务线 is_new_line
     */
    private String isNewLine;

    /**
     * 使用部门名称展示方式 use_dept_name_show_way
     */
    private DeptDisplayWay useDeptNameShowWay;

    /**
     * 管理部门名称展示方式 manage_dept_name_show_way
     */
    private DeptDisplayWay manageDeptNameShowWay;

    /**
     * 是否生效 is_effective
     */
    private YesNo isEffective;

    /**
     * 创建人账号
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 委托记账主体是否变动 is_modify_company
     */
    private YesNo isModifyCompany;

    /**
     * 资产配置（库存）
     */
    private Map<String, String> configs;
}
