package com.mi.oa.asset.commons.config.domain.labeltemplate.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/02/14 10:13
 */
@Getter
@AllArgsConstructor
public enum LabelDataSource {

    ASSET_SN("asset_sn", "资产SN明细"),
    ASSET_ACCOUNT("asset_account", "资产台账"),
    STOCK_SERIAL("amg_stock_serial", "库存串号列表");


    private final String code;
    private final String desc;

    public static LabelDataSource getEnum(String code) {
        for (LabelDataSource value : LabelDataSource.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
