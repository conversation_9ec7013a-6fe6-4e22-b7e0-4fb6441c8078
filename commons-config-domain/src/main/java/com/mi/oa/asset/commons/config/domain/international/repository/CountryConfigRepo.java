package com.mi.oa.asset.commons.config.domain.international.repository;

import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;

import java.util.List;

/**
* <AUTHOR>
* @description 【amg_country_config(国家地区配置表)】
* @createDate 2025-04-29 15:01:49
*/
public interface CountryConfigRepo{

    List<CountryConfigDo> searchAll();

    CountryConfigDo getById(Integer id);

    List<CountryConfigDo> getByIds(List<Integer> countryIdList);

    Integer save(CountryConfigDo countryConfigDo);

    Integer updateById(CountryConfigDo entity);

    void updateBatchById(List<CountryConfigDo> doList);

    void deleteByIds(List<Integer> idList);

    List<CountryConfigDo> getByRegionId(Integer id);

    List<CountryConfigDo> getByRegionIds(List<Integer> idList);

    List<CountryConfigDo> getByThreeCode(List<String> threeCode);
    CountryConfigDo getByCountryCodeAlphaThree(String countryCodeAlphaThree);

}
