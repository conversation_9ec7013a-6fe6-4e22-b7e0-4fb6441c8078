package com.mi.oa.asset.commons.config.domain.use.repository;

import com.mi.oa.asset.commons.config.domain.use.entity.Use;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/16 10:32
 */

public interface UseRepo {
    List<Use> list(Use request);

    List<Use> listUseByUserCode(String businessLine, String userCode);

    List<Use> listUseByDeptCode(String businessLine, List<String> deptCode);
}
