package com.mi.oa.asset.commons.config.domain.project.repository;

import com.mi.oa.asset.commons.config.domain.project.entity.ProjectCfg;

import java.util.List;

public interface ProjectCfgRepo {

    ProjectCfg getProjectCfg(String businessLine, String projectCode);

    ProjectCfg getProjectCfg(Integer projectId);

    List<ProjectCfg> listProjectCfg(String businessLine);

    void saveProjectCfg(ProjectCfg req);

    void updateStatus(List<Integer> ids, String recordStatus);
}
