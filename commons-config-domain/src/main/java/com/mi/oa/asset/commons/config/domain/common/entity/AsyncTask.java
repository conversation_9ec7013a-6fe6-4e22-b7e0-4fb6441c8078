package com.mi.oa.asset.commons.config.domain.common.entity;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.common.enums.ExecutionStatus;
import com.mi.oa.asset.commons.config.api.common.enums.AsyncTaskType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/9 9:22
 */

@Data
public class AsyncTask {

    private Integer taskId;

    /**
     * 执行状态
     */
    private ExecutionStatus executionStatus;

    /**
     * 任务类型
     */
    private AsyncTaskType taskType;

    /**
     * 业务线
     */
    private BusinessLine businessLine;

    /**
     * 任务编号
     */
    private String recordNo;

    /**
     * 执行结果
     */
    private String executionResult;

    /*
     * 原始文件链接
     */
    private String originFileUrl;

    /**
     * 错误结果链接
     */
    private String resultFileUrl;

}
