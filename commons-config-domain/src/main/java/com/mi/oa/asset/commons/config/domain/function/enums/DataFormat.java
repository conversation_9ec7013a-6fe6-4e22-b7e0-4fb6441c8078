package com.mi.oa.asset.commons.config.domain.function.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/4/07 16:07
 */
@Getter
public enum DataFormat {

    INT("int", "int"),
    STRING("varchar","text"),
    TINYINT("tinyint","int"),
    TIMESTAMP("timestamp","date"),
    DATE("date","date"),
    DATETIME("datetime","date"),
    DECIMAL("decimal","number"),
    CHAR("char","text"),
    ;
    private final String code;

    private final String name;

    DataFormat(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DataFormat getByCode(String code) {
        for (DataFormat value : DataFormat.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getFormatByMysql(String code) {
        for (DataFormat value : DataFormat.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
        return STRING.name;
    }
}
