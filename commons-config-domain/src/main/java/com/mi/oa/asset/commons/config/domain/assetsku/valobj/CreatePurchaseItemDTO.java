package com.mi.oa.asset.commons.config.domain.assetsku.valobj;

import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.mi.oa.asset.commons.config.api.assetsku.BooleanTo01Adapter;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.mi.oa.asset.eam.feign.config.BooleanTo01Deserializer;
import com.mi.oa.asset.eam.feign.config.BooleanTo01Serializer;

@Data
public class CreatePurchaseItemDTO {
    /**
     * 物料号
     */
    @JsonProperty("MATERIALCODE")
    @SerializedName("MATERIALCODE")
    private String skuId;

    /**
     * 分类编码
     */
    @JsonProperty("ATCODE")
    @SerializedName("ATCODE")
    private String cateCode;

    /**
     * 采购条目名称
     */
    @JsonProperty("PINAME")
    @SerializedName("PINAME")
    private String skuName;

    /**
     * 国家
     */
    @JsonProperty("COUNTRY")
    @SerializedName("COUNTRY")
    private String country;

    /**
     * 是否串码管理
     */
    @JsonProperty("PISNWHETHER")
    @SerializedName("PISNWHETHER")
    @JsonSerialize(using = BooleanTo01Serializer.class)
    @JsonDeserialize(using = BooleanTo01Deserializer.class)
    @JsonAdapter(BooleanTo01Adapter.class)
    private Boolean isSn;

    /**
     * 是否小米商品
     */
    @JsonProperty("PIISMIGOODS")
    @SerializedName("PIISMIGOODS")
    @JsonSerialize(using = BooleanTo01Serializer.class)
    @JsonDeserialize(using = BooleanTo01Deserializer.class)
    @JsonAdapter(BooleanTo01Adapter.class)
    private Boolean isMiGoods;


    /**
     * 是否耗材
     */
    @JsonProperty("ISCONSUMABLE")
    @SerializedName("ISCONSUMABLE")
    @JsonSerialize(using = BooleanTo01Serializer.class)
    @JsonDeserialize(using = BooleanTo01Deserializer.class)
    @JsonAdapter(BooleanTo01Adapter.class)
    private Boolean isConsumable;

    /**
     * 是否启用
     * 0 - 禁用
     * 1 - 启用
     */
    @JsonProperty("status")
    @SerializedName("status")
    @JsonSerialize(using = BooleanTo01Serializer.class)
    @JsonDeserialize(using = BooleanTo01Deserializer.class)
    @JsonAdapter(BooleanTo01Adapter.class)
    private Boolean isActive;

    /**
     * 应用业务
     */
    @JsonProperty("PIBUSINESS")
    @SerializedName("PIBUSINESS")
    private String purchaseItemBizType;

    /**
     * 参考价格
     */
    @JsonProperty("PIPRICE")
    @SerializedName("PIPRICE")
    private Double price = 0D;

    /**
     * 商品ID
     */
    @JsonProperty("COMMID")
    @SerializedName("COMMID")
    private String goodsId;


    /**
     * 物料编码
     * 更新时必传
     */
    @JsonProperty("PICODE")
    @SerializedName("PICODE")
    private String miSkuCode;
}
