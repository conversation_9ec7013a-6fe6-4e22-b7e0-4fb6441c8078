package com.mi.oa.asset.commons.config.domain.international.enums;

import lombok.Getter;

@Getter
public enum DefaultDataEnums {

    DEFAULTCURRENCY(1, "是默认的货币"),
    NOT_DEFAULTCURRENCY(0, "不是默认的货币"),

    DEFAULTTAXRATE(1, "是默认的税率"),
    NOT_DEFAULTTAXRATE(0, "不是默认的税率"),
    ;


    private final Integer key;

    private final String desc;

    DefaultDataEnums(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
