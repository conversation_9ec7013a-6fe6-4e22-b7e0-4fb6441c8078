package com.mi.oa.asset.commons.config.domain.labeltemplate.repository;

import com.mi.oa.asset.commons.config.domain.labeltemplate.entity.LabelTemplate;

import java.util.List;

public interface LabelTemplateRepo {

    List<LabelTemplate> findListByType(String templateType, boolean filterActive);

    LabelTemplate findById(Integer id);

    LabelTemplate findHistoryById(Integer id);

    void save(LabelTemplate labelTemplate);

    void update(LabelTemplate labelTemplate);

    void deleteById(Integer id);

    void toggleTemplateActive(Integer id, Boolean isActive);

    void cancelTemplateDefault(Integer id);

    List<LabelTemplate> getBusinessTemplateList(String businessLine, String userId);

    void addUserDefaultTemplate(String userId, LabelTemplate labelTemplate);

    void addLastUsedTemplate(String authedUserName, LabelTemplate labelTemplate);
}
