package com.mi.oa.asset.commons.config.domain.warehouse.valobj;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class WarehouseData {

    @ExcelProperty(value = "仓库编码", index = 0)
    private String houseCode;
    @ExcelProperty(value = "仓库名称", index = 1)
    private String houseName;
    @ExcelProperty(value = "", index = 2)
    private String departCode;
    @ExcelProperty(value = "管理员", index = 3)
    private String adminCodes;
    @ExcelProperty(value = "联系电话", index = 4)
    private String phone;
    @ExcelProperty(value = "邮箱", index = 5)
    private String email;
    @ExcelProperty(value = "所在地区", index = 6)
    private String area;
    @ExcelProperty(value = "详细地址", index = 7)
    private String address;
    @ExcelProperty(value = "状态", index = 8)
    private String houseStatus;
    @ExcelProperty(value = "业务线", index = 9)
    private String businessLine;
    @ExcelProperty(value = "仓库类型", index = 10)
    private String houseType;
    @ExcelProperty(value = "物流属性", index = 11)
    private String logisticsType;
    @ExcelProperty(value = "可支持服务", index = 12)
    private String services;
    @ExcelProperty(value = "启用货架管理", index = 13)
    private String shelves;

    private Integer id;
    private String adminNames;
    private String country;
    private String currency;
    private String shelvesNum;
    private String thirdHouseCode;
    private String sapHouseCode;
    private String factoryCode;
    private String companyCode;
    private String zitiArea;
    private String mailArea;
    private String departName;
    private String departFullName;
    private Integer sort;

}
