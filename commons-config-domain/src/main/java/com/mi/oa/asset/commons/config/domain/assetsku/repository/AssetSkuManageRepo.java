package com.mi.oa.asset.commons.config.domain.assetsku.repository;

import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSkuManage;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/07/19 19:33
 */

public interface AssetSkuManageRepo {

    void saveAssetSkuManage(AssetSkuManage manage);

    void saveBatchAssetSkuManage(List<AssetSkuManage> assetSkuMgList);

    void saveAssetSkuManage(List<AssetSkuManage> manages);

    void deleteAssetSkuManage(List<Integer> ids);

    void deleteAssetSkuManageBySkuIds(List<Integer> skuIds);

    List<AssetSkuManage> getByAssetSkuId(Integer skuId);

    Map<Integer, List<AssetSkuManage>> getAssetSkusManages(List<Integer> skuIds);

    Map<Integer, List<AssetSkuManage>> getAssetSkusManagesByBusinessLines(List<Integer> skuIds, List<String> businessLines);

    int sharedBusinessLine(Integer skuId, List<String> businessLines);
}
