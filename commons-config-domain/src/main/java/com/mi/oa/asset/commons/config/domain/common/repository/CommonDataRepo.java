package com.mi.oa.asset.commons.config.domain.common.repository;

import com.mi.oa.asset.commons.config.domain.common.valobj.FunctionSubscribe;
import com.mi.oa.asset.commons.config.domain.common.valobj.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/18 17:49
 */

public interface CommonDataRepo {

    /**
     * 查询全部采购目录
     * @return
     */
    List<PurchaseCatalog> getAllPurchaseCatalogs();

    /**
     * 查询公司
     * @param keyword
     * @param countryCode
     * @return
     */
    List<Company> getCompanies(String keyword, String countryCode);
    /**
     * 查询公司
     * @param companyCode
     * @return
     */
    Company getCompany(String companyCode);

    /**
     * 查询公司
     * @param companyCodes
     * @return
     */
    List<Company> getBatchCompany(List<String> companyCodes);

    /**
     * 查询公司
     * @param keyword
     * @param countryCode
     * @return
     */
    Company getCompany(String keyword, String countryCode);

    /**
     * 查询全部sap资产分类
     * @return
     */
    List<SapAssetCategory> getSapAssetCategories();

    /**
     *
     * @return
     */
    List<SapAssetCategory> getSapAssetCategories4EAM();
    /**
     * 查询供应商
     * @param keyword
     * @return
     */
    List<Provider> getProviders(String keyword);

    /**
     * 查询供应商
     * @param providerCodes
     * @return
     */
    List<Provider> getProviders(List<String> providerCodes);

    /**
     * 查询供应商
     * @param keyword
     * @return
     */
    Provider getProvider(String keyword);

    /**
     * 查询模板下载地址
     * @param funId 功能id
     * @return
     */
    TemplateUrl getTemplateUrl(String funId);


    /**
     * 查询订阅信息
     *
     * @param businessLine
     * @param funCode
     * @param userCode
     * @return
     */
    FunctionSubscribe getSubscribe(String businessLine, String funCode, String userCode);

    /**
     * 通过业务线或功能编码查询所有订阅用户
     *
     * @param funCode
     * @return
     */
    List<FunctionSubscribe> getSubscribeByFunCode(String businessLine, String funCode);

    /**
     * 创建用户订阅记录
     */
    void createOrUpdateSubscribe(FunctionSubscribe subscribe);
}
