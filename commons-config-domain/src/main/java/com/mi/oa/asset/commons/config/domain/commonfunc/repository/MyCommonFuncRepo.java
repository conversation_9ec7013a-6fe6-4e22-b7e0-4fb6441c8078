package com.mi.oa.asset.commons.config.domain.commonfunc.repository;

import com.mi.oa.asset.commons.config.domain.commonfunc.entity.MyCommonFunc;

/**
 * <AUTHOR>
 * @date 2025/1/13 11:22
 * @description
 */
public interface MyCommonFuncRepo {

    /**
     * 保存并排序我的通用功能
     *
     * @param commonFunc 通用功能对象
     */
    void saveMyFuncSort(MyCommonFunc commonFunc);

    /**
     * 根据管理线代码和创建用户列出并排序功能列表
     *
     * @param manageLineCode 管理线代码
     * @param createUser     创建用户
     * @return 排序后的功能列表
     */
    MyCommonFunc listMyFuncSort(String manageLineCode, String createUser);
}
