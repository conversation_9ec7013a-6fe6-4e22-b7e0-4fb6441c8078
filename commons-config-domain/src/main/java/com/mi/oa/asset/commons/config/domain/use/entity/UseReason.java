package com.mi.oa.asset.commons.config.domain.use.entity;

import com.mi.oa.asset.commons.config.domain.use.enums.UseReasonEnum;
import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.*;

import java.util.Date;

/**
 * 用途原因
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UseReason extends DTO {

    private static final long serialVersionUID = 2699057267839248881L;
    /**
     * 主键
     */
    private Integer id;

    /**
     * 用途ID
     */
    private Integer useId;

    /**
     * 用途理由
     */
    private UseReasonEnum useReason;

    /**
     * 创建人账号
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人账号
     */
    private String updateUser;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Integer isDeleted;
}