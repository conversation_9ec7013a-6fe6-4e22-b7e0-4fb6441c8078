package com.mi.oa.asset.commons.config.domain.assetuseway.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 申请理由枚举
 * <AUTHOR>
 * @Date 2024/4/12 10:13
 */
@Getter
@AllArgsConstructor
public enum UsewayReasonType {
    update_replace("0", "迭代换新"),
    business("1", "出差"),
    dev_test("2", "开发测试"),
    system("3", "多系统需求"),
    other("4", "其他"),
    operations("5", "大量运算"),
    display_requirements("6", "显示屏需求"),
    new_workplaces("7", "职场新建"),
    renovation_and_renovation("8", "装修改造"),
    directed_use("9", "定向使用"),
    non_self_use("10", "非自用领取"),
    ;
    private final String code;
    private final String desc;

    public static UsewayReasonType getByCode(String code) {
        for (UsewayReasonType value : UsewayReasonType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
