package com.mi.oa.asset.commons.config.domain.common.valobj;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 功能订阅用户
 *
 * <AUTHOR>
 * @date 2025-03-07 10:00:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FunctionSubscribe {
    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 功能编码
     */
    private String funCode;

    /**
     * 功能名称
     */
    private String funName;

    /**
     * 消息发送时间
     */
    private Date sendTime;

    /**
     * 是否订阅，是-1，否-0
     */
    private Integer isSubscribe;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 创建人用户名
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 更新人用户名
     */
    private String updateUser;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
