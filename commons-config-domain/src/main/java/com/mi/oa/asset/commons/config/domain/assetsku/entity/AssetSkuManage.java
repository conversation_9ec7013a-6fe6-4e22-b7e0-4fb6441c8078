package com.mi.oa.asset.commons.config.domain.assetsku.entity;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.MaterialType;
import com.mi.oa.asset.commons.config.api.assetsku.ManageModel;
import com.mi.oa.asset.commons.config.api.assetsku.SnTypeEnum;
import com.mi.oa.asset.commons.config.domain.assetsku.enums.StockCostEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/19 16:43
 */

@Data
public class AssetSkuManage implements Serializable {

    private Integer id;

    /**
     * skuId
     */
     
    private Integer skuId;

    /**
     * 业务线
     */
    private BusinessLine businessLine;

    /**
     * 分类
     */

    private Integer cateId;

    /**
     * 分类
     */
    private String cateCode;

    /**
     * 分类
     */
    private String cateName;

    /**
     * 管理模式 asset_account：资产台账管理 asset_stock：资产库存管理 stock：库存管理
     */
    private ManageModel mgModel;

    /**
     * 串号管理
     */
    private SnTypeEnum serialMg;

    /**
     * 存货成本核算
     */
    private StockCostEnum costing = StockCostEnum.MOVING_AVG;

    /**
     * 安全库存
     */
    private BigDecimal secureQuantity;

    /**
     * 最高库存
     */
    private BigDecimal highestQuantity;

    /**
     * 最低库存
     */
    private BigDecimal miniQuantity;

    /**
     * 物料类型
     */
    private MaterialType materialType;

}
