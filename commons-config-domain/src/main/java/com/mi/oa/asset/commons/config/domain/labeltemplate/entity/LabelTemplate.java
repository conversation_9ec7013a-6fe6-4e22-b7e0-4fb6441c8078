package com.mi.oa.asset.commons.config.domain.labeltemplate.entity;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/02/14 17:09
 */
@Data
@Builder
public class LabelTemplate {

    /**
     * 模版id
     */
    private Integer id;

    /**
     * 模版类型 业务模板：business、系统模板：system
     */
    private String templateType;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 是否默认模版，0-否，1-是
     */
    private Integer isDefault;

    /**
     * 是否启用，0-否，1-是
     */
    private Integer isActive;

    /**
     * 扫码展示字段
     */
    private String scanDisplayField;

    /**
     * 是否展示编码
     */
    private Integer isShow;

    /**
     * 其他配置信息
     */
    private String extConf;

    /**
     * 是否用户默认0-否，1-是
     */
    private Integer isUserDefault = 0;

    /**
     * 数据来源
     */
    private String dataSource;

}
