package com.mi.oa.asset.commons.config.domain.common.repository;

import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfigDept;

import java.util.List;

public interface FunctionConfigDeptRepo {

    void saveAuthDept(List<FunctionConfigDept> configDeptList);

    void deleteAuthDeptByConfigId(Integer configId);

    void deleteAuthDeptByConfigIds(List<Integer> configIds);

    List<FunctionConfigDept> listByDeptCodes(List<String> allDeptCode);

    List<FunctionConfigDept> listByDeptCodeAndId(List<String> allDeptCode, List<Integer> configIds);
}
