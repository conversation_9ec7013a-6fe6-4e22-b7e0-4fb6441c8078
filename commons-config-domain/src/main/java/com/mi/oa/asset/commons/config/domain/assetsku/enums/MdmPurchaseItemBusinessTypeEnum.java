package com.mi.oa.asset.commons.config.domain.assetsku.enums;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mi.oa.asset.common.enums.IEnum;

public enum MdmPurchaseItemBusinessTypeEnum implements IEnum<MdmPurchaseItemBusinessTypeEnum> {

    @JsonProperty("XZ0001")
    XZ("XZ0001", "行政资产", "1"),

    @JsonProperty("LIB")
    LIB("LIB", "汽车部实验室", "3"),

    @JsonProperty("GLIB")
    GLIB("GLIB", "集团实验室", "4"),
    ;

    private final String code;

    private final String desc;

    private final String materialBusinessTypeCode;

    MdmPurchaseItemBusinessTypeEnum(String code, String desc, String materialBusinessTypeCode) {
        this.code = code;
        this.desc = desc;
        this.materialBusinessTypeCode = materialBusinessTypeCode;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public String getMaterialBusinessTypeCode() {
        return materialBusinessTypeCode;
    }


    public static MdmPurchaseItemBusinessTypeEnum getByCode(String code) {
        for (MdmPurchaseItemBusinessTypeEnum i : MdmPurchaseItemBusinessTypeEnum.values()) {
            if(i.code.equals(code)) return i;
        }

        return null;
    }

    public static MdmPurchaseItemBusinessTypeEnum getByBusinessTypeCode(String code) {
        for (MdmPurchaseItemBusinessTypeEnum i : MdmPurchaseItemBusinessTypeEnum.values()) {
            if(i.materialBusinessTypeCode.equals(code)) return i;
        }

        return null;
    }

}
