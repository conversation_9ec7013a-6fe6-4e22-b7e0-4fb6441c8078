package com.mi.oa.asset.commons.config.domain.international.repository;

import com.mi.oa.asset.commons.config.domain.international.entity.CountryCurrencyDo;

import java.util.List;

/**
* <AUTHOR>
* @description 【amg_country_currency(货币配置表)】
* @createDate 2025-04-29 19:34:38
*/
public interface CountryCurrencyRepo {

    List<CountryCurrencyDo> searchAll();

    CountryCurrencyDo getById(Integer id);

    Integer save(CountryCurrencyDo countryCurrencyDo);

    void updateById(CountryCurrencyDo entity);

    void deleteByIds(List<Integer> idList);

    /**
     * 根据国家id查询国家货币配置
     * @param countryId
     * @return
     */
    List<CountryCurrencyDo> getByCountryId(Integer countryId);

    /**
     * 查询国家的默认货币信息
     * @param countryId
     * @param defaultCurrency
     * @return
     */
    CountryCurrencyDo getCountryDefaultCurrency(Integer countryId, int defaultCurrency);

    List<CountryCurrencyDo> listCountryDefaultCurrency(List<Integer> countryIdList, Integer defaultCurrency);

    List<CountryCurrencyDo> getByCountryCode(String countryCode);
}
