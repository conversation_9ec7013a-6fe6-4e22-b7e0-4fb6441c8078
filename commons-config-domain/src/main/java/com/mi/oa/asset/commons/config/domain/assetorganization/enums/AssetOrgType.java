package com.mi.oa.asset.commons.config.domain.assetorganization.enums;

import com.mi.oa.asset.common.enums.IEnum;
import com.mi.oa.asset.common.model.CodeNameProperty;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/15 16:35
 */

@Getter
public enum AssetOrgType implements IEnum<AssetOrgType> {

    DEPARTMENT("department", "部门"),
    OFFICE("office", "职场"),
    LAB("lab", "实验室"),
    OEM("oem", "代工厂"),
    ;

    private final String code;

    private final String desc;

    AssetOrgType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AssetOrgType getByCode(String code) {
        for (AssetOrgType value : AssetOrgType.values()) {
            if (value.code.equals(code)) return value;
        }

        return null;
    }

    public static List<CodeNameProperty> toCodeNameProperties() {
        return Arrays.stream(AssetOrgType.values()).map(AssetOrgType::toCodeNameProperty).collect(Collectors.toList());
    }
}
