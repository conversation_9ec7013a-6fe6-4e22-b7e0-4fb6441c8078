package com.mi.oa.asset.commons.config.domain.warehouse.repository;

import com.mi.oa.asset.commons.config.domain.warehouse.entity.Warehouse;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/16 10:32
 */

public interface WarehouseRepo {

    void saveWarehouse(Warehouse warehouse);

    void saveWarehouse(List<Warehouse> Warehouses);

    void deleteWarehouse(List<Integer> ids);

    List<Warehouse> getByCode(String code);

    List<Warehouse> findByCodes(List<String> codes);
    Warehouse findByCode(String code);

    List<Warehouse> getBatchByServiceType(String businessLine, String serviceType, String key);

    // 根据业务线查询生效的仓库
    List<Warehouse> getByBusinessLine(String businessLine);
}
