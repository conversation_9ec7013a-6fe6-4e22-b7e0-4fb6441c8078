package com.mi.oa.asset.commons.config.domain.function.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/4/07 16:07
 */
@Getter
public enum ColControlType {
    INT("int", "number"),
    STRING("varchar","text"),
    TINYINT("tinyint","number"),
    TIMESTAMP("timestamp","date"),
    DATE("date","date"),
    DATETIME("datetime","date"),
    DECIMAL("decimal","number"),
    CHAR("char","string"),
    ;
    private final String code;

    private final String name;

    ColControlType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ColControlType getByCode(String code) {
        for (ColControlType value : ColControlType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getColControlByMysql(String code) {
        for (ColControlType value : ColControlType.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
        return STRING.name;
    }
}
