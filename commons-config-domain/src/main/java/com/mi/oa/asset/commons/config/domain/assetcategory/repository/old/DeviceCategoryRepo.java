package com.mi.oa.asset.commons.config.domain.assetcategory.repository.old;

import com.mi.oa.asset.commons.config.domain.assetcategory.entity.old.DeviceCategory;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:37
 */

public interface DeviceCategoryRepo {
    /**
     * 获取全部资产分类
     *
     * @param businessLineCode
     * @return
     */
    List<DeviceCategory> getAllAssetCategory(String businessLineCode, Integer level);

    /**
     * 获取全部资产分类
     *
     * @param showId
     * @return
     */
    DeviceCategory getAssetCategory(String showId);

}
