package com.mi.oa.asset.commons.config.domain.bpm.entity;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description bpm审批记录
 * @Date 2024/9/23 11:11
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class BpmApprovalTask {

    /**
     * 任务id
     */
    private String taskId;
    /**
     * 节点名称
     */
    private String taskName;
    /**
     * 审批类型
     */
    private String signType;

    /**
     * 节点状态
     */
    private String nodeState;

    /**
     * 任务列表
     */
    private List<BpmApprover> taskList;

    /**
     * 是否预测节点
     */
    private Integer isPredict;

}
