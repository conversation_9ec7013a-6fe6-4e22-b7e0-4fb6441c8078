package com.mi.oa.asset.commons.config.domain.assetshare.repository;

import com.mi.oa.asset.commons.config.domain.assetshare.entity.CustomShareList;
import com.xiaomi.mit.api.PageData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-06 16:05
 */
public interface CustomShareListRepo {

    /**
     * 通过共享主键查询所有自定义资产
     *
     * @param shareId
     * @return
     */
    List<CustomShareList> getByShareId(Integer shareId);

    List<CustomShareList> getByShareIds(List<Integer> shareIds);

    /**
     * 通过共享主键分页查询
     *
     * @param shareId
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<CustomShareList> pageQuery(Integer shareId, Integer pageNum, Integer pageSize);

    /**
     * 批量保存或更新自定义共享资产
     *
     * @param shareLists
     */
    void batchSaveShareList(List<CustomShareList> shareLists);

    /**
     * 批量移除
     *
     * @param ids
     */
    void batchRemove(List<Integer> ids);
}
