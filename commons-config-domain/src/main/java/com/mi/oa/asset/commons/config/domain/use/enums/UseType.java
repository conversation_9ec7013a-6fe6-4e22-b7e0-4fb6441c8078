package com.mi.oa.asset.commons.config.domain.use.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 * 申请用途
 */

@Getter
@AllArgsConstructor
public enum UseType {

    GIFT(0, "内部领用"),
    ASSET(1, "行政资产"),
    ALL(3, "全部");

    @JsonValue
    private Integer code;

    private String desc;

    public static UseType getByCode(Integer code) {
        if(null == code) {
            return null;
        }

        for (UseType useType: UseType.values()) {
            if (useType.code.equals(code)) {
                return useType;
            }
        }
        return null;
    }

    public static UseType getByDesc(String desc) {
        if(StringUtils.isBlank(desc)) {
            return null;
        }

        for (UseType useType: UseType.values()) {
            if (useType.desc.equals(desc)) {
                return useType;
            }
        }
        return null;
    }
}
