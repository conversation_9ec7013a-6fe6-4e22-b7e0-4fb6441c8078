package com.mi.oa.asset.commons.config.domain.position.repository;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import com.mi.oa.asset.commons.config.domain.position.entity.Position;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/16 10:32
 */

public interface PositionRepo {

    void savePosition(Position position);

    void savePositions(List<Position> positions);

    void updatePositiions(List<Position> positions);

    void updatePositionPath(Position position);

    Position getPosition(Integer positionId);

    List<Position> getPositions(List<Integer> positionIds);

    Position getPositionByCode(String positionCode, BusinessLine businessLine);

    List<Position> getPositionByCodes(List<String> positionCodes, BusinessLine businessLine);

    PageData<Position> getPositionPageData(String positionPath, String keyword, PageRequest pageRequest);

    PageData<Position> getPositionPageData(List<String> businessLineCodes, String keyword, PageRequest pageRequest);

    void deleteByPaths(List<String> paths);

    List<Position> getAllPosition(List<String> businessLineCodes);

    List<Position> getPositionByBusinessLine(BusinessLine businessLine, DataCreateSource source);

    List<Position> getPositionByPaths(List<String> positionPaths);
}
