package com.mi.oa.asset.commons.config.domain.businessrole.repository;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRole;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRoleUser;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/15 19:42
 */
public interface BusinessRoleRepo {

    List<BusinessRole> getRoleList();

    BusinessRole findByCode(String code);

    List<BusinessRole> findByCodes(List<String> codes);

    BusinessRoleUser getRoleByOrgCode(String roleCode, String orgCode, BusinessLine businessLine);

    List<BusinessRoleUser> getRolesByOrgCode(String roleCode, String orgCode, BusinessLine businessLine);

    List<BusinessRoleUser> getRolesByRoleCodes(List<String> roleCodes, String userName, Integer limit, String businessLine);

    List<BusinessRoleUser> getRolesByUserCode(String userCode, List<String> roleCodes, BusinessLine businessLine);

    void deleteByIds(List<Integer> ids);

    void createRoleUser(List<BusinessRoleUser> list);

    List<BusinessRoleUser> getRoleByOrgCodeAndBusiness(String orgCode, BusinessLine businessLine);

    void deleteRoles(String orgCode, BusinessLine businessLine);

    void deleteRolesByOrgCodes(List<String> orgCode, BusinessLine businessLine);

}
