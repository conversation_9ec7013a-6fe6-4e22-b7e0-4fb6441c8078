package com.mi.oa.asset.commons.config.domain.assetshare.valobj;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-01-10 16:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomShareListExport {
    private Integer id;
    private String assetCode;
    private String assetName;
    private String assetType;
    private String useStatus;
    private String checkStatus;
    private String userCode;
    private String userName;
    private Integer quantity;
    private Integer transQuantity;
}
