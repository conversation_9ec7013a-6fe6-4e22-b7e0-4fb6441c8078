package com.mi.oa.asset.commons.config.domain.address.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 领域实体类
 *
 * <AUTHOR>
 * @date 2024-04-08 11:14:07
 */
@Data
public class AssetReceiveAddress implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 收货地址状态(0: 已删除  1: 正在使用 )
     */
    private Integer status;
    /**
     * 是否是默认收货地址
     */
    private Boolean isDefault;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 收货人姓名
     */
    private String receiveName;
    /**
     * 收货人电话
     */
    private String receiveMobile;
    /**
     * 收货人邮箱
     */
    private String receiveEmail;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 省名
     */
    private String provinceName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 市名
     */
    private String cityName;
    /**
     * 区编码
     */
    private String areaCode;
    /**
     * 区名
     */
    private String areaName;
    /**
     * 街道 编码
     */
    private String streetCode;
    /**
     * 街道名
     */
    private String streetName;
    /**
     * 详细地址
     */
    private String detailAddress;
    /**
     * 创建人 create_user
     */
    private String createUser;
    /**
     * 创建时间 create_time
     */
    private Long createTime;
    /**
     * 最后更新人 update_user
     */
    private String updateUser;
    /**
     * 最后更新时间 update_time
     */
    private Long updateTime;

}
