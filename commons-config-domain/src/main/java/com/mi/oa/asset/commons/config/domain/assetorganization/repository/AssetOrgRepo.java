package com.mi.oa.asset.commons.config.domain.assetorganization.repository;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgStructure;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgUnitQuery;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/15 18:06
 */

public interface AssetOrgRepo {

    AssetOrgStructure getOrgStructureByCode(String code);

    AssetOrgStructure getOrgStructureByCode(String code, Boolean disabledIgnore);

    List<AssetOrgStructure> getOrgStructuresByCodes(List<String> orgCodes);

    AssetOrgUnit getOrgUnitByCode(String code, BusinessLine businessLine);

    PageData<AssetOrgUnit> getOrgUnitPageByCode(BusinessLine businessLine,PageRequest pageRequest);

    List<AssetOrgUnit> getBatchOrgUnitByCode(List<String> code, BusinessLine businessLine);

    AssetOrgUnit getOrgUnitById(Integer id);

    void createOrgUnit(AssetOrgUnit unit);

    void updateOrgUnit(AssetOrgUnit unit);

    void createOrgStructure(AssetOrgStructure structure);

    void createOrgStructures(List<AssetOrgStructure> structures);

    void updateOrgStructure(AssetOrgStructure structure);

    void updateOrgStructures(List<AssetOrgStructure> structures);

    List<AssetOrgStructure> getAllOrgStructure(List<String> businessLineCodes);

    boolean hasSubList(String orgCode);

    boolean hasSubOrgUnitList(String orgCode, BusinessLine businessLine);

    void deleteOrgStructure(AssetOrgStructure orgStructure);

    void deleteOrgStructures(List<AssetOrgStructure> orgStructures);

    void deleteOrgUnitByOrgCode(String orgCode);

    void deleteOrgUnitByIds(List<Integer> ids);

    PageData<AssetOrgUnit> orgUnitPageData(AssetOrgUnitQuery params, PageRequest pageRequest);

    List<AssetOrgStructure> getOrgStructures(List<String> orgCodes, String orderSql);

    List<AssetOrgUnit> getOrgUnits(AssetOrgUnitQuery params);

    AssetOrgUnit getOrgUnits(String keyword, String businessLine);

    PageData<AssetOrgStructure> orgStructurePageData(String businessLine, String keyword, PageRequest pageRequest);

    List<AssetOrgStructure> getOrgStructuresByLevel(Integer level);

    void inactivateOrgStructure(List<String> orgCodes);

    void batchCreateOrgUnit(List<AssetOrgUnit> orgUnits);

    List<AssetOrgUnit> getLabTypeOrgList(String businessLine, String orgCode);

    List<AssetOrgStructure> getOrgStructuresByParentCodes(List<String> orgCodes);
}
