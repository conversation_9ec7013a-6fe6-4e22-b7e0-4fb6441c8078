package com.mi.oa.asset.commons.config.domain.systemvar.repository;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.domain.systemvar.entity.SystemVar;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/19 15:08
 */

public interface SystemVarRepo {

    /**
     * 保存系统变量
     * @param systemVar
     */
    void saveSystemVar(SystemVar systemVar);

    /**
     * 更新系统变量
     * @param systemVar
     */
    void updateSystemVar(SystemVar systemVar);

    /**
     * 获取系统变量
     * @param varCode
     * @param businessLine
     * @return
     */
    SystemVar getSystemVar(String varCode, BusinessLine businessLine);

    /**
     * 获取多个系统变量
     * @param varCodes
     * @param businessLine
     * @return
     */
    List<SystemVar> getSystemVar(List<String> varCodes, BusinessLine businessLine);
}
