package com.mi.oa.asset.commons.config.domain.common.valobj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/10/26 17:15
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Provider {

    private Integer id;
    /**
     * 供应商编码
     */
    private String providerCode;

    /**
     * 供应商名称
     */
    private String providerName;
    /**
     * 是否禁用  1 是 0 否
     */
    private Integer disabled;


}
