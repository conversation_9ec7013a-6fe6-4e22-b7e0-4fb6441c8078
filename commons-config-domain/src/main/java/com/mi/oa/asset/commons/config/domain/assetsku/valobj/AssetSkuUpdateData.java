package com.mi.oa.asset.commons.config.domain.assetsku.valobj;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.MaterialType;
import com.mi.oa.asset.commons.config.api.assetsku.ManageModel;
import com.mi.oa.asset.commons.config.api.assetsku.SnTypeEnum;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSkuManage;
import com.mi.oa.asset.excel.annotation.MiExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/31 14:03
 * 顺序不能调整
 */
@Data
public class AssetSkuUpdateData implements Serializable {

    private static final long serialVersionUID = 7004067799803864100L;

    @ExcelProperty("物料编码")
    @MiExcelProperty(requiredCell = true)
    private String skuCode;

    @ExcelProperty("物料名称")
    private String skuName;

    @ExcelProperty("品牌")
    private String brand;

    @ExcelProperty("型号")
    private String spec;

    @ExcelIgnore
    private String measureCode;

    @ExcelProperty("计量单位")
    private String measureName;

    @ExcelProperty("分类")
    private String cateCode;

    @ExcelIgnore
    private Integer cateId;

    @ExcelIgnore
    private String cateName;

    @ExcelProperty("参考价格")
    private BigDecimal price;

    @ExcelProperty("国家编码")
    private String country;

    @ExcelProperty("业务线")
    @MiExcelProperty(dropdownEnum = BusinessLine.class)
    private String businessLine;

    @ExcelProperty("物料类型")
    @MiExcelProperty(dropdownEnum = MaterialType.class)
    private String materialType;

    @ExcelProperty("串号管理")
    @MiExcelProperty(dropdownEnum = SnTypeEnum.class)
    private String serialMg;

    @ExcelProperty("管理模式")
    @MiExcelProperty(dropdownEnum = ManageModel.class)
    private String mgModel;

    @ExcelProperty("安全库存量")
    private BigDecimal secureQuantity;

    @ExcelProperty("最高库存量")
    private BigDecimal highestQuantity;

    @ExcelProperty("最低库存量")
    private BigDecimal miniQuantity;

    @ExcelProperty("小米料号")
    private String miSkuCode;

    @ExcelProperty("商品ID")
    private String miGoodsId;

    @ExcelProperty("商品名称")
    private String productName;

    @ExcelProperty("项目编号")
    private String projectCode;

    @ExcelProperty("项目阶段")
    private String projectPhase;

    @ExcelProperty("上市日期")
    private Date saleDate;

    @ExcelIgnore
    private List<AssetSkuManage> manages;

}
