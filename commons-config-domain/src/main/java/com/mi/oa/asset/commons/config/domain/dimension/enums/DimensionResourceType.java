package com.mi.oa.asset.commons.config.domain.dimension.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:07
 */
@Getter
public enum DimensionResourceType {

    TREE("tree", "树状"),
    FLAT("flat","平铺");

    private final String code;

    private final String name;

    DimensionResourceType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DimensionResourceType getByCode(String code) {
        for (DimensionResourceType value : DimensionResourceType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
