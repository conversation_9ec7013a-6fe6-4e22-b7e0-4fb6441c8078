package com.mi.oa.asset.commons.config.domain.assetcategory.entity;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.MaterialType;
import com.mi.oa.asset.commons.config.api.assetsku.ManageModel;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/9/11 17:18
 */

@Data
@Builder
public class AssetCategory implements Serializable {

    private static final long serialVersionUID = 1217317218518930951L;

    /**
     * 分类id
     */
    private Integer cateId;

    /**
     * 业务线
     */
    private BusinessLine businessLine;

    /**
     * 分类编码
     */
    private String cateCode;

    /**
     * 分类名称
     */
    private String cateName;

    /**
     * 分类名称（英文）
     */
    private String cateNameEn;

    /**
     * 分类名称全
     */
    private String fullCateName;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 上级编码
     */
    private String parentCateCode;

    /**
     * 分类路径
     */
    private String catePath;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 禁用
     */
    private Boolean disabled;
    /**
     * 国家编码 默认CHN
     */
    private String country;
    /**
     * 国家
     */
    private String countryName;

    /**
     * sap资产分类编码
     */
    private String sapCateCode;

    /**
     * sap资产分类名称
     */
    private String sapCateName;

    /**
     * 使用年限，默认使用采购目录对应的折旧年限
     */
    private Integer useYear;

    /**
     * 使用期间
     */
    private Integer useMonth;

    /**
     * 是否唯一码管理
     */
    private Boolean isSerialCodeManage;

    /**
     * 关联采购目录编码
     */
    private String purchaseCatalogCode;

    /**
     * 关联采购目录编码（全路径）
     */
    private String purchaseCatalogCodePath;

    /**
     * 关联采购目录名称
     */
    private String purchaseCatalogName;

    /**
     * 关联采购目录名称（全路径）
     */
    private String purchaseCatalogNamePath;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 数据来源, 默认手动录入
     */
    @Builder.Default
    private DataCreateSource dataSource = DataCreateSource.MANUAL;

    /**
     * 是否一卡多物管理
     */
    private Boolean isMultipleManage;

    /**
     * 物料类型
     */
    private MaterialType materialType;
    /**
     * 管理模式
     */
    private ManageModel mgModel;

    /**
     * 品类经理账号
     */
    private String managerCode;
    /**
     * 品类经理
     */
    private String managerName;
    /**
     * 交期(天)
     */
    private Integer deliveryDays;
    /**
     * 编码生成方式
     */
    private Integer codeGenType;
}
