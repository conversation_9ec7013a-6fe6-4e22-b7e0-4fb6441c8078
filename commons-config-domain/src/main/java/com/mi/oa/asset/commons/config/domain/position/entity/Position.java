package com.mi.oa.asset.commons.config.domain.position.entity;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/10/16 10:30
 */

@Data
@Builder
public class Position {

    /**
     * 位置id
     */
    private Integer positionId;

    /**
     * 位置编码
     */
    private String positionCode;

    /**
     * 位置名称
     */
    private String positionName;

    /**
     * 英文名称
     */
    private String positionNameEn;

    /**
     * 位置类型
     */
    private String positionType;

    /**
     * 位置名称全
     */
    private String fullPositionName;

    /**
     * 位置路径
     */
    private String positionPath;

    /**
     * 上级编码
     */
    private String parentCode;

    /**
     * 上级名称
     */
    private String parentName;

    /**
     * 业务线
     */
    private BusinessLine businessLine;

    /**
     * 数据来源, 默认手动录入
     */
    @Builder.Default
    private DataCreateSource dataSource = DataCreateSource.MANUAL;

    /**
     * 禁用
     */
    private Boolean disabled;

    /**
     * 外部系统编码
     */
    private String outSysCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    private String createUser;
}
