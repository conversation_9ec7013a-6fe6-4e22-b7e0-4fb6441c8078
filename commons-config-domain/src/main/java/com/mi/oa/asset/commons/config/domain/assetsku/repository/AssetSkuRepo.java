package com.mi.oa.asset.commons.config.domain.assetsku.repository;

import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSku;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/9 14:42
 */

public interface AssetSkuRepo {

    void saveAssetSku(AssetSku assetSku);

    void batchSaveAssetSku(List<AssetSku> assetSkus);

    void updateBatchAssetSku(List<AssetSku> assetSkus);

    void deleteAssetSku(List<Integer> ids);

    boolean isExists(String skuCode);

    PageData<AssetSku> getAssetSkuPageData(List<Integer> relationCateIds, String keyword, PageRequest pageRequest, Boolean disabled, Boolean containMiGoods);

    PageData<AssetSku> getAssetSkuPageDataV1(List<Integer> relationCateIds, List<String> businessLines, String keyword,
                                             PageRequest pageRequest, Boolean disabled, Boolean containMiGoods);

    List<AssetSku> listAssetSkuByParams(List<Integer> relationCateIds, List<String> businessLines, String keyword);

    AssetSku getAssetSku(Integer skuId);

    List<AssetSku> getAssetSkus(List<Integer> skuIds);

    List<AssetSku> getAssetSkuCodes(List<String> skuCodes);

    AssetSku getAssetSkuBySkuCode(String skuCode);

    long getAssetSkuCountsByCategory(List<Integer> cateIds);

    List<Boolean> updateFromDistribute(List<AssetSku> assetSkus);
}
