package com.mi.oa.asset.commons.config.domain.queryfield.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/8 11:11
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FieldConfig {

    /**
     * 方案主键
     */
    private Integer fieldId;

    /**
     * 管理线
     */
    private String manageLine;

    /**
     * 功能Id
     */
    private String funId;

    /**
     * 字段配置信息
     */
    private String fieldConfig;

    /**
     * 台账扩展属性
     */
    private String extra;

}
