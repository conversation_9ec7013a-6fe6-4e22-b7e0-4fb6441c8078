package com.mi.oa.asset.commons.config.domain.common.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/6 21:18
 **/
@Data
public class FunctionConfigUser {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 管理线 manage_line
     */
    private String manageLine;

    /**
     * 管理线名称 manage_line_name
     */
    private String manageLineName;

    /**
     * 用户账号 user_code
     */
    private String userCode;

    /**
     * 用户姓名 user_name
     */
    private String userName;

    /**
     * 排序
     */
    private Integer sort;

}
