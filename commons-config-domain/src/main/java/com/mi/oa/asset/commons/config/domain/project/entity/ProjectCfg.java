package com.mi.oa.asset.commons.config.domain.project.entity;

import lombok.Data;

import java.util.Date;

/**
 * 项目配置表
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class ProjectCfg {
    /**
     * ID
     */
    private Integer id;
    /**
     * 执行状态 待创建wait_create 已创建created
     */
    private String recordStatus;
    /**
     * 业务线
     */
    private String businessLine;
    /**
     * 项目编号
     */
    private String projectCode;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 父项目编号
     */
    private String parentProjectCode;
    /**
     * PM账户
     */
    private String pmUserCode;
    /**
     * PM工号
     */
    private String pmEmpCode;
    /**
     * PM
     */
    private String pmUserName;
    /**
     * 是否JIS之前机型
     */
    private Integer beforeJis;
    /**
     * 项目阶段
     */
    private String projectPhase;
    /**
     * 项目类型
     */
    private String projectType;

    /**
     * 预研编号
     */
    private String preProjectCode;
    /**
     * 项目等级
     */
    private String projectLevel;
    /**
     * 产品类别
     */
    private String productType;
    /**
     * 试产工厂
     */
    private String factoryName;
    /**
     * 试产工厂编码
     */
    private String factoryCode;
    /**
     * 试产日期
     */
    private Date trialDate;
    /**
     * 是否上市
     */
    private Integer isSale;
    /**
     * 上市日期
     */
    private Date saleDate;
    /**
     * 国家
     */
    private String country;
    /**
     * 项目信息来源 R平台 手动创建 UPC平台
     */
    private String source;
    /**
     * 是否生效，是-1，否-0
     */
    private Integer enabled;
    /**
     * 创建人账号
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人账号
     */
    private String updateUser;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Integer isDeleted;
}