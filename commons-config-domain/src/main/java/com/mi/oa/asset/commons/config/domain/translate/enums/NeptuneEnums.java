package com.mi.oa.asset.commons.config.domain.translate.enums;

import lombok.Getter;

import java.io.Serializable;

@Getter
public enum NeptuneEnums implements Serializable {

    CACHE_TRANSLATE_NEPTUNT_HASH_KEY("CACHE_TRANSLATE_NEPTUNT_HASH_KEY", "翻译缓存hash-key"),
    CACHE_TRANSLATE_NEPTUN_JSON_DATA_KEY("CACHE_TRANSLATE_NEPTUN_JSON_DATA_KEY", "翻译缓存json数据key"),

    EN_US("en-US", "英语"),
    ZN_CN("zh-CN", "中文");

    private String key;

    private String desc;

    NeptuneEnums(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
