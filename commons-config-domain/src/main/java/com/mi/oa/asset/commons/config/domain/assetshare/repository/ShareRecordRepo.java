package com.mi.oa.asset.commons.config.domain.assetshare.repository;

import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-06 16:03
 */
public interface ShareRecordRepo {

    ShareRecord getById(Integer id);

    /**
     * 通过业务线和适用部门编码筛选共享条件
     *
     * @param businessLine
     * @param userCode
     * @param orgCodes
     * @param shareScene
     * @param shareClient
     * @return
     */
    List<ShareRecord> queryList(List<String> businessLine, String userCode, List<String> orgCodes, String shareScene, String shareClient);

    /**
     * 保存或更新
     *
     * @param shareRecord
     * @return
     */
    ShareRecord saveShareRecord(ShareRecord shareRecord);

    /**
     * 删除
     *
     * @param id
     */
    void removeShareRecord(Integer id);
}
