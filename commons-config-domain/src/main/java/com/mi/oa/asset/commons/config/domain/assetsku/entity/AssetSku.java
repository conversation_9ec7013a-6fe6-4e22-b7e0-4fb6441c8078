package com.mi.oa.asset.commons.config.domain.assetsku.entity;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.MaterialType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/9 14:40
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssetSku implements Serializable {

    private static final long serialVersionUID = -6912457367302271820L;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku名称(英文)
     */
    private String skuNameEn;

    /**
     * 别名
     */
    private String aliasName;

    /**
     * 业务线
     */
    private BusinessLine businessLine;

    /**
     * 管理信息业务线
     */
    private List<BusinessLine> businessLines;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 分类id
     */
    private Integer cateId;

    /**
     * 分类编码
     */
    private String cateCode;

    /**
     * 分类名称
     */
    private String cateName;

    /**
     * 小米sku编码
     */
    private String miSkuCode;

    /**
     * 小米商品id
     */
    private String miGoodsId;
    /**
     * 国家编码
     */
    private String country;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 规格
     */
    private String spec;
    /**
     * 型号
     */
    private String model;
    /**
     * 新旧属性
     */
    private String newOld;

    /**
     * 物料类型
     */
    private MaterialType materialType;

    /**
     * 是否一卡多物管理
     */
    private Integer isMultipleManage;

    /**
     * 是否SN
     */
    private Integer isSn;

    /**
     * 计量单位编码
     */
    private String measureCode;

    /**
     * 计量单位名称
     */
    private String measureName;

    /**
     * 禁用
     */
    private Boolean disabled;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 商品名称
     */
    private String productName;


    /**
     * 项目编码
     */
    private String projectCode;


    /**
     * 项目阶段
     */
    private String projectPhase;
    /**
     * 上市时间
     */
    private Date saleDate;
    /**
     * 应归还日期
     */
    private Date endData;
    /**
     * 交期(天)
     */
    private Integer deliveryDays;
    /**
     * 管理信息
     */
    private List<AssetSkuManage> manages;

    /**
     * mdm状态
     * 1 可用   其他不可用
     */
    private Integer mdmCreateStatus;

}
