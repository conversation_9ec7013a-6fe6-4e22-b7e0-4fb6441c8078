package com.mi.oa.asset.commons.config.domain.common.valobj;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/9/11 15:24
 */

@Data
@Builder
public class PurchaseCatalog implements Serializable {

    private static final long serialVersionUID = 833033717880318264L;

    /**
     * 采购目录编码
     */
    private String catalogCode;

    /**
     * 采购目录名称
     */
    private String catalogName;

    /**
     * 采购目录名称英文
     */
    private String catalogNameEn;

    /**
     * 上级编码
     */
    private String parentCode;

    /**
     * 级别
     */
    private Integer level;

    /**
     * sap分类编码
     */
    private String sapCateCode;

    /**
     * sap分类名称
     */
    private String sapCateName;

    /**
     * 使用年限
     */
    private Integer useYear;
}
