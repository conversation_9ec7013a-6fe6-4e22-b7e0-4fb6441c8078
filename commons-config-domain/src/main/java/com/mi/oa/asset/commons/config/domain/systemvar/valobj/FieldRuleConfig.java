package com.mi.oa.asset.commons.config.domain.systemvar.valobj;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024-07-08 11:08
 */
@Data
public class FieldRuleConfig {
    /**
     * 资产台账对应的字段名称，自定义文本使用custom表示，流水号长度使用swift_number表示
     */
    private String code;

    /**
     * 名称：例如：验收日期-年月日（8位）
     */
    private String name;

    /**
     * 资产台账字段一般表示为连接符，选项有：无、-；自定义文本可输入1-5位的数字或字母;流水号可输入5-8位之间的正整数
     */
    private String connector;

    /**
     * 策略模式
     */
    private String strategy;

    /**
     * 默认为空字符串，用于格式化处理
     */
    private String format;

    /**
     * 可配置的业务线，配置多个在后面添加即可，用";"隔开
     */
    private String businessLine;
}
