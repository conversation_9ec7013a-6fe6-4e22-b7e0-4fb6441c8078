package com.mi.oa.asset.commons.config.domain.international.repository;

import com.mi.oa.asset.commons.config.domain.international.entity.CountryBusinessLineDo;

import java.util.List;

/**
* <AUTHOR>
* @description 【amg_country_business_line(国家和业务线关系表)】
*/
public interface CountryBusinessLineRepo {

    List<CountryBusinessLineDo> searchAll();

    CountryBusinessLineDo getById(Integer id);

    Integer save(CountryBusinessLineDo regionCountryDo);

    void updateById(CountryBusinessLineDo entity);

    void updateBatchById(List<CountryBusinessLineDo> doList);

    void deleteByIds(List<Integer> idList);

    List<CountryBusinessLineDo> getByCountryId(Integer countryConfigId);

    List<CountryBusinessLineDo> getByCountryIds(List<Integer> idList);

    List<CountryBusinessLineDo> getByBusinessLine(String businessLine);

    List<CountryBusinessLineDo> getByBusinessLineAndCountryId(String businessLine, Integer countryConfigId);

    void deleteByCountryIds(List<Integer> ids);

    void saveAll(List<CountryBusinessLineDo> countryBusinessLineDoList);

}
