package com.mi.oa.asset.commons.config.domain.common.valobj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/9/18 17:16
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Company implements Serializable {

    private static final long serialVersionUID = -5687989152489954561L;
    /**
     * id
     */
    private Integer id;
    /**
     * 公司代码
     */
    private String companyCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 国家编码
     */
    private String countryCode;
    /**
     * 是否禁用 1 是 0 否
     */
    private Integer disabled;
}
