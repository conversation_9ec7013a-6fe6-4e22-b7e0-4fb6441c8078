package com.mi.oa.asset.commons.config.domain.address.valobj;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/9/3 11:51
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
@Builder
public class AddressParse {

    @JsonProperty("status")
    private Integer status;
    @JsonProperty("address")
    private String address;
    @JsonProperty("province")
    private ProvinceDTO province;
    @JsonProperty("city")
    private CityDTO city;
    @JsonProperty("district")
    private DistrictDTO district;
    @JsonProperty("town")
    private TownDTO town;

    @Data
    public static class ProvinceDTO {
        @JsonProperty("code")
        private String code;
        @JsonProperty("title")
        private String title;
    }

    @Data
    public static class CityDTO {
        @JsonProperty("code")
        private String code;
        @JsonProperty("title")
        private String title;
    }

    @Data
    public static class DistrictDTO {
        @JsonProperty("code")
        private String code;
        @JsonProperty("title")
        private String title;
    }

    @Data
    public static class TownDTO {
        @JsonProperty("code")
        private String code;
        @JsonProperty("title")
        private String title;
    }
}
