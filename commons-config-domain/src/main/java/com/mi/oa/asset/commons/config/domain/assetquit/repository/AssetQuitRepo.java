package com.mi.oa.asset.commons.config.domain.assetquit.repository;


import com.mi.oa.asset.commons.config.domain.assetquit.entity.AssetQuit;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;

import java.util.Date;
import java.util.List;

/**
 * 离职员工信息表 仓储接口
 * <AUTHOR>
 * @date 2024-04-12 02:05:55
 */
public interface AssetQuitRepo {

    PageData<AssetQuit> page(AssetQuit entity,PageRequest pageRequest);

    List<AssetQuit> list(AssetQuit request);

    AssetQuit getById(Integer id);

    boolean exists(String userCode);

    void save(AssetQuit entity);

    void updateById(AssetQuit entity);

    void deleteByIds(List<Integer> idList);

    /**
     * 查询指定（实际离职日期或确认离职日期）的待离职与已离职人员
     *
     * @param dateTime
     * @return
     */
    List<AssetQuit> queryResignEmpByTime(String dateTime);
}

