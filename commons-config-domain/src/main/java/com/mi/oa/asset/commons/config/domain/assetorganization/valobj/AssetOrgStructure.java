package com.mi.oa.asset.commons.config.domain.assetorganization.valobj;

import com.mi.oa.asset.common.enums.BusinessLine;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/9/15 16:29
 */

@Data
@Builder
public class AssetOrgStructure implements Serializable {

    private static final long serialVersionUID = 2671481588363719077L;

    private String structureId;

    /**
     * 组织单元编码
     */
    private String orgCode;

    /**
     * 组织单元名称
     */
    private String orgName;

    /**
     * 组织单元英文名称
     */
    private String orgNameEn;

    /**
     * 上级编码
     */
    private String parentCode;

    /**
     * 默认成本中心
     */
    private String defaultCostCenter;

    /**
     * 是否虚拟组织
     */
    private Boolean isVirtual;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 业务线
     */
    private BusinessLine businessLine;

    /**
     * 禁用
     */
    private Integer disabled;

    /**
     * 数据来源, 默认手动录入 DataCreateSource
     */
    private String dataSource;
}
