package com.mi.oa.asset.commons.config.domain.assetsku.valobj;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.MaterialType;
import com.mi.oa.asset.commons.config.api.assetsku.ManageModel;
import com.mi.oa.asset.commons.config.api.assetsku.SnTypeEnum;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSkuManage;
import com.mi.oa.asset.excel.annotation.MiExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/29 17:55
 * 顺序不能调整
 */
@Data
public class AssetSkuImportData implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "物料编码", index = 0)
    private String skuCode;

    @ExcelProperty(value = "物料名称", index = 1)
    @MiExcelProperty(requiredCell = true)
    private String skuName;

    @ExcelProperty(value = "品牌", index = 2)
    private String brand;

    @ExcelProperty(value = "型号", index = 3)
    private String model;

    @ExcelIgnore
    private String measureCode;

    @ExcelProperty(value = "计量单位", index = 4)
    @MiExcelProperty(requiredCell = true)
    private String measureName;

    @ExcelProperty(value = "分类", index = 5)
    @MiExcelProperty(requiredCell = true)
    private String cateCode;

    @ExcelIgnore
    private Integer cateId;

    @ExcelIgnore
    private String cateName;

    @ExcelProperty(value = "参考价格", index = 6)
    @MiExcelProperty(requiredCell = true)
    private BigDecimal price;

    @ExcelProperty(value = "国家编码", index = 7)
    private String country;

    @ExcelProperty(value = "业务线", index = 8)
    @MiExcelProperty(requiredCell = true, dropdownEnum = BusinessLine.class)
    private String businessLine;

    @ExcelProperty(value = "物料类型", index = 9)
    @MiExcelProperty(requiredCell = true, dropdownEnum = MaterialType.class)
    private String materialType;

    @ExcelProperty(value = "串号管理", index = 10)
    @MiExcelProperty(dropdownEnum = SnTypeEnum.class)
    private String serialMg;

    @ExcelProperty(value = "管理模式", index = 11)
    @MiExcelProperty(requiredCell = true, dropdownEnum = ManageModel.class)
    private String mgModel;

    @ExcelProperty(value = "安全库存量", index = 12)
    private BigDecimal secureQuantity;

    @ExcelProperty(value = "最高库存量", index = 13)
    private BigDecimal highestQuantity;

    @ExcelProperty(value = "最低库存量", index = 14)
    private BigDecimal miniQuantity;

    @ExcelProperty(value = "小米料号", index = 15)
    private String miSkuCode;

    @ExcelProperty(value = "商品ID", index = 16)
    private String miGoodsId;

    @ExcelProperty(value = "商品名称", index = 17)
    private String productName;

    @ExcelProperty(value = "项目编号", index = 18)
    private String projectCode;

    @ExcelProperty(value = "项目阶段", index = 19)
    private String projectPhase;

    @ExcelProperty(value = "上市日期", index = 20)
    private Date saleDate;

    @ExcelIgnore
    private List<AssetSkuManage> manages;

}
