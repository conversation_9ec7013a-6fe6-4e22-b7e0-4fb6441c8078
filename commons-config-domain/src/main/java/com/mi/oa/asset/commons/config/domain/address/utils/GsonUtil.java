package com.mi.oa.asset.commons.config.domain.address.utils;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;

@Slf4j
public class GsonUtil {
    private static Gson gson = null;
    private static Gson prettyGson = null;
    public static Gson dateGson=null;

    static {
        if(null == gson) {
            gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
        }
        if(null == prettyGson) {
            prettyGson = new GsonBuilder().setPrettyPrinting().create();
        }
        if(null==dateGson){
            GsonBuilder builder=new GsonBuilder().registerTypeAdapter(Date.class, new JsonDeserializer<Date>() {
                @Override
                public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
                    Long asString = json.getAsJsonPrimitive().getAsLong();
                    System.out.println(asString);
                    Date d = new Date(asString);
                    return d;
                }

            });
            dateGson= builder.setDateFormat("yyyy-MM-dd").create();
        }
    }
    public static Gson getGson() { return gson; }

    public static <T> T parseObject(String json, Class<T> typeOfT) {
        return dateGson.fromJson(json, typeOfT);
    }

    public static JsonObject toJsonObject(String json) {
        return gson.fromJson(json, JsonObject.class);
    }

    public static JsonArray toJsonArray(String json) {
        return gson.fromJson(json, JsonArray.class);
    }

    /**
     * string to bean
     * @param json
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T toBean(String json, Class clazz) {
        return (T) gson.fromJson(json, clazz);
    }

    /**
     * string to Bean
     * @param json
     * @param type
     * @param <T>
     * @return
     */
    public static <T> T toBean(String json, Type type) {
        return (T) gson.fromJson(json, type);
    }


    /**
     * string to map
     * @param json
     * @return
     */
    public static Map toMap(String json) {
        Map<String, Object> map = new HashMap<>();
        try {
            map = gson.fromJson(json, new TypeToken<Map>(){}.getType());
        } catch (JsonParseException e) {
            log.error("Convert to map failed, string: {}", json);
        }

        return map;
    }

    /**
     * Object to map
     * @param object
     * @return
     */
    public static Map<String,Object> toMap(Object object) {
        String jsonString = toJsonString(object);
        Map<String, Object> map = new HashMap<>();
        try {
            map = gson.fromJson(jsonString, new TypeToken<Map>(){}.getType());
        } catch (JsonParseException e) {
            log.error("Convert to map failed, string: {}", jsonString);
        }
        return map;
    }

    /**
     * string to list
     * @param json
     * @param <T>
     * @return
     */
    public static <T> List<T> toList(String json, Class clazz) {
        List<T> list = new ArrayList<>();
        try {
            Type type = new ParameterizedTypeImpl(clazz);

            list = gson.fromJson(json, type);
        } catch (JsonParseException e) {
            log.error("Convert to list failed, string: {}", json);
        }

        return list;
    }

    public static String toJsonString(Object obj) {
        return gson.toJson(obj);
    }

    public static String toJsonStringPretty(Object obj) {
        return prettyGson.toJson(obj);
    }

    private static class ParameterizedTypeImpl implements ParameterizedType {
        Class clazz;

        public ParameterizedTypeImpl(Class clz) {
            clazz = clz;
        }

        @Override
        public Type[] getActualTypeArguments() {
            return new Type[]{clazz};
        }

        @Override
        public Type getRawType() {
            return List.class;
        }

        @Override
        public Type getOwnerType() {
            return null;
        }
    }
}
