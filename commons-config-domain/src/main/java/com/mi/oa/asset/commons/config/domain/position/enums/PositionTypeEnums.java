package com.mi.oa.asset.commons.config.domain.position.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PositionTypeEnums {

    CAMPUS("campus", "园区"),
    BUILDING("building", "楼栋"),
    FLOOR("floor", "楼层"),
    ROOM("room", "房间"),
    WORKSHOP("workshop", "分厂"),
    LINE("line", "产线"),
    WORKSTATION("workstation", "工位"),
    PROCESS_SECTION("processSection", "工段"),
    PROCESS("process", "工序");

    private final String code;
    private final String desc;

    public static PositionTypeEnums getEnumByCode(String code) {
        for (PositionTypeEnums e : PositionTypeEnums.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

}
