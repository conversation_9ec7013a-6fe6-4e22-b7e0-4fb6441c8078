package com.mi.oa.asset.commons.config.domain.common.repository;

import com.mi.oa.asset.commons.config.domain.common.entity.AssetDisposeType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-26 14:50
 */
public interface AssetDisposeTypeRepo {
    /**
     * 保存处置类型
     * @param disposeType
     */
    Integer saveOrUpdateDisposeType(AssetDisposeType disposeType);

    /**
     * 根据主键批量删除处置类型
     * @param idList
     */
    void batchDeleteDisposeType(List<Integer> idList);

    /**
     * 查询处置
     * @param recordNo
     */
    Integer getDisposeTypeNum(String recordNo);

    /**
     * 查询处置
     * @param id
     */
    AssetDisposeType getDisposeTypeNum(Integer id);

}
