package com.mi.oa.asset.commons.config.domain.assetorganization.entity;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgType;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/9/15 16:29
 */

@Data
@Builder
public class AssetOrgUnit {

    /**
     * 组织单元id
     */
    private Integer orgId;

    /**
     * 组织单元编码
     */
    private String orgCode;

    /**
     * 组织单元名称
     */
    private String orgName;

    /**
     * 组织单元英文名称
     */
    private String orgNameEn;

    /**
     * 别名
     */
    private String aliasName;

    /**
     * 默认成本中心
     */
    private String defaultCostCenter;

    /**
     * 编码全路径，-分隔
     */
    private String orgCodePath;

    /**
     * 名称全路径，-分隔
     */
    private String orgNamePath;

    /**
     * 名称全路径，-分隔
     */
    private String orgNamePathEn;

    /**
     * 上级编码
     */
    private String parentCode;

    /**
     * 上级名称
     */
    private String parentName;

    /**
     * 业务线
     */
    private BusinessLine businessLine;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 是否虚拟组织
     */
    private Boolean isVirtual;

    /**
     * 是否资产管理组织
     */
    private Boolean isAssetManageOrg;

    /**
     * 是否资产使用组织
     */
    private Boolean isAssetUseOrg;

    /**
     * 组织类型
     */
    private AssetOrgType orgType;

    /**
     * 委托记账主体编码
     */
    private String companyCode;

    /**
     * 委托记账主体名称
     */
    private String companyName;

    /**
     * 委托记账成本中心
     */
    private String costCenter;

    /**
     * 地址
     */
    private String address;

    /**
     * 创建时间
     */
    private Date createTime;
}
