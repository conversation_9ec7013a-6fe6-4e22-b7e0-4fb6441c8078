package com.mi.oa.asset.commons.config.domain.function.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/2/25 14:14
 */

@Data
public class FuncTable {
    private Integer id;

    private String title;

    private String tableName;

    private String code;

    private String remark;

    private String parentId;

    private Integer valid;

    /**
     * 创建人账号
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人账号
     */
    private String updateUser;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;
}
