package com.mi.oa.asset.commons.config.domain.use.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UseReasonEnum {
    UPDATE_REPLACE("0", "迭代换新"),
    BUSINESS("1", "出差"),
    DEV_TEST("2", "开发测试"),
    SYSTEM("3", "多系统需求"),
    OTHER("4", "其他"),
    OPERATIONS("5", "大量运算"),
    DISPLAY_REQUIREMENTS("6", "显示屏需求"),
    NEW_WORKPLACES("7", "职场新建"),
    RENOVATION_AND_RENOVATION("8", "装修改造"),
    DIRECTED_USE("9", "定向使用"),
    COMPETE_ANALYSE("10", "拆机分析"),
    COMPETE_TEST("11", "整机测试"),
    ;
    private final String code;
    private final String desc;

    public static UseReasonEnum getByCode(String code) {
        for (UseReasonEnum value : UseReasonEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
