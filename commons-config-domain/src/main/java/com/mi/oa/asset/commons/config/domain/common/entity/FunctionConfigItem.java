package com.mi.oa.asset.commons.config.domain.common.entity;

import com.mi.oa.asset.commons.config.api.myfunctions.Country;
import com.mi.oa.asset.commons.config.api.myfunctions.SaveFunctionConfigDeptReq;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 首页功能配置-业务线配置
 * @Date 2025/4/29 18:58
 **/
@Data
public class FunctionConfigItem {

    /**
     * 主键
     */
    private Integer itemId;

    /**
     * 配置id config_id
     */
    private Integer configId;

    /**
     * 业务线 business_line
     */
    private String businessLine;

    /**
     * 业务线名称 business_line_name
     */
    private String businessLineName;

    /**
     * 授权范围 auth_type
     */
    private Integer authType;

    /**
     * 启用的国家或地区 country
     */
    private List<Country> countries;

    /**
     * 授权部门
     */
    @HttpApiDocClassDefine(value = "授权部门")
    private List<FunctionConfigDept> authDeptList;

}
