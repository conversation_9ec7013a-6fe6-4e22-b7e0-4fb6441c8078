package com.mi.oa.asset.commons.config.domain.assetshare.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.mi.oa.asset.common.model.ExtraFieldInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自定义共享清单
 *
 * <AUTHOR>
 * @date 2025-01-06 16:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomShareList {
    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 共享记录主键
     */
    private Integer shareId;

    /**
     * 资产台账主键
     */
    private Integer assetId;

    /**
     * 台账数量
     */
    private Integer assetQuantity;

    /**
     * 共享数量
     */
    private Integer shareQuantity;

    /**
     * 创建人用户名
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 更新人用户名
     */
    private String updateUser;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /*以下参数不存在于数据库中*/
    /**
     * 资产编号
     */
    private String assetCode;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 责任人账号
     */
    private String userCode;

    /**
     * 责任人名称
     */
    private String userName;

    /**
     * 原值
     */
    private BigDecimal originValue;

    /**
     * 净值
     */
    private BigDecimal netValue;

    /**
     * 台账扩展字段
     */
    private List<ExtraFieldInfo> extraFields = new ArrayList<>();
}

