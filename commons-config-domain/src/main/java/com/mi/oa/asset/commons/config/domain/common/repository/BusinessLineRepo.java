package com.mi.oa.asset.commons.config.domain.common.repository;

import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/31 19:31
 */
public interface BusinessLineRepo {
    BusinessLineDo searchByBusinessLine(String businessLine);

    List<BusinessLineDo> batchSearchByBusinessLine(List<String> businessLine);

    BusinessLineDo searchById(Integer businessLineId);

    Integer save(BusinessLineDo businessLineDo);

    List<BusinessLineDo> searchAll();

    List<BusinessLineDo> getBusinessLine(List<String> manageLines);

}
