package com.mi.oa.asset.commons.config.domain.queryfield.entity;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/8 11:10
 */

@Data
@Builder
public class QueryConfig {

    /**
     * 查询方案主键
     */
    private Integer queryId;

    /**
     * 管理线 manage_line
     */
    private String manageLine;

    /**
     * 功能Id fun_id
     */
    private String funId;

    /**
     * 查询方案名称 query_name
     */
    private String queryName;

    /**
     * 查询条件 query_cond
     */
    private String queryCond;
}
