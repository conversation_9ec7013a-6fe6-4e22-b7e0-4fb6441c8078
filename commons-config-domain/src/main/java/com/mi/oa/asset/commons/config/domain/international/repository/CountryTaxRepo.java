package com.mi.oa.asset.commons.config.domain.international.repository;

import com.mi.oa.asset.commons.config.domain.international.entity.CountryTaxDo;

import java.util.List;

/**
* <AUTHOR>
* @description 【amg_country_tax(税率配置表)】
* @createDate 2025-04-29 19:34:18
*/
public interface CountryTaxRepo {

    List<CountryTaxDo> searchAll();

    CountryTaxDo getById(Integer id);

    Integer save(CountryTaxDo countryTaxDo);

    void updateById(CountryTaxDo entity);

    void deleteByIds(List<Integer> idList);

    List<CountryTaxDo> getByCountryId(Integer countryId);
}
