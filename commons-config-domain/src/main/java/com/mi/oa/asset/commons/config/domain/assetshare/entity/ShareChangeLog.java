package com.mi.oa.asset.commons.config.domain.assetshare.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 共享规则变更日志
 *
 * <AUTHOR>
 * @date 2025-03-07 10:07:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareChangeLog {
    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 共享记录主键
     */
    private Integer shareId;

    /**
     * 变动前数据JSON
     */
    private String beforeChange;

    /**
     * 变动后数据JSON
     */
    private String afterChange;

    /**
     * 创建人用户名
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 更新人用户名
     */
    private String updateUser;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
