package com.mi.oa.asset.commons.config.domain.assetcategory.repository;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.AssetCategory;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:37
 */

public interface AssetCategoryRepo {

    /**
     * 保存资产分类
     * @param assetCategory
     */
    void saveAssetCategory(AssetCategory assetCategory);

    /**
     * 保存资产分类
     * @param assetCategories
     */
    void saveAssetCategories(List<AssetCategory> assetCategories);

    /**
     * 更新编码路径
     * @param assetCategory
     */
    void updateCatePath(AssetCategory assetCategory);

    /**
     * 批量更新编码路径
     * @param assetCategorys
     */
    void updateCatePathBatch(List<AssetCategory> assetCategorys);

    /**
     * 根据资产分类编码及业务线获取
     * @param businessLine
     * @param cateCodes
     * @return
     */
    List<AssetCategory> getByCateCode(String businessLine, List<String> cateCodes);

    /**
     * 根据资产分类编码及业务线获取
     * @param businessLine
     * @param cateCode
     * @return
     */
    AssetCategory getByCateCode(String businessLine, String cateCode);

    List<AssetCategory> getByCateCodes(List<String> cateCodes);
    /**
     * 获取全部资产分类
     *
     * @param businessLineCodes
     * @param enabled
     * @return
     */
    List<AssetCategory> getAllAssetCategory(List<String> businessLineCodes, boolean enabled);

    /**
     * 获取单个资产分类
     * @param id
     * @return
     */
    AssetCategory getAssetCategory(Integer id);

    /**
     * 获取多个资产分类
     * @param ids
     * @return
     */
    List<AssetCategory> getAssetCategories(List<Integer> ids);

    /**
     * 根据分类路径删除分类及其子分类
     * @param catePaths
     */
    void deleteByPath(List<String> catePaths);

    PageData<AssetCategory> assetCategoryPageData(List<String> businessLineCodes, String keyword, PageRequest pageRequest);

    /**
     * 分页查询资产分类，含下级
     * @param catePath
     * @param keyword
     * @param pageRequest
     * @return
     */
    PageData<AssetCategory> assetCategoryPageData(String catePath, String keyword, PageRequest pageRequest);

    /**
     * 根据采购目录编码查询
     * @param catalogCodes
     * @param businessLine
     * @return
     */
    List<AssetCategory> getByCatalogCodes(List<String> catalogCodes, BusinessLine businessLine);

    /**
     * 根据分类路径查询
     * @param catePaths
     * @return
     */
    List<AssetCategory> getByCatePaths(List<String> catePaths);

    /**
     * 分类编码是否存在
     * @param cateCode
     * @return
     */
    boolean isExists(String cateCode);

}
