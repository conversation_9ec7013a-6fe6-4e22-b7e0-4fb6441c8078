package com.mi.oa.asset.commons.config.domain.assetsku.enums;

import com.fasterxml.jackson.annotation.JsonProperty;


public enum MdmPurchaseItemTypeEnum {

    @JsonProperty("1")
    CARGO("1", "货物"),

    @JsonProperty("2")
    SOFTWARE("2", "软件"),

    @JsonProperty("3")
    SERVICE("3", "服务"),

    @JsonProperty("4")
    PROJECT("4", "工程"),
    ;

    private String code;

    private String desc;

    MdmPurchaseItemTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static MdmPurchaseItemTypeEnum getByCode(String code) {
        for (MdmPurchaseItemTypeEnum i : MdmPurchaseItemTypeEnum.values()) {
            if(i.code.equals(code)) return i;
        }

        return null;
    }
}