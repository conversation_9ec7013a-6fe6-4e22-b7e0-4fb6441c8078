package com.mi.oa.asset.commons.config.domain.mesurement.entity;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/9/15 16:29
 */

@Data
@Builder
public class MeasurementUnit {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 计量单元编码
     */
    private String measureCode;

    /**
     * 组织单元编码
     */
    private String measureName;
    /**
     * 计量单位名称-英文
     */
    private String measureNameEn;

    /**
     * 组织单元编码
     */
    private String techCode;

    /**
     * 计量单位唯一值
     */
    private String muId;

    /**
     * 组织单元编码
     */
    private String unitTypeCode;

    /**
     * 组织单元编码
     */
    private String unitTypeName;

    /**
     * 来源
     */
    private String dataSource;

    /**
     * 禁用
     */
    private Integer disabled;
}
