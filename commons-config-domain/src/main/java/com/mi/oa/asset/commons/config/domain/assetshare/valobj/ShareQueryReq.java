package com.mi.oa.asset.commons.config.domain.assetshare.valobj;

import com.mi.oa.asset.commons.config.domain.assetshare.entity.CustomShareList;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRecord;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-13 17:48
 * 查询共享规则条件对象
 */
@Data
public class ShareQueryReq {

    /**
     * 业务线
     */
    private List<String> businessLine;

    /**
     * 人员账号
     */
    private String userCode;

    /**
     * 姓名
     */
    private String deptCode;

    /**
     * 共享场景
     */
    private String shareScene;

    /**
     * 应用的客户端
     */
    private String shareClient;

    /**
     * 可见共享规则
     */
    private List<ShareRecord> shareRecords = new ArrayList<>();

    /**
     * 可见共享规则下的自定义导入共享清单
     */
    private List<CustomShareList> allShareLists = new ArrayList<>();
}
