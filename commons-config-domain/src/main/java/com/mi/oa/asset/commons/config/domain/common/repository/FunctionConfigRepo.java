package com.mi.oa.asset.commons.config.domain.common.repository;

import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfig;

import java.util.List;

public interface FunctionConfigRepo {

    /**
     * 保存首页功能配置
     *
     * @param config
     */
    int saveFunctionConfig(FunctionConfig config);

    /**
     * 根据管理线查询数据
     *
     * @param manageLine
     * @param functionName
     * @param functionNameEn
     * @return
     */
    FunctionConfig getConfig(String manageLine, String functionName, String functionNameEn);

    /**
     * 根据id集合查询
     *
     * @param configIds
     * @return
     */
    List<FunctionConfig> listByIds(List<Integer> configIds);

    /**
     * 根据管理线查询
     *
     * @param manageLine
     * @return
     */
    List<FunctionConfig> listByManageLine(String manageLine);

    /**
     * 删除
     *
     * @param ids
     */
    void deleteByConfigIds(List<Integer> ids);
}
