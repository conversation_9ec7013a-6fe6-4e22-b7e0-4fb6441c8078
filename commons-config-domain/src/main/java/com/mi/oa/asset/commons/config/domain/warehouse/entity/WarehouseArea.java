package com.mi.oa.asset.commons.config.domain.warehouse.entity;

import lombok.Data;

@Data
public class WarehouseArea {
    private Integer id;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 优先级，正整数 必填
     */
    private Integer priority;

    /**
     * 国家id
     */
    private String countryId;

    /**
     * 省id
     */
    private String provinceId;

    /**
     * 市id
     */
    private String cityId;

    /**
     * 区id
     */
    private String areaId;

    /**
     * 街道id
     */
    private String streetId;

    /**
     * 地区名称
     */
    private String areaName;

    private String createUser;

    private String createUserName;

    private String updateUser;

    private String updateUserName;

    private Integer isDeleted;
}
