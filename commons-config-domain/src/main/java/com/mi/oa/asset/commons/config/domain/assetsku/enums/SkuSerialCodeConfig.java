package com.mi.oa.asset.commons.config.domain.assetsku.enums;

import com.mi.oa.asset.common.enums.BusinessLine;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/10/9 19:01
 */

@Getter
public enum SkuSerialCodeConfig {

    P1("CAR", BusinessLine.CAR, 6),
    P2("3", BusinessLine.ADM_PUB, 4),
    P3("EMP", BusinessLine.ADM_EMP, 6),
    P4("6", BusinessLine.COMPETE_MOBILE, 5),
    ;

    private final String prefix;

    private final BusinessLine businessLine;

    private final int zeroPadSize;

    SkuSerialCodeConfig(String prefix, BusinessLine businessLine, int zeroPadSize) {
        this.prefix = prefix;
        this.businessLine = businessLine;
        this.zeroPadSize = zeroPadSize;
    }

    public static SkuSerialCodeConfig getPrefix(BusinessLine businessLine) {
        for (SkuSerialCodeConfig item : SkuSerialCodeConfig.values()) {
            if (item.getBusinessLine().equals(businessLine)) {
                return item;
            }
        }
        return P2;
    }
}
