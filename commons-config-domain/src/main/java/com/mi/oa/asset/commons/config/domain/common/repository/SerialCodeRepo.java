package com.mi.oa.asset.commons.config.domain.common.repository;

import com.mi.oa.asset.commons.config.domain.common.valobj.SerialCode;

/**
 * <AUTHOR>
 * @Date 2023/9/19 15:00
 */

public interface SerialCodeRepo {

    boolean isSerialCodeExisted(String codePrefix, String indexCode);

    SerialCode getSerialCode(String codePrefix, String indexCode);

    void createSerialCode(String codePrefix, String indexCode);

    void updateSerialCode(SerialCode serialCode, String indexCode);
}
