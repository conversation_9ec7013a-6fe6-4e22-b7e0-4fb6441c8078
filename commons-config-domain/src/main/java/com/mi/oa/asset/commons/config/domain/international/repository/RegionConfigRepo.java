package com.mi.oa.asset.commons.config.domain.international.repository;

import com.mi.oa.asset.commons.config.api.regionconfig.EmployeeRegionCountryRes;
import com.mi.oa.asset.commons.config.domain.international.entity.RegionConfigDo;

import java.util.List;

/**
* <AUTHOR>
* @description 【amg_region_config(区域配置表)】
* @createDate 2025-04-28 15:43:27
*/
public interface RegionConfigRepo{

    List<RegionConfigDo> searchAll();

    RegionConfigDo getById(Integer id);

    Integer save(RegionConfigDo regionConfigDo);

    int updateById(RegionConfigDo entity);

    void deleteByIds(List<Integer> idList);
}
