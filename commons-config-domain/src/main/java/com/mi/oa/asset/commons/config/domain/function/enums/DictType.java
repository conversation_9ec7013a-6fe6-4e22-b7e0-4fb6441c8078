package com.mi.oa.asset.commons.config.domain.function.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/4/07 16:07
 */
@Getter
public enum DictType {

    KEY(0, "key"),
    VALUE(1,"value");

    private final Integer code;

    private final String name;

    DictType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DictType getByCode(String code) {
        for (DictType value : DictType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
