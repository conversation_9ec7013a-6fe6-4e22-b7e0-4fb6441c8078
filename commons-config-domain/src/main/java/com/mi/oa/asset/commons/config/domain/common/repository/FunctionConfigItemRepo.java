package com.mi.oa.asset.commons.config.domain.common.repository;

import com.mi.oa.asset.commons.config.domain.common.entity.FunctionConfigItem;

import java.util.List;

public interface FunctionConfigItemRepo {

    List<FunctionConfigItem> saveConfigItems(List<FunctionConfigItem> configItems);

    void deleteByConfigId(Integer configId);

    void deleteByConfigIds(List<Integer> configIds);

    List<FunctionConfigItem> getByCountry(String country);

    List<FunctionConfigItem> listByConfigIds(List<Integer> configIds);
}
