
package com.mi.oa.asset.commons.config.domain.assetsku.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.mi.oa.asset.common.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2024/7/30 11:09
 */
@Getter
@AllArgsConstructor
public enum StockCostEnum implements IEnum<StockCostEnum> {

    MOVING_AVG("moving_avg", "移动加权平均"),

    ;
    @JsonValue
    private final String code;

    private final String desc;

    public static StockCostEnum getByCode(String code) {
        if(null == code) {
            return null;
        }

        for (StockCostEnum compensateType: StockCostEnum.values()) {
            if (compensateType.code.equals(code)) {
                return compensateType;
            }
        }
        return null;
    }

    public static StockCostEnum getByDesc(String desc) {
        if(StringUtils.isBlank(desc)) {
            return null;
        }

        for (StockCostEnum compensateType: StockCostEnum.values()) {
            if (compensateType.desc.equals(desc)) {
                return compensateType;
            }
        }
        return null;
    }
}
