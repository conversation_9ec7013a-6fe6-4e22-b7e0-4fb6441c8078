package com.mi.oa.asset.commons.config.domain.warehouse.repository;

import com.mi.oa.asset.commons.config.domain.warehouse.entity.WarehouseArea;

import java.util.List;

/**
 * 仓库区域
 */
public interface WarehouseAreaRepo {

    /**
     * 新增仓库
     *
     * @param warehouseArea
     * @return
     */
    Integer saveWarehouse(WarehouseArea warehouseArea);


    /**
     * - 移除：可对添加的仓库移除与地区的关联关系
     *
     * @param warehouseCode
     * @param countryId
     * @param provinceId
     * @param cityId
     * @param areaId
     * @param streetId
     * @return
     */
    void removeWarehouse(String warehouseCode, String countryId, String provinceId, String cityId, String areaId, String streetId);


    /**
     * 获取仓库区域列表
     * @param businessLines 业务线，必传
     * @param countryId 国家id，可选
     * @param provinceId 省id，可选
     * @param cityId 市id，可选
     * @param areaId 区id，可选
     * @param streetId 街道id，可选
     * @param areaName 区域名称，可选
     * @return
     */
    List<WarehouseArea> listByBizAndRegion(List<String> businessLines, String countryId, String provinceId, String cityId, String areaId, String streetId, String areaName);

    /**
     * 根据关键词（仓编码或仓名称）模糊查询仓库区域，支持业务线过滤
     */
    List<WarehouseArea> searchWarehouseArea(String keyword, List<String> businessLines);

}
