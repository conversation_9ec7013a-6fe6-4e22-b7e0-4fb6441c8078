package com.mi.oa.asset.commons.config.domain.assetquit.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 离职员工信息表 领域实体类
 * <AUTHOR>
 * @date 2024-04-12 02:05:55
 */
@Data
public class AssetQuit implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 员工姓名
	 */
	private String userName;
	/**
	 * 员工账号
	 */
	private String userCode;
	/**
	 * 所属部门
	 */
	private String deptName;
	/**
	 * 申请离职日期
	 */
	private Date createDate;
	/**
	 * 所属部门ID
	 */
	private String deptId;
	/**
	 * 实际离职日期
	 */
	private Date quitDate;
	/**
	 * 岗位代码
	 */
	private String postCode;
	/**
	 * 岗位名称
	 */
	private String postName;
	/**
	 * 员工工号
	 */
	private String empCode;
	/**
	 * 部门编码
	 */
	private String deptCode;
	/**
	 * 手机号
	 */
	private String phone;
	/**
	 * 岗位ID
	 */
	private String postId;
	/**
	 * 主键
	 */
	private String quitId;
	/**
	 * 添加人ID
	 */
	private String addUserid;
	/**
	 * 添加时间
	 */
	private Date addDate;
	/**
	 * 修改人ID
	 */
	private String modifyUserid;
	/**
	 * 修改时间
	 */
	private Date modifyDate;
	/**
	 * 系统租户ID
	 */
	private String tenantId;
	/**
	 * 离职状态
	 */
	private String quitState;
	/**
	 * 工作地点
	 */
	private String officeAddress;
	/**
	 * 邮箱
	 */
	private String email;
	/**
	 * 是否确认离职
	 */
	private String isQuit;
	/**
	 * 撤销日期
	 */
	private Date revokeDate;
	/**
	 * 操作人账号
	 */
	private String confirmUser;
	/**
	 * 操作人账号
	 */
	private String confirmUsercode;
	/**
	 * 确认离职时间
	 */
	private Date confirmDate;
	/**
	 * 是否逾期
	 */
	private String isExceed;
	/**
	 * 逾期天数
	 */
	private BigDecimal exceedDay;
	/**
	 * 资产数量(使用中)
	 */
	private BigDecimal useNum;
	/**
	 * 资产数量(异动中)
	 */
	private BigDecimal numAction;
	/**
	 * 一级部门编码
	 */
	private String deptCodeLv1;
	/**
	 * 一级部门名称
	 */
	private String deptNameLv1;
	/**
	 * 部门名称(全)
	 */
	private String fullDeptName;
	/**
	 * 在管公共资产
	 */
	private String havePubAsset;
	/**
	 * 国家编码
	 */
	private String country;
	/**
	 * 国家名称
	 */
	private String countryName;
	/**
	 * 是否推送PS
	 */
	private String isPushPs;
	/**
	 * 资产价值总额
	 */
	private BigDecimal totalMoney;
	/**
	 * 离职资产处置单主键
	 */
	private String scrapId;
	/**
	 * 是否删除
	 */
	private String isDelete;

}
