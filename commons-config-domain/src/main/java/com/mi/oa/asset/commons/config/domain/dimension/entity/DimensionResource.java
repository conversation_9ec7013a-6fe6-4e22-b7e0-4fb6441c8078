package com.mi.oa.asset.commons.config.domain.dimension.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/02/24/03:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DimensionResource implements Serializable {
    /**
     *  id
     */
    private Integer id;

    /**
     *  dimension_code
     */
    private String dimensionCode;

    /**
     *  name
     */
    private String dimensionName;

    /**
     *  sql
     */
    private String resourceSql;
    /**
     *  field
     */
    private String fieldCode;
    /**
     *  field
     */
    private String fieldName;
    /**
     *  field
     */
    private String fieldLevel;
    /**
     *  field
     */
    private String parentFieldCode;
    /**
     *  resource_type
     */
    private String resourceType;

    /**
     *  first_level_value
     */
    private String firstLevelValue;
    /**
     *  is_layer
     */
    private Boolean isLayer;
}