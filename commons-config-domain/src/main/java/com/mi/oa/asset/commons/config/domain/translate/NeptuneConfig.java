package com.mi.oa.asset.commons.config.domain.translate;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "neptune-manager")
@Data
public class NeptuneConfig {
    private String applyUrl;
    private String applyUrlSg;
    private String applyMethod;
    private String appId;
    private String appKey;
    private boolean queryProdVersion;
    private boolean sg;
    private Long period;
}
