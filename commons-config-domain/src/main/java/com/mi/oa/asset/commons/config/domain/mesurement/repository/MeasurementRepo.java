package com.mi.oa.asset.commons.config.domain.mesurement.repository;

import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.MeasurementUnitQuery;
import com.mi.oa.asset.commons.config.domain.mesurement.entity.MeasurementUnit;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/15 18:06
 */

public interface MeasurementRepo {

    List<MeasurementUnit> queryListByMeasureCode(String measureCode);

    List<MeasurementUnit> queryListByMeasureCodes(List<String> measureCodes);

    List<MeasurementUnit> queryListByMeasureNames(List<String> measureNames);

    List<MeasurementUnit> getMeasurementUnits(List<String> muIds);

    List<MeasurementUnit> queryAllMeasurementUnits();

    void createMeasurements(List<MeasurementUnit> creates);

    void updateMeasurements(List<MeasurementUnit> updates);

    /**
     * 根据 muId 唯一键，批量停用计量单位
     */
    void inactivateMeasurement(List<String> muIds);

    PageData<MeasurementUnit> measurementUnitPageData(MeasurementUnitQuery params, PageRequest pageRequest);
}
