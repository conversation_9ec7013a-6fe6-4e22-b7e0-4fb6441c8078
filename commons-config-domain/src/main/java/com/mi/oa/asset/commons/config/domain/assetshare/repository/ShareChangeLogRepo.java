package com.mi.oa.asset.commons.config.domain.assetshare.repository;

import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareChangeLog;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-07 10:47:38
 */
public interface ShareChangeLogRepo {
    void save(ShareChangeLog shareChangeLog);

    /**
     * 通过共享规则主键查询指定时间到当前时间变动日志
     *
     * @param shareIds
     * @return
     */
    List<ShareChangeLog> getByShareIds(List<Integer> shareIds, Date startTime);
}
