package com.mi.oa.asset.commons.config.domain.warehouse.entity;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.warehouse.enums.WarehouseType;
import lombok.Data;

import java.util.Map;

@Data
public class Warehouse {
    private Integer id;
    private String houseCode;
    private String houseName;
    private WarehouseType houseType;
    private String adminCodes;
    private String adminNames;
    private String country;
    private String currency;
    private String address;
    private Boolean shelves;
    private String shelvesNum;
    private String thirdHouseCode;
    private String sapHouseCode;
    private String factoryCode;
    private String companyCode;
    private String services;
    private String servicesName;
    private String zitiArea;
    private String mailArea;
    private String houseStatus;
    private BusinessLine businessLine;
    private String logisticsType;
    private String departCode;
    private String departName;
    private String departFullName;
    private String phone;
    private String sort;
    private String area;
    private String email;

    private String productTypes;
    private Map<String, String> stockAllotConfig;

}
