package com.mi.oa.asset.commons.config.domain.assetorganization.valobj;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgType;
import lombok.Builder;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/2 10:56
 */

@Data
@Builder
public class AssetOrgUnitQuery {

    /**
     * 组织单元id列表
     */
    private List<Integer> orgIds;

    /**
     * 组织单元编码
     */
    private String orgCode;

    /**
     * 组织单元编码列表
     */
    private List<String> orgCodes;

    /**
     * 上级编码
     */
    private String parentCode;

    /**
     * 业务线
     */
    private BusinessLine businessLine;

    /**
     * 业务线列表
     */
    private List<BusinessLine> businessLines;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 是否虚拟组织
     */
    private Boolean isVirtual;

    /**
     * 是否资产管理组织
     */
    private Boolean isAssetManageOrg;

    /**
     * 是否资产使用组织
     */
    private Boolean isAssetUseOrg;

    /**
     * 组织类型
     */
    private AssetOrgType orgType;

    /**
     * 组织类型列表
     */
    private List<AssetOrgType> orgTypes;

    /**
     * 搜索关键字
     */
    private String keyword;

    /**
     * 调入人账号
     */
    private String inUseCode;

    /**
     * 调入使用部门
     */
    private String inUseDeptCode;

    public boolean isAllEmpty() {
        Field[] fields = this.getClass().getDeclaredFields();
        try {
            for (Field field : fields) {
                field.setAccessible(true);

                Object value = field.get(this);
                if(value instanceof Collection) {
                    if(!((Collection<?>) value).isEmpty()) return false;
                } else {
                    if (value != null) return false;
                }
            }
        } catch (Exception ignored) {
        }

        return true;
    }
}
