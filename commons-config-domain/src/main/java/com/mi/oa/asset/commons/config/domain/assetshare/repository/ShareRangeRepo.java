package com.mi.oa.asset.commons.config.domain.assetshare.repository;

import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRange;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-06 16:04
 */
public interface ShareRangeRepo {

    /**
     * 通过共享主键查询
     *
     * @param shareId
     * @return
     */
    List<ShareRange> getByShareId(Integer shareId);

    List<ShareRange> getByShareIds(List<Integer> shareIds);

    /**
     * 批量保存或更新共享资产更多筛选条件
     *
     * @param shareRangeList
     */
    void batchSaveShareRange(List<ShareRange> shareRangeList);

    /**
     * 每次保存时先删除共享记录下的更多范围条件记录
     *
     * @param shareId
     */
    void removeByShareId(Integer shareId);
}
