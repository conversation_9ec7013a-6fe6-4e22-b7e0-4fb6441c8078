package com.mi.oa.asset.commons.config.domain.common.valobj;

import lombok.Data;

import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @date 2023-10-26 14:52
 */
@Data
public class AttachInfo {

    /**
     * 自增主键 id
     */
    private Integer id;

    /**
     * 业务单据id record_id
     */
    private Integer recordId;

    /**
     * 业务单据编码 record_no
     */
    private String recordNo;

    /**
     * 业务单据类型 record_type
     */
    private String recordType;

    /**
     * 附件原始名称 origin_name
     */
    private String originName;

    /**
     * 附件名称 attach_name
     */
    private String attachName;

    /**
     * fds文件链接 attach_link
     */
    private String attachLink;

    /**
     * 创建人用户名 create_user
     */
    private String createUser;

    /**
     * 创建时间 create_time
     */
    private ZonedDateTime createTime;

    /**
     * 更新人用户名 update_user
     */
    private String updateUser;

    /**
     * 更新时间 update_time
     */
    private ZonedDateTime updateTime;
}
