package com.mi.oa.asset.commons.config.domain.assetcategory.entity.old;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date    2023/6/27 17:19
 */

/**
    * 展示分类
    */
@Data
@Builder
public class DeviceCategory {
    /**
     * 主键
     */
    private String showId;

    /**
     * 分类编码
     */
    private String showCode;

    /**
     * 级别
     */
    private BigDecimal showLevel;

    /**
     * 分类名称
     */
    private String showName;

    /**
     * 添加人ID
     */
    private String addUserid;

    /**
     * 添加时间
     */
    private Date addDate;

    /**
     * 修改人ID
     */
    private String modifyUserid;

    /**
     * 修改时间
     */
    private Date modifyDate;

    /**
     * 系统租户ID
     */
    private String tenantId;

    /**
     * 是否生效
     */
    private String isValid;

    /**
     * 父ID
     */
    private String parentId;

    /**
     * SAP资产分类编码
     */
    private String accCode;

    /**
     * 排序字段
     */
    private BigDecimal assetSort;

    /**
     * 业务渠道
     */
    private String busType;

    /**
     * 国家编码
     */
    private String country;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 分类名称(英文)
     */
    private String showNameEn;

    /**
     * SAP资产分类ID
     */
    private String accId;

    /**
     * 是否叶子节点
     */
    private String isLeaf;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * SAP资产分类名称
     */
    @TableField(value = "acc_name")
    private String accName;

    /**
     * 使用期间
     */
    private String useMonth;

    /**
     * 使用年限
     */
    private String useYear;
}