package com.mi.oa.asset.commons.config.domain.international.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Data
@NoArgsConstructor
public class CountryCurrencyDo {

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 币种名称
     */
    private String currencyName;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 默认币种
     */
    private Integer defaultCurrency;

    /**
     * 国家数据表id
     */
    private Integer countryConfigId;

    /**
     * 国家/地区代码（3位）
     */
    private String countryCodeAlphaThree;

    /**
     * 国家/地区代码（2位）
     */
    private String countryCodeAlphaTwo;

    /**
     * 国家/地区
     */
    private String countryName;

    /**
     * 限制小数位
     */
    private Integer isLimitDecimal;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建人用户名
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新人用户名
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}
