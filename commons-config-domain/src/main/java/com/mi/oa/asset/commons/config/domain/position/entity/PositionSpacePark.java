package com.mi.oa.asset.commons.config.domain.position.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/10/16 10:30
 */

@Data
public class PositionSpacePark {
    /**
     * 位置id
     */
    private Integer positionId;

    /**
     * 位置编码
     */
    private String positionCode;
    /**
     * 位置名称
     */
    private String positionName;
    /**
     * 英文名称
     */
    private String positionNameEn;
    /**
     * 上级编码
     */
    private String parentCode;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;
}
