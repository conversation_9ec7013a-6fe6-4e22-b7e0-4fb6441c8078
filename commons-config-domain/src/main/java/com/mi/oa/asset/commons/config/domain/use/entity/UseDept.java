package com.mi.oa.asset.commons.config.domain.use.entity;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.*;

import java.util.Date;

/**
 * 用途部门
 * @TableName amg_use_dept
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UseDept extends DTO {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 申请用途ID
     */
    private String useId;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 创建人账号
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人账号
     */
    private String updateUser;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Integer isDeleted;
}