package com.mi.oa.asset.commons.config.domain.function.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/4/07 16:07
 */
@Getter
public enum MysqlDataType {

    INT("int", "int"),
    STRING("varchar","string"),
    TINYINT("tinyint","int"),
    TIMESTAMP("timestamp","date"),
    DATE("date","date"),
    DATETIME("datetime","date"),
    DECIMAL("decimal","double"),
    CHAR("char","string"),
    ;
    private final String code;

    private final String name;

    MysqlDataType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MysqlDataType getByCode(String code) {
        for (MysqlDataType value : MysqlDataType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getDataTypeByMysql(String code) {
        for (MysqlDataType value : MysqlDataType.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
        return STRING.name;
    }
}
