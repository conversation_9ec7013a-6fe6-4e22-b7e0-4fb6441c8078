package com.mi.oa.asset.commons.config.domain.common.constant;

/**
 * <AUTHOR>
 * @date 2024-07-09 09:11
 */
public class CommonConstant {

    public static final String SEMICOLON = ";";
    public static final String WAVE = "~";
    public static final String COMMA = ",";
    public static final String IN_JOINT = "','";
    public static final String HORIZONTAL = "-";

    // 共享相关
    public static final String ASSET_SHARE_FUN = "asset_share_list";
    public static final String SHARE_LIST_FUN = "custom_share_list";
    public static final String ASSET_ACCOUNT_FUN = "asset_account";
    public static final String SHARE_RECORD_NO_PREFIX = "S";
    public static final String QUANTITY_CONSTANT = "quantity";
    public static final String SHARE_QUANTITY_CONSTANT = "shareQuantity";
    public static final String KEY_ID = "id";

    // 资产编码生成相关
    public static final String ASSET_CODE_RULE_VAR_CODE = "{0}_asset_code_rule_build";
    public static final String ASSET_CODE_RULE_VAR_DESC = "{0}资产编码生成规则";
    public static final String NONE = "无";

    // 物料导入相关
    public static final String ASSET_CATE = "assetCate";
    public static final String ASSET_CATE_CODE = "cateCode";
    public static final String ASSET_SKU_CODE = "skuCode";
    public static final String BUSINESS_SKU = "businessAndSku";
    public static final String MEASURE_NAME = "measureName";
    public static final String MEASURE = "measure";
    public static final String COUNTRY = "country";

    //串号明细
    public static final String STOCK_SERIAL_FUN = "amg_stock_serial";


    public static final String ID = "id";

    public static final String DEFAULT_TIME_ZONE = "Asia/Shanghai";

    public static final String M5 = "M5";
    // 过滤计量单位全局变量
    public static final String MEASURE_UNIT_FILTER = "amgMeasureUnitFilter";

}
