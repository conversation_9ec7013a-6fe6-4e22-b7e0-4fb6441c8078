package com.mi.oa.asset.commons.config.domain.bpm.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description 审批人
 * @Date 2024/9/23 11:11
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class BpmApprover {

    private String taskId;

    private String taskName;

    private String userName;

    private String displayName;

    private String avatar;

    private String createTime;

    private String endTime;

    private String operation;

    private String operationColor;

    private String activityType;

    private String comment;

}
