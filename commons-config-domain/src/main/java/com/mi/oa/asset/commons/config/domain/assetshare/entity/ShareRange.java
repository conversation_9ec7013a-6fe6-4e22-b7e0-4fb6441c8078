package com.mi.oa.asset.commons.config.domain.assetshare.entity;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 资产共享范围配置
 *
 * <AUTHOR>
 * @date 2025-01-06 16:08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareRange {
    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 共享记录主键
     */
    private Integer shareId;

    /**
     * 筛选字段编码
     */
    private String fieldCode;

    /**
     * 运算逻辑符号
     */
    private String queryCond;

    /**
     * 目标值
     */
    private String fieldValues;

    /**
     * 创建人用户名
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 更新人用户名
     */
    private String updateUser;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}

