package com.mi.oa.asset.commons.config.domain.bpm.enums;

import lombok.Getter;

@Getter
public enum BpmApproveStatus {

    AGREE("agree", "同意"),
    REJECT("reject", "驳回"),
    SIGN("sign", "加签"),
    TRANSFER("transfer", "转审"),
    DELEGATE("delegate", "委托"),
    TERMINATE("interrupt", "终止"),
    ROLLBACK("rollback", "退回"),
    CC("cc", "抄送"),
    RESOLVE("resolve", ""),
    SUBMIT("submit", "提交"),
    END("end", "审批通过"),
    PIN("pin", "pin"),
    RECALL("recall", "撤回"),
    SIGNATURE("signature", "加签（新）"),
    PENDING("pending", "待审批");

    private String code;

    private String desc;

    BpmApproveStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
