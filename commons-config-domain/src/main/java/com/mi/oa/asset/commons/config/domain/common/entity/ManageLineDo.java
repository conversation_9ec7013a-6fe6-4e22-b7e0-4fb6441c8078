package com.mi.oa.asset.commons.config.domain.common.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/9/19 14:51
 */

@Data
public class ManageLineDo {

    private Integer id;

    /**
     * 管理线 manage_line
     */
    private String manageLine;

    /**
     * 管理线名称 manage_line_name
     */
    private String manageLineName;

    private String manageLineNameEn;

    /**
     * 资产管理员账号 manager_user
     */
    private String managerUser;

    /**
     * 资产管理员姓名 manager_user_name
     */
    private String managerUserName;

    /**
     * 审批人账号 approval_user
     */
    private String approvalUser;

    /**
     * 审批人姓名 approval_user_name
     */
    private String approvalUserName;

    /**
     * 是否生效 is_valid
     */
    private String isValid;

    /**
     * 是否新管理线 is_new_line
     */
    private String isNewLine;

    /**
     * bpm唯一标识 business_key
     */
    private String businessKey;

    /**
     * 单据状态（审核中/已完成）
     */
    private String recordStatus;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

}
