package com.mi.oa.asset.commons.config.domain.businessrole.entity;

import com.mi.oa.asset.common.enums.BusinessLine;
import lombok.Builder;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/10/27 15:42
 */

@Data
@Builder
public class BusinessRoleUser {
    private Integer id;

    /**
     * 角色编码 role_code
     */
    private String roleCode;

    /**
     * 角色名称 role_name
     */
    private String roleName;

    /**
     * 角色描述 role_desc
     */
    private String roleDesc;

    /**
     * 账号 user_code
     */
    private String userCode;

    /**
     * 姓名 user_name
     */
    private String userName;

    /**
     * 用户部门名称
     */
    private String deptName;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 组织结构编码
     */
    private String orgCode;

    /**
     * 业务线
     */
    private BusinessLine businessLine;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BusinessRoleUser that = (BusinessRoleUser) o;
        return Objects.equals(roleCode, that.roleCode) && Objects.equals(userCode, that.userCode) && Objects.equals(orgCode, that.orgCode) && businessLine == that.businessLine;
    }

    @Override
    public int hashCode() {
        return Objects.hash(roleCode, userCode, orgCode, businessLine);
    }
}
