package com.mi.oa.asset.commons.config.domain.function.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/4/07 16:07
 */
@Getter
public enum FuncManageType {

    SYSTEM(0, "系统默认"),
    MANAGE_LINE(1,"管理线"),
    BUSINESS_LINE(2, "业务线");

    private final Integer code;

    private final String name;

    FuncManageType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static FuncManageType getByCode(String code) {
        for (FuncManageType value : FuncManageType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
