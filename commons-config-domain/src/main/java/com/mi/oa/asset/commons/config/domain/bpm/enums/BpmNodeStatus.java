package com.mi.oa.asset.commons.config.domain.bpm.enums;

import com.mi.oa.asset.common.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/9/23 11:11
 **/
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum BpmNodeStatus implements IEnum<BpmNodeStatus> {
    COMPLETE("Completed node", "已完成"),
    PENDING("Node in progress", "待处理"),
    UNREACHED("Unreached node", "未到达的节点"),
    UNCLAIMED("Unclaimed node", "未领取节点"),
    ;

    private String code;

    private String desc;

}
