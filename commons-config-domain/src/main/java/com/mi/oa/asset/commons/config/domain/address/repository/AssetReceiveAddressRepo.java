package com.mi.oa.asset.commons.config.domain.address.repository;


import com.mi.oa.asset.commons.config.api.myfunctions.Country;
import com.mi.oa.asset.commons.config.domain.address.entity.AssetReceiveAddress;

import java.util.List;

/**
 * 仓储接口
 *
 * <AUTHOR>
 * @date 2024-04-08 11:14:07
 */
public interface AssetReceiveAddressRepo {

    List<AssetReceiveAddress> list(AssetReceiveAddress request);

    AssetReceiveAddress getById(Long id);

    Long save(AssetReceiveAddress entity);

    void updateById(AssetReceiveAddress entity);

    void cancelDefault(String userId);

    void deleteByIds(List<Long> idList);

    AssetReceiveAddress getDefault(String userId);

    Boolean setDefault(Long id);

    List<Country> listCountryV1();
}

