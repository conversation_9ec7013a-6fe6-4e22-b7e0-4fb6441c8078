package com.mi.oa.asset.commons.config.domain.common.valobj;

import com.mi.oa.asset.common.enums.FieldPropType;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigDataRage;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/10 13:26
 * 资产调拨配置
 * 
 */

@Data
public class AllotConfig {

    /**
     * 配置Id
     */
    private Integer configId;

    /**
     * 业务线Id
     */
    private Integer businessLineId;

    /**
     * 字段编码
     */
    private AllotConfigField fieldCode;

    /**
     * 字段属性
     */
    private FieldPropType fieldProperty;

    /**
     * 数据范围
     */
    private AllotConfigDataRage dataRage;

    /**
     * 限定内容
     */
    private List<String> limitBizData;

    /**
     * 排序
     */
    private Integer sort;
}