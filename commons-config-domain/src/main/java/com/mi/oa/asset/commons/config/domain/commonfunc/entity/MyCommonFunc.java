package com.mi.oa.asset.commons.config.domain.commonfunc.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/01/13/11:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MyCommonFunc implements Serializable {
    /**
     * 主键 id
     */
    private Integer id;

    /**
     * 管理线编码 manage_line_code
     */
    private String manageLineCode;

    /**
     * 创建人用户名 create_user
     */
    private String createUser;
    /**
     * 创建人用户名 create_user_name
     */
    private String createUserName;

    /**
     * 我的功能列表（有序） resource_codes
     */
    private String resourceCodes;

    /**
     * 0-否 1-是  如果没添加个人功能，返回默认的管理功能列表 is_default
     */
    private Byte isDefault;
}