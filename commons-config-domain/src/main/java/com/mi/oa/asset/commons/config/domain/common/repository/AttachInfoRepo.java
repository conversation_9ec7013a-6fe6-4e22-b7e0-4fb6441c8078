package com.mi.oa.asset.commons.config.domain.common.repository;

import com.mi.oa.asset.commons.config.domain.common.valobj.AttachInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-26 14:50
 */
public interface AttachInfoRepo {
    /**
     * 保存附件
     *
     * @param attachInfoList
     */
    void batchSaveAttach(List<AttachInfo> attachInfoList);

    /**
     * 删除附件
     *
     * @param recordId
     * @param recordType
     */
    void deleteAttach(Integer recordId, String recordType);

    /**
     * 删除附件
     * @param recordId
     * @param recordTypes
     */
    void deleteAttach(Integer recordId, List<String> recordTypes);

    /**
     * 删除附件
     *
     * @param recordIds 集合
     * @param recordType
     */
    void batchDeleteAttach(List<Integer> recordIds, String recordType);

    /**
     * 删除附件
     * @param recordId
     * @param attachFileNames
     * @param recordType
     */
    void deleteAttach(Integer recordId, List<String> attachFileNames, String recordType);

    /**
     * 根据主键批量删除附件
     *
     * @param idList
     */
    void deleteById(List<Integer> idList);

    /**
     * 查询附件
     *
     * @param recordIds
     * @param recordType
     */
    List<AttachInfo> getAttachList(List<Integer> recordIds, String recordType);

    /**
     * 根据类型集合查询附件
     * @param recordId
     * @param recordTypes
     * @return
     */
    List<AttachInfo> listByTypes(Integer recordId, List<String> recordTypes);

    AttachInfo getTemplateAttach(String recordNo);
}
