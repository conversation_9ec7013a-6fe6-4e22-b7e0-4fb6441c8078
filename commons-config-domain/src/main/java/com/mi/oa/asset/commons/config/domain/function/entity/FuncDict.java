package com.mi.oa.asset.commons.config.domain.function.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/07 14:14
 */

@Data
public class FuncDict {

    private Integer id;

    private String code;

    private String parentCode;

    private Integer manageType;

    private String businessLine;

    private Integer valid;

    private Integer auth;

    /**
     * 创建人账号
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人账号
     */
    private String updateUser;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

    private String relatedCode;

}
