package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.enums.ColumnUseWay;
import com.mi.oa.asset.common.enums.FuncManageType;
import com.mi.oa.asset.commons.config.api.queryfield.FieldConfigInfo;
import com.mi.oa.asset.commons.config.api.queryfield.FieldConfigRes;
import com.mi.oa.asset.commons.config.domain.queryfield.entity.FieldConfig;
import com.mi.oa.asset.eam.mybatis.FuncColumnMapper;
import com.mi.oa.asset.eam.mybatis.FuncColumnPo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class QueryFieldAbilityTest {

    @InjectMocks
    private QueryFieldAbility queryFieldAbility;

    @Mock
    private FuncColumnMapper funcColumnMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getDefaultConfig_ShouldReturnConfig_WhenFound() {
        // 准备测试数据
        FieldConfig config = new FieldConfig();
        config.setManageLine("LINE1,LINE2");
        config.setFunId("FUN1");
        
        queryFieldAbility.setFieldConfigs(Collections.singletonList(config));

        // 执行测试
        FieldConfig result = queryFieldAbility.getDefaultConfig("LINE1", "FUN1");

        // 验证结果
        assertNotNull(result);
        assertEquals("LINE1,LINE2", result.getManageLine());
        assertEquals("FUN1", result.getFunId());
    }

    @Test
    void getDefaultConfig_ShouldReturnNull_WhenNotFound() {
        // 准备测试数据
        queryFieldAbility.setFieldConfigs(Collections.emptyList());

        // 执行测试
        FieldConfig result = queryFieldAbility.getDefaultConfig("LINE1", "FUN1");

        // 验证结果
        assertNull(result);
    }

    @Test
    void getDefaultConfig_ShouldReturnDefaultConfig_WhenSpecificConfigNotFound() {
        // 准备测试数据
        FieldConfig defaultConfig = new FieldConfig();
        defaultConfig.setFunId("FUN1");
        defaultConfig.setManageLine("DEFAULT_LINE");
        queryFieldAbility.setDefaultFieldConfigs(Collections.singletonList(defaultConfig));
        queryFieldAbility.setFieldConfigs(Collections.emptyList());

        // 执行测试
        FieldConfig result = queryFieldAbility.getDefaultConfig("LINE1", "FUN1");

        // 验证结果
        assertNotNull(result);
        assertEquals("FUN1", result.getFunId());
        assertEquals("DEFAULT_LINE", result.getManageLine());
    }

    @Test
    void switchLanguage_ShouldSwitchToEnglish() {
        // 准备测试数据
        FieldConfigRes configRes = new FieldConfigRes();
        List<FieldConfigInfo> fieldConfigs = Arrays.asList(
            createFieldConfigInfo("field1", "字段1"),
            createFieldConfigInfo("field2", "字段2")
        );
        configRes.setFieldConfig(fieldConfigs);

        FuncColumnPo columnPo1 = new FuncColumnPo();
        columnPo1.setCode("field1");
        columnPo1.setName("字段1");
        columnPo1.setEnCode("Field1");
        columnPo1.setManageType(FuncManageType.SYSTEM.getCode());
        columnPo1.setUseWay(ColumnUseWay.FUNC_COLUMN.getCode());

        when(funcColumnMapper.selectList(any())).thenReturn(Collections.singletonList(columnPo1));

        // 执行测试
        queryFieldAbility.switchLanguage(configRes, "FUNC_CODE", "MANAGE_LINE", "en-US");

        // 验证结果
        assertEquals("Field1", fieldConfigs.get(0).getDataValue());
    }

    @Test
    void switchLanguage_ShouldHandleNullConfigRes() {
        // 执行测试
        queryFieldAbility.switchLanguage(null, "FUNC_CODE", "MANAGE_LINE", "en-US");
        
        // 验证没有异常抛出
    }

    @Test
    void switchLanguage_ShouldHandleEmptyFieldConfigs() {
        // 准备测试数据
        FieldConfigRes configRes = new FieldConfigRes();
        configRes.setFieldConfig(Collections.emptyList());
        configRes.setExtra(Collections.emptyList());

        // 执行测试
        queryFieldAbility.switchLanguage(configRes, "FUNC_CODE", "MANAGE_LINE", "en-US");
        
        // 验证没有异常抛出
    }

    private FieldConfigInfo createFieldConfigInfo(String dataKey, String dataValue) {
        FieldConfigInfo info = new FieldConfigInfo();
        info.setDataKey(dataKey);
        info.setDataValue(dataValue);
        return info;
    }
}
