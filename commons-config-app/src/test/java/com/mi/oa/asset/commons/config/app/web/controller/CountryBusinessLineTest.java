package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineProvider;
import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineReq;
import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineRes;
import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxRes;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;

/**
 * CountryBusinessLineController单元测试类
 */
@ExtendWith(MockitoExtension.class)
class CountryBusinessLineTest {

    @InjectMocks
    private CountryBusinessLineController controller;

    @Mock
    private CountryBusinessLineProvider provider;

    @Test
    void byCountryCode() {
        String businessLine = "intel";
        // 执行测试
        Result<List<CountryBusinessLineRes>> result = controller.byBusinessLine(businessLine);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getByBusinessLine(businessLine);
    }

    @Test
    void detail() {
        Integer countryCurrencyId = 1;
        // 执行测试
        Result<CountryBusinessLineRes> result = controller.info(countryCurrencyId);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getById(countryCurrencyId);
    }



    @Test
    void list() {
        // 执行测试
        Result<List<CountryBusinessLineRes>> result = controller.list();
        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getCountryRegionList();
    }

    @Test
    void save() {
        // 准备数据
        CountryBusinessLineReq req = new CountryBusinessLineReq();
        req.setId(null);
        req.setCountryName("guojiaCS");
        req.setCountryCodeAlphaThree("GJC");


        // 执行测试
        Result<Integer> result = controller.edit(req);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).saveOrUpdate(req);
    }

//    @Test
//    void update() {
//        // 准备数据
//        CountryBusinessLineReq req = new CountryBusinessLineReq();
//        req.setId(5);
//        req.setCountryName("rn");
//        req.setCountryEnglishName("ern");
//        req.setCountryCodeAlphaThree("GJC");
//
//
//        // 执行测试
//        Result<Void> result = controller.edit(req);
//
//        // 验证结果
//        assertNotNull(result);
//        // Result类没有isSuccess方法，直接验证其他属性
//        assertEquals(0, result.getCode());
//
//        // 验证调用
//        verify(provider).saveOrUpdate(req);
//    }

    @Test
    void delete() {
        // 准备数据
        CountryBusinessLineReq req = new CountryBusinessLineReq();
        req.setId(null);

        // 执行测试
        Result<Void> result = controller.delete(req);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());
        // 验证调用
        verify(provider).removeByIds(Collections.singletonList(req.getId()));
    }

} 