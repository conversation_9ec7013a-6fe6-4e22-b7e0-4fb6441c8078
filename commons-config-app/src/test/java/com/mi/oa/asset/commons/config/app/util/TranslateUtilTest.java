package com.mi.oa.asset.commons.config.app.util;

import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class TranslateUtilTest {
    @Test
    void calculateFileMD5_normal() {
        String content = "test content";
        InputStream is = new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8));
        String md5 = TranslateUtil.calculateFileMD5(is);
        assertNotNull(md5);
        assertEquals(32, md5.length());
    }

    @Test
    void reload_and_data() {
        Map<String, Map<String, String>> map = new HashMap<>();
        Map<String, String> zh = new HashMap<>();
        zh.put("k1", "v1");
        map.put("zh", zh);
        TranslateUtil.reload(map);
        assertNotNull(TranslateUtil.data);
        assertEquals("v1", TranslateUtil.data.get("zh").get("k1"));
    }
} 