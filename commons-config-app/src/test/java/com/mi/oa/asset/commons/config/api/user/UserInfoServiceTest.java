package com.mi.oa.asset.commons.config.api.user;

import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleProvider;
import com.mi.oa.asset.commons.config.app.StartApplication;
import com.mi.oa.asset.commons.config.app.ability.WhitelistAbility;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = StartApplication.class)
class UserInfoServiceTest {

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private BusinessRoleProvider provider;

    @Resource
    private WhitelistAbility whitelistAbility;

    @Test
    void getUserInfoByUserName_test() {
        UserBaseInfoRes res = userInfoService.getUserInfoByUserName("minpan");
        System.out.println(JacksonUtils.bean2Json(res));
    }

    @Test
    void test() {
        Set<String> batchBusinessRoleWithUp = provider.getBusinessRoleWithUp("AM06", "adm_pub", "workplace_manager");
        System.out.println(batchBusinessRoleWithUp);
    }

    @Test
    void testAuthWorkbench() {
        whitelistAbility.getWorkbenchAuth("songming");
        System.out.println("getWorkbenchAuth success");
    }

    @Test
    void getMobileByUserName_Success() {
        UserMobileRes res = userInfoService.getMobileByUserName("minpan");
        System.out.println(JacksonUtils.bean2Json(res));
        assertNotNull(res);
        assertNotNull(res.getZoneCode());
        assertNotNull(res.getMobile());
    }

    @Test
    void getMobileByUserName_UserNotFound() {
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            userInfoService.getMobileByUserName("not_exist_user");
        });
        assertEquals(ErrorCodes.BAD_REQUEST, exception.getErrorCode());
    }

    @Test
    void getMobileByUserName_NullUserName() {
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            userInfoService.getMobileByUserName(null);
        });
        assertEquals(ErrorCodes.BAD_REQUEST, exception.getErrorCode());
    }

    @Test
    void getUserInfoByUid_Success() {
        // 先通过用户名获取 uid
        UserBaseInfoRes userInfo = userInfoService.getUserInfoByUserName("minpan");
        assertNotNull(userInfo);
        String uid = userInfo.getUid();
        assertNotNull(uid);
        
        // 使用获取到的 uid 测试 getUserInfoByUid 方法
        UserBaseInfoRes res = userInfoService.getUserInfoByUid(uid);
        assertNotNull(res);
        assertNotNull(res.getUid());
        assertNotNull(res.getUserName());
        assertNotNull(res.getDisplayName());
    }

    @Test
    void getUserInfoByUid_NotFound() {
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            userInfoService.getUserInfoByUid("");
        });
        assertEquals(ErrorCodes.BAD_REQUEST, exception.getErrorCode());
    }

    @Test
    void getUserInfoByUserName_Success() {
        UserBaseInfoRes res = userInfoService.getUserInfoByUserName("minpan");
        assertNotNull(res);
        assertNotNull(res.getUid());
        assertNotNull(res.getUserName());
        assertNotNull(res.getDisplayName());
    }

    @Test
    void getUserInfoByUserName_NotFound() {
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            userInfoService.getUserInfoByUserName("not_exist_user");
        });
        assertEquals(ErrorCodes.BAD_REQUEST, exception.getErrorCode());
    }

    @Test
    void getUsersByUserName_Success() {
        List<String> userNames = Arrays.asList("minpan", "songming");
        List<User> users = userInfoService.getUsersByUserName(userNames);
        assertNotNull(users);
        assertFalse(users.isEmpty());
        users.forEach(user -> {
            assertNotNull(user.getUid());
            assertNotNull(user.getUserName());
            assertNotNull(user.getDisplayName());
        });
    }

    @Test
    void getUsersByUserName_EmptyList() {
        List<User> users = userInfoService.getUsersByUserName(Arrays.asList());
        assertNotNull(users);
        assertTrue(users.isEmpty());
    }

    @Test
    void getUserByUserName_Success() {
        User user = userInfoService.getUserByUserName("minpan");
        assertNotNull(user);
        assertNotNull(user.getUid());
        assertNotNull(user.getUserName());
        assertNotNull(user.getDisplayName());
    }

    @Test
    void getUserByUserName_NotFound() {
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            userInfoService.getUserByUserName("not_exist_user");
        });
        assertEquals(ErrorCodes.BAD_REQUEST, exception.getErrorCode());
    }

    @Test
    void getUserByLikeUserName_Success() {
        List<UserBaseInfoRes> users = userInfoService.getUserByLikeUserName("min", "10");
        assertNotNull(users);
        assertFalse(users.isEmpty());
        users.forEach(user -> {
            assertNotNull(user.getUid());
            assertNotNull(user.getUserName());
            assertNotNull(user.getDisplayName());
        });
    }

    @Test
    void getUserByLikeUserName_EmptyResult() {
        List<UserBaseInfoRes> users = userInfoService.getUserByLikeUserName("not_exist_user", "10");
        assertNotNull(users);
        assertTrue(users.isEmpty());
    }

    @Test
    void fuzzySearchUserInfo_Success() {
        List<UserInfoRes> users = userInfoService.fuzzySearchUserInfo("min", "10");
        assertNotNull(users);
        assertFalse(users.isEmpty());
        users.forEach(user -> {
            assertNotNull(user.getUid());
            assertNotNull(user.getUserName());
            assertNotNull(user.getDisplayName());
        });
    }

    @Test
    void fuzzySearchUserInfo_EmptyResult() {
        List<UserInfoRes> users = userInfoService.fuzzySearchUserInfo("not_exist_user", "10");
        assertNotNull(users);
        assertTrue(users.isEmpty());
    }

    @Test
    void getUsersByUserNames_Success() {
        List<String> userNames = Arrays.asList("minpan", "songming");
        List<UserInfoRes> users = userInfoService.getUsersByUserNames(userNames);
        assertNotNull(users);
        assertFalse(users.isEmpty());
        users.forEach(user -> {
            assertNotNull(user.getUid());
            assertNotNull(user.getUserName());
            assertNotNull(user.getDisplayName());
        });
    }

    @Test
    void getUsersByUserNames_EmptyList() {
        List<UserInfoRes> users = userInfoService.getUsersByUserNames(Arrays.asList());
        assertNotNull(users);
        assertTrue(users.isEmpty());
    }

    @Test
    void getUserBaseInfosByUserNames_Success() {
        List<String> userNames = Arrays.asList("minpan", "songming");
        List<UserBaseInfoRes> users = userInfoService.getUserBaseInfosByUserNames(userNames);
        assertNotNull(users);
        assertFalse(users.isEmpty());
        users.forEach(user -> {
            assertNotNull(user.getUid());
            assertNotNull(user.getUserName());
            assertNotNull(user.getDisplayName());
        });
    }

    @Test
    void getUserBaseInfosByUserNames_EmptyList() {
        List<UserBaseInfoRes> users = userInfoService.getUserBaseInfosByUserNames(Arrays.asList());
        assertNotNull(users);
        assertTrue(users.isEmpty());
    }

    @Test
    void getAllotLikeUserInfo_Success() {
        List<UserInfoRes> users = userInfoService.getAllotLikeUserInfo("adm_pub", "minpan", "10");
        assertNotNull(users);
        assertFalse(users.isEmpty());
        users.forEach(user -> {
            assertNotNull(user.getUid());
            assertNotNull(user.getUserName());
            assertNotNull(user.getDisplayName());
        });
    }

    @Test
    void getAllotLikeUserInfo_EmptyResult() {
        List<UserInfoRes> users = userInfoService.getAllotLikeUserInfo("adm_pub", "not_exist_user", "10");
        assertNotNull(users);
        assertTrue(users.isEmpty());
    }

    @Test
    void getAllotLikeUserInfo_InvalidBusinessLine() {
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            userInfoService.getAllotLikeUserInfo("f_mobile", "min", "10");
        });
        assertEquals(ErrorCodes.INTERNAL_SERVER_ERROR, exception.getErrorCode());
        assertEquals("该业务线调入人属性未配置", exception.getMessage());
    }

    @Test
    void getEmpInfoByUserName_Success() {
        EmployeeInfoRes emp = userInfoService.getEmpInfoByUserName("minpan");
        assertNotNull(emp);
        assertNotNull(emp.getUserName());
        assertNotNull(emp.getDisplayName());
        assertNotNull(emp.getEmployeeId());
    }

    @Test
    void getEmpInfoByUserName_NotFound() {
        EmployeeInfoRes emp = userInfoService.getEmpInfoByUserName("not_exist_user");
        assertNull(emp);
    }
}
