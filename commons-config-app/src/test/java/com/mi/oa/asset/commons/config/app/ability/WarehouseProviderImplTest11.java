package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.warehouse.WarehouseRes;
import com.mi.oa.asset.commons.config.app.StartApplication;
import com.mi.oa.asset.commons.config.app.converter.AppWarehouseConverter;
import com.mi.oa.asset.commons.config.app.provider.WarehouseProviderImpl;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.Warehouse;
import com.mi.oa.asset.commons.config.domain.warehouse.repository.WarehouseRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verify;

@Slf4j
@SpringBootTest(classes = StartApplication.class)
@Disabled
@ExtendWith(MockitoExtension.class)
public class WarehouseProviderImplTest11 {


    @InjectMocks
    private WarehouseProviderImpl warehouseProvider;

    @Mock
    private WarehouseRepo warehouseRepoMock;

    @Mock
    private AppWarehouseConverter converterMock;



    @Test
    @DisplayName("当业务线为空字符串时应该抛出异常")
    void getByBusinessLine_WithEmptyBusinessLine_ShouldThrowException() {
        // 准备测试数据 - 空业务线
        String emptyBusinessLine = "";

        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            warehouseProvider.getByBusinessLine(emptyBusinessLine);
        });

        // 验证异常信息
        assertEquals(ErrorCodes.BAD_PARAMETER, exception.getErrorCode());
        assertEquals("业务线不能为空", exception.getMessage());

        // 验证方法调用 - 应该不会调用仓库和转换器
        verifyNoInteractions(warehouseRepoMock);
        verifyNoInteractions(converterMock);
    }

    @Test
    @DisplayName("当业务线为null时应该抛出异常")
    void getByBusinessLine_WithNullBusinessLine_ShouldThrowException() {
        // 准备测试数据 - null业务线
        String nullBusinessLine = null;

        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            warehouseProvider.getByBusinessLine(nullBusinessLine);
        });

        // 验证异常信息
        assertEquals(ErrorCodes.BAD_PARAMETER, exception.getErrorCode());
        assertEquals("业务线不能为空", exception.getMessage());

        // 验证方法调用 - 应该不会调用仓库和转换器
        verifyNoInteractions(warehouseRepoMock);
        verifyNoInteractions(converterMock);
    }

    @Test
    @DisplayName("当业务线有效且存在匹配仓库时应该返回仓库列表")
    void getByBusinessLine_WithValidBusinessLine_ShouldReturnWarehouseList() {
        // 准备测试数据
        String businessLine = "car";

        // 模拟领域对象
        Warehouse warehouse1 = new Warehouse();
        warehouse1.setId(1);
        warehouse1.setHouseCode("WH001");
        warehouse1.setHouseName("仓库1");
        warehouse1.setBusinessLine(BusinessLine.CAR);

        Warehouse warehouse2 = new Warehouse();
        warehouse2.setId(2);
        warehouse2.setHouseCode("WH002");
        warehouse2.setHouseName("仓库2");
        warehouse2.setBusinessLine(BusinessLine.CAR);

        List<Warehouse> warehouseList = Arrays.asList(warehouse1, warehouse2);

        // 模拟API响应对象
        WarehouseRes warehouseRes1 = new WarehouseRes();
        warehouseRes1.setId(1);
        warehouseRes1.setHouseCode("WH001");
        warehouseRes1.setHouseName("仓库1");
        warehouseRes1.setBusinessLine(businessLine);

        WarehouseRes warehouseRes2 = new WarehouseRes();
        warehouseRes2.setId(2);
        warehouseRes2.setHouseCode("WH002");
        warehouseRes2.setHouseName("仓库2");
        warehouseRes2.setBusinessLine(businessLine);

        List<WarehouseRes> expectedWarehouseResList = Arrays.asList(warehouseRes1, warehouseRes2);

        // 模拟仓库和转换器行为
        when(warehouseRepoMock.getByBusinessLine(businessLine)).thenReturn(warehouseList);
        when(converterMock.toWarehouseResList(warehouseList)).thenReturn(expectedWarehouseResList);

        // 执行测试
        List<WarehouseRes> result = warehouseProvider.getByBusinessLine(businessLine);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(expectedWarehouseResList, result);

        // 验证方法调用
        verify(warehouseRepoMock).getByBusinessLine(businessLine);
        verify(converterMock).toWarehouseResList(warehouseList);
    }

    @Test
    @DisplayName("当业务线有效但无匹配仓库时应该返回空列表")
    void getByBusinessLine_WithValidBusinessLineButNoWarehousesExist_ShouldReturnEmptyList() {
        // 准备测试数据
        String businessLine = "car";
        List<Warehouse> emptyWarehouseList = Collections.emptyList();
        List<WarehouseRes> emptyWarehouseResList = Collections.emptyList();

        // 模拟仓库和转换器行为
        when(warehouseRepoMock.getByBusinessLine(businessLine)).thenReturn(emptyWarehouseList);
        when(converterMock.toWarehouseResList(emptyWarehouseList)).thenReturn(emptyWarehouseResList);

        // 执行测试
        List<WarehouseRes> result = warehouseProvider.getByBusinessLine(businessLine);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(warehouseRepoMock).getByBusinessLine(businessLine);
        verify(converterMock).toWarehouseResList(emptyWarehouseList);
    }
}
