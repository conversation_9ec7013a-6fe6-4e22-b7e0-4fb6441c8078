package com.mi.oa.asset.commons.config.app.provider;

import com.mi.info.infra.moon.dto.ClientSingleResponse;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.menuresource.*;
import com.mi.oa.asset.commons.config.app.converter.MenuResourceConverter;
import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.domain.common.entity.ManageLineDo;
import com.mi.oa.asset.commons.config.domain.common.repository.BusinessLineRepo;
import com.mi.oa.asset.commons.config.domain.common.repository.ManageLineRepo;
import com.mi.oa.asset.commons.config.infra.rpc.role.SysRoleClient;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.newauth.autoconfig.authority.AuthorityProperties;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.AccountAuthorityResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.ResourceTreeResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.RoleNewResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.remote.AuthorityClientService;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.remote.ResourceService;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.remote.RoleService;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.mi.oa.infra.uc.common.enmu.AccountTypeEnum;
import com.xiaomi.mit.api.error.ErrorCodeException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MenuResourceServiceImplTest {

    @InjectMocks
    private MenuResourceServiceImpl menuResourceService;

    @Mock
    private AuthorityProperties authorityProperties;

    @Mock
    private ResourceService resourceService;

    @Mock
    private MenuResourceConverter resourceConverter;

    @Mock
    private AuthorityClientService authorityClientService;

    @Mock
    private RoleService roleService;

    @Mock
    private BusinessLineRepo businessLineRepo;

    @Mock
    private ManageLineRepo manageLineRepo;

    @Mock
    private SysRoleClient roleClient;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private ValueOperations<String, Object> valueOperations;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getMenuFunctionResource_WhenAdminSource_ShouldReturnMenuFunctionList() {
        // 准备测试数据
        String userName = "testUser";
        String appCode = "testApp";
        String accountType = "1";
        Integer isFilterShow = 1;
        String eamSource = "EAM_ADMIN";

        // 模拟 resourceService.queryMenuFunction 返回值
        List<ResourceTreeResp> resourceTreeList = new ArrayList<>();
        ResourceTreeResp resourceTree = new ResourceTreeResp();
        resourceTree.setResourceCode("testResource");
        resourceTree.setResourceName("测试资源");
        resourceTreeList.add(resourceTree);

        ClientSingleResponse<List<ResourceTreeResp>> response = new ClientSingleResponse<>();
        response.setCode(String.valueOf(BaseResp.CODE_SUCCESS));
        response.setData(resourceTreeList);
        when(resourceService.queryMenuFunction(eq(appCode), eq(userName), eq("EXTERNAL"))).thenReturn(response);

        // 模拟 resourceConverter 转换结果
        List<MenuRes> menuResList = new ArrayList<>();
        MenuRes menuRes = new MenuRes();
        menuRes.setResourceCode("testResource");
        menuRes.setResourceName("测试资源");
        menuRes.setIsShow(true);
        menuResList.add(menuRes);
        when(resourceConverter.resourceTreeListToResList(anyList(), eq("zh-CN"))).thenReturn(menuResList);

        // 模拟 authorityClientService.queryAccountAuth 返回值
        AccountAuthorityResp authorityResp = new AccountAuthorityResp();
        // 确保角色列表包含非员工角色，且不包含 CLIENT_USER_ROLE
        List<String> roleCodeList = Arrays.asList("ADMIN_ROLE", "MANAGER_ROLE");
        authorityResp.setRoleCodeList(roleCodeList);
        Map<String, List<String>> dimensionResourceCodeMap = new HashMap<>();
        dimensionResourceCodeMap.put("MANAGE_LINE", Arrays.asList("group_asset"));
        dimensionResourceCodeMap.put("BUSINESS_LINE", Arrays.asList("business_line_1"));
        authorityResp.setDimensionResourceCodeMap(dimensionResourceCodeMap);

        BaseResp<AccountAuthorityResp> accountAuthorityResp = new BaseResp<>();
        accountAuthorityResp.setCode(BaseResp.CODE_SUCCESS);
        accountAuthorityResp.setData(authorityResp);
        when(authorityClientService.queryAccountAuth(eq(userName), eq("EXTERNAL"), eq(appCode))).thenReturn(accountAuthorityResp);

        // 模拟 roleService.queryRoleLoadResource 返回值
        RoleNewResp roleNewResp = new RoleNewResp();
        roleNewResp.setMenuResourceCodeList(Arrays.asList("testResource"));
        roleNewResp.setFunctionResourceCodeList(Arrays.asList("testFunction"));
        Map<String, List<String>> dimensionDataResourceCodeMapList = new HashMap<>();
        dimensionDataResourceCodeMapList.put("MANAGE_LINE", Arrays.asList("group_asset"));
        roleNewResp.setDimensionDataResourceCodeMapList(dimensionDataResourceCodeMapList);
        
        BaseResp<RoleNewResp> roleResp = new BaseResp<>();
        roleResp.setCode(BaseResp.CODE_SUCCESS);
        roleResp.setData(roleNewResp);
        when(roleService.queryRoleLoadResource(eq(appCode), eq("ADMIN_ROLE"))).thenReturn(roleResp);
        when(roleService.queryRoleLoadResource(eq(appCode), eq("MANAGER_ROLE"))).thenReturn(roleResp);

        // 模拟 manageLineRepo.getManageLine 返回值
        List<ManageLineDo> manageLineList = new ArrayList<>();
        ManageLineDo manageLine = new ManageLineDo();
        manageLine.setManageLine("group_asset");
        manageLine.setManageLineName("集团资产");
        manageLineList.add(manageLine);
        when(manageLineRepo.getManageLine()).thenReturn(manageLineList);

        // 模拟 businessLineRepo.searchAll 返回值
        List<BusinessLineDo> businessLineList = new ArrayList<>();
        BusinessLineDo businessLine = new BusinessLineDo();
        businessLine.setBusinessLine("business_line_1");
        businessLine.setBusinessLineName("业务线1");
        businessLine.setManageLineCode("group_asset");
        businessLine.setIsNewLine("1");
        businessLineList.add(businessLine);
        when(businessLineRepo.searchAll()).thenReturn(businessLineList);

        try (MockedStatic<RedisUtils> redisUtilsMockedStatic = mockStatic(RedisUtils.class)) {
            // 模拟 RedisUtils.get 返回值
            redisUtilsMockedStatic.when(() -> RedisUtils.get(anyString())).thenReturn(null);
            // 模拟 RedisUtils.setEx 行为
            redisUtilsMockedStatic.when(() -> RedisUtils.setEx(anyString(), anyString(), anyLong(), any(TimeUnit.class)))
                    .thenAnswer(invocation -> null);

            // 执行测试
            List<MenuFunctionRes> result = menuResourceService.getMenuFunctionResource(userName, appCode, Integer.parseInt(accountType), isFilterShow, eamSource, EAMConstants.CHINESE);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.isEmpty());
            assertEquals(1, result.size());
            MenuFunctionRes menuFunctionRes = result.get(0);
            assertEquals("group_asset", menuFunctionRes.getId());
            assertEquals("集团资产", menuFunctionRes.getName());
            assertNotNull(menuFunctionRes.getChildren());
            assertFalse(menuFunctionRes.getChildren().isEmpty());
            assertEquals("testResource", menuFunctionRes.getChildren().get(0).getResourceCode());
            assertEquals("测试资源", menuFunctionRes.getChildren().get(0).getResourceName());
            assertTrue(menuFunctionRes.getChildren().get(0).getIsShow());

            // 验证方法调用
            verify(resourceService).queryMenuFunction(eq(appCode), eq(userName), eq("EXTERNAL"));
            verify(resourceConverter).resourceTreeListToResList(eq(resourceTreeList), eq("zh-CN"));
            verify(authorityClientService).queryAccountAuth(eq(userName), eq("EXTERNAL"), eq(appCode));
            verify(roleService).queryRoleLoadResource(eq(appCode), eq("ADMIN_ROLE"));
            verify(roleService).queryRoleLoadResource(eq(appCode), eq("MANAGER_ROLE"));
            verify(manageLineRepo).getManageLine();
            verify(businessLineRepo).searchAll();
        }
    }

    @Test
    void getMenuFunctionResource_WhenClientSource_ShouldReturnMenuFunctionList() {
        try (MockedStatic<RedisUtils> redisUtils = Mockito.mockStatic(RedisUtils.class)) {
            // 准备测试数据
            String userName = "testUser";
            String appCode = "testApp";
            String accountType = "1";
            Integer isFilterShow = 1;
            String eamSource = "EAM_CLIENT";

            // 模拟 resourceService.queryMenuFunction 返回值
            List<ResourceTreeResp> resourceTreeList = new ArrayList<>();
            ResourceTreeResp resourceTree = new ResourceTreeResp();
            resourceTree.setResourceCode("TEST_MENU");
            resourceTree.setResourceName("测试菜单");
            resourceTreeList.add(resourceTree);
            
            ClientSingleResponse<List<ResourceTreeResp>> clientResponse = new ClientSingleResponse<>();
            clientResponse.setCode(String.valueOf(BaseResp.CODE_SUCCESS));
            clientResponse.setData(resourceTreeList);
            when(resourceService.queryMenuFunction(eq(appCode), eq(userName), eq("EXTERNAL"))).thenReturn(clientResponse);

            // 模拟 resourceConverter 转换结果
            List<MenuRes> menuResList = new ArrayList<>();
            MenuRes menuRes = new MenuRes();
            menuRes.setResourceCode("TEST_MENU");
            menuRes.setResourceName("测试菜单");
            menuRes.setIsShow(true);
            menuResList.add(menuRes);
            when(resourceConverter.resourceTreeListToResList(anyList(), eq("zh-CN"))).thenReturn(menuResList);

            // 模拟 authorityClientService.queryAccountAuth 返回值
            AccountAuthorityResp accountAuthorityResp = new AccountAuthorityResp();
            accountAuthorityResp.setRoleCodeList(Arrays.asList("CLIENT_USER_ROLE"));
            Map<String, List<String>> dimensionResourceCodeMap = new HashMap<>();
            dimensionResourceCodeMap.put("MANAGE_LINE", Arrays.asList("MANAGE_LINE_1"));
            dimensionResourceCodeMap.put("BUSINESS_LINE", Arrays.asList("BUSINESS_LINE_1"));
            accountAuthorityResp.setDimensionResourceCodeMap(dimensionResourceCodeMap);
            
            BaseResp<AccountAuthorityResp> baseResp = new BaseResp<>();
            baseResp.setCode(BaseResp.CODE_SUCCESS);
            baseResp.setData(accountAuthorityResp);
            when(authorityClientService.queryAccountAuth(eq(userName), eq("EXTERNAL"), eq(appCode))).thenReturn(baseResp);

            // 模拟 roleService.queryRoleLoadResource 返回值
            RoleNewResp roleNewResp = new RoleNewResp();
            roleNewResp.setMenuResourceCodeList(Arrays.asList("TEST_MENU"));
            roleNewResp.setFunctionResourceCodeList(Arrays.asList("testFunction"));
            Map<String, List<String>> dimensionDataResourceCodeMapList = new HashMap<>();
            dimensionDataResourceCodeMapList.put("MANAGE_LINE", Arrays.asList("MANAGE_LINE_1"));
            roleNewResp.setDimensionDataResourceCodeMapList(dimensionDataResourceCodeMapList);
            
            BaseResp<RoleNewResp> roleResp = new BaseResp<>();
            roleResp.setCode(BaseResp.CODE_SUCCESS);
            roleResp.setData(roleNewResp);
            when(roleService.queryRoleLoadResource(eq(appCode), eq("CLIENT_USER_ROLE"))).thenReturn(roleResp);

            // 模拟 manageLineRepo.getManageLine 返回值
            List<ManageLineDo> manageLineList = new ArrayList<>();
            ManageLineDo manageLine = new ManageLineDo();
            manageLine.setManageLine("MANAGE_LINE_1");
            manageLine.setManageLineName("测试管理线");
            manageLineList.add(manageLine);
            when(manageLineRepo.getManageLine()).thenReturn(manageLineList);

            // 模拟 businessLineRepo.searchAll 返回值
            List<BusinessLineDo> businessLineList = new ArrayList<>();
            BusinessLineDo businessLine = new BusinessLineDo();
            businessLine.setBusinessLine("BUSINESS_LINE_1");
            businessLine.setBusinessLineName("测试业务线");
            businessLine.setManageLineCode("MANAGE_LINE_1");
            businessLine.setIsNewLine("1");
            businessLineList.add(businessLine);
            when(businessLineRepo.searchAll()).thenReturn(businessLineList);

            // 模拟 RedisUtils.get 返回值
            redisUtils.when(() -> RedisUtils.get(anyString())).thenReturn(null);
            // 模拟 RedisUtils.setEx 行为
            redisUtils.when(() -> RedisUtils.setEx(anyString(), anyString(), anyLong(), any(TimeUnit.class)))
                    .thenAnswer(invocation -> null);

            // 执行测试
            List<MenuFunctionRes> result = menuResourceService.getMenuFunctionResource(userName, appCode, Integer.parseInt(accountType), isFilterShow, eamSource, EAMConstants.CHINESE);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.isEmpty());
            assertEquals(1, result.size());
            MenuFunctionRes menuFunctionRes = result.get(0);
            assertEquals("MANAGE_LINE_1", menuFunctionRes.getId());
            assertEquals("测试管理线", menuFunctionRes.getName());
            assertNotNull(menuFunctionRes.getChildren());
            assertFalse(menuFunctionRes.getChildren().isEmpty());
            assertEquals("TEST_MENU", menuFunctionRes.getChildren().get(0).getResourceCode());
            assertEquals("测试菜单", menuFunctionRes.getChildren().get(0).getResourceName());
            assertTrue(menuFunctionRes.getChildren().get(0).getIsShow());

            // 验证方法调用
            verify(resourceService).queryMenuFunction(eq(appCode), eq(userName), eq("EXTERNAL"));
            verify(resourceConverter).resourceTreeListToResList(eq(resourceTreeList), eq("zh-CN"));
            verify(authorityClientService).queryAccountAuth(eq(userName), eq("EXTERNAL"), eq(appCode));
            verify(roleService).queryRoleLoadResource(eq(appCode), eq("CLIENT_USER_ROLE"));
            verify(manageLineRepo).getManageLine();
            verify(businessLineRepo).searchAll();
        }
    }

    @Test
    void getMenuFunctionResource_WhenNoMenuResource_ShouldThrowException() {
        try (MockedStatic<RedisUtils> redisUtils = Mockito.mockStatic(RedisUtils.class)) {
            // 准备测试数据
            String userName = "testUser";
            String appCode = "testApp";
            String accountType = "1";
            Integer isFilterShow = 1;
            String eamSource = "EAM_ADMIN";

            // 模拟 resourceService.queryMenuFunction 返回值
            ClientSingleResponse<List<ResourceTreeResp>> clientResponse = new ClientSingleResponse<>();
            clientResponse.setCode(String.valueOf(BaseResp.CODE_SUCCESS));
            clientResponse.setData(Collections.emptyList());
            when(resourceService.queryMenuFunction(eq(appCode), eq(userName), eq("EXTERNAL"))).thenReturn(clientResponse);

            // 模拟 RedisUtils.get 返回值
            redisUtils.when(() -> RedisUtils.get(anyString())).thenReturn(null);

            // 执行测试并验证异常
            Exception exception = assertThrows(ErrorCodeException.class, () -> 
                menuResourceService.getMenuFunctionResource(userName, appCode, Integer.parseInt(accountType), isFilterShow, eamSource, "")
            );
            assertEquals("请先申请权限", exception.getMessage());

            // 验证方法调用
            verify(resourceService).queryMenuFunction(eq(appCode), eq(userName), eq("EXTERNAL"));
        }
    }
}
