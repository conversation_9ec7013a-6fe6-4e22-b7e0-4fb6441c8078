package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.common.DelByIdsReq;
import com.mi.oa.asset.commons.config.api.myfunctions.*;
import com.mi.oa.asset.commons.config.app.ability.CommonFuncAbility;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class MyCommonFuncControllerTest {

    @InjectMocks
    private MyCommonFuncController myCommonFuncController;

    @Mock
    private CommonFuncAbility commonFuncAbility;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSave() {
        // 准备测试数据
        SaveFunctionConfigReq req = new SaveFunctionConfigReq();
        
        // 执行测试
        Result<Void> result = myCommonFuncController.save(req);
        
        // 验证结果
        assertNotNull(result);
        verify(commonFuncAbility, times(1)).submit(req);
    }

    @Test
    void testList() {
        // 准备测试数据
        String userName = "testUser";
        String country = "CHN";
        String language = "ZH-CN";

        List<FunctionConfigRes> expectedResponse = new ArrayList<>();
        
        when(commonFuncAbility.functionConfig(userName, country, language)).thenReturn(expectedResponse);
        
        // 执行测试
        Result<List<FunctionConfigRes>> result = myCommonFuncController.list(userName, country, language);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResponse, result.getData());
        verify(commonFuncAbility, times(1)).functionConfig(userName, country, language);
    }

    @Test
    void testListByManageLine() {
        // 准备测试数据
        String manageLine = "testLine";
        List<FunctionConfigRes> expectedResponse = new ArrayList<>();
        
        when(commonFuncAbility.functionConfigByManageLine(manageLine)).thenReturn(expectedResponse);
        
        // 执行测试
        Result<List<FunctionConfigRes>> result = myCommonFuncController.listByManageLine(manageLine);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResponse, result.getData());
        verify(commonFuncAbility, times(1)).functionConfigByManageLine(manageLine);
    }

    @Test
    void testDelete() {
        // 准备测试数据
        DelByIdsReq req = new DelByIdsReq();
        req.setIds(Arrays.asList(1, 2));
        
        // 执行测试
        Result<Void> result = myCommonFuncController.delete(req);
        
        // 验证结果
        assertNotNull(result);
        verify(commonFuncAbility, times(1)).deleteFunctionConfig(req);
    }

    @Test
    void testPersonalConfig() {
        // 准备测试数据
        List<SaveFunctionConfigUserReq> configUserReq = new ArrayList<>();
        SaveFunctionConfigUserReq req = new SaveFunctionConfigUserReq();
        configUserReq.add(req);
        
        // 执行测试
        Result<Void> result = myCommonFuncController.personalConfig(configUserReq);
        
        // 验证结果
        assertNotNull(result);
        verify(commonFuncAbility, times(1)).personalConfig(configUserReq);
    }
}
