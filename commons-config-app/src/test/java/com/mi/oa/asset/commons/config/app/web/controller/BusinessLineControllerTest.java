package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.businessline.*;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class BusinessLineControllerTest {

    @InjectMocks
    private BusinessLineController businessLineController;

    @Mock
    private BusinessLineProvider businessLineProvider;

    private MockHttpServletRequest request;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        request = new MockHttpServletRequest();
        Field requestField = BusinessLineController.class.getDeclaredField("request");
        requestField.setAccessible(true);
        requestField.set(businessLineController, request);
    }

    @Test
    void getBusinessLineList_ShouldReturnSuccess() {
        // 准备测试数据
        BusinessLineRes businessLine1 = new BusinessLineRes();
        BusinessLineRes businessLine2 = new BusinessLineRes();
        List<BusinessLineRes> expectedList = Arrays.asList(businessLine1, businessLine2);
        
        // Mock请求头
        request.addHeader("eam-language", EAMConstants.CHINESE);
        // Mock provider返回值
        when(businessLineProvider.getBusinessLineList(EAMConstants.CHINESE)).thenReturn(expectedList);

        // 执行测试
        Result<List<BusinessLineRes>> result = businessLineController.getBusinessLineList();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccessful());
        assertNotNull(result.getData());
        assertEquals(expectedList.size(), result.getData().size());
        assertEquals(expectedList, result.getData());
        verify(businessLineProvider, times(1)).getBusinessLineList(EAMConstants.CHINESE);
    }

    @Test
    void getBusinessLineList_WithEnglishLanguage_ShouldReturnSuccess() {
        // 准备测试数据
        BusinessLineRes businessLine1 = new BusinessLineRes();
        BusinessLineRes businessLine2 = new BusinessLineRes();
        List<BusinessLineRes> expectedList = Arrays.asList(businessLine1, businessLine2);
        
        // Mock请求头
        request.addHeader("eam-language", "en-US");
        // Mock provider返回值
        when(businessLineProvider.getBusinessLineList("en-US")).thenReturn(expectedList);

        // 执行测试
        Result<List<BusinessLineRes>> result = businessLineController.getBusinessLineList();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccessful());
        assertNotNull(result.getData());
        assertEquals(expectedList.size(), result.getData().size());
        assertEquals(expectedList, result.getData());
        verify(businessLineProvider, times(1)).getBusinessLineList("en-US");
    }

    @Test
    void getManageLineList_ShouldReturnSuccess() {
        // 准备测试数据
        ManageLineRes manageLine1 = new ManageLineRes();
        ManageLineRes manageLine2 = new ManageLineRes();
        List<ManageLineRes> expectedList = Arrays.asList(manageLine1, manageLine2);
        
        // Mock请求头
        request.addHeader("eam-language", EAMConstants.CHINESE);
        // Mock provider返回值
        when(businessLineProvider.getManageLineList(EAMConstants.CHINESE)).thenReturn(expectedList);

        // 执行测试
        Result<List<ManageLineRes>> result = businessLineController.getManageLineList();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccessful());
        assertNotNull(result.getData());
        assertEquals(expectedList.size(), result.getData().size());
        assertEquals(expectedList, result.getData());
        verify(businessLineProvider, times(1)).getManageLineList(EAMConstants.CHINESE);
    }

    @Test
    void getManageLineList_WithEnglishLanguage_ShouldReturnSuccess() {
        // 准备测试数据
        ManageLineRes manageLine1 = new ManageLineRes();
        ManageLineRes manageLine2 = new ManageLineRes();
        List<ManageLineRes> expectedList = Arrays.asList(manageLine1, manageLine2);
        
        // Mock请求头
        request.addHeader("eam-language", "en-US");
        // Mock provider返回值
        when(businessLineProvider.getManageLineList("en-US")).thenReturn(expectedList);

        // 执行测试
        Result<List<ManageLineRes>> result = businessLineController.getManageLineList();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccessful());
        assertNotNull(result.getData());
        assertEquals(expectedList.size(), result.getData().size());
        assertEquals(expectedList, result.getData());
        verify(businessLineProvider, times(1)).getManageLineList("en-US");
    }
}
