package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.labelprint.GetLabelTemplateDataReq;
import com.mi.oa.asset.commons.config.domain.labeltemplate.entity.LabelTemplate;
import com.mi.oa.asset.commons.config.domain.labeltemplate.enums.LabelDataSource;
import com.mi.oa.asset.commons.config.domain.labeltemplate.repository.LabelTemplateRepo;
import com.mi.oa.asset.eam.dto.BaseRes;
import com.mi.oa.asset.eam.jxs.req.BaseQueryReq;
import com.mi.oa.asset.eam.jxs.res.ListDataRes;
import com.mi.oa.asset.eam.jxs.service.EamJxsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LabelTemplateAbilityTest {

    @InjectMocks
    private LabelTemplateAbility labelTemplateAbility;

    @Mock
    private LabelTemplateRepo labelTemplateRepo;

    @Mock
    private EamJxsService eamJxsService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void batchGetTemplateData_WhenStockSerial_ShouldReturnData() {
        // 准备测试数据
        GetLabelTemplateDataReq req = new GetLabelTemplateDataReq();
        req.setTemplateId(1);
        req.setDataSource(LabelDataSource.STOCK_SERIAL.getCode());
        req.setIds(Arrays.asList("id1", "id2"));

        LabelTemplate template = LabelTemplate.builder()
                .id(1)
                .businessLine("testBusinessLine")
                .build();

        ListDataRes<Map<String, String>> listDataRes = new ListDataRes<>();
        listDataRes.setList(Arrays.asList(
            new HashMap<String, String>() {{ put("id", "1"); put("name", "test1"); }},
            new HashMap<String, String>() {{ put("id", "2"); put("name", "test2"); }}
        ));

        BaseRes<ListDataRes<Map<String, String>>> baseRes = new BaseRes<>();
        baseRes.setData(listDataRes);

        // 配置mock行为
        when(labelTemplateRepo.findById(1)).thenReturn(template);
        when(eamJxsService.baseQuery(any(BaseQueryReq.class))).thenReturn(baseRes);

        // 执行测试
        List<?> result = labelTemplateAbility.batchGetTemplateData(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(labelTemplateRepo).findById(1);
        verify(eamJxsService).baseQuery(any(BaseQueryReq.class));
    }

    @Test
    void batchGetTemplateData_WhenAssetAccount_ShouldReturnData() {
        // 准备测试数据
        GetLabelTemplateDataReq req = new GetLabelTemplateDataReq();
        req.setTemplateId(1);
        req.setDataSource(LabelDataSource.ASSET_ACCOUNT.getCode());
        req.setIds(Arrays.asList("id1", "id2"));

        LabelTemplate template = LabelTemplate.builder()
                .id(1)
                .businessLine("testBusinessLine")
                .build();

        ListDataRes<Map<String, String>> listDataRes = new ListDataRes<>();
        listDataRes.setList(Arrays.asList(
            new HashMap<String, String>() {{ put("id", "1"); put("name", "test1"); }},
            new HashMap<String, String>() {{ put("id", "2"); put("name", "test2"); }}
        ));

        BaseRes<ListDataRes<Map<String, String>>> baseRes = new BaseRes<>();
        baseRes.setData(listDataRes);

        // 配置mock行为
        when(labelTemplateRepo.findById(1)).thenReturn(template);
        when(eamJxsService.baseQuery(any(BaseQueryReq.class))).thenReturn(baseRes);

        // 执行测试
        List<?> result = labelTemplateAbility.batchGetTemplateData(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(labelTemplateRepo).findById(1);
        verify(eamJxsService).baseQuery(any(BaseQueryReq.class));
    }

    @Test
    void batchGetTemplateData_WhenInvalidDataSource_ShouldReturnEmptyList() {
        // 准备测试数据
        GetLabelTemplateDataReq req = new GetLabelTemplateDataReq();
        req.setTemplateId(1);
        req.setDataSource("INVALID_SOURCE");
        req.setIds(Arrays.asList("id1", "id2"));

        LabelTemplate template = LabelTemplate.builder()
                .id(1)
                .businessLine("testBusinessLine")
                .build();

        // 配置mock行为
        when(labelTemplateRepo.findById(1)).thenReturn(template);

        // 执行测试
        List<?> result = labelTemplateAbility.batchGetTemplateData(req);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(labelTemplateRepo).findById(1);
        verify(eamJxsService, never()).baseQuery(any(BaseQueryReq.class));
    }
}
