package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgProvider;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.CustomShareList;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRecord;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.CustomShareListRepo;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.ShareRecordRepo;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.mi.oa.asset.eam.jxs.req.BaseQueryReq;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AssetShareExportAbilityTest {

    @InjectMocks
    private AssetShareExportAbility assetShareExportAbility;

    @Mock
    private AssetShareAbility assetShareAbility;

    @Mock
    private ShareRecordRepo shareRecordRepo;

    @Mock
    private CustomShareListRepo shareListRepo;

    @Mock
    private AssetOrgProvider assetOrgProvider;

    @Mock
    private UserInfoService userInfoService;

    private BaseQueryReq queryReq;
    private List<Map<String, String>> data;
    private User mockUser;

    @BeforeEach
    void setUp() {
        queryReq = new BaseQueryReq();
        data = new ArrayList<>();
        Map<String, String> headMap = new HashMap<>();
        queryReq.setHeadMap(headMap);
        
        mockUser = new User();
        mockUser.setUserName("testUser");
        mockUser.setDeptCode("testDept");
    }

    @Test
    void handle_EmptyData_NoProcessing() {
        // 准备数据
        List<Map<String, String>> emptyData = new ArrayList<>();
        queryReq.setBusinessType(CommonConstant.ASSET_SHARE_FUN);

        // 执行测试
        assetShareExportAbility.handle(emptyData, queryReq);

        // 验证结果
        verify(shareRecordRepo, never()).queryList(any(), anyString(), anyList(), anyString(), anyString());
        verify(assetShareAbility, never()).handleShareQuantity(any(), any());
    }

    @Test
    void handle_NonAssetShareBusinessType_NoProcessing() {
        // 准备数据
        data.add(new HashMap<>());
        queryReq.setBusinessType("OTHER_TYPE");

        // 执行测试
        assetShareExportAbility.handle(data, queryReq);

        // 验证结果
        verify(shareRecordRepo, never()).queryList(any(), anyString(), anyList(), anyString(), anyString());
        verify(assetShareAbility, never()).handleShareQuantity(any(), any());
    }

    @Test
    void handle_AssetShareWithUserName_Success() {
        // 准备数据
        data.add(new HashMap<>());
        queryReq.setBusinessType(CommonConstant.ASSET_SHARE_FUN);
        queryReq.setUserName("testUser");
        queryReq.setEamSource("testSource");

        List<String> orgCodes = Arrays.asList("org1", "org2");
        List<ShareRecord> shareRecords = Arrays.asList(new ShareRecord());
        List<CustomShareList> customShareLists = Arrays.asList(new CustomShareList());

        // Mock行为
        when(userInfoService.getUserByUserName("testUser")).thenReturn(mockUser);
        when(assetOrgProvider.getParentCodes("testDept")).thenReturn(orgCodes);
        when(shareRecordRepo.queryList(null, "testUser", orgCodes, "", "testSource")).thenReturn(shareRecords);
        when(shareListRepo.getByShareIds(anyList())).thenReturn(customShareLists);

        // 执行测试
        assetShareExportAbility.handle(data, queryReq);

        // 验证结果
        verify(assetShareAbility).handleShareQuantity(data, customShareLists);
        assertEquals("共享数量", queryReq.getHeadMap().get(CommonConstant.SHARE_QUANTITY_CONSTANT));
    }

    @Test
    void handle_AssetShareWithoutUserName_Success() {
        // 准备数据
        data.add(new HashMap<>());
        queryReq.setBusinessType(CommonConstant.ASSET_SHARE_FUN);
        queryReq.setEamSource("testSource");

        List<String> orgCodes = Arrays.asList("org1", "org2");
        List<ShareRecord> shareRecords = Arrays.asList(new ShareRecord());
        List<CustomShareList> customShareLists = Arrays.asList(new CustomShareList());

        // Mock行为
        try (MockedStatic<AuthFacade> authFacadeMockedStatic = mockStatic(AuthFacade.class)) {
            authFacadeMockedStatic.when(AuthFacade::authedUserName).thenReturn("testUser");
            when(userInfoService.getUserByUserName("testUser")).thenReturn(mockUser);
            when(assetOrgProvider.getParentCodes("testDept")).thenReturn(orgCodes);
            when(shareRecordRepo.queryList(null, "testUser", orgCodes, "", "testSource")).thenReturn(shareRecords);
            when(shareListRepo.getByShareIds(anyList())).thenReturn(customShareLists);

            // 执行测试
            assetShareExportAbility.handle(data, queryReq);

            // 验证结果
            verify(assetShareAbility).handleShareQuantity(data, customShareLists);
            assertEquals("共享数量", queryReq.getHeadMap().get(CommonConstant.SHARE_QUANTITY_CONSTANT));
        }
    }
}
