package com.mi.oa.asset.commons.config.app.scheduler;

import com.mi.oa.asset.commons.config.app.converter.AssetOrgConverter;
import com.mi.oa.asset.commons.config.domain.assetorganization.repository.AssetOrgRepo;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgStructure;
import com.mi.oa.asset.commons.config.infra.common.CacheKey;
import com.mi.oa.asset.eam.feign.mdm.MdmClient;
import com.mi.oa.asset.eam.feign.mdm.req.MdmBaseReq;
import com.mi.oa.asset.eam.feign.mdm.res.MdmBaseRes;
import com.mi.oa.asset.eam.feign.mdm.res.MdmDepartmentRes;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SyncDepartment单元测试类
 * 重点测试cacheAssetOrgStructureFullName方法的各种场景
 */
@ExtendWith(MockitoExtension.class)
class SyncDepartmentTest {

    @InjectMocks
    private SyncDepartment syncDepartment;

    @Mock
    private MdmClient mdmClient;

    @Mock
    private AssetOrgRepo assetOrgRepo;

    @Mock
    private AssetOrgConverter converter;

    @Mock
    private SyncDepartment self;

    /**
     * 测试cacheAssetOrgStructureFullName - 一级部门（无父级）
     */
    @Test
    @DisplayName("测试缓存一级部门全称 - 无父级部门")
    void cacheAssetOrgStructureFullName_LevelOneDepartment_Success() {
        // 准备数据 - 一级部门
        List<AssetOrgStructure> levelOneData = Arrays.asList(
            createAssetOrgStructure("DEPT001", "技术部", "Technology Department", null, 1)
        );

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            // 执行测试
            syncDepartment.cacheAssetOrgStructureFullName(levelOneData);

            // 验证Redis调用
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("DEPT001"), "技术部"), 
                times(1)
            );
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("DEPT001"), "Technology Department"), 
                times(1)
            );
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_BY_NAME.genKey("技术部"), "Technology Department"), 
                times(1)
            );
        }
    }

    /**
     * 测试cacheAssetOrgStructureFullName - 二级部门（有父级）
     */
    @Test
    @DisplayName("测试缓存二级部门全称 - 有父级部门")
    void cacheAssetOrgStructureFullName_LevelTwoDepartment_Success() {
        // 准备数据 - 二级部门
        List<AssetOrgStructure> levelTwoData = Arrays.asList(
            createAssetOrgStructure("DEPT002", "研发部", "R&D Department", "DEPT001", 2)
        );

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            // 模拟父级部门的全称
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("DEPT001")))
                .thenReturn("技术部");
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("DEPT001")))
                .thenReturn("Technology Department");

            // 执行测试
            syncDepartment.cacheAssetOrgStructureFullName(levelTwoData);

            // 验证Redis调用
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("DEPT002"), "技术部-研发部"), 
                times(1)
            );
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("DEPT002"), "Technology Department-R&D Department"), 
                times(1)
            );
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_BY_NAME.genKey("技术部-研发部"), "Technology Department-R&D Department"), 
                times(1)
            );
        }
    }

    /**
     * 测试cacheAssetOrgStructureFullName - 英文名称为空的情况
     */
    @Test
    @DisplayName("测试缓存部门全称 - 英文名称为空时使用中文名称")
    void cacheAssetOrgStructureFullName_EmptyEnglishName_Success() {
        // 准备数据 - 英文名称为空
        List<AssetOrgStructure> data = Arrays.asList(
            createAssetOrgStructure("DEPT001", "技术部", null, null, 1)
        );

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            // 执行测试
            syncDepartment.cacheAssetOrgStructureFullName(data);

            // 验证Redis调用 - 英文名称应该使用中文名称
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("DEPT001"), "技术部"), 
                times(1)
            );
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_BY_NAME.genKey("技术部"), "技术部"), 
                times(1)
            );
        }
    }

    /**
     * 测试cacheAssetOrgStructureFullName - 父级英文名称为空的情况
     */
    @Test
    @DisplayName("测试缓存部门全称 - 父级英文名称为空时跳过英文全称拼接")
    void cacheAssetOrgStructureFullName_ParentEnglishNameEmpty_Success() {
        // 准备数据 - 二级部门
        List<AssetOrgStructure> levelTwoData = Arrays.asList(
            createAssetOrgStructure("DEPT002", "研发部", "R&D Department", "DEPT001", 2)
        );

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            // 模拟父级部门的中文全称存在，但英文全称为空
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("DEPT001")))
                .thenReturn("技术部");
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("DEPT001")))
                .thenReturn(null);

            // 执行测试
            syncDepartment.cacheAssetOrgStructureFullName(levelTwoData);

            // 验证Redis调用 - 英文全称应该只使用当前部门的英文名称
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("DEPT002"), "R&D Department"), 
                times(1)
            );
        }
    }

    /**
     * 测试cacheAssetOrgStructureFullName - 空数据列表
     */
    @Test
    @DisplayName("测试缓存部门全称 - 空数据列表")
    void cacheAssetOrgStructureFullName_EmptyDataList_Success() {
        // 准备空数据
        List<AssetOrgStructure> emptyData = Collections.emptyList();

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            // 执行测试
            syncDepartment.cacheAssetOrgStructureFullName(emptyData);

            // 验证Redis没有被调用
            redisUtilsMock.verifyNoInteractions();
        }
    }

    /**
     * 测试cacheAssetOrgStructureFullName - 多个部门混合层级
     */
    @Test
    @DisplayName("测试缓存部门全称 - 多个部门混合层级")
    void cacheAssetOrgStructureFullName_MultipleDepartmentsMixedLevels_Success() {
        // 准备数据 - 混合层级
        List<AssetOrgStructure> mixedData = Arrays.asList(
            createAssetOrgStructure("DEPT001", "技术部", "Technology Department", null, 1),
            createAssetOrgStructure("DEPT002", "研发部", "R&D Department", "DEPT001", 2),
            createAssetOrgStructure("DEPT003", "测试部", "Test Department", "DEPT001", 2)
        );

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            // 模拟父级部门的全称
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("DEPT001")))
                .thenReturn("技术部");
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("DEPT001")))
                .thenReturn("Technology Department");

            // 执行测试
            syncDepartment.cacheAssetOrgStructureFullName(mixedData);

            // 验证一级部门
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("DEPT001"), "技术部"), 
                times(1)
            );
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("DEPT001"), "Technology Department"), 
                times(1)
            );

            // 验证二级部门
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("DEPT002"), "技术部-研发部"), 
                times(1)
            );
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("DEPT002"), "Technology Department-R&D Department"), 
                times(1)
            );
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("DEPT003"), "技术部-测试部"), 
                times(1)
            );
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("DEPT003"), "Technology Department-Test Department"), 
                times(1)
            );
        }
    }

    /**
     * 测试cacheAssetOrgStructureFullName - 三级部门（多级嵌套）
     */
    @Test
    @DisplayName("测试缓存部门全称 - 三级部门多级嵌套")
    void cacheAssetOrgStructureFullName_LevelThreeDepartment_Success() {
        // 准备数据 - 三级部门
        List<AssetOrgStructure> levelThreeData = Arrays.asList(
            createAssetOrgStructure("DEPT003", "前端组", "Frontend Team", "DEPT002", 3)
        );

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            // 模拟父级部门的全称
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("DEPT002")))
                .thenReturn("技术部-研发部");
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("DEPT002")))
                .thenReturn("Technology Department-R&D Department");

            // 执行测试
            syncDepartment.cacheAssetOrgStructureFullName(levelThreeData);

            // 验证Redis调用
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("DEPT003"), "技术部-研发部-前端组"), 
                times(1)
            );
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("DEPT003"), "Technology Department-R&D Department-Frontend Team"), 
                times(1)
            );
        }
    }

    /**
     * 测试cacheAssetOrgStructureByLevel - 正常流程
     */
    @Test
    @DisplayName("测试按层级缓存部门结构 - 正常流程")
    void cacheAssetOrgStructureByLevel_NormalFlow_Success() {
        // 准备数据
        List<AssetOrgStructure> levelOneData = Arrays.asList(
            createAssetOrgStructure("DEPT001", "技术部", "Technology Department", null, 1)
        );
        List<AssetOrgStructure> levelTwoData = Arrays.asList(
            createAssetOrgStructure("DEPT002", "研发部", "R&D Department", "DEPT001", 2)
        );

        // 模拟Repository调用
        when(assetOrgRepo.getOrgStructuresByLevel(1)).thenReturn(levelOneData);
        when(assetOrgRepo.getOrgStructuresByLevel(2)).thenReturn(levelTwoData);
        when(assetOrgRepo.getOrgStructuresByLevel(3)).thenReturn(Collections.emptyList());

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            // 执行测试
            syncDepartment.cacheAssetOrgStructureByLevel();

            // 验证Repository调用
            verify(assetOrgRepo, times(1)).getOrgStructuresByLevel(1);
            verify(assetOrgRepo, times(1)).getOrgStructuresByLevel(2);
            verify(assetOrgRepo, times(1)).getOrgStructuresByLevel(3);

            // 验证Redis调用 - 设置最大层级
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_LEVEL_MAX.getKey(), 2), 
                times(1)
            );
        }
    }

    /**
     * 测试cacheAssetOrgStructureByLevel - 只有一级部门
     */
    @Test
    @DisplayName("测试按层级缓存部门结构 - 只有一级部门")
    void cacheAssetOrgStructureByLevel_OnlyLevelOne_Success() {
        // 准备数据
        List<AssetOrgStructure> levelOneData = Arrays.asList(
            createAssetOrgStructure("DEPT001", "技术部", "Technology Department", null, 1)
        );

        // 模拟Repository调用
        when(assetOrgRepo.getOrgStructuresByLevel(1)).thenReturn(levelOneData);
        when(assetOrgRepo.getOrgStructuresByLevel(2)).thenReturn(Collections.emptyList());

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            // 执行测试
            syncDepartment.cacheAssetOrgStructureByLevel();

            // 验证Repository调用
            verify(assetOrgRepo, times(1)).getOrgStructuresByLevel(1);
            verify(assetOrgRepo, times(1)).getOrgStructuresByLevel(2);

            // 验证Redis调用 - 最大层级应该是1
            redisUtilsMock.verify(() -> 
                RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_LEVEL_MAX.getKey(), 1), 
                times(1)
            );
        }
    }

    /**
     * 测试evictLevelDataCache - 清除缓存
     */
    @Test
    @DisplayName("测试清除层级数据缓存")
    void evictLevelDataCache_Success() {
        // 执行测试
        syncDepartment.evictLevelDataCache(1);

        // 验证方法正常执行（主要是日志输出）
        // 由于使用了@OACacheEvict注解，实际的缓存清除由框架处理
    }

    /**
     * 测试execute - 正常同步流程
     */
    @Test
    @DisplayName("测试部门同步 - 正常流程")
    void execute_NormalFlow_Success() {
        // 准备数据
        MdmBaseRes<MdmDepartmentRes> mockResponse = mock(MdmBaseRes.class);
        List<MdmDepartmentRes> mockDepartments = Arrays.asList(
            createMockDepartment("DEPT001", "技术部", "Technology Department", "PARENT001", true),
            createMockDepartment("DEPT002", "研发部", "R&D Department", "DEPT001", true),
            createMockDepartment("DEPT003", "测试部", "Test Department", "DEPT001", false)
        );
        
        when(mockResponse.getList()).thenReturn(mockDepartments);
        when(mockResponse.getTotal()).thenReturn(3);
        when(mdmClient.getDepartment(any(MdmBaseReq.class))).thenReturn(mockResponse);
        
        // 模拟已存在的部门
        List<AssetOrgStructure> existingStructures = Arrays.asList(
            createAssetOrgStructure("DEPT001", "技术部", "Technology Department", null, 1)
        );
        when(assetOrgRepo.getOrgStructuresByCodes(anyList())).thenReturn(existingStructures);
        
        // 模拟转换器
        when(converter.createAssetOrgStructure(anyList())).thenReturn(Collections.emptyList());
        when(converter.updateAssetOrgStructure(anyList(), anyList())).thenReturn(Collections.emptyList());

        // 执行测试
        syncDepartment.execute();

        // 验证调用
        verify(mdmClient, times(1)).getDepartment(any(MdmBaseReq.class));
        verify(assetOrgRepo, times(1)).inactivateOrgStructure(anyList());
        verify(assetOrgRepo, times(1)).createOrgStructures(anyList());
        verify(assetOrgRepo, times(1)).updateOrgStructures(anyList());
        verify(self, times(1)).cacheAssetOrgStructureByLevel();
    }

    // 辅助方法：创建模拟的MdmDepartmentRes
    private MdmDepartmentRes createMockDepartment(String deptCode, String deptName, String deptNameEn, String parentDeptCode, boolean isActive) {
        MdmDepartmentRes department = mock(MdmDepartmentRes.class);
        lenient().when(department.getDeptCode()).thenReturn(deptCode);
        lenient().when(department.getDeptName()).thenReturn(deptName);
        lenient().when(department.getDeptNameEn()).thenReturn(deptNameEn);
        lenient().when(department.getParentDeptCode()).thenReturn(parentDeptCode);
        lenient().when(department.isActive()).thenReturn(isActive);
        return department;
    }

    // 辅助方法：创建AssetOrgStructure
    private AssetOrgStructure createAssetOrgStructure(String orgCode, String orgName, String orgNameEn, String parentCode, Integer level) {
        return AssetOrgStructure.builder()
            .orgCode(orgCode)
            .orgName(orgName)
            .orgNameEn(orgNameEn)
            .parentCode(parentCode)
            .level(level)
            .build();
    }
}
