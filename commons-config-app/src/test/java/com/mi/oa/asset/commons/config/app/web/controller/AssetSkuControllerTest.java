package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.assetsku.AssetSkuMgRes;
import com.mi.oa.asset.commons.config.api.assetsku.AssetSkuProvider;
import com.mi.oa.asset.commons.config.api.assetsku.AssetSkuRes;
import com.mi.oa.asset.commons.config.api.assetsku.DelAssetSkuReq;
import com.mi.oa.asset.commons.config.app.ability.AssetSkuAbility;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * AssetSkuController单元测试类
 */
@ExtendWith(MockitoExtension.class)
class AssetSkuControllerTest {

    @InjectMocks
    private AssetSkuController controller;

    @Mock
    private AssetSkuProvider provider;

    @Mock
    private AssetSkuAbility assetSkuAbility;

    /**
     * 测试delete方法 - 正常场景
     * 删除SKU，验证是否正确调用provider
     */
    @Test
    void delete_ShouldCallProviderAndReturnSuccess() {
        // 准备数据
        DelAssetSkuReq req = new DelAssetSkuReq();
        req.setIds(Arrays.asList(1, 2));
        req.setBusinessLine("OA");

        // 设置Mock行为
        doNothing().when(provider).deleteAssetSku(any(DelAssetSkuReq.class));

        // 执行测试
        Result<Void> result = controller.delete(req);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).deleteAssetSku(eq(req));
    }

    /**
     * 测试list方法 - 有分类ID场景
     * 查询SKU列表，验证是否正确调用provider
     */
//    @Test
//    void list_WithCateId_ShouldCallProviderAndReturnData() {
//        // 准备数据
//        Integer cateId = 1;
//        String businessLine = "OA";
//        String keyword = "test";
//        Integer pageNum = 1;
//        Integer pageSize = 10;
//        Boolean disabled = false;
//
//        PageData<AssetSkuRes> pageData = mock(PageData.class);
//        when(pageData.getPageNum()).thenReturn(pageNum);
//        when(pageData.getPageSize()).thenReturn(pageSize);
////        when(pageData.getTotal()).thenReturn(0);
//        when(pageData.getList()).thenReturn(new ArrayList<>());
//
//        // 设置Mock行为
//        when(provider.getAssetSkuPageData(
//                eq(cateId),
//                eq(Arrays.asList(businessLine)),
//                eq(keyword),
//                eq(pageNum),
//                eq(pageSize),
//                eq(disabled),
//                eq(true)
//        )).thenReturn(pageData);
//
//        // 执行测试
//        Result<PageData<AssetSkuRes>> result = controller.list(cateId, businessLine, keyword, pageNum, pageSize, disabled);
//
//        // 验证结果
//        assertNotNull(result);
//        // Result类没有isSuccess方法，直接验证其他属性
//        assertEquals(0, result.getCode());
//        assertNotNull(result.getData());
//        assertEquals(pageNum, result.getData().getPageNum());
//        assertEquals(pageSize, result.getData().getPageSize());
//
//        // 验证调用
//        verify(provider).getAssetSkuPageData(
//                eq(cateId),
//                eq(Arrays.asList(businessLine)),
//                eq(keyword),
//                eq(pageNum),
//                eq(pageSize),
//                eq(disabled),
//                eq(true)
//        );
//    }

    /**
     * 测试list方法 - 无分类ID场景
     * 查询SKU列表，验证是否正确调用provider
     */
//    @Test
//    void list_WithoutCateId_ShouldCallProviderAndReturnData() {
//        // 准备数据
//        Integer cateId = null;
//        String businessLine = "OA";
//        String keyword = "test";
//        Integer pageNum = 1;
//        Integer pageSize = 10;
//        Boolean disabled = false;
//
//        PageData<AssetSkuRes> pageData = mock(PageData.class);
//        when(pageData.getPageNum()).thenReturn(pageNum);
//        when(pageData.getPageSize()).thenReturn(pageSize);
////        when(pageData.getTotal()).thenReturn(0);
//        when(pageData.getList()).thenReturn(new ArrayList<>());
//
//        // 设置Mock行为
//        when(provider.getAssetSkuPageData(
//                eq(Arrays.asList(businessLine)),
//                eq(keyword),
//                eq(pageNum),
//                eq(pageSize),
//                eq(disabled),
//                eq(true)
//        )).thenReturn(pageData);
//
//        // 执行测试
//        Result<PageData<AssetSkuRes>> result = controller.list(cateId, businessLine, keyword, pageNum, pageSize, disabled);
//
//        // 验证结果
//        assertNotNull(result);
//        // Result类没有isSuccess方法，直接验证其他属性
//        assertEquals(0, result.getCode());
//        assertNotNull(result.getData());
//        assertEquals(pageNum, result.getData().getPageNum());
//        assertEquals(pageSize, result.getData().getPageSize());
//
//        // 验证调用
//        verify(provider).getAssetSkuPageData(
//                eq(Arrays.asList(businessLine)),
//                eq(keyword),
//                eq(pageNum),
//                eq(pageSize),
//                eq(disabled),
//                eq(true)
//        );
//    }

    /**
     * 测试list方法 - 空业务线场景
     * 查询SKU列表，验证是否正确调用provider
     */
//    @Test
//    void list_WithEmptyBusinessLine_ShouldCallProviderWithEmptyList() {
//        // 准备数据
//        Integer cateId = null;
//        String businessLine = "";
//        String keyword = "test";
//        Integer pageNum = 1;
//        Integer pageSize = 10;
//        Boolean disabled = false;
//
//        PageData<AssetSkuRes> pageData = mock(PageData.class);
//        when(pageData.getPageNum()).thenReturn(pageNum);
//        when(pageData.getPageSize()).thenReturn(pageSize);
////        when(pageData.getTotal()).thenReturn(0);
//        when(pageData.getList()).thenReturn(new ArrayList<>());
//
//        // 设置Mock行为
//        when(provider.getAssetSkuPageData(
//                eq(Collections.emptyList()),
//                eq(keyword),
//                eq(pageNum),
//                eq(pageSize),
//                eq(disabled),
//                eq(true)
//        )).thenReturn(pageData);
//
//        // 执行测试
//        Result<PageData<AssetSkuRes>> result = controller.list(cateId, businessLine, keyword, pageNum, pageSize, disabled);
//
//        // 验证结果
//        assertNotNull(result);
//        // Result类没有isSuccess方法，直接验证其他属性
//        assertEquals(0, result.getCode());
//        assertNotNull(result.getData());
//
//        // 验证调用
//        verify(provider).getAssetSkuPageData(
//                eq(Collections.emptyList()),
//                eq(keyword),
//                eq(pageNum),
//                eq(pageSize),
//                eq(disabled),
//                eq(true)
//        );
//    }

    /**
     * 测试detail方法 - 有业务线场景
     * 查询SKU详情，验证是否正确调用provider并过滤业务线
     */
    @Test
    void detail_WithBusinessLine_ShouldFilterByBusinessLine() {
        // 准备数据
        Integer skuId = 1;
        String businessLine = "OA";

        // 使用mock创建AssetSkuRes实例，避免使用构造函数
        AssetSkuRes assetSkuRes = mock(AssetSkuRes.class);
        when(assetSkuRes.getSkuId()).thenReturn(skuId);
        
        List<AssetSkuMgRes> manageList = new ArrayList<>();
        
        AssetSkuMgRes mgRes1 = new AssetSkuMgRes();
        mgRes1.setBusinessLine("OA");
        manageList.add(mgRes1);
        
        AssetSkuMgRes mgRes2 = new AssetSkuMgRes();
        mgRes2.setBusinessLine("IOT");
        manageList.add(mgRes2);
        
        when(assetSkuRes.getManages()).thenReturn(manageList);

        // 设置Mock行为
        when(provider.getAssetSku(eq(skuId))).thenReturn(assetSkuRes);

        // 执行测试
        Result<AssetSkuRes> result = controller.detail(skuId, businessLine);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(skuId, result.getData().getSkuId());
        assertEquals(1, result.getData().getManages().size());
        assertEquals("OA", result.getData().getManages().get(0).getBusinessLine());

        // 验证调用
        verify(provider).getAssetSku(eq(skuId));
    }

    /**
     * 测试detail方法 - 无业务线场景
     * 查询SKU详情，验证是否正确调用provider并保留所有业务线
     */
    @Test
    void detail_WithoutBusinessLine_ShouldRemoveAllManages() {
        // 准备数据
        Integer skuId = 1;
        String businessLine = null;

        // 使用mock创建AssetSkuRes实例，避免使用构造函数
        AssetSkuRes assetSkuRes = mock(AssetSkuRes.class);
        when(assetSkuRes.getSkuId()).thenReturn(skuId);
        
        List<AssetSkuMgRes> manageList = new ArrayList<>();
        
        AssetSkuMgRes mgRes1 = new AssetSkuMgRes();
        mgRes1.setBusinessLine("OA");
        manageList.add(mgRes1);
        
        AssetSkuMgRes mgRes2 = new AssetSkuMgRes();
        mgRes2.setBusinessLine("IOT");
        manageList.add(mgRes2);
        
        when(assetSkuRes.getManages()).thenReturn(manageList);

        // 设置Mock行为
        when(provider.getAssetSku(eq(skuId))).thenReturn(assetSkuRes);

        // 执行测试
        Result<AssetSkuRes> result = controller.detail(skuId, businessLine);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(skuId, result.getData().getSkuId());
        assertEquals(0, result.getData().getManages().size());

        // 验证调用
        verify(provider).getAssetSku(eq(skuId));
    }

    /**
     * 测试detail方法 - 多业务线场景
     * 查询SKU详情，验证是否正确调用provider并过滤多个业务线
     */
    @Test
    void detail_WithMultipleBusinessLines_ShouldFilterByBusinessLines() {
        // 准备数据
        Integer skuId = 1;
        String businessLine = "OA,IOT";

        // 使用mock创建AssetSkuRes实例，避免使用构造函数
        AssetSkuRes assetSkuRes = mock(AssetSkuRes.class);
        when(assetSkuRes.getSkuId()).thenReturn(skuId);
        
        List<AssetSkuMgRes> manageList = new ArrayList<>();
        
        AssetSkuMgRes mgRes1 = new AssetSkuMgRes();
        mgRes1.setBusinessLine("OA");
        manageList.add(mgRes1);
        
        AssetSkuMgRes mgRes2 = new AssetSkuMgRes();
        mgRes2.setBusinessLine("IOT");
        manageList.add(mgRes2);
        
        AssetSkuMgRes mgRes3 = new AssetSkuMgRes();
        mgRes3.setBusinessLine("OTHER");
        manageList.add(mgRes3);
        
        when(assetSkuRes.getManages()).thenReturn(manageList);

        // 设置Mock行为
        when(provider.getAssetSku(eq(skuId))).thenReturn(assetSkuRes);

        // 执行测试
        Result<AssetSkuRes> result = controller.detail(skuId, businessLine);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertEquals(skuId, result.getData().getSkuId());
        assertEquals(2, result.getData().getManages().size());
        
        List<String> resultBusinessLines = new ArrayList<>();
        for (AssetSkuMgRes mgRes : result.getData().getManages()) {
            resultBusinessLines.add(mgRes.getBusinessLine());
        }
        assertTrue(resultBusinessLines.contains("OA"));
        assertTrue(resultBusinessLines.contains("IOT"));
        assertFalse(resultBusinessLines.contains("OTHER"));

        // 验证调用
        verify(provider).getAssetSku(eq(skuId));
    }
} 