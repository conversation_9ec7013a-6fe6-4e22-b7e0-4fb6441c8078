package com.mi.oa.asset.commons.config.app.ability;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.common.enums.ColumnUseWay;
import com.mi.oa.asset.commons.config.domain.function.entity.ExcelColumnModel;
import com.mi.oa.asset.commons.config.domain.function.repository.FunColumnRepo;
import com.mi.oa.asset.eam.mybatis.FuncColumnMapper;
import com.mi.oa.asset.eam.mybatis.FuncColumnPo;
import com.mi.oa.asset.eam.mybatis.FunctionMapper;
import com.mi.oa.asset.eam.mybatis.FunctionPo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TranslateColumnAbilityTest {

    @InjectMocks
    private TranslateColumnAbility translateColumnAbility;

    @Mock
    private FuncColumnMapper funcColumnMapper;

    @Mock
    private FunColumnRepo funColumnRepo;

    @Mock
    private FunctionMapper functionMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void translateColumn_ShouldUpdateColumnNames() throws IOException {
        // 准备测试数据
        List<ExcelColumnModel> excelData = new ArrayList<>();
        ExcelColumnModel model1 = new ExcelColumnModel();
        model1.setKey("table1.column1");
        model1.setEnName("column1_en");
        excelData.add(model1);

        List<FunctionPo> functionPos = new ArrayList<>();
        FunctionPo functionPo = new FunctionPo();
        functionPo.setCode("func1");
        functionPo.setTableName("table1");
        functionPos.add(functionPo);

        List<FuncColumnPo> tableColumns = new ArrayList<>();
        FuncColumnPo tableColumn = new FuncColumnPo();
        tableColumn.setFuncCode("func1");
        tableColumn.setCode("column1");
        tableColumn.setUseWay(ColumnUseWay.TABLE.getCode());
        tableColumns.add(tableColumn);

        List<FuncColumnPo> funcColumns = new ArrayList<>();
        FuncColumnPo funcColumn = new FuncColumnPo();
        funcColumn.setFuncCode("func1");
        funcColumn.setCode("column2");
        funcColumn.setUseWay(ColumnUseWay.FUNC_COLUMN.getCode());
        funcColumns.add(funcColumn);

        List<FuncColumnPo> formColumns = new ArrayList<>();
        FuncColumnPo formColumn = new FuncColumnPo();
        formColumn.setFuncCode("func1");
        formColumn.setCode("column3");
        formColumn.setUseWay(ColumnUseWay.FUNC_FORM.getCode());
        formColumns.add(formColumn);

        // Mock Excel文件内容
        String excelContent = "key,enName\ntable1.column1,column1_en";
        MultipartFile file = new MockMultipartFile(
            "test.xlsx",
            "test.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            excelContent.getBytes()
        );

        // Mock依赖行为
        when(functionMapper.selectList(any())).thenReturn(functionPos);
        
        // 使用any()匹配器替代复杂的argThat
        when(funcColumnMapper.selectList(any())).thenReturn(tableColumns)
            .thenReturn(funcColumns)
            .thenReturn(formColumns);

        // 执行测试
        translateColumnAbility.translateColumn(file);

        // 验证结果
        verify(funColumnRepo, times(1)).batchUpdate(tableColumns);
        verify(funColumnRepo, times(1)).batchUpdate(funcColumns);
        verify(funColumnRepo, times(1)).batchUpdate(formColumns);
    }

    @Test
    void translateColumn_WithEmptyExcel_ShouldDoNothing() throws IOException {
        // 准备空的Excel文件（只包含表头）
        String emptyExcelContent = "key,enName";
        MultipartFile file = new MockMultipartFile(
            "empty.xlsx",
            "empty.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            emptyExcelContent.getBytes()
        );

        // 执行测试
        translateColumnAbility.translateColumn(file);

        // 验证没有进行任何更新操作
        verify(funColumnRepo, never()).batchUpdate(any());
    }
}
