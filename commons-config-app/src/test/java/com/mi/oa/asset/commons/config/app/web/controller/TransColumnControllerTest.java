package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.function.TranslateColumnProvider;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

class TransColumnControllerTest {

    @InjectMocks
    private TransColumnController transColumnController;

    @Mock
    private TranslateColumnProvider translateColumnProvider;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testColumnTranslation() {
        // 准备测试数据
        String content = "test content";
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            content.getBytes()
        );

        // 执行测试
        Result<Void> result = transColumnController.column(file);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        verify(translateColumnProvider, times(1)).translateColumn(file);
    }

    @Test
    void testDictTranslation() {
        // 准备测试数据
        String content = "test content";
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            content.getBytes()
        );

        // 执行测试
        Result<Void> result = transColumnController.transDict(file);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        verify(translateColumnProvider, times(1)).translateDict(file);
    }
}
