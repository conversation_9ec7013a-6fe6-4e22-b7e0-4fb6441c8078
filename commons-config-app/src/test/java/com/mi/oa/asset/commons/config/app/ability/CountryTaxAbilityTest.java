package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.domain.international.entity.CountryTaxDo;
import com.mi.oa.asset.commons.config.domain.international.enums.DefaultDataEnums;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryTaxRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CountryTaxAbilityTest {
    @InjectMocks
    private CountryTaxAbility ability;
    @Mock
    private CountryTaxRepo countryTaxRepo;

    @Test
    void check_duplicateTaxCombination() {
        CountryTaxDo entity = new CountryTaxDo();
        entity.setCountryConfigId(1);
        entity.setTaxCode("VAT");
        entity.setTaxRate(new BigDecimal("13.00"));
        entity.setId(null);
        CountryTaxDo exist = new CountryTaxDo();
        exist.setId(2);
        exist.setTaxCode("VAT");
        exist.setTaxRate(new BigDecimal("13.00"));
        List<CountryTaxDo> existList = Collections.singletonList(exist);
        when(countryTaxRepo.getByCountryId(1)).thenReturn(existList);
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.check(entity));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
        assertTrue(ex.getMessage().contains("税码和税率组合"));
    }

    @Test
    void check_duplicateDefaultTax() {
        CountryTaxDo entity = new CountryTaxDo();
        entity.setCountryConfigId(1);
        entity.setDefaultTaxRate(DefaultDataEnums.DEFAULTTAXRATE.getKey());
        entity.setId(null);
        CountryTaxDo exist = new CountryTaxDo();
        exist.setId(2);
        exist.setDefaultTaxRate(DefaultDataEnums.DEFAULTTAXRATE.getKey());
        exist.setTaxCode("VAT");
        exist.setTaxRate(new BigDecimal("6.00"));
        List<CountryTaxDo> existList = Collections.singletonList(exist);
        when(countryTaxRepo.getByCountryId(1)).thenReturn(existList);
        // 先通过taxCode+taxRate校验，再通过defaultTaxRate校验
        entity.setTaxCode("VAT2");
        entity.setTaxRate(new BigDecimal("7.00"));
        exist.setTaxCode("VAT3");
        exist.setTaxRate(new BigDecimal("8.00"));
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.check(entity));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
        assertTrue(ex.getMessage().contains("默认的税率"));
    }

    @Test
    void check_noConflict() {
        CountryTaxDo entity = new CountryTaxDo();
        entity.setCountryConfigId(1);
        entity.setTaxCode("VAT");
        entity.setTaxRate(new BigDecimal("13.00"));
        entity.setDefaultTaxRate(DefaultDataEnums.DEFAULTTAXRATE.getKey());
        entity.setId(1);
        CountryTaxDo exist = new CountryTaxDo();
        exist.setId(2);
        exist.setTaxCode("VAT2");
        exist.setTaxRate(new BigDecimal("6.00"));
        exist.setDefaultTaxRate(0);
        List<CountryTaxDo> existList = Collections.singletonList(exist);
        when(countryTaxRepo.getByCountryId(1)).thenReturn(existList);
        assertDoesNotThrow(() -> ability.check(entity));
    }

    @Test
    void check_emptyList() {
        CountryTaxDo entity = new CountryTaxDo();
        entity.setCountryConfigId(1);
        when(countryTaxRepo.getByCountryId(1)).thenReturn(Collections.emptyList());
        assertDoesNotThrow(() -> ability.check(entity));
    }
} 