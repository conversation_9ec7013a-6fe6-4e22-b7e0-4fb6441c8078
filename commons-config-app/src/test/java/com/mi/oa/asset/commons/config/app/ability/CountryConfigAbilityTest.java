package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineRes;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigReq;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigRes;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryBusinessLineDo;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryBusinessLineRepo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryConfigRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CountryConfigAbilityTest {
    @Spy
    @InjectMocks
    private CountryConfigAbility ability;
    @Mock
    private CountryConfigRepo countryConfigRepo;
    @Mock
    private CountryBusinessLineRepo countryBusinessLineRepo;
    @Mock
    private com.mi.oa.asset.commons.config.app.converter.CountryConfigConverter countryConfigConverter;
    @Mock
    private com.mi.oa.asset.commons.config.app.converter.CountryBusinessLineConverter countryBusinessLineConverter;

    @Test
    void getCountryConfigList_normal() {
        CountryConfigRes res = new CountryConfigRes();
        res.setId(1);
        List<CountryConfigRes> input = Collections.singletonList(res);
        CountryBusinessLineDo do1 = new CountryBusinessLineDo();
        do1.setCountryConfigId(1);
        do1.setBusinessLine("BL1");
        List<CountryBusinessLineDo> doList = Collections.singletonList(do1);
        CountryBusinessLineRes blRes = new CountryBusinessLineRes();
        blRes.setCountryConfigId(1);
        blRes.setBusinessLine("BL1");
        List<CountryBusinessLineRes> blResList = Collections.singletonList(blRes);
        when(countryBusinessLineRepo.getByCountryIds(anyList())).thenReturn(doList);
        when(countryBusinessLineConverter.listDoToRes(doList)).thenReturn(blResList);
        List<CountryConfigRes> result = ability.getCountryConfigList(input);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("BL1", result.get(0).getBusinessLineResList().get(0));
    }

    @Test
    void getCountryConfigList_emptyInput() {
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.getCountryConfigList(Collections.emptyList()));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
    }

    @Test
    void getById_normal() {
        CountryConfigRes res = new CountryConfigRes();
        res.setId(2);
        CountryBusinessLineDo do1 = new CountryBusinessLineDo();
        do1.setCountryConfigId(2);
        do1.setBusinessLine("BL2");
        List<CountryBusinessLineDo> doList = Collections.singletonList(do1);
        when(countryBusinessLineRepo.getByCountryId(2)).thenReturn(doList);
        CountryConfigRes result = ability.getById(res);
        assertNotNull(result);
        assertEquals("BL2", result.getBusinessLineResList().get(0));
    }

    @Test
    void saveOrUpdate_emptyBusinessLine() {
        CountryConfigReq req = new CountryConfigReq();
        req.setBusinessLineResList(Collections.emptyList());
        try (MockedStatic<CollectionUtils> utils = Mockito.mockStatic(CollectionUtils.class)) {
            utils.when(() -> CollectionUtils.isEmpty(anyList())).thenReturn(true);
            ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.saveOrUpdate(req));
            assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
        }
    }

    @Test
    void saveOrUpdate_duplicateData() {
        CountryConfigReq req = new CountryConfigReq();
        req.setBusinessLineResList(Arrays.asList("BL1"));
        CountryConfigDo entity = new CountryConfigDo();
        entity.setId(null);
        when(countryConfigConverter.reqToDo(req)).thenReturn(entity);
        doNothing().when(ability).check(entity);
        when(countryConfigRepo.getById(null)).thenReturn(new CountryConfigDo());
        try (MockedStatic<CollectionUtils> utils = Mockito.mockStatic(CollectionUtils.class)) {
            utils.when(() -> CollectionUtils.isEmpty(anyList())).thenReturn(false);
            ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.saveOrUpdate(req));
            assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
        }
    }

    @Test
    void saveOrUpdate_newCountry() {
        CountryConfigReq req = new CountryConfigReq();
        req.setBusinessLineResList(Arrays.asList("BL1", "BL2"));
        CountryConfigDo entity = new CountryConfigDo();
        entity.setId(null);
        when(countryConfigConverter.reqToDo(req)).thenReturn(entity);
        doNothing().when(ability).check(entity);
        when(countryConfigRepo.getById(null)).thenReturn(null);
        when(countryConfigRepo.save(entity)).thenReturn(100);
        doNothing().when(ability).saveCountryBusinessLineDo(entity, req.getBusinessLineResList(), 100);
        try (MockedStatic<CollectionUtils> utils = Mockito.mockStatic(CollectionUtils.class)) {
            utils.when(() -> CollectionUtils.isEmpty(anyList())).thenReturn(false);
            assertDoesNotThrow(() -> ability.saveOrUpdate(req));
            verify(ability).saveCountryBusinessLineDo(entity, req.getBusinessLineResList(), 100);
        }
    }

    @Test
    void saveOrUpdate_updateCountry_addAndRemove() {
        CountryConfigReq req = new CountryConfigReq();
        req.setBusinessLineResList(Arrays.asList("BL1", "BL2", "BL3"));
        CountryConfigDo entity = new CountryConfigDo();
        entity.setId(10);
        when(countryConfigConverter.reqToDo(req)).thenReturn(entity);
        doNothing().when(ability).check(entity);
        when(countryConfigRepo.updateById(entity)).thenReturn(1);
        CountryBusinessLineDo b1 = new CountryBusinessLineDo(); b1.setId(1); b1.setBusinessLine("BL1");
        CountryBusinessLineDo b2 = new CountryBusinessLineDo(); b2.setId(2); b2.setBusinessLine("BL2");
        List<CountryBusinessLineDo> alreadyBound = Arrays.asList(b1, b2);
        when(countryBusinessLineRepo.getByCountryId(10)).thenReturn(alreadyBound);
        List<String> bindBusinessLineList = Arrays.asList("BL1", "BL2");
        try (MockedStatic<CollectionUtils> utils = Mockito.mockStatic(CollectionUtils.class)) {
            utils.when(() -> CollectionUtils.isNotEmpty(alreadyBound)).thenReturn(true);
            utils.when(() -> CollectionUtils.isEqualCollection(bindBusinessLineList, req.getBusinessLineResList())).thenReturn(false);
            // addDifferenceBusinessLineList = [BL3], delDifferenceBusinessLineList = []
            doNothing().when(ability).saveCountryBusinessLineDo(entity, Arrays.asList("BL3"), 10);
            doNothing().when(ability).updateCountryBusinessLineDo(entity);
            assertDoesNotThrow(() -> ability.saveOrUpdate(req));
            verify(ability).saveCountryBusinessLineDo(entity, Arrays.asList("BL3"), 10);
        }
    }

    @Test
    void saveOrUpdate_updateCountry_deleteBusinessLine() {
        CountryConfigReq req = new CountryConfigReq();
        req.setBusinessLineResList(Arrays.asList("BL1"));
        CountryConfigDo entity = new CountryConfigDo();
        entity.setId(10);
        when(countryConfigConverter.reqToDo(req)).thenReturn(entity);
        doNothing().when(ability).check(entity);
        when(countryConfigRepo.updateById(entity)).thenReturn(1);
        CountryBusinessLineDo b1 = new CountryBusinessLineDo(); b1.setId(1); b1.setBusinessLine("BL1");
        CountryBusinessLineDo b2 = new CountryBusinessLineDo(); b2.setId(2); b2.setBusinessLine("BL2");
        List<CountryBusinessLineDo> alreadyBound = Arrays.asList(b1, b2);
        when(countryBusinessLineRepo.getByCountryId(10)).thenReturn(alreadyBound);
        List<String> bindBusinessLineList = Arrays.asList("BL1", "BL2");
        try (MockedStatic<CollectionUtils> utils = Mockito.mockStatic(CollectionUtils.class)) {
            utils.when(() -> CollectionUtils.isNotEmpty(alreadyBound)).thenReturn(true);
            utils.when(() -> CollectionUtils.isEqualCollection(bindBusinessLineList, req.getBusinessLineResList())).thenReturn(false);
            // addDifferenceBusinessLineList = [], delDifferenceBusinessLineList = [BL2]
            doNothing().when(ability).updateCountryBusinessLineDo(entity);
            doNothing().when(countryBusinessLineRepo).deleteByIds(Arrays.asList(2));
            assertDoesNotThrow(() -> ability.saveOrUpdate(req));
            verify(countryBusinessLineRepo).deleteByIds(Arrays.asList(2));
        }
    }

    @Test
    void check_duplicateAlphaThree() {
        CountryConfigDo entity = new CountryConfigDo();
        entity.setCountryCodeAlphaThree("CHN");
        entity.setId(null);
        CountryConfigDo exist = new CountryConfigDo();
        exist.setId(100);
        when(countryConfigRepo.getByCountryCodeAlphaThree("CHN")).thenReturn(exist);
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.check(entity));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
    }

    @Test
    void deleteByIds_normal() {
        doNothing().when(countryConfigRepo).deleteByIds(anyList());
        doNothing().when(countryBusinessLineRepo).deleteByCountryIds(anyList());
        assertDoesNotThrow(() -> ability.deleteByIds(Arrays.asList(1,2)));
    }

    @Test
    void saveCountryBusinessLineDo_normal() {
        CountryConfigDo entity = new CountryConfigDo();
        List<String> bls = Arrays.asList("BL1", "BL2");
        int id = 10;
        CountryBusinessLineDo dto = new CountryBusinessLineDo();
        when(countryConfigConverter.countryConfigDoToCountryBusinessLineDo(entity)).thenReturn(dto);
        doNothing().when(countryBusinessLineRepo).saveAll(anyList());
        assertDoesNotThrow(() -> ability.saveCountryBusinessLineDo(entity, bls, id));
    }

    @Test
    void updateCountryBusinessLineDo_normal() {
        CountryConfigDo entity = new CountryConfigDo();
        CountryBusinessLineDo base = new CountryBusinessLineDo();
        when(countryConfigConverter.countryConfigDoToCountryBusinessLineDo(entity)).thenReturn(base);
        CountryBusinessLineDo b1 = new CountryBusinessLineDo(); b1.setId(1); b1.setBusinessLine("BL1");
        List<CountryBusinessLineDo> byCountryId = Arrays.asList(b1);
        when(countryBusinessLineRepo.getByCountryId(entity.getId())).thenReturn(byCountryId);
        when(countryBusinessLineConverter.clone(base)).thenReturn(new CountryBusinessLineDo());
        doNothing().when(countryBusinessLineRepo).updateBatchById(anyList());
        assertDoesNotThrow(() -> ability.updateCountryBusinessLineDo(entity));
    }
} 