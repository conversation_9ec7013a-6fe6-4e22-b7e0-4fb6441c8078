package com.mi.oa.asset.commons.config.app.ability;

import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.mi.oa.asset.common.enums.MaterialType;
import com.mi.oa.asset.common.enums.YesNo;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.AssetCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.repository.AssetCategoryRepo;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSku;
import com.mi.oa.asset.commons.config.domain.assetsku.repository.AssetSkuRepo;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryConfigRepo;
import com.mi.oa.asset.commons.config.app.provider.AssetSkuProviderImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AssetSkuProviderImplTest1 {

    @InjectMocks
    private AssetSkuProviderImpl assetSkuProvider;

    @Mock
    private AssetSkuRepo assetSkuRepo;

    @Mock
    private AssetCategoryRepo assetCategoryRepo;

    @Mock
    private CountryConfigRepo countryConfigRepo;

    @Mock
    private Gson gson;

    private List<String> validSkuCodes;
    private List<AssetSku> validAssetSkus;
    private List<AssetCategory> validCategories;
    private List<CountryConfigDo> validCountryConfigs;

    @BeforeEach
    void setUp() {
        validSkuCodes = Arrays.asList("SKU001", "SKU002");
        
        // 模拟AssetSku
        validAssetSkus = new ArrayList<>();
        AssetSku sku1 = new AssetSku();
        sku1.setSkuCode("SKU001");
        sku1.setSkuName("测试SKU1");
        sku1.setCateCode("CATE001");
        sku1.setCountry("CHN");
        sku1.setPrice(new BigDecimal("100.00"));
        sku1.setIsSn(YesNo.YES.getCode());
        sku1.setMdmCreateStatus(YesNo.NO.getCode());
        sku1.setMaterialType(MaterialType.CONSUMABLE);

        AssetSku sku2 = new AssetSku();
        sku2.setSkuCode("SKU002");
        sku2.setSkuName("测试SKU2");
        sku2.setCateCode("CATE002");
        sku2.setCountry("USA");
        sku2.setPrice(new BigDecimal("200.00"));
        sku2.setIsSn(YesNo.NO.getCode());
        sku2.setMdmCreateStatus(YesNo.YES.getCode());
        sku2.setMiSkuCode("MI_SKU002");
        sku2.setMaterialType(null);

        validAssetSkus.add(sku1);
        validAssetSkus.add(sku2);
        
        // 模拟AssetCategory
        validCategories = new ArrayList<>();
        AssetCategory category1 = AssetCategory.builder()
                .cateCode("CATE001")
                .purchaseCatalogCode("PC001")
                .build();
        
        AssetCategory category2 = AssetCategory.builder()
                .cateCode("CATE002")
                .purchaseCatalogCode("PC002")
                .build();
        
        validCategories.add(category1);
        validCategories.add(category2);
        
        // 模拟CountryConfigDo
        validCountryConfigs = new ArrayList<>();
        CountryConfigDo countryConfig1 = new CountryConfigDo();
        countryConfig1.setCountryCodeAlphaThree("CHN");
        countryConfig1.setCountryCodeAlphaTwo("CN");

        CountryConfigDo countryConfig2 = new CountryConfigDo();
        countryConfig2.setCountryCodeAlphaThree("USA");
        countryConfig2.setCountryCodeAlphaTwo("US");
        
        validCountryConfigs.add(countryConfig1);
        validCountryConfigs.add(countryConfig2);
    }



    @Test
    void createPurchaseItem_EmptySkuCodes_ThrowsException() {
        // 准备数据 - 空的SKU编码列表
        List<String> emptySkuCodes = Collections.emptyList();
        
        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            assetSkuProvider.createPurchaseItem(emptySkuCodes);
        });
        
        assertTrue(exception.getMessage().contains("skuCode不能为空"));
    }
    
    @Test
    void createPurchaseItem_NoMatchingSkus_ThrowsException() {
        // 准备数据 - 查询不到SKU
        when(assetSkuRepo.getAssetSkuCodes(validSkuCodes)).thenReturn(Collections.emptyList());
        
        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            assetSkuProvider.createPurchaseItem(validSkuCodes);
        });
        
        assertTrue(exception.getMessage().contains("未找到对应的SKU数据"));
        verify(assetSkuRepo).getAssetSkuCodes(validSkuCodes);
    }
    
    @Test
    void createPurchaseItem_CategoryNotExists_ThrowsException() {
        // 准备数据 - 分类编码不存在
        when(assetSkuRepo.getAssetSkuCodes(validSkuCodes)).thenReturn(validAssetSkus);
        when(assetCategoryRepo.isExists("CATE001")).thenReturn(false);
        when(assetCategoryRepo.isExists("CATE002")).thenReturn(true);
        
        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            assetSkuProvider.createPurchaseItem(validSkuCodes);
        });
        
        assertTrue(exception.getMessage().contains("数据异常,分类编码不存在"));
        verify(assetSkuRepo).getAssetSkuCodes(validSkuCodes);
        verify(assetCategoryRepo, times(2)).isExists(anyString());
    }
    
    @Test
    void createPurchaseItem_EmptySkuName_ThrowsException() {
        // 准备数据 - SKU名称为空
        when(assetSkuRepo.getAssetSkuCodes(validSkuCodes)).thenReturn(validAssetSkus);
        when(assetCategoryRepo.isExists(anyString())).thenReturn(true);
        when(assetCategoryRepo.getByCateCodes(anyList())).thenReturn(validCategories);
        when(countryConfigRepo.getByThreeCode(anyList())).thenReturn(validCountryConfigs);
        
        // 设置SKU名称为空
        validAssetSkus.get(0).setSkuName("");
        
        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            assetSkuProvider.createPurchaseItem(validSkuCodes);
        });
        
        assertTrue(exception.getMessage().contains("SKU名称不能为空"));
    }
    
    @Test
    void createPurchaseItem_MiGoodsIdNotEmpty_ThrowsException() {
        // 准备数据 - 小米商品ID不为空
        when(assetSkuRepo.getAssetSkuCodes(validSkuCodes)).thenReturn(validAssetSkus);
        when(assetCategoryRepo.isExists(anyString())).thenReturn(true);
        when(assetCategoryRepo.getByCateCodes(anyList())).thenReturn(validCategories);
        when(countryConfigRepo.getByThreeCode(anyList())).thenReturn(validCountryConfigs);
        
        // 设置小米商品ID不为空
        validAssetSkus.get(0).setMiGoodsId("MI-GOODS-001");
        
        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            assetSkuProvider.createPurchaseItem(validSkuCodes);
        });
        
        assertTrue(exception.getMessage().contains("小米商品,无法推送SKU"));
    }
    

    
    @Test
    void createPurchaseItem_NegativePrice_ThrowsException() {
        // 准备数据 - 价格为负数
        when(assetSkuRepo.getAssetSkuCodes(validSkuCodes)).thenReturn(validAssetSkus);
        when(assetCategoryRepo.isExists(anyString())).thenReturn(true);
        when(assetCategoryRepo.getByCateCodes(anyList())).thenReturn(validCategories);
        when(countryConfigRepo.getByThreeCode(anyList())).thenReturn(validCountryConfigs);
        
        // 设置价格为负数
        validAssetSkus.get(0).setPrice(new BigDecimal("-10.00"));
        
        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            assetSkuProvider.createPurchaseItem(validSkuCodes);
        });
        
        assertTrue(exception.getMessage().contains("价格不能为负数"));
    }
}
