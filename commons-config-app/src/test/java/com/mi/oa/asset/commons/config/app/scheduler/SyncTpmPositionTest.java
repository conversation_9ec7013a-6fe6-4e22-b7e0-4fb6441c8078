package com.mi.oa.asset.commons.config.app.scheduler;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.app.ability.PositionAbility;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import com.mi.oa.asset.commons.config.domain.position.enums.TpmMdmTypeEnums;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.*;

/**
 * SyncTpmPosition单元测试类
 */
@ExtendWith(MockitoExtension.class)
class SyncTpmPositionTest {

    @InjectMocks
    private SyncTpmPosition syncTpmPosition;

    @Mock
    private PositionAbility positionAbility;

    @Test
    @DisplayName("测试execute方法正常执行流程")
    void execute_Normal_Success() {
        // 准备数据
        // 不需要特别准备数据，因为execute方法没有入参

        // 执行测试
        syncTpmPosition.execute();

        // 验证结果
        // 验证syncTpmPosition方法被调用了3次，对应3个枚举值WORKSHOP、LINE、WORKSTATION
        verify(positionAbility, times(1)).syncTpmPosition(TpmMdmTypeEnums.WORKSHOP);
        verify(positionAbility, times(1)).syncTpmPosition(TpmMdmTypeEnums.LINE);
        verify(positionAbility, times(1)).syncTpmPosition(TpmMdmTypeEnums.WORKSTATION);
        
        // 验证updateTpmPositionPath方法被调用
        verify(positionAbility, times(1)).updateTpmPositionPath(BusinessLine.HEA_FACTORY, DataCreateSource.SYS_TPM);
        
        // 验证syncSpacePosition方法被调用
        verify(positionAbility, times(1)).syncSpacePosition();
    }

} 