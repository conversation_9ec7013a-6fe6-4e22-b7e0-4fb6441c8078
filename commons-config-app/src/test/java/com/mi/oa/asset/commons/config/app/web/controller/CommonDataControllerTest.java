package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.common.model.CodeNameProperty;
import com.mi.oa.asset.commons.config.api.function.DictRes;
import com.mi.oa.asset.commons.config.api.function.FunctionProvider;
import com.mi.oa.asset.commons.config.app.converter.CommonDataConverter;
import com.mi.oa.asset.commons.config.domain.common.repository.CommonDataRepo;
import com.mi.oa.asset.eam.feign.service.EcpService;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.mi.oa.asset.common.enums.EAMConstants.CHINESE;
import static com.mi.oa.asset.common.enums.EAMConstants.LANGUAGE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CommonDataControllerTest {

    @InjectMocks
    private CommonDataController commonDataController;

    @Mock
    private CommonDataRepo commonDataRepo;

    @Mock
    private CommonDataConverter commonDataConverter;

    @Mock
    private EcpService ecpService;

    @Mock
    private FunctionProvider functionProvider;

    @Mock
    private MockHttpServletRequest request;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
    }

    @Test
    void materialType_WhenChineseLanguage_ShouldReturnChineseNames() {
        // 准备测试数据
        DictRes dictRes1 = new DictRes();
        dictRes1.setCode("TYPE1");
        dictRes1.setName("类型1");
        dictRes1.setEnName("Type1");

        DictRes dictRes2 = new DictRes();
        dictRes2.setCode("TYPE2");
        dictRes2.setName("类型2");
        dictRes2.setEnName("Type2");

        List<DictRes> dictList = Arrays.asList(dictRes1, dictRes2);
        Map<String, List<DictRes>> dictMap = Collections.singletonMap("amg_material_type", dictList);

        // 设置mock行为
        when(request.getHeader(LANGUAGE)).thenReturn(CHINESE);
        when(functionProvider.getDictList(Arrays.asList("amg_material_type"))).thenReturn(dictMap);

        // 执行测试
        Result<List<CodeNameProperty>> result = commonDataController.materialType();

        // 验证结果
        assertNotNull(result);
        List<CodeNameProperty> properties = result.getData();
        assertEquals(2, properties.size());
        assertEquals("类型1", properties.get(0).getName());
        assertEquals("类型2", properties.get(1).getName());
    }

    @Test
    void materialType_WhenEnglishLanguage_ShouldReturnEnglishNames() {
        // 准备测试数据
        DictRes dictRes1 = new DictRes();
        dictRes1.setCode("TYPE1");
        dictRes1.setName("类型1");
        dictRes1.setEnName("Type1");

        DictRes dictRes2 = new DictRes();
        dictRes2.setCode("TYPE2");
        dictRes2.setName("类型2");
        dictRes2.setEnName("Type2");

        List<DictRes> dictList = Arrays.asList(dictRes1, dictRes2);
        Map<String, List<DictRes>> dictMap = Collections.singletonMap("amg_material_type", dictList);

        // 设置mock行为
        when(request.getHeader(LANGUAGE)).thenReturn("en");
        when(functionProvider.getDictList(Arrays.asList("amg_material_type"))).thenReturn(dictMap);

        // 执行测试
        Result<List<CodeNameProperty>> result = commonDataController.materialType();

        // 验证结果
        assertNotNull(result);
        List<CodeNameProperty> properties = result.getData();
        assertEquals(2, properties.size());
        assertEquals("Type1", properties.get(0).getName());
        assertEquals("Type2", properties.get(1).getName());
    }

    @Test
    void materialType_WhenEmptyLanguage_ShouldReturnChineseNames() {
        // 准备测试数据
        DictRes dictRes1 = new DictRes();
        dictRes1.setCode("TYPE1");
        dictRes1.setName("类型1");
        dictRes1.setEnName("Type1");

        List<DictRes> dictList = Collections.singletonList(dictRes1);
        Map<String, List<DictRes>> dictMap = Collections.singletonMap("amg_material_type", dictList);

        // 设置mock行为
        when(request.getHeader(LANGUAGE)).thenReturn("");
        when(functionProvider.getDictList(Arrays.asList("amg_material_type"))).thenReturn(dictMap);

        // 执行测试
        Result<List<CodeNameProperty>> result = commonDataController.materialType();

        // 验证结果
        assertNotNull(result);
        List<CodeNameProperty> properties = result.getData();
        assertEquals(1, properties.size());
        assertEquals("类型1", properties.get(0).getName());
    }

    @Test
    void getRate_ValidCurrencyCode_ReturnsSuccessResult() {
        // 准备数据
        String baseCurrencyCode = "USD";
        BigDecimal expectedRate = new BigDecimal("7.23");
        when(ecpService.queryExRateByCurCode(anyString(), anyString())).thenReturn(expectedRate);

        // 执行测试
        Result<BigDecimal> result = commonDataController.getRate(baseCurrencyCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(expectedRate, result.getData());
    }

    @Test
    void getRate_InvalidCurrencyCode_ReturnsSuccessResult() {
        // 准备数据
        String baseCurrencyCode = "INVALID";
        BigDecimal expectedRate = new BigDecimal("0");
        when(ecpService.queryExRateByCurCode(anyString(), anyString())).thenReturn(expectedRate);

        // 执行测试
        Result<BigDecimal> result = commonDataController.getRate(baseCurrencyCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(expectedRate, result.getData());
    }
}
