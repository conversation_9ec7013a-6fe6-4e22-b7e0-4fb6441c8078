package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.bpm.BpmApprovalTaskRes;
import com.mi.oa.asset.commons.config.api.bpm.BpmApproverRes;
import com.mi.oa.asset.commons.config.app.ability.BpmApprovalAbility;
import com.mi.oa.asset.commons.config.app.converter.BpmApproveConverter;
import com.mi.oa.asset.commons.config.domain.bpm.entity.BpmApprovalTask;
import com.mi.oa.asset.commons.config.domain.bpm.entity.BpmApprover;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BpmProviderImplTest {

    @InjectMocks
    private BpmProviderImpl bpmProviderImpl;

    @Mock
    private BpmApprovalAbility bpmApprovalAbility;

    @Mock
    private BpmApproveConverter approveConverter;

    private String businessKey;
    private String language;
    private List<BpmApprovalTask> mockApprovalTasks;
    private List<BpmApprovalTaskRes> mockApprovalTaskRes;

    @BeforeEach
    void setUp() {
        businessKey = "test-business-key-123";
        language = "zh-CN";
        
        // 准备模拟数据
        mockApprovalTasks = createMockApprovalTasks();
        mockApprovalTaskRes = createMockApprovalTaskRes();
    }

    @Test
    void getApprovalTaskList_SingleParameter_ReturnsApprovalTaskList() {
        // 准备数据
        when(bpmApprovalAbility.listApprovalHistory(eq(businessKey), eq(false), eq("zh")))
                .thenReturn(mockApprovalTasks);
        when(approveConverter.toTaskResList(mockApprovalTasks))
                .thenReturn(mockApprovalTaskRes);

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(businessKey);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockApprovalTaskRes, result);
        verify(bpmApprovalAbility).listApprovalHistory(businessKey, false, "zh");
        verify(approveConverter).toTaskResList(mockApprovalTasks);
    }

    @Test
    void getApprovalTaskList_SingleParameterWithEmptyBusinessKey_ReturnsEmptyList() {
        // 准备数据
        String emptyBusinessKey = "";

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(emptyBusinessKey);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(bpmApprovalAbility, never()).listApprovalHistory(any(), anyBoolean(), anyString());
        verify(approveConverter, never()).toTaskResList(any());
    }

    @Test
    void getApprovalTaskList_SingleParameterWithNullBusinessKey_ReturnsEmptyList() {
        // 准备数据
        String nullBusinessKey = null;

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(nullBusinessKey);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(bpmApprovalAbility, never()).listApprovalHistory(any(), anyBoolean(), anyString());
        verify(approveConverter, never()).toTaskResList(any());
    }

    @Test
    void getApprovalTaskList_SingleParameterWithBlankBusinessKey_ReturnsEmptyList() {
        // 准备数据
        String blankBusinessKey = "   ";

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(blankBusinessKey);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(bpmApprovalAbility, never()).listApprovalHistory(any(), anyBoolean(), anyString());
        verify(approveConverter, never()).toTaskResList(any());
    }

    @Test
    void getApprovalTaskList_TwoParametersWithNeedPredictTrue_ReturnsApprovalTaskList() {
        // 准备数据
        boolean needPredict = true;
        when(bpmApprovalAbility.listApprovalHistory(eq(businessKey), eq(needPredict), eq("zh")))
                .thenReturn(mockApprovalTasks);
        when(approveConverter.toTaskResList(mockApprovalTasks))
                .thenReturn(mockApprovalTaskRes);

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(businessKey, needPredict);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockApprovalTaskRes, result);
        verify(bpmApprovalAbility).listApprovalHistory(businessKey, needPredict, "zh");
        verify(approveConverter).toTaskResList(mockApprovalTasks);
    }

    @Test
    void getApprovalTaskList_TwoParametersWithNeedPredictFalse_ReturnsApprovalTaskList() {
        // 准备数据
        boolean needPredict = false;
        when(bpmApprovalAbility.listApprovalHistory(eq(businessKey), eq(needPredict), eq("zh")))
                .thenReturn(mockApprovalTasks);
        when(approveConverter.toTaskResList(mockApprovalTasks))
                .thenReturn(mockApprovalTaskRes);

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(businessKey, needPredict);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockApprovalTaskRes, result);
        verify(bpmApprovalAbility).listApprovalHistory(businessKey, needPredict, "zh");
        verify(approveConverter).toTaskResList(mockApprovalTasks);
    }

    @Test
    void getApprovalTaskList_TwoParametersWithEmptyBusinessKey_ReturnsEmptyList() {
        // 准备数据
        String emptyBusinessKey = "";
        boolean needPredict = true;

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(emptyBusinessKey, needPredict);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(bpmApprovalAbility, never()).listApprovalHistory(any(), anyBoolean(), anyString());
        verify(approveConverter, never()).toTaskResList(any());
    }

    @Test
    void getApprovalTaskList_ThreeParametersWithValidInputs_ReturnsApprovalTaskList() {
        // 准备数据
        boolean needPredict = true;
        when(bpmApprovalAbility.listApprovalHistory(eq(businessKey), eq(needPredict), eq(language)))
                .thenReturn(mockApprovalTasks);
        when(approveConverter.toTaskResList(mockApprovalTasks))
                .thenReturn(mockApprovalTaskRes);

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(businessKey, needPredict, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockApprovalTaskRes, result);
        verify(bpmApprovalAbility).listApprovalHistory(businessKey, needPredict, language);
        verify(approveConverter).toTaskResList(mockApprovalTasks);
    }

    @Test
    void getApprovalTaskList_ThreeParametersWithEnglishLanguage_ReturnsApprovalTaskList() {
        // 准备数据
        boolean needPredict = false;
        String englishLanguage = "en-US";
        when(bpmApprovalAbility.listApprovalHistory(eq(businessKey), eq(needPredict), eq(englishLanguage)))
                .thenReturn(mockApprovalTasks);
        when(approveConverter.toTaskResList(mockApprovalTasks))
                .thenReturn(mockApprovalTaskRes);

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(businessKey, needPredict, englishLanguage);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockApprovalTaskRes, result);
        verify(bpmApprovalAbility).listApprovalHistory(businessKey, needPredict, englishLanguage);
        verify(approveConverter).toTaskResList(mockApprovalTasks);
    }

    @Test
    void getApprovalTaskList_ThreeParametersWithEmptyBusinessKey_ReturnsEmptyList() {
        // 准备数据
        String emptyBusinessKey = "";
        boolean needPredict = true;

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(emptyBusinessKey, needPredict, language);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(bpmApprovalAbility, never()).listApprovalHistory(any(), anyBoolean(), anyString());
        verify(approveConverter, never()).toTaskResList(any());
    }

    @Test
    void getApprovalTaskList_ThreeParametersWithNullBusinessKey_ReturnsEmptyList() {
        // 准备数据
        String nullBusinessKey = null;
        boolean needPredict = false;

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(nullBusinessKey, needPredict, language);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(bpmApprovalAbility, never()).listApprovalHistory(any(), anyBoolean(), anyString());
        verify(approveConverter, never()).toTaskResList(any());
    }

    @Test
    void getApprovalTaskList_ThreeParametersWithBlankBusinessKey_ReturnsEmptyList() {
        // 准备数据
        String blankBusinessKey = "   ";
        boolean needPredict = true;

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(blankBusinessKey, needPredict, language);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(bpmApprovalAbility, never()).listApprovalHistory(any(), anyBoolean(), anyString());
        verify(approveConverter, never()).toTaskResList(any());
    }

    @Test
    void getApprovalTaskList_ThreeParametersWithEmptyAbilityResult_ReturnsEmptyList() {
        // 准备数据
        boolean needPredict = true;
        when(bpmApprovalAbility.listApprovalHistory(eq(businessKey), eq(needPredict), eq(language)))
                .thenReturn(Collections.emptyList());
        when(approveConverter.toTaskResList(Collections.emptyList()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(businessKey, needPredict, language);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(bpmApprovalAbility).listApprovalHistory(businessKey, needPredict, language);
        verify(approveConverter).toTaskResList(Collections.emptyList());
    }

    @Test
    void getApprovalTaskList_ThreeParametersWithNullAbilityResult_ReturnsEmptyList() {
        // 准备数据
        boolean needPredict = false;
        when(bpmApprovalAbility.listApprovalHistory(eq(businessKey), eq(needPredict), eq(language)))
                .thenReturn(null);
        when(approveConverter.toTaskResList(null))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(businessKey, needPredict, language);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(bpmApprovalAbility).listApprovalHistory(businessKey, needPredict, language);
        verify(approveConverter).toTaskResList(null);
    }

    @Test
    void getApprovalTaskList_ThreeParametersWithSingleTask_ReturnsSingleTaskList() {
        // 准备数据
        boolean needPredict = true;
        List<BpmApprovalTask> singleTask = Collections.singletonList(mockApprovalTasks.get(0));
        List<BpmApprovalTaskRes> singleTaskRes = Collections.singletonList(mockApprovalTaskRes.get(0));
        
        when(bpmApprovalAbility.listApprovalHistory(eq(businessKey), eq(needPredict), eq(language)))
                .thenReturn(singleTask);
        when(approveConverter.toTaskResList(singleTask))
                .thenReturn(singleTaskRes);

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(businessKey, needPredict, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(singleTaskRes, result);
        verify(bpmApprovalAbility).listApprovalHistory(businessKey, needPredict, language);
        verify(approveConverter).toTaskResList(singleTask);
    }

    @Test
    void getApprovalTaskList_ThreeParametersWithMultipleTasks_ReturnsMultipleTaskList() {
        // 准备数据
        boolean needPredict = false;
        List<BpmApprovalTask> multipleTasks = new ArrayList<>(mockApprovalTasks);
        multipleTasks.add(createMockApprovalTask("task-2", "审批任务2", "SINGLE", "Completed node", 0));
        List<BpmApprovalTaskRes> multipleTaskRes = new ArrayList<>(mockApprovalTaskRes);
        multipleTaskRes.add(createMockApprovalTaskRes("task-2", "审批任务2", "SINGLE", "Completed node", 0));
        
        when(bpmApprovalAbility.listApprovalHistory(eq(businessKey), eq(needPredict), eq(language)))
                .thenReturn(multipleTasks);
        when(approveConverter.toTaskResList(multipleTasks))
                .thenReturn(multipleTaskRes);

        // 执行测试
        List<BpmApprovalTaskRes> result = bpmProviderImpl.getApprovalTaskList(businessKey, needPredict, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(multipleTaskRes, result);
        verify(bpmApprovalAbility).listApprovalHistory(businessKey, needPredict, language);
        verify(approveConverter).toTaskResList(multipleTasks);
    }

    // 辅助方法
    private List<BpmApprovalTask> createMockApprovalTasks() {
        List<BpmApprovalTask> tasks = new ArrayList<>();
        tasks.add(createMockApprovalTask("task-1", "审批任务1", "SINGLE", "Completed node", 0));
        return tasks;
    }

    private BpmApprovalTask createMockApprovalTask(String taskId, String taskName, String signType, String nodeState, Integer isPredict) {
        List<BpmApprover> approvers = new ArrayList<>();
        approvers.add(createMockApprover("approver-1", "张三", "zhangsan", "avatar1", "2024-01-01 10:00:00", "2024-01-01 11:00:00", "agree", "同意"));
        
        return BpmApprovalTask.builder()
                .taskId(taskId)
                .taskName(taskName)
                .signType(signType)
                .nodeState(nodeState)
                .taskList(approvers)
                .isPredict(isPredict)
                .build();
    }

    private BpmApprover createMockApprover(String taskId, String displayName, String userName, String avatar, 
                                          String createTime, String endTime, String operation, String comment) {
        return BpmApprover.builder()
                .taskId(taskId)
                .displayName(displayName)
                .userName(userName)
                .avatar(avatar)
                .createTime(createTime)
                .endTime(endTime)
                .operation(operation)
                .comment(comment)
                .build();
    }

    private List<BpmApprovalTaskRes> createMockApprovalTaskRes() {
        List<BpmApprovalTaskRes> taskRes = new ArrayList<>();
        taskRes.add(createMockApprovalTaskRes("task-1", "审批任务1", "SINGLE", "Completed node", 0));
        return taskRes;
    }

    private BpmApprovalTaskRes createMockApprovalTaskRes(String taskId, String taskName, String signType, String nodeState, Integer isPredict) {
        List<BpmApproverRes> approverRes = new ArrayList<>();
        approverRes.add(createMockApproverRes("approver-1", "张三", "zhangsan", "avatar1", "2024-01-01 10:00:00", "2024-01-01 11:00:00", "agree", "同意"));
        
        BpmApprovalTaskRes taskRes = new BpmApprovalTaskRes();
        taskRes.setTaskId(taskId);
        taskRes.setTaskName(taskName);
        taskRes.setSignType(signType);
        taskRes.setNodeState(nodeState);
        taskRes.setTaskList(approverRes);
        taskRes.setIsPredict(isPredict);
        return taskRes;
    }

    private BpmApproverRes createMockApproverRes(String taskId, String displayName, String userName, String avatar, 
                                                String createTime, String endTime, String operation, String comment) {
        BpmApproverRes approverRes = new BpmApproverRes();
        approverRes.setTaskId(taskId);
        approverRes.setDisplayName(displayName);
        approverRes.setUserName(userName);
        approverRes.setAvatar(avatar);
        approverRes.setCreateTime(createTime);
        approverRes.setEndTime(endTime);
        approverRes.setOperation(operation);
        approverRes.setComment(comment);
        return approverRes;
    }
}