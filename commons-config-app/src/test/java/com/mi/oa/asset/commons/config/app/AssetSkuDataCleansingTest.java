package com.mi.oa.asset.commons.config.app;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.commons.config.api.assetsku.ManageModel;
import com.mi.oa.asset.commons.config.api.assetsku.SnTypeEnum;
import com.mi.oa.asset.commons.config.domain.assetsku.enums.StockCostEnum;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetSkuManagePo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetSkuPo;
import com.mi.oa.asset.commons.config.infra.repository.impl.AssetSkuManageRepoImpl;
import com.mi.oa.asset.commons.config.infra.repository.impl.AssetSkuRepoImpl;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 15:06
 */
@SpringBootTest(classes = StartApplication.class)
@Disabled
class AssetSkuDataCleansingTest {

    @Resource
    private AssetSkuRepoImpl assetSkuRepo;
    @Resource
    private AssetSkuManageRepoImpl assetSkuManageRepo;


    @Test
    void dataCleansingTest() {
        //物料数据清洗
        List<AssetSkuPo> skuPoList = assetSkuRepo.list(Wrappers.lambdaQuery(AssetSkuPo.class).eq(AssetSkuPo::getIsDeleted, 0));
        List<AssetSkuManagePo> skuManagePoList = new ArrayList<>();
        for (AssetSkuPo skuPo : skuPoList) {
            skuManagePoList.add(buildSkuManage(skuPo));
        }
        assetSkuManageRepo.saveBatch(skuManagePoList);

    }

    private AssetSkuManagePo buildSkuManage(AssetSkuPo skuPo) {
        AssetSkuManagePo po = new AssetSkuManagePo();
        po.setSkuId(skuPo.getId());
        po.setBusinessLine(skuPo.getBusinessLine());
        po.setCateId(skuPo.getCateId());
        po.setCateCode(skuPo.getCateCode());
        po.setCateName(skuPo.getCateName());
        po.setMgModel(ManageModel.ASSET_ACCOUNT.getCode());
        if (skuPo.getIsMultipleManage() == 1) {
            po.setSerialMg(SnTypeEnum.MULTIPLE.getCode());
        }
        if (skuPo.getIsSn() == 1) {
            po.setSerialMg(SnTypeEnum.SN.getCode());
        }
        po.setCosting(StockCostEnum.MOVING_AVG.getCode());
        po.setSecureQuantity(new BigDecimal("0"));
        po.setHighestQuantity(new BigDecimal("0"));
        po.setMiniQuantity(new BigDecimal("0"));
        po.setMaterialType(skuPo.getMaterialType());
        po.setIsDeleted(0);
        po.setCreateUser(skuPo.getCreateUser());
        po.setCreateUserName(skuPo.getCreateUserName());
        po.setCreateTime(skuPo.getCreateTime());
        po.setUpdateUser(skuPo.getUpdateUser());
        po.setUpdateUserName(skuPo.getUpdateUserName());
        po.setUpdateTime(skuPo.getUpdateTime());
        return po;
    }

}
