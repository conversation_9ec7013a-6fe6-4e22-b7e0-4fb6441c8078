package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.app.web.controller.AssetOpenController;
import com.mi.oa.asset.commons.config.infra.rpc.mdm.*;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 已通过
 */
@ExtendWith(MockitoExtension.class)
class AssetOpenControllerTest {

    @InjectMocks
    private AssetOpenController assetOpenController;

    @Mock
    private MdmService mdmService;

    @Mock
    private SapService sapService;

    @Test
    @DisplayName("当数据列表不为空时应该正确处理并返回成功")
    void distributePurchaseItem_WithValidData_ShouldProcessAndReturnSuccess() {
        // 准备测试数据
        MdmDistributeVO mdmDistributeVO = new MdmDistributeVO();
        mdmDistributeVO.setModelId("testModelId");
        mdmDistributeVO.setModelName("testModelName");
        
        List<MdmDistributePurchaseItemVO> items = new ArrayList<>();
        MdmDistributePurchaseItemVO item = new MdmDistributePurchaseItemVO();
        item.setSkuId("SKU001");
        item.setMiSkuCode("MISKU001");
        items.add(item);
        
        mdmDistributeVO.setData(items);
        
        // 模拟服务方法行为
        DistributeFeedbackDTO.Item feedbackItem = DistributeFeedbackDTO.Item.builder()
                .masterId("masterId")
                .bussId("bussId")
                .success(true)
                .build();
        
        when(mdmService.savePurchaseItemFromDistribute(anyList())).thenReturn(Collections.singletonList(feedbackItem));
        doNothing().when(sapService).distributeFeedback(any(DistributeFeedbackDTO.class));
        
        // 执行测试
        Result<Void> result = assetOpenController.distributePurchaseItem(mdmDistributeVO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        
        // 验证方法调用
        verify(mdmService).savePurchaseItemFromDistribute(items);
        
        // 捕获传递给distributeFeedback方法的参数
        ArgumentCaptor<DistributeFeedbackDTO> feedbackCaptor = ArgumentCaptor.forClass(DistributeFeedbackDTO.class);
        verify(sapService).distributeFeedback(feedbackCaptor.capture());
        
        // 验证回调参数
        DistributeFeedbackDTO capturedFeedback = feedbackCaptor.getValue();
        assertEquals("testModelId", capturedFeedback.getModelId());
        assertEquals("testModelName", capturedFeedback.getModelName());
        assertEquals("EAM2", capturedFeedback.getSys());
        assertEquals(1, capturedFeedback.getData().size());
        assertEquals(feedbackItem, capturedFeedback.getData().get(0));
    }

    @Test
    @DisplayName("当数据列表为空时不应调用保存方法但应调用回调")
    void distributePurchaseItem_WithEmptyList_ShouldNotSaveButCallback() {
        // 准备测试数据
        MdmDistributeVO mdmDistributeVO = new MdmDistributeVO();
        mdmDistributeVO.setModelId("testModelId");
        mdmDistributeVO.setModelName("testModelName");
        mdmDistributeVO.setData(Collections.emptyList());
        
        // 执行测试
        Result<Void> result = assetOpenController.distributePurchaseItem(mdmDistributeVO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        
        // 验证方法调用
        verify(mdmService, never()).savePurchaseItemFromDistribute(anyList());
        
        // 验证回调
        ArgumentCaptor<DistributeFeedbackDTO> feedbackCaptor = ArgumentCaptor.forClass(DistributeFeedbackDTO.class);
        verify(sapService).distributeFeedback(feedbackCaptor.capture());
        
        DistributeFeedbackDTO capturedFeedback = feedbackCaptor.getValue();
        assertEquals("testModelId", capturedFeedback.getModelId());
        assertEquals("testModelName", capturedFeedback.getModelName());
        assertNull(capturedFeedback.getData());
    }

    @Test
    @DisplayName("当数据列表为null时不应调用保存方法但应调用回调")
    void distributePurchaseItem_WithNullList_ShouldNotSaveButCallback() {
        // 准备测试数据
        MdmDistributeVO mdmDistributeVO = new MdmDistributeVO();
        mdmDistributeVO.setModelId("testModelId");
        mdmDistributeVO.setModelName("testModelName");
        mdmDistributeVO.setData(null);
        
        // 执行测试
        Result<Void> result = assetOpenController.distributePurchaseItem(mdmDistributeVO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        
        // 验证方法调用
        verify(mdmService, never()).savePurchaseItemFromDistribute(anyList());
        
        // 验证回调
        ArgumentCaptor<DistributeFeedbackDTO> feedbackCaptor = ArgumentCaptor.forClass(DistributeFeedbackDTO.class);
        verify(sapService).distributeFeedback(feedbackCaptor.capture());
        
        DistributeFeedbackDTO capturedFeedback = feedbackCaptor.getValue();
        assertEquals("testModelId", capturedFeedback.getModelId());
        assertEquals("testModelName", capturedFeedback.getModelName());
        assertNull(capturedFeedback.getData());
    }

    @Test
    @DisplayName("当数据列表包含非法类型时应抛出异常")
    @Disabled
    void distributePurchaseItem_WithInvalidItemType_ShouldThrowException() {
        // 准备测试数据
        MdmDistributeVO mdmDistributeVO = new MdmDistributeVO();
        mdmDistributeVO.setModelId("testModelId");
        mdmDistributeVO.setModelName("testModelName");
        
        // 使用一个伪造的List，包含错误类型的对象
        List<MdmDistributePurchaseItemVO> dataList = new ArrayList<>();
        MdmDistributePurchaseItemVO validItem = new MdmDistributePurchaseItemVO();
        dataList.add(validItem);
        
        // 添加一个非MdmDistributePurchaseItemVO类型的对象到列表中
        // 由于泛型在运行时被擦除，我们可以通过反射或模拟对象来实现这一点
        // 在这里，我们将使用一个匿名的自定义列表来模拟这种情况
        List<Object> mixedList = new ArrayList<>();
        mixedList.add(validItem);
        mixedList.add(new Object()); // 这不是MdmDistributePurchaseItemVO类型
        
        mdmDistributeVO.setData((List) mixedList);
        
        // 验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            assetOpenController.distributePurchaseItem(mdmDistributeVO);
        });
        
        // 验证异常消息
        assertTrue(exception.getMessage().contains("预期是MdmDistributePurchaseItemVO"));
        
        // 验证方法调用
        verify(mdmService, never()).savePurchaseItemFromDistribute(anyList());
        verify(sapService, never()).distributeFeedback(any());
    }
} 