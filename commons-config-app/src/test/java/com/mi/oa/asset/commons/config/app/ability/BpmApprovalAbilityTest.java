package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.enums.BpmLanguage;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.common.enums.YesNo;
import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.domain.bpm.entity.BpmApprovalTask;
import com.mi.oa.asset.commons.config.domain.bpm.entity.BpmApprover;
import com.mi.oa.asset.commons.config.domain.bpm.enums.BpmApproveStatus;
import com.mi.oa.asset.commons.config.domain.bpm.enums.BpmNodeStatus;
import com.mi.oa.infra.oaucf.bpm.rep.ApprovalTaskResp;
import com.mi.oa.infra.oaucf.bpm.rep.BpmUser;
import com.mi.oa.infra.oaucf.bpm.rep.UserTaskSignType;
import com.mi.oa.infra.oaucf.bpm.service.ApprovalService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BpmApprovalAbilityTest {

    @InjectMocks
    private BpmApprovalAbility bpmApprovalAbility;

    @Mock
    private ApprovalService approvalService;

    @Mock
    private UserInfoService userInfoService;

    private String businessKey;
    private String language;

    @BeforeEach
    void setUp() {
        businessKey = "test-business-key";
        language = "zh-CN";
    }

    @Test
    void listApprovalHistory_ServiceReturnsError_ReturnsEmptyList() {
        // 准备数据
        BaseResp<List<ApprovalTaskResp>> errorResp = new BaseResp<>();
        errorResp.setCode(500);
        errorResp.setMessage("Service error");
        when(approvalService.list(businessKey, true, "zh")).thenReturn(errorResp);

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(approvalService).list(businessKey, true, "zh");
    }

    @Test
    void listApprovalHistory_EmptyData_ReturnsEmptyList() {
        // 准备数据
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Collections.emptyList());
        when(approvalService.list(businessKey, false, "zh")).thenReturn(successResp);

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, false, language);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(approvalService).list(businessKey, false, "zh");
    }

    @Test
    void listApprovalHistory_SingleCompletedTask_ReturnsCorrectResult() {
        // 准备数据
        ApprovalTaskResp taskResp = createApprovalTaskResp("task1", "user1", "张三", 
            LocalDateTime.now().minusHours(1), LocalDateTime.now(), "agree", "同意", UserTaskSignType.SINGLE);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(taskResp));
        
        when(approvalService.list(businessKey, true, "zh")).thenReturn(successResp);
        when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        BpmApprovalTask task = result.get(0);
        assertEquals("task1", task.getTaskName());
        assertEquals(BpmNodeStatus.COMPLETE.getCode(), task.getNodeState());
        assertEquals(YesNo.NO.getCode(), task.getIsPredict());
        assertEquals(UserTaskSignType.SINGLE.name(), task.getSignType());
        
        assertNotNull(task.getTaskList());
        assertEquals(1, task.getTaskList().size());
        
        BpmApprover approver = task.getTaskList().get(0);
        assertEquals("user1", approver.getUserName());
        assertEquals("张三", approver.getDisplayName());
        assertEquals("", approver.getOperation()); // operation为null时返回空字符串
        assertEquals("同意", approver.getComment());
        assertEquals("avatar1", approver.getAvatar());
        
        verify(approvalService).list(businessKey, true, "zh");
        verify(userInfoService).getUserByUserName("user1");
    }

    @Test
    void listApprovalHistory_MultipleTasksSameName_ReturnsMergedResult() {
        // 准备数据
        ApprovalTaskResp task1 = createApprovalTaskResp("task1", "user1", "张三", 
            LocalDateTime.now().minusHours(2), LocalDateTime.now().minusHours(1), "agree", "同意", UserTaskSignType.PARALLEL_ALL);
        ApprovalTaskResp task2 = createApprovalTaskResp("task1", "user2", "李四", 
            LocalDateTime.now().minusHours(1), LocalDateTime.now(), "agree", "同意", UserTaskSignType.PARALLEL_ALL);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(task1, task2));
        
        when(approvalService.list(businessKey, false, "zh")).thenReturn(successResp);
        when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));
        when(userInfoService.getUserByUserName("user2")).thenReturn(createUser("user2", "avatar2"));

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, false, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        BpmApprovalTask task = result.get(0);
        assertEquals("task1", task.getTaskName());
        assertEquals(BpmNodeStatus.COMPLETE.getCode(), task.getNodeState());
        assertEquals(YesNo.NO.getCode(), task.getIsPredict());
        assertEquals(UserTaskSignType.PARALLEL_ALL.name(), task.getSignType());
        
        assertNotNull(task.getTaskList());
        assertEquals(2, task.getTaskList().size());
        
        verify(approvalService).list(businessKey, false, "zh");
        verify(userInfoService).getUserByUserName("user1");
        verify(userInfoService).getUserByUserName("user2");
    }

    @Test
    void listApprovalHistory_DifferentTaskNames_ReturnsSeparateResults() {
        // 准备数据
        ApprovalTaskResp task1 = createApprovalTaskResp("task1", "user1", "张三", 
            LocalDateTime.now().minusHours(2), LocalDateTime.now().minusHours(1), "agree", "同意", UserTaskSignType.SINGLE);
        ApprovalTaskResp task2 = createApprovalTaskResp("task2", "user2", "李四", 
            LocalDateTime.now().minusHours(1), null, null, null, UserTaskSignType.SINGLE);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(task1, task2));
        
        when(approvalService.list(businessKey, true, "zh")).thenReturn(successResp);
        when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));
        when(userInfoService.getUserByUserName("user2")).thenReturn(createUser("user2", "avatar2"));

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 第一个任务已完成
        BpmApprovalTask completedTask = result.get(0);
        assertEquals("task1", completedTask.getTaskName());
        assertEquals(BpmNodeStatus.COMPLETE.getCode(), completedTask.getNodeState());
        assertEquals(YesNo.NO.getCode(), completedTask.getIsPredict());
        
        // 第二个任务待处理
        BpmApprovalTask pendingTask = result.get(1);
        assertEquals("task2", pendingTask.getTaskName());
        assertEquals(BpmNodeStatus.PENDING.getCode(), pendingTask.getNodeState());
        assertEquals(YesNo.NO.getCode(), pendingTask.getIsPredict());
        
        verify(approvalService).list(businessKey, true, "zh");
        verify(userInfoService).getUserByUserName("user1");
        verify(userInfoService).getUserByUserName("user2");
    }

    @Test
    void listApprovalHistory_PendingTaskWithNoOperation_SetsPendingOperation() {
        // 准备数据
        ApprovalTaskResp taskResp = createApprovalTaskResp("task1", "user1", "张三", 
            LocalDateTime.now().minusHours(1), null, null, null, UserTaskSignType.SINGLE);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(taskResp));
        
        when(approvalService.list(businessKey, false, "zh")).thenReturn(successResp);
        when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, false, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        BpmApprovalTask task = result.get(0);
        assertEquals(BpmNodeStatus.PENDING.getCode(), task.getNodeState());
        
        BpmApprover approver = task.getTaskList().get(0);
        assertEquals(BpmApproveStatus.PENDING.getCode(), approver.getOperation());
        
        verify(approvalService).list(businessKey, false, "zh");
        verify(userInfoService).getUserByUserName("user1");
    }

    @Test
    void listApprovalHistory_UserInfoServiceException_HandlesGracefully() {
        // 准备数据
        ApprovalTaskResp taskResp = createApprovalTaskResp("task1", "user1", "张三", 
            LocalDateTime.now().minusHours(1), LocalDateTime.now(), "agree", "同意", UserTaskSignType.SINGLE);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(taskResp));
        
        when(approvalService.list(businessKey, true, "zh")).thenReturn(successResp);
        when(userInfoService.getUserByUserName("user1")).thenThrow(new RuntimeException("Service error"));

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        BpmApprovalTask task = result.get(0);
        assertEquals("task1", task.getTaskName());
        assertEquals(BpmNodeStatus.COMPLETE.getCode(), task.getNodeState());
        
        BpmApprover approver = task.getTaskList().get(0);
        assertEquals("", approver.getAvatar()); // 异常时头像为空
        
        verify(approvalService).list(businessKey, true, "zh");
        verify(userInfoService).getUserByUserName("user1");
    }

    @Test
    void listApprovalHistory_NullAssignee_HandlesGracefully() {
        // 准备数据
        ApprovalTaskResp taskResp = createApprovalTaskResp("task1", null, null, 
            LocalDateTime.now().minusHours(1), LocalDateTime.now(), "agree", "同意", UserTaskSignType.SINGLE);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(taskResp));
        
        when(approvalService.list(businessKey, false, "zh")).thenReturn(successResp);

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, false, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        BpmApprovalTask task = result.get(0);
        assertEquals("task1", task.getTaskName());
        
        BpmApprover approver = task.getTaskList().get(0);
        assertEquals("", approver.getUserName());
        assertEquals("", approver.getDisplayName());
        assertEquals("", approver.getAvatar());
        
        verify(approvalService).list(businessKey, false, "zh");
        verify(userInfoService, never()).getUserByUserName(any());
    }

    @Test
    void listApprovalHistory_ParallelOneSignType_AllTasksCompleted() {
        // 准备数据
        ApprovalTaskResp task1 = createApprovalTaskResp("task1", "user1", "张三", 
            LocalDateTime.now().minusHours(2), LocalDateTime.now().minusHours(1), "agree", "同意", UserTaskSignType.PARALLEL_ONE);
        ApprovalTaskResp task2 = createApprovalTaskResp("task1", "user2", "李四", 
            LocalDateTime.now().minusHours(1), LocalDateTime.now(), "agree", "同意", UserTaskSignType.PARALLEL_ONE);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(task1, task2));
        
        when(approvalService.list(businessKey, true, "zh")).thenReturn(successResp);
        when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));
        when(userInfoService.getUserByUserName("user2")).thenReturn(createUser("user2", "avatar2"));

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        BpmApprovalTask task = result.get(0);
        assertEquals(BpmNodeStatus.COMPLETE.getCode(), task.getNodeState());
        assertEquals(UserTaskSignType.PARALLEL_ONE.name(), task.getSignType());
        
        verify(approvalService).list(businessKey, true, "zh");
    }

    @Test
    void listApprovalHistory_SequentialSignType_PartialTasksCompleted() {
        // 准备数据
        ApprovalTaskResp task1 = createApprovalTaskResp("task1", "user1", "张三", 
            LocalDateTime.now().minusHours(2), LocalDateTime.now().minusHours(1), "agree", "同意", UserTaskSignType.SEQUENTIAL);
        ApprovalTaskResp task2 = createApprovalTaskResp("task1", "user2", "李四", 
            LocalDateTime.now().minusHours(1), null, null, null, UserTaskSignType.SEQUENTIAL);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(task1, task2));
        
        when(approvalService.list(businessKey, false, "zh")).thenReturn(successResp);
        when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));
        when(userInfoService.getUserByUserName("user2")).thenReturn(createUser("user2", "avatar2"));

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, false, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        BpmApprovalTask task = result.get(0);
        assertEquals(BpmNodeStatus.PENDING.getCode(), task.getNodeState());
        assertEquals(UserTaskSignType.SEQUENTIAL.name(), task.getSignType());
        
        verify(approvalService).list(businessKey, false, "zh");
    }

    @Test
    void listApprovalHistory_ComplexScenario_MultipleNodesWithDifferentStates() {
        // 准备数据 - 3个不同的任务节点
        ApprovalTaskResp task1 = createApprovalTaskResp("task1", "user1", "张三", 
            LocalDateTime.now().minusHours(3), LocalDateTime.now().minusHours(2), "agree", "同意", UserTaskSignType.SINGLE);
        ApprovalTaskResp task2 = createApprovalTaskResp("task2", "user2", "李四", 
            LocalDateTime.now().minusHours(2), LocalDateTime.now().minusHours(1), "agree", "同意", UserTaskSignType.SINGLE);
        ApprovalTaskResp task3 = createApprovalTaskResp("task3", "user3", "王五", 
            LocalDateTime.now().minusHours(1), null, null, null, UserTaskSignType.SINGLE);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(task1, task2, task3));
        
        when(approvalService.list(businessKey, true, "zh")).thenReturn(successResp);
        when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));
        when(userInfoService.getUserByUserName("user2")).thenReturn(createUser("user2", "avatar2"));
        when(userInfoService.getUserByUserName("user3")).thenReturn(createUser("user3", "avatar3"));

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 第一个任务已完成
        BpmApprovalTask completedTask1 = result.get(0);
        assertEquals("task1", completedTask1.getTaskName());
        assertEquals(BpmNodeStatus.COMPLETE.getCode(), completedTask1.getNodeState());
        assertEquals(YesNo.NO.getCode(), completedTask1.getIsPredict());
        
        // 第二个任务已完成
        BpmApprovalTask completedTask2 = result.get(1);
        assertEquals("task2", completedTask2.getTaskName());
        assertEquals(BpmNodeStatus.COMPLETE.getCode(), completedTask2.getNodeState());
        assertEquals(YesNo.NO.getCode(), completedTask2.getIsPredict());
        
        // 第三个任务待处理
        BpmApprovalTask pendingTask = result.get(2);
        assertEquals("task3", pendingTask.getTaskName());
        assertEquals(BpmNodeStatus.PENDING.getCode(), pendingTask.getNodeState());
        assertEquals(YesNo.NO.getCode(), pendingTask.getIsPredict());
        
        verify(approvalService).list(businessKey, true, "zh");
        verify(userInfoService).getUserByUserName("user1");
        verify(userInfoService).getUserByUserName("user2");
        verify(userInfoService).getUserByUserName("user3");
    }

    @Test
    void listApprovalHistory_UnreachedNodeScenario_SetsCorrectState() {
        // 准备数据 - 第一个节点已完成，第二个节点待处理，第三个节点未到达
        ApprovalTaskResp task1 = createApprovalTaskResp("task1", "user1", "张三", 
            LocalDateTime.now().minusHours(3), LocalDateTime.now().minusHours(2), "agree", "同意", UserTaskSignType.SINGLE);
        ApprovalTaskResp task2 = createApprovalTaskResp("task2", "user2", "李四", 
            LocalDateTime.now().minusHours(2), null, null, null, UserTaskSignType.SINGLE);
        ApprovalTaskResp task3 = createApprovalTaskResp("task3", "user3", "王五", 
            LocalDateTime.now().minusHours(1), null, null, null, UserTaskSignType.SINGLE);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(task1, task2, task3));
        
        when(approvalService.list(businessKey, true, "zh")).thenReturn(successResp);
        when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));
        when(userInfoService.getUserByUserName("user2")).thenReturn(createUser("user2", "avatar2"));
        when(userInfoService.getUserByUserName("user3")).thenReturn(createUser("user3", "avatar3"));

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 第一个任务已完成
        BpmApprovalTask completedTask = result.get(0);
        assertEquals("task1", completedTask.getTaskName());
        assertEquals(BpmNodeStatus.COMPLETE.getCode(), completedTask.getNodeState());
        assertEquals(YesNo.NO.getCode(), completedTask.getIsPredict());
        
        // 第二个任务待处理
        BpmApprovalTask pendingTask = result.get(1);
        assertEquals("task2", pendingTask.getTaskName());
        assertEquals(BpmNodeStatus.PENDING.getCode(), pendingTask.getNodeState());
        assertEquals(YesNo.NO.getCode(), pendingTask.getIsPredict());
        
        // 第三个任务未到达
        BpmApprovalTask unreachedTask = result.get(2);
        assertEquals("task3", unreachedTask.getTaskName());
        assertEquals(BpmNodeStatus.UNREACHED.getCode(), unreachedTask.getNodeState());
        assertEquals(YesNo.YES.getCode(), unreachedTask.getIsPredict());
        
        verify(approvalService).list(businessKey, true, "zh");
    }

    @Test
    void listApprovalHistory_SingleTaskWithEndTime_IsDone() {
        // 准备数据 - 单个任务有结束时间
        ApprovalTaskResp taskResp = createApprovalTaskResp("task1", "user1", "张三", 
            LocalDateTime.now().minusHours(1), LocalDateTime.now(), "agree", "同意", UserTaskSignType.SINGLE);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(taskResp));
        
        when(approvalService.list(businessKey, false, "zh")).thenReturn(successResp);
        when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, false, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        BpmApprovalTask task = result.get(0);
        assertEquals(BpmNodeStatus.COMPLETE.getCode(), task.getNodeState());
        assertEquals(YesNo.NO.getCode(), task.getIsPredict());
        
        verify(approvalService).list(businessKey, false, "zh");
    }

    @Test
    void listApprovalHistory_ParallelOneSignType_PartialTasksCompleted() {
        // 准备数据 - PARALLEL_ONE类型，部分任务完成
        ApprovalTaskResp task1 = createApprovalTaskResp("task1", "user1", "张三", 
            LocalDateTime.now().minusHours(2), LocalDateTime.now().minusHours(1), "agree", "同意", UserTaskSignType.PARALLEL_ONE);
        ApprovalTaskResp task2 = createApprovalTaskResp("task1", "user2", "李四", 
            LocalDateTime.now().minusHours(1), null, null, null, UserTaskSignType.PARALLEL_ONE);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(task1, task2));
        
        when(approvalService.list(businessKey, true, "zh")).thenReturn(successResp);
        when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));
        when(userInfoService.getUserByUserName("user2")).thenReturn(createUser("user2", "avatar2"));

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        BpmApprovalTask task = result.get(0);
        assertEquals(BpmNodeStatus.PENDING.getCode(), task.getNodeState());
        assertEquals(UserTaskSignType.PARALLEL_ONE.name(), task.getSignType());
        
        verify(approvalService).list(businessKey, true, "zh");
    }

    @Test
    void listApprovalHistory_NullCreateTime_HandlesGracefully() {
        // 准备数据 - 创建时间为空
        ApprovalTaskResp taskResp = createApprovalTaskResp("task1", "user1", "张三", 
            null, LocalDateTime.now(), "agree", "同意", UserTaskSignType.SINGLE);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(taskResp));
        
        when(approvalService.list(businessKey, false, "zh")).thenReturn(successResp);
        when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, false, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        BpmApprovalTask task = result.get(0);
        assertEquals("task1", task.getTaskName());
        
        BpmApprover approver = task.getTaskList().get(0);
        assertEquals("", approver.getCreateTime()); // 创建时间为空时应该为空字符串
        
        verify(approvalService).list(businessKey, false, "zh");
    }

    @Test
    void listApprovalHistory_EmptyUserName_HandlesGracefully() {
        // 准备数据 - 用户名为空字符串
        ApprovalTaskResp taskResp = createApprovalTaskResp("task1", "", "张三", 
            LocalDateTime.now().minusHours(1), LocalDateTime.now(), "agree", "同意", UserTaskSignType.SINGLE);
        
        BaseResp<List<ApprovalTaskResp>> successResp = new BaseResp<>();
        successResp.setCode(BaseResp.CODE_SUCCESS);
        successResp.setData(Arrays.asList(taskResp));
        
        when(approvalService.list(businessKey, true, "zh")).thenReturn(successResp);

        // 执行测试
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        BpmApprovalTask task = result.get(0);
        assertEquals("task1", task.getTaskName());
        
        BpmApprover approver = task.getTaskList().get(0);
        assertEquals("", approver.getAvatar()); // 用户名为空时头像应该为空
        
        verify(approvalService).list(businessKey, true, "zh");
        verify(userInfoService, never()).getUserByUserName(any());
    }

    @Test
    void listApprovalHistory_BaseRespIsNull_ReturnsEmptyList() {
        // BaseResp为null
        when(approvalService.list(businessKey, true, "zh")).thenReturn(null);
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(approvalService).list(businessKey, true, "zh");
    }

    @Test
    void listApprovalHistory_DataIsNull_ReturnsEmptyList() {
        // data为null
        BaseResp<List<ApprovalTaskResp>> resp = new BaseResp<>();
        resp.setCode(BaseResp.CODE_SUCCESS);
        resp.setData(null);
        when(approvalService.list(businessKey, false, "zh")).thenReturn(resp);
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, false, language);
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(approvalService).list(businessKey, false, "zh");
    }

    @Test
    void listApprovalHistory_OperationAllEnumValues_CorrectMapping() {
        for (BpmApproveStatus status : BpmApproveStatus.values()) {
            ApprovalTaskResp taskResp = createApprovalTaskResp("task1", "user1", "张三", LocalDateTime.now().minusHours(1), LocalDateTime.now(), status.getCode(), "备注", UserTaskSignType.SINGLE);
            // 由于无法直接创建UserTaskOperation实例，我们测试operation为null的情况，主要验证其他逻辑
            taskResp.setOperation(null);
            BaseResp<List<ApprovalTaskResp>> resp = new BaseResp<>();
            resp.setCode(BaseResp.CODE_SUCCESS);
            resp.setData(Collections.singletonList(taskResp));
            when(approvalService.list(businessKey, true, "zh")).thenReturn(resp);
            when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));
            List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);
            assertNotNull(result);
            assertEquals(1, result.size());
            BpmApprover approver = result.get(0).getTaskList().get(0);
            assertEquals("", approver.getOperation()); // operation为null时返回空字符串
        }
    }

    @Test
    void listApprovalHistory_CommentIsNullOrSpecial_CorrectMapping() {
        // comment为null
        ApprovalTaskResp taskResp1 = createApprovalTaskResp("task1", "user1", "张三", LocalDateTime.now().minusHours(1), LocalDateTime.now(), "agree", null, UserTaskSignType.SINGLE);
        // comment为特殊字符
        ApprovalTaskResp taskResp2 = createApprovalTaskResp("task1", "user1", "张三", LocalDateTime.now().minusHours(1), LocalDateTime.now(), "agree", "特殊字符!@#￥%", UserTaskSignType.SINGLE);
        BaseResp<List<ApprovalTaskResp>> resp = new BaseResp<>();
        resp.setCode(BaseResp.CODE_SUCCESS);
        resp.setData(Arrays.asList(taskResp1, taskResp2));
        when(approvalService.list(businessKey, true, "zh")).thenReturn(resp);
        when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);
        assertNotNull(result);
        assertEquals(1, result.size());
        List<BpmApprover> approvers = result.get(0).getTaskList();
        assertNull(approvers.get(0).getComment());
        assertEquals("特殊字符!@#￥%", approvers.get(1).getComment());
    }

    @Test
    void listApprovalHistory_AssigneeIsEmptyObject_CorrectMapping() {
        // assignee为new BpmUser()，userName/displayName为空
        ApprovalTaskResp taskResp = new ApprovalTaskResp();
        taskResp.setTaskName("task1");
        taskResp.setTaskId("task-" + System.currentTimeMillis());
        taskResp.setSignType(UserTaskSignType.SINGLE);
        taskResp.setAssignee(new BpmUser());
        taskResp.setCreateTime(LocalDateTime.now().atZone(ZoneId.systemDefault()));
        taskResp.setEndTime(LocalDateTime.now().atZone(ZoneId.systemDefault()));
        BaseResp<List<ApprovalTaskResp>> resp = new BaseResp<>();
        resp.setCode(BaseResp.CODE_SUCCESS);
        resp.setData(Collections.singletonList(taskResp));
        // 注意：getLanguage方法会将"zh-CN"转换为"zh"，所以这里需要mock转换后的参数
        when(approvalService.list(businessKey, false, "zh")).thenReturn(resp);
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, false, language);
        assertNotNull(result);
        assertEquals(1, result.size());
        BpmApprover approver = result.get(0).getTaskList().get(0);
        assertNull(approver.getUserName());
        assertNull(approver.getDisplayName());
    }

    @Test
    void listApprovalHistory_AllFieldsNull_CorrectMapping() {
        // ApprovalTaskResp所有字段为null
        ApprovalTaskResp taskResp = new ApprovalTaskResp();
        taskResp.setSignType(UserTaskSignType.SINGLE); // 保证signType不为null
        BaseResp<List<ApprovalTaskResp>> resp = new BaseResp<>();
        resp.setCode(BaseResp.CODE_SUCCESS);
        resp.setData(Collections.singletonList(taskResp));
        when(approvalService.list(businessKey, true, "zh")).thenReturn(resp);
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);
        assertNotNull(result);
        assertEquals(1, result.size());
        BpmApprovalTask task = result.get(0);
        assertNull(task.getTaskName());
        assertEquals(UserTaskSignType.SINGLE.name(), task.getSignType()); // 默认SINGLE
    }

    @Test
    void listApprovalHistory_SignTypeAllEnumValues_CorrectNodeState() {
        for (UserTaskSignType signType : UserTaskSignType.values()) {
            ApprovalTaskResp taskResp = createApprovalTaskResp("task1", "user1", "张三", LocalDateTime.now().minusHours(1), LocalDateTime.now(), "agree", "同意", signType);
            BaseResp<List<ApprovalTaskResp>> resp = new BaseResp<>();
            resp.setCode(BaseResp.CODE_SUCCESS);
            resp.setData(Collections.singletonList(taskResp));
            when(approvalService.list(businessKey, false, "zh")).thenReturn(resp);
            when(userInfoService.getUserByUserName("user1")).thenReturn(createUser("user1", "avatar1"));
            List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, false, language);
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(signType.name(), result.get(0).getSignType());
        }
    }

    @Test
    void listApprovalHistory_AvatarMapMiss_CorrectMapping() {
        // avatarMap未命中userName
        ApprovalTaskResp taskResp = createApprovalTaskResp("task1", "userX", "张三", LocalDateTime.now().minusHours(1), LocalDateTime.now(), "agree", "同意", UserTaskSignType.SINGLE);
        BaseResp<List<ApprovalTaskResp>> resp = new BaseResp<>();
        resp.setCode(BaseResp.CODE_SUCCESS);
        resp.setData(Collections.singletonList(taskResp));
        when(approvalService.list(businessKey, true, "zh")).thenReturn(resp);
        when(userInfoService.getUserByUserName("userX")).thenReturn(createUser("userX", null));
        List<BpmApprovalTask> result = bpmApprovalAbility.listApprovalHistory(businessKey, true, language);
        assertNotNull(result);
        assertEquals(1, result.size());
        BpmApprover approver = result.get(0).getTaskList().get(0);
        assertEquals("", approver.getAvatar());
    }

    // getLanguage方法测试
    @Test
    void getLanguage_NullInput_ReturnsZhCode() {
        String result = bpmApprovalAbility.getLanguage(null);
        assertEquals(BpmLanguage.ZH.getCode(), result);
    }

    @Test
    void getLanguage_EmptyString_ReturnsZhCode() {
        String result = bpmApprovalAbility.getLanguage("");
        assertEquals(BpmLanguage.ZH.getCode(), result);
    }

    @Test
    void getLanguage_BlankString_ReturnsZhCode() {
        String result = bpmApprovalAbility.getLanguage("   ");
        assertEquals(BpmLanguage.ZH.getCode(), result);
    }

    @Test
    void getLanguage_ChineseInput_ReturnsZhCode() {
        String result = bpmApprovalAbility.getLanguage(EAMConstants.CHINESE);
        assertEquals(BpmLanguage.ZH.getCode(), result);
    }

    @Test
    void getLanguage_EnglishInput_ReturnsEnCode() {
        String result = bpmApprovalAbility.getLanguage(EAMConstants.ENGLISH);
        assertEquals(BpmLanguage.EN.getCode(), result);
    }

    @Test
    void getLanguage_OtherLanguage_ReturnsOriginalValue() {
        String otherLanguage = "fr-FR";
        String result = bpmApprovalAbility.getLanguage(otherLanguage);
        assertEquals(otherLanguage, result);
    }

    @Test
    void getLanguage_UpperCaseEnglish_ReturnsEnCode() {
        String result = bpmApprovalAbility.getLanguage(EAMConstants.ENGLISH);
        assertEquals(BpmLanguage.EN.getCode(), result);
    }

    @Test
    void getLanguage_LowerCaseChinese_ReturnsZhCode() {
        String result = bpmApprovalAbility.getLanguage(EAMConstants.CHINESE);
        assertEquals(BpmLanguage.ZH.getCode(), result);
    }

    // 辅助方法
    private ApprovalTaskResp createApprovalTaskResp(String taskName, String userName, String displayName, 
                                                   LocalDateTime createTime, LocalDateTime endTime, 
                                                   String operationCode, String comment, UserTaskSignType signType) {
        ApprovalTaskResp resp = new ApprovalTaskResp();
        resp.setTaskName(taskName);
        resp.setCreateTime(createTime != null ? createTime.atZone(ZoneId.systemDefault()) : null);
        resp.setEndTime(endTime != null ? endTime.atZone(ZoneId.systemDefault()) : null);
        resp.setSignType(signType);
        resp.setTaskId("task-" + System.currentTimeMillis());
        
        if (userName != null) {
            BpmUser bpmUser = new BpmUser();
            bpmUser.setUserName(userName);
            bpmUser.setDisplayName(displayName);
            resp.setAssignee(bpmUser);
        }
        
        if (operationCode != null) {
            // 简化处理，设置operation为null，测试中主要关注endTime的逻辑
            resp.setOperation(null);
        }
        
        resp.setComment(comment);
        return resp;
    }

    private User createUser(String userName, String avatar) {
        User user = new User();
        user.setUserName(userName);
        user.setAvatar(avatar);
        return user;
    }

} 