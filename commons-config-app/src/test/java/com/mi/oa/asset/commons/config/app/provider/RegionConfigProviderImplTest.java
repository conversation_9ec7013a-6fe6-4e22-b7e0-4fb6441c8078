package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.regionconfig.EmployeeRegionCountryRes;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigReq;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigRes;
import com.mi.oa.asset.commons.config.app.ability.RegionConfigAbility;
import com.mi.oa.asset.commons.config.app.converter.RegionConfigConverter;
import com.mi.oa.asset.commons.config.domain.international.entity.RegionConfigDo;
import com.mi.oa.asset.commons.config.domain.international.repository.RegionConfigRepo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RegionConfigProviderImplTest {
    @InjectMocks
    private RegionConfigProviderImpl provider;
    @Mock
    private RegionConfigRepo repo;
    @Mock
    private RegionConfigConverter converter;
    @Mock
    private RegionConfigAbility regionConfigAbility;

    @Test
    void getRegionConfigList_normal() {
        List<RegionConfigDo> doList = Collections.singletonList(new RegionConfigDo());
        List<RegionConfigRes> resList = Collections.singletonList(new RegionConfigRes());

        when(repo.searchAll()).thenReturn(doList);
        when(converter.listDoToRes(doList)).thenReturn(resList);

        List<RegionConfigRes> result = provider.getRegionConfigList();
        assertEquals(1, result.size());
    }

    @Test
    void getRegionAndCountrysList_normal() {
        List<RegionConfigDo> doList = Collections.singletonList(new RegionConfigDo());
        List<EmployeeRegionCountryRes> resList = Collections.singletonList(new EmployeeRegionCountryRes());
        when(repo.searchAll()).thenReturn(doList);
        when(converter.listDoToEmployeeRegionCountryRes(doList)).thenReturn(resList);
        when(regionConfigAbility.getRegionAndCountrysList(resList)).thenReturn(resList);
        List<EmployeeRegionCountryRes> result = provider.getRegionAndCountrysList();
        assertEquals(1, result.size());
    }

    @Test
    void getRegionAndCountrys_normal() {
        RegionConfigDo do1 = new RegionConfigDo();
        EmployeeRegionCountryRes res = new EmployeeRegionCountryRes();
        when(repo.getById(1)).thenReturn(do1);
        when(converter.doToEmployeeRegionCountryRes(do1)).thenReturn(res);
        when(regionConfigAbility.getRegionAndCountrys(res)).thenReturn(res);
        assertEquals(res, provider.getRegionAndCountrys(1));
    }

    @Test
    void saveOrUpdate_insert() {
        RegionConfigReq req = new RegionConfigReq();
        req.setRegionName("ee");
        req.setRegionEnglishName("ee");
        assertDoesNotThrow(() -> provider.saveOrUpdate(req));
    }


    @Test
    void saveOrUpdate_update() {
        RegionConfigReq req = new RegionConfigReq();
        req.setId(10);
        req.setRegionName("ee");
        req.setRegionEnglishName("ee");
        assertDoesNotThrow(() -> provider.saveOrUpdate(req));
    }

    @Test
    void removeByIds_normal() {
        assertDoesNotThrow(() -> provider.removeByIds(Arrays.asList(1,2)));
    }

} 