package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.common.enums.YesNo;
import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.address.AssetReceiveAddressProvider;
import com.mi.oa.asset.commons.config.api.common.DelByIdsReq;
import com.mi.oa.asset.commons.config.api.myfunctions.MyFunctionsSortReq;
import com.mi.oa.asset.commons.config.api.myfunctions.*;
import com.mi.oa.asset.commons.config.api.user.EmployeeInfoRes;
import com.mi.oa.asset.commons.config.api.user.UserBaseInfoRes;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.domain.common.entity.*;
import com.mi.oa.asset.commons.config.domain.common.repository.*;
import com.mi.oa.asset.commons.config.domain.commonfunc.entity.MyCommonFunc;
import com.mi.oa.asset.commons.config.domain.commonfunc.repository.MyCommonFuncRepo;
import com.mi.oa.asset.commons.config.infra.repository.converter.FunctionConfigConverter;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.xiaomi.mit.api.error.ErrorCodeException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CommonFuncAbilityTest {

    @InjectMocks
    private CommonFuncAbility commonFuncAbility;

    @Mock
    private MyCommonFuncRepo commonFuncRepo;

    @Mock
    private FunctionConfigRepo configRepo;

    @Mock
    private FunctionConfigItemRepo configItemRepo;

    @Mock
    private FunctionConfigDeptRepo configDeptRepo;

    @Mock
    private FunctionConfigUserRepo configUserRepo;

    @Mock
    private FunctionConfigConverter converter;

    @Mock
    private UserInfoService userInfoService;

    @Mock
    private AssetReceiveAddressProvider addressProvider;

    @Mock
    private ManageLineRepo manageLineRepo;

    @Mock
    private BusinessLineRepo businessLineRepo;

    private User mockUser;
    private EmployeeInfoRes mockEmployeeInfo;

    private List<FunctionConfig> functionConfigs;
    private Map<Integer, List<FunctionConfigItem>> configMap;
    private Map<Integer, List<FunctionConfigDept>> deptMap;
    private List<Integer> authConfigIds;
    private List<FunctionConfigUser> configUsers;
    private List<ManageLineDo> manageLineList;
    private List<BusinessLineDo> businessLineDos;

    @BeforeEach
    void setUp() throws Exception {
        mockUser = new User();
        mockUser.setUserName("testUser");
        mockUser.setDisplayName("Test User");

        mockEmployeeInfo = mock(EmployeeInfoRes.class);
        
        // 使用反射设置 selfAbility
        Field selfAbilityField = CommonFuncAbility.class.getDeclaredField("selfAbility");
        selfAbilityField.setAccessible(true);
        selfAbilityField.set(commonFuncAbility, commonFuncAbility);

        // 初始化测试数据
        functionConfigs = new ArrayList<>();
        configMap = new HashMap<>();
        deptMap = new HashMap<>();
        authConfigIds = new ArrayList<>();
        configUsers = new ArrayList<>();
        manageLineList = new ArrayList<>();
        businessLineDos = new ArrayList<>();

        // 设置FunctionConfig
        FunctionConfig config = new FunctionConfig();
        config.setConfigId(1);
        config.setManageLine("ML001");
        config.setManageLineName("管理线1");
        functionConfigs.add(config);

        // 设置FunctionConfigItem
        FunctionConfigItem item = new FunctionConfigItem();
        item.setItemId(1);
        item.setConfigId(1);
        item.setBusinessLine("BL001");
        item.setBusinessLineName("业务线1");
        item.setAuthType(YesNo.YES.getCode());
        configMap.put(1, Collections.singletonList(item));

        // 设置FunctionConfigDept
        FunctionConfigDept dept = new FunctionConfigDept();
        dept.setConfigId(1);
        dept.setConfigItemId(1);
        deptMap.put(1, Collections.singletonList(dept));

        // 设置authConfigIds
        authConfigIds.add(1);

        // 设置FunctionConfigUser
        FunctionConfigUser user = new FunctionConfigUser();
        user.setManageLine("ML001");
        user.setSort(1);
        configUsers.add(user);

        // 设置ManageLineDo
        ManageLineDo manageLine = new ManageLineDo();
        manageLine.setManageLine("ML001");
        manageLine.setManageLineName("管理线1");
        manageLine.setManageLineNameEn("Manage Line 1");
        manageLineList.add(manageLine);

        // 设置BusinessLineDo
        BusinessLineDo businessLine = new BusinessLineDo();
        businessLine.setBusinessLine("BL001");
        businessLine.setBusinessLineName("业务线1");
        businessLine.setBusinessLineNameEn("Business Line 1");
        businessLineDos.add(businessLine);
    }

    @Test
    void saveMyFunctionsSort_Success() {
        // 准备测试数据
        MyFunctionsSortReq req = new MyFunctionsSortReq();
        req.setResourceCodes(Arrays.asList("RES1", "RES2"));
        req.setManageLineCode("ML1");

        // 使用 MockedStatic 模拟静态方法
        try (MockedStatic<AuthFacade> authFacadeMock = mockStatic(AuthFacade.class)) {
            authFacadeMock.when(AuthFacade::authedUserInfo).thenReturn(mockUser);

            // 执行测试
            commonFuncAbility.saveMyFunctionsSort(req);

            // 验证调用
            verify(commonFuncRepo).saveMyFuncSort(any(MyCommonFunc.class));
        }
    }

    @Test
    void listMyFuncSort_Success() {
        // 准备测试数据
        String manageLineCode = "ML1";
        MyCommonFunc mockCommonFunc = new MyCommonFunc();
        mockCommonFunc.setResourceCodes("RES1,RES2");
        // 使用反射设置isDefault
        try {
            java.lang.reflect.Field field = MyCommonFunc.class.getDeclaredField("isDefault");
            field.setAccessible(true);
            field.set(mockCommonFunc, (byte)1);
        } catch (Exception e) {
            fail("Failed to set isDefault", e);
        }

        // 模拟依赖行为
        when(commonFuncRepo.listMyFuncSort(manageLineCode, mockUser.getUserName()))
                .thenReturn(mockCommonFunc);

        // 使用 MockedStatic 模拟静态方法
        try (MockedStatic<AuthFacade> authFacadeMock = mockStatic(AuthFacade.class)) {
            authFacadeMock.when(AuthFacade::authedUserInfo).thenReturn(mockUser);

            // 执行测试
            MyFunctionsSortResp result = commonFuncAbility.listMyFuncSort(manageLineCode);

            // 验证结果
            assertNotNull(result);
            assertEquals(manageLineCode, result.getManageLineCode());
            assertEquals(Arrays.asList("RES1", "RES2"), result.getResourceCode());
            assertTrue(result.isDefault());
        }
    }

    @Test
    void functionConfig_Success() {
        // 准备测试数据
        String userName = "testUser";
        String country = "CN";
        
        // 创建并设置 FunctionConfigItem
        FunctionConfigItem mockItem = new FunctionConfigItem();
        mockItem.setConfigId(1);
        mockItem.setItemId(1);
        mockItem.setAuthType(YesNo.YES.getCode());
        mockItem.setBusinessLine("BL1");
        mockItem.setBusinessLineName("Business Line 1");
        List<FunctionConfigItem> mockItems = Collections.singletonList(mockItem);
        
        // 创建并设置 FunctionConfig
        FunctionConfig mockConfig = new FunctionConfig();
        mockConfig.setConfigId(1);
        mockConfig.setManageLine("ML1");
        mockConfig.setName("Test Config");
        mockConfig.setNameEn("Test Config EN");
        mockConfig.setItems(mockItems);
        List<FunctionConfig> mockConfigs = Collections.singletonList(mockConfig);
        
        // 创建并设置 FunctionConfigUser
        FunctionConfigUser mockConfigUser = new FunctionConfigUser();
        mockConfigUser.setManageLine("ML1");
        mockConfigUser.setSort(1);
        List<FunctionConfigUser> mockConfigUsers = Collections.singletonList(mockConfigUser);

        // 创建并设置 FunctionConfigDept
        FunctionConfigDept mockConfigDept = new FunctionConfigDept();
        mockConfigDept.setConfigId(1);
        mockConfigDept.setConfigItemId(1);
        mockConfigDept.setDeptCode("DEPT1");
        List<FunctionConfigDept> mockConfigDepts = Collections.singletonList(mockConfigDept);

        // 创建并设置 UserBaseInfoRes
        UserBaseInfoRes mockUserBaseInfo = new UserBaseInfoRes();
        mockUserBaseInfo.setFullDeptDescr("[{\"deptId\":\"DEPT1\",\"deptName\":\"部门1\",\"level\":\"1\"},{\"deptId\":\"DEPT2\",\"deptName\":\"部门2\",\"level\":\"1\"}]");

        // 模拟依赖行为
        when(configItemRepo.getByCountry(country)).thenReturn(mockItems);
        when(userInfoService.getUserInfoByUserName(userName)).thenReturn(mockUserBaseInfo);
        when(configRepo.listByIds(any())).thenReturn(mockConfigs);
        when(configUserRepo.listByUserCode(userName)).thenReturn(mockConfigUsers);
        when(configDeptRepo.listByDeptCodeAndId(any(), any())).thenReturn(mockConfigDepts);
        
        // 模拟 ManageLineRepo
        ManageLineDo manageLineDo = new ManageLineDo();
        manageLineDo.setManageLine("ML1");
        manageLineDo.setManageLineName("Manage Line 1");
        manageLineDo.setManageLineNameEn("Manage Line 1 EN");
        when(manageLineRepo.getManageLine()).thenReturn(Collections.singletonList(manageLineDo));
        
        // 模拟 BusinessLineRepo
        BusinessLineDo businessLineDo = new BusinessLineDo();
        businessLineDo.setBusinessLine("BL1");
        businessLineDo.setBusinessLineName("Business Line 1");
        businessLineDo.setBusinessLineNameEn("Business Line 1 EN");
        when(businessLineRepo.searchAll()).thenReturn(Collections.singletonList(businessLineDo));
        
        // 模拟 FunctionConfigConverter
        FunctionConfigRes mockConfigRes = new FunctionConfigRes();
        mockConfigRes.setConfigId(1);
        mockConfigRes.setManageLine("ML1");
        mockConfigRes.setName("Test Config");
        mockConfigRes.setNameEn("Test Config EN");
        when(converter.toConfigRes(any())).thenReturn(mockConfigRes);

        // 执行测试
        List<FunctionConfigRes> result = commonFuncAbility.functionConfig(userName, country, EAMConstants.CHINESE);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals("ML1", result.get(0).getManageLine());
        verify(configItemRepo).getByCountry(country);
        verify(userInfoService).getUserInfoByUserName(userName);
        verify(configRepo).listByIds(any());
        verify(configUserRepo).listByUserCode(userName);
        verify(configDeptRepo).listByDeptCodeAndId(any(), any());
        verify(manageLineRepo).getManageLine();
        verify(businessLineRepo).searchAll();
    }

    @Test
    void functionConfig_InvalidUserName() {
        // 测试空用户名
        assertThrows(ErrorCodeException.class, () -> commonFuncAbility.functionConfig("", "CN", EAMConstants.CHINESE));
    }

    @Test
    void personalConfig_Success() {
        // 准备测试数据
        SaveFunctionConfigUserReq req = new SaveFunctionConfigUserReq();
        req.setManageLine("ML1");
        List<SaveFunctionConfigUserReq> reqs = Collections.singletonList(req);

        // 使用 MockedStatic 模拟静态方法
        try (MockedStatic<AuthFacade> authFacadeMock = mockStatic(AuthFacade.class)) {
            authFacadeMock.when(AuthFacade::authedUserInfo).thenReturn(mockUser);

            // 模拟依赖行为
            when(configUserRepo.listByUserCode(mockUser.getUserName()))
                    .thenReturn(Collections.emptyList());
            when(converter.reqToConfigUser(any(), any())).thenReturn(new FunctionConfigUser());

            // 执行测试
            commonFuncAbility.personalConfig(reqs);

            // 验证调用
            verify(configUserRepo).saveConfigUser(any());
        }
    }

    @Test
    void personalConfig_InvalidRequest() {
        // 测试空请求
        assertThrows(ErrorCodeException.class, () -> commonFuncAbility.personalConfig(null));
    }

    @Test
    void submit_Success() {
        // 准备测试数据
        SaveFunctionConfigReq req = new SaveFunctionConfigReq();
        req.setManageLine("ML1");
        req.setName("Test Config");
        req.setNameEn("Test Config EN");
        
        SaveFunctionConfigItemReq itemReq = new SaveFunctionConfigItemReq();
        itemReq.setBusinessLine("BL1");
        itemReq.setBusinessLineName("Business Line 1");
        itemReq.setAuthType(YesNo.NO.getCode());
        
        Country country = new Country();
        country.setCountry("CN");
        country.setCountryName("China");
        itemReq.setCountries(Collections.singletonList(country));
        
        req.setItems(Collections.singletonList(itemReq));

        // 模拟依赖行为
        lenient().when(configRepo.getConfig(req.getManageLine(), req.getName(), null)).thenReturn(null);
        lenient().when(configRepo.getConfig(req.getManageLine(), null, req.getNameEn())).thenReturn(null);
        
        // 创建并设置 FunctionConfig
        FunctionConfig mockConfig = new FunctionConfig();
        mockConfig.setConfigId(1);
        mockConfig.setManageLine(req.getManageLine());
        mockConfig.setName(req.getName());
        mockConfig.setNameEn(req.getNameEn());
        
        // 创建并设置 FunctionConfigItem
        FunctionConfigItem mockItem = new FunctionConfigItem();
        mockItem.setConfigId(1);
        mockItem.setItemId(1);
        mockItem.setBusinessLine(itemReq.getBusinessLine());
        mockItem.setBusinessLineName(itemReq.getBusinessLineName());
        mockItem.setAuthType(itemReq.getAuthType());
        mockItem.setAuthDeptList(Collections.emptyList());
        mockConfig.setItems(Collections.singletonList(mockItem));
        
        when(converter.reqToConfig(any())).thenReturn(mockConfig);
        when(configRepo.saveFunctionConfig(any())).thenReturn(1);
        when(configItemRepo.saveConfigItems(any())).thenReturn(Collections.singletonList(mockItem));

        // 执行测试
        commonFuncAbility.submit(req);

        // 验证调用
        verify(configRepo).saveFunctionConfig(any());
        verify(configItemRepo).saveConfigItems(any());
        verify(configItemRepo).deleteByConfigId(anyInt());
    }

    @Test
    void submit_InvalidRequest() {
        // 测试空请求
        assertThrows(ErrorCodeException.class, () -> commonFuncAbility.submit(null));
    }

    @Test
    void submit_DuplicateConfig() {
        // 准备测试数据
        SaveFunctionConfigReq req = new SaveFunctionConfigReq();
        req.setManageLine("ML1");
        req.setName("Test Config");
        req.setNameEn("Test Config EN");
        
        SaveFunctionConfigItemReq itemReq = new SaveFunctionConfigItemReq();
        itemReq.setBusinessLine("BL1");
        itemReq.setBusinessLineName("Business Line 1");
        itemReq.setAuthType(YesNo.NO.getCode());
        
        Country country = new Country();
        country.setCountry("CN");
        country.setCountryName("China");
        itemReq.setCountries(Collections.singletonList(country));
        
        req.setItems(Collections.singletonList(itemReq));
        
        FunctionConfig existingConfig = new FunctionConfig();
        existingConfig.setConfigId(2); // 设置不同的 configId
        
        // 模拟依赖行为
        lenient().when(configRepo.getConfig(req.getManageLine(), req.getName(), null)).thenReturn(existingConfig);
        lenient().when(configRepo.getConfig(req.getManageLine(), null, req.getNameEn())).thenReturn(null);

        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> commonFuncAbility.submit(req));
        assertTrue(exception.getMessage().contains("不能重复添加"));
    }

    @Test
    void submit_InvalidBusinessLine() {
        // 准备测试数据
        SaveFunctionConfigReq req = new SaveFunctionConfigReq();
        req.setManageLine("ML1");
        req.setName("Test Config");
        
        SaveFunctionConfigItemReq itemReq = new SaveFunctionConfigItemReq();
        // 不设置业务线，应该抛出异常
        req.setItems(Collections.singletonList(itemReq));

        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> commonFuncAbility.submit(req));
        assertTrue(exception.getMessage().contains("业务线编码不能为空"));
    }

    @Test
    void submit_InvalidCountry() {
        // 准备测试数据
        SaveFunctionConfigReq req = new SaveFunctionConfigReq();
        req.setManageLine("ML1");
        req.setName("Test Config");
        
        SaveFunctionConfigItemReq itemReq = new SaveFunctionConfigItemReq();
        itemReq.setBusinessLine("BL1");
        itemReq.setBusinessLineName("Business Line 1");
        // 不设置国家信息，应该抛出异常
        req.setItems(Collections.singletonList(itemReq));

        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> commonFuncAbility.submit(req));
        assertTrue(exception.getMessage().contains("国家或地区不能为空"));
    }

    @Test
    void converterConfigRes_Success() {
        // 设置mock行为
        when(manageLineRepo.getManageLine()).thenReturn(manageLineList);
        when(businessLineRepo.searchAll()).thenReturn(businessLineDos);
        when(converter.toConfigRes(any())).thenReturn(new FunctionConfigRes());

        // 执行测试
        List<FunctionConfigRes> result = commonFuncAbility.converterConfigRes(
                functionConfigs, configMap, deptMap, authConfigIds, configUsers, EAMConstants.CHINESE);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void converterConfigRes_English() {
        // 设置mock行为
        when(manageLineRepo.getManageLine()).thenReturn(manageLineList);
        when(businessLineRepo.searchAll()).thenReturn(businessLineDos);
        when(converter.toConfigRes(any())).thenReturn(new FunctionConfigRes());

        // 执行测试
        List<FunctionConfigRes> result = commonFuncAbility.converterConfigRes(
                functionConfigs, configMap, deptMap, authConfigIds, configUsers, "en");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void buildBusinessLineItem_Success() {
        // 准备测试数据
        FunctionConfigItem item = new FunctionConfigItem();
        item.setBusinessLine("BL001");
        item.setBusinessLineName("业务线1");
        List<FunctionConfigItemRes> itemResList = new ArrayList<>();
        Map<String, BusinessLineDo> businessLineMap = new HashMap<>();
        BusinessLineDo businessLine = new BusinessLineDo();
        businessLine.setBusinessLine("BL001");
        businessLine.setBusinessLineNameEn("Business Line 1");
        businessLineMap.put("BL001", businessLine);

        // 执行测试 - 中文
        commonFuncAbility.buildBusinessLineItem(item, true, businessLineMap, itemResList);
        assertEquals(1, itemResList.size());
        assertEquals("业务线1", itemResList.get(0).getBusinessLineName());

        // 清空结果列表
        itemResList.clear();

        // 执行测试 - 英文
        commonFuncAbility.buildBusinessLineItem(item, false, businessLineMap, itemResList);
        assertEquals(1, itemResList.size());
        assertEquals("Business Line 1", itemResList.get(0).getBusinessLineName());
    }

    @Test
    void buildBusinessLineItem_WithNullBusinessLine() {
        // 准备测试数据
        FunctionConfigItem item = new FunctionConfigItem();
        item.setBusinessLine("BL001");
        item.setBusinessLineName("业务线1");
        List<FunctionConfigItemRes> itemResList = new ArrayList<>();
        Map<String, BusinessLineDo> businessLineMap = new HashMap<>();

        // 执行测试 - 中文
        commonFuncAbility.buildBusinessLineItem(item, true, businessLineMap, itemResList);
        assertEquals(1, itemResList.size());
        assertEquals("业务线1", itemResList.get(0).getBusinessLineName());
        assertEquals("BL001", itemResList.get(0).getBusinessLine());

        // 清空结果列表
        itemResList.clear();

        // 执行测试 - 英文
        commonFuncAbility.buildBusinessLineItem(item, false, businessLineMap, itemResList);
        assertEquals(1, itemResList.size());
        //assertEquals("", itemResList.get(0).getBusinessLineName());
        assertEquals("BL001", itemResList.get(0).getBusinessLine());
    }

    @Test
    void functionConfigByManageLine_Success() {
        // 准备测试数据
        String manageLine = "ML1";
        
        // 创建并设置 Country
        Country mockCountry = new Country();
        mockCountry.setCountry("CN");
        mockCountry.setCountryName("China");
        List<Country> mockCountries = Collections.singletonList(mockCountry);
        
        // 创建并设置 FunctionConfigItem
        FunctionConfigItem mockItem = new FunctionConfigItem();
        mockItem.setConfigId(1);
        mockItem.setItemId(1);
        mockItem.setAuthType(YesNo.YES.getCode());
        mockItem.setBusinessLine("BL1");
        mockItem.setBusinessLineName("Business Line 1");
        mockItem.setCountries(mockCountries);
        List<FunctionConfigItem> mockItems = Collections.singletonList(mockItem);
        
        // 创建并设置 FunctionConfig
        FunctionConfig mockConfig = new FunctionConfig();
        mockConfig.setConfigId(1);
        mockConfig.setManageLine(manageLine);
        mockConfig.setName("Test Config");
        mockConfig.setNameEn("Test Config EN");
        mockConfig.setItems(mockItems);
        List<FunctionConfig> mockConfigs = Collections.singletonList(mockConfig);

        // 创建并设置 FunctionConfigDept
        FunctionConfigDept mockDept = new FunctionConfigDept();
        mockDept.setConfigId(1);
        mockDept.setConfigItemId(1);
        mockDept.setDeptCode("DEPT1");
        mockDept.setDeptName("部门1");
        List<FunctionConfigDept> mockDepts = Collections.singletonList(mockDept);

        // 模拟依赖行为
        when(configRepo.listByManageLine(manageLine)).thenReturn(mockConfigs);
        when(configItemRepo.listByConfigIds(any())).thenReturn(mockItems);
        when(configDeptRepo.listByDeptCodeAndId(any(), any())).thenReturn(mockDepts);
        when(addressProvider.getCountryV1()).thenReturn(mockCountries);
        
        // 模拟 FunctionConfigConverter
        FunctionConfigRes mockConfigRes = new FunctionConfigRes();
        mockConfigRes.setConfigId(1);
        mockConfigRes.setManageLine(manageLine);
        mockConfigRes.setName("Test Config");
        mockConfigRes.setNameEn("Test Config EN");
        when(converter.toConfigRes(any())).thenReturn(mockConfigRes);
        
        // 模拟 FunctionConfigItemRes
        FunctionConfigItemRes mockItemRes = new FunctionConfigItemRes();
        mockItemRes.setItemId(1);
        mockItemRes.setConfigId(1);
        mockItemRes.setBusinessLine("BL1");
        mockItemRes.setBusinessLineName("Business Line 1");
        mockItemRes.setCountries(mockCountries);
        when(converter.toItemRes(any())).thenReturn(mockItemRes);
        
        // 模拟 FunctionConfigDeptRes
        FunctionConfigDeptRes mockDeptRes = new FunctionConfigDeptRes();
        mockDeptRes.setConfigItemId(1);
        mockDeptRes.setDeptCode("DEPT1");
        mockDeptRes.setDeptName("部门1");
        when(converter.toDeptResList(any())).thenReturn(Collections.singletonList(mockDeptRes));

        // 执行测试
        List<FunctionConfigRes> result = commonFuncAbility.functionConfigByManageLine(manageLine);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        verify(configRepo).listByManageLine(manageLine);
        verify(configItemRepo).listByConfigIds(any());
        verify(configDeptRepo).listByDeptCodeAndId(any(), any());
        verify(addressProvider).getCountryV1();
        verify(converter).toConfigRes(any());
        verify(converter).toItemRes(any());
        verify(converter).toDeptResList(any());
    }

    @Test
    void functionConfigByManageLine_EmptyManageLine() {
        // 测试空管理线
        List<FunctionConfigRes> result = commonFuncAbility.functionConfigByManageLine("");
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void deleteFunctionConfig_Success() {
        // 准备测试数据
        DelByIdsReq req = new DelByIdsReq();
        req.setIds(Collections.singletonList(1));
        
        // 执行测试
        commonFuncAbility.deleteFunctionConfig(req);

        // 验证调用
        verify(configRepo).deleteByConfigIds(req.getIds());
        verify(configItemRepo).deleteByConfigIds(req.getIds());
        verify(configDeptRepo).deleteAuthDeptByConfigIds(req.getIds());
    }

    @Test
    void deleteFunctionConfig_EmptyIds() {
        // 准备测试数据
        DelByIdsReq req = new DelByIdsReq();
        req.setIds(Collections.emptyList());
        
        // 执行测试
        commonFuncAbility.deleteFunctionConfig(req);

        // 验证没有调用删除方法
        verify(configRepo, never()).deleteByConfigIds(any());
        verify(configItemRepo, never()).deleteByConfigIds(any());
        verify(configDeptRepo, never()).deleteAuthDeptByConfigIds(any());
    }

    @Test
    void deleteFunctionConfig_NullRequest() {
        // 测试空请求
        try {
            commonFuncAbility.deleteFunctionConfig(null);
            fail("Expected NullPointerException to be thrown");
        } catch (NullPointerException e) {
            // 期望抛出 NullPointerException
            assertTrue(true);
        }
    }

    @Test
    void converterConfigRes_WithEmptyInput() {
        // 测试空输入
        List<FunctionConfigRes> result = commonFuncAbility.converterConfigRes(
                Collections.emptyList(), 
                Collections.emptyMap(), 
                Collections.emptyMap(), 
                Collections.emptyList(), 
                Collections.emptyList(), 
                EAMConstants.CHINESE);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void converterConfigRes_WithNullBusinessLine() {
        // 准备测试数据
        FunctionConfig config = new FunctionConfig();
        config.setConfigId(1);
        config.setManageLine("ML1");
        
        FunctionConfigItem item = new FunctionConfigItem();
        item.setConfigId(1);
        item.setItemId(1);
        item.setBusinessLine(null);
        item.setBusinessLineName("Test Business Line");
        item.setAuthType(YesNo.NO.getCode());  // 设置为全员开放
        
        Map<Integer, List<FunctionConfigItem>> configMap = new HashMap<>();
        configMap.put(1, Collections.singletonList(item));
        
        // 设置mock行为
        when(manageLineRepo.getManageLine()).thenReturn(manageLineList);
        when(businessLineRepo.searchAll()).thenReturn(businessLineDos);
        when(converter.toConfigRes(any())).thenReturn(new FunctionConfigRes());

        // 执行测试
        List<FunctionConfigRes> result = commonFuncAbility.converterConfigRes(
                Collections.singletonList(config),
                configMap,
                Collections.emptyMap(),
                Collections.singletonList(1),
                Collections.emptyList(),
                EAMConstants.CHINESE);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void functionConfig_WithEmptyConfigs() {
        // 准备测试数据
        String userName = "testUser";
        String country = "CN";

        // 模拟依赖行为
        when(configItemRepo.getByCountry(country)).thenReturn(Collections.emptyList());

        // 执行测试
        List<FunctionConfigRes> result = commonFuncAbility.functionConfig(userName, country, EAMConstants.CHINESE);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @Disabled
    void functionConfig_WithInvalidDeptInfo() {
        // 准备测试数据
        String userName = "testUser";
        String country = "CN";
        
        // 创建并设置 UserBaseInfoRes，包含无效的部门信息
        UserBaseInfoRes mockUserBaseInfo = new UserBaseInfoRes();
        mockUserBaseInfo.setFullDeptDescr("invalid json");

        // 创建并设置 FunctionConfigItem
        FunctionConfigItem mockItem = new FunctionConfigItem();
        mockItem.setConfigId(1);
        mockItem.setItemId(1);
        mockItem.setAuthType(YesNo.YES.getCode());  // 设置为需要部门授权
        mockItem.setBusinessLine("BL1");
        mockItem.setBusinessLineName("Business Line 1");
        List<FunctionConfigItem> mockItems = Collections.singletonList(mockItem);

        // 创建并设置 FunctionConfig
        FunctionConfig mockConfig = new FunctionConfig();
        mockConfig.setConfigId(1);
        mockConfig.setManageLine("ML1");
        mockConfig.setName("Test Config");
        mockConfig.setNameEn("Test Config EN");
        List<FunctionConfig> mockConfigs = Collections.singletonList(mockConfig);

        // 模拟依赖行为
        when(configItemRepo.getByCountry(country)).thenReturn(mockItems);
        when(userInfoService.getUserInfoByUserName(userName)).thenReturn(mockUserBaseInfo);
        when(configRepo.listByIds(any())).thenReturn(mockConfigs);

        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> 
            commonFuncAbility.functionConfig(userName, country, EAMConstants.CHINESE));
        assertTrue(exception.getMessage().contains("queryDeptError"));
    }
}
