package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.domain.international.entity.CountryCurrencyDo;
import com.mi.oa.asset.commons.config.domain.international.enums.DefaultDataEnums;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryCurrencyRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CountryCurrencyAbilityTest {
    @InjectMocks
    private CountryCurrencyAbility ability;
    @Mock
    private CountryCurrencyRepo currencyRepo;

    @Test
    void check_duplicateCurrency() {
        CountryCurrencyDo entity = new CountryCurrencyDo();
        entity.setCountryConfigId(1);
        entity.setCurrencyCode("CNY");
        entity.setId(null);
        CountryCurrencyDo exist = new CountryCurrencyDo();
        exist.setId(2);
        exist.setCurrencyCode("CNY");
        List<CountryCurrencyDo> existList = Collections.singletonList(exist);
        when(currencyRepo.getByCountryId(1)).thenReturn(existList);
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.check(entity));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
        assertTrue(ex.getMessage().contains("货币代码"));
    }

    @Test
    void check_duplicateDefaultCurrency() {
        CountryCurrencyDo entity = new CountryCurrencyDo();
        entity.setCountryConfigId(1);
        entity.setDefaultCurrency(DefaultDataEnums.DEFAULTCURRENCY.getKey());
        entity.setId(null);
        CountryCurrencyDo exist = new CountryCurrencyDo();
        exist.setId(2);
        exist.setDefaultCurrency(DefaultDataEnums.DEFAULTCURRENCY.getKey());
        List<CountryCurrencyDo> existList = Collections.singletonList(exist);
        when(currencyRepo.getByCountryId(1)).thenReturn(existList);
        // 先通过currencyCode校验，再通过defaultCurrency校验
        // 需要让currencyCode不同，才能走到defaultCurrency校验
        entity.setCurrencyCode("USD");
        exist.setCurrencyCode("EUR");
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.check(entity));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
        assertTrue(ex.getMessage().contains("默认的币种"));
    }

    @Test
    void check_noConflict() {
        CountryCurrencyDo entity = new CountryCurrencyDo();
        entity.setCountryConfigId(1);
        entity.setCurrencyCode("CNY");
        entity.setDefaultCurrency(DefaultDataEnums.DEFAULTCURRENCY.getKey());
        entity.setId(1);
        CountryCurrencyDo exist = new CountryCurrencyDo();
        exist.setId(2);
        exist.setCurrencyCode("USD");
        exist.setDefaultCurrency(0);
        List<CountryCurrencyDo> existList = Collections.singletonList(exist);
        when(currencyRepo.getByCountryId(1)).thenReturn(existList);
        assertDoesNotThrow(() -> ability.check(entity));
    }

    @Test
    void check_emptyList() {
        CountryCurrencyDo entity = new CountryCurrencyDo();
        entity.setCountryConfigId(1);
        when(currencyRepo.getByCountryId(1)).thenReturn(Collections.emptyList());
        assertDoesNotThrow(() -> ability.check(entity));
    }
}
