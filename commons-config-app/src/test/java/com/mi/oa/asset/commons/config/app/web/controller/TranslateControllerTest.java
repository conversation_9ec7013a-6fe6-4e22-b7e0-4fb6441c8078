package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.translate.NeputunReq;
import com.mi.oa.asset.commons.config.api.translate.TranslateProvider;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TranslateControllerTest {
    @InjectMocks
    private TranslateController controller;
    @Mock
    private TranslateProvider translateProvider;

    @Test
    void translateFromKey_normal() {
        NeputunReq req = new NeputunReq();
        req.setKey("k");
        req.setLang("zh");
        when(translateProvider.getTranslateFromKey("k", "zh")).thenReturn("v");
        Result<Object> result = controller.translateFromKey(req);
        assertEquals(0, result.getCode());
        assertEquals("v", result.getData());
    }

    @Test
    void getTranslateData_normal() {
        NeputunReq req = new NeputunReq();
        when(translateProvider.getTranslateJsonData(req)).thenReturn("json");
        Result<Object> result = controller.getTranslateData(req);
        assertEquals(0, result.getCode());
        assertEquals("json", result.getData());
    }
} 