package com.mi.oa.asset.commons.config.app;

import com.alibaba.fastjson.JSON;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgProvider;
import com.mi.oa.asset.commons.config.api.datarange.enums.DataRange;
import com.mi.oa.asset.commons.config.api.serialcode.RollingType;
import com.mi.oa.asset.commons.config.api.serialcode.SerialCodeProvider;
import com.mi.oa.asset.commons.config.app.scheduler.SyncDepartment;
import com.mi.oa.asset.commons.config.domain.assetorganization.repository.AssetOrgRepo;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgStructure;
import com.mi.oa.asset.commons.config.infra.common.CacheKey;
import com.mi.oa.asset.eam.feign.client.HrodClient;
import com.mi.oa.asset.eam.feign.req.HrodEmployeeConditionReq;
import com.mi.oa.asset.eam.feign.res.HrodEmployeeRes;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest(classes = StartApplication.class)
@Disabled
public class TestApplication {

    @Resource
    SerialCodeProvider serialCodeProvider;

    @Resource
    SyncDepartment syncDepartment;

    @Resource
    AssetOrgRepo assetOrgRepo;

    @Resource
    AssetOrgProvider assetOrgProvider;

    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }

    @Test
    public void serialCodeTest() {
        System.out.println(serialCodeProvider.genSerialCodes("TEST", RollingType.YMD, 6, 5, null));

        System.out.println(serialCodeProvider.genSerialCodes("S", null, 6, 5, null));
    }

    @Test
    public void testCacheAssetOrg() {
        syncDepartment.execute();
        int maxLevel = (int) RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_LEVEL_MAX.getKey());
        System.out.println("max level: " + maxLevel);

        System.out.println(System.currentTimeMillis());
        for (int i = 1; i <= maxLevel; i++) {
            List<AssetOrgStructure> data = assetOrgRepo.getOrgStructuresByLevel(i);
            System.out.println(i);
            System.out.println(data.size());
        }
        System.out.println(System.currentTimeMillis());
    }

    @Test
    public void testGetAssetOrg() {
//        int maxLevel = (int) RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_LEVEL_MAX.getKey());
//        System.out.println("max level: " + maxLevel);
//
//        System.out.println(System.currentTimeMillis());
//        assetOrgProvider.getOrgStructureWithSub("AM");
//        System.out.println(System.currentTimeMillis());
        syncDepartment.cacheAssetOrgStructureByLevel();
        System.out.println(assetOrgProvider.getOrgStructureByCode("AM"));
        System.out.println(assetOrgProvider.getOrgStructureByCode("IT550105"));

    }

    @Resource
    private HrodClient hrodClient;

    @Test
    public void testHrodClient() {
        String userName = ("chendaiquan");
        HrodEmployeeConditionReq build = HrodEmployeeConditionReq.builder().userName(userName).build();
        HrodEmployeeRes hrodEmployeeRes = hrodClient.getEmployeeByUserName(build);
        System.out.println(hrodEmployeeRes.getMiCompanyCode() + hrodEmployeeRes.getCompanyName());
        System.out.println(hrodEmployeeRes);
        System.out.println(JSON.toJSONString(hrodEmployeeRes));
    }
}

