package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.function.*;
import com.mi.oa.asset.commons.config.app.ability.FunctionAbility;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.RoleResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.remote.RoleService;
import com.mi.oa.infra.uc.common.enmu.RoleStatusEnum;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.error.ErrorCodeException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FunctionProviderImplTest {

    @InjectMocks
    private FunctionProviderImpl functionProvider;

    @Mock
    private FunctionAbility functionAbility;

    @Mock
    private RoleService roleService;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    void listDictRelByBusinessLine_ValidRequest_ReturnsDictList() {
        // 准备测试数据
        DictBusinessLineQueryReq queryReq = new DictBusinessLineQueryReq();
        queryReq.setCode("TEST_CODE");
        queryReq.setBusinessLine("TEST_BUSINESS_LINE");
        queryReq.setManageLine("TEST_MANAGE_LINE");
        queryReq.setFuncCode("TEST_FUNC_CODE");

        List<DictRes> expectedDictList = Arrays.asList(
            createDictRes("CODE1", "NAME1"),
            createDictRes("CODE2", "NAME2")
        );

        // 设置mock行为
        when(functionAbility.listDictRelByBusinessLine(any())).thenReturn(expectedDictList);

        // 执行测试
        List<DictRes> result = functionProvider.listDictRelByBusinessLine(queryReq);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedDictList.size(), result.size());
        assertEquals(expectedDictList.get(0).getCode(), result.get(0).getCode());
        assertEquals(expectedDictList.get(1).getCode(), result.get(1).getCode());
    }

    @Test
    void listDictRelByBusinessLine_EmptyResult_ReturnsEmptyList() {
        // 准备测试数据
        DictBusinessLineQueryReq queryReq = new DictBusinessLineQueryReq();
        queryReq.setCode("TEST_CODE");
        queryReq.setBusinessLine("TEST_BUSINESS_LINE");
        queryReq.setManageLine("TEST_MANAGE_LINE");
        queryReq.setFuncCode("TEST_FUNC_CODE");

        // 设置mock行为
        when(functionAbility.listDictRelByBusinessLine(any())).thenReturn(Collections.emptyList());

        // 执行测试
        List<DictRes> result = functionProvider.listDictRelByBusinessLine(queryReq);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void listDictRelByBusinessLine_NullRequest_ThrowsException() {
        // 执行测试并验证异常
        assertThrows(NullPointerException.class, () -> {
            functionProvider.listDictRelByBusinessLine(null);
        });
    }

    @Test
    void listDictRelByBusinessLineBatch_ValidRequest_ReturnsDictMap() {
        // 准备测试数据
        DictBusinessLineBatchReq queryReq = new DictBusinessLineBatchReq();
        queryReq.setCode(Arrays.asList("CODE1", "CODE2"));
        queryReq.setBusinessLine("TEST_BUSINESS_LINE");
        queryReq.setManageLine("TEST_MANAGE_LINE");
        queryReq.setFuncCode("TEST_FUNC_CODE");

        Map<String, List<DictRes>> expectedDictMap = new HashMap<>();
        expectedDictMap.put("CODE1", Arrays.asList(createDictRes("CODE1", "NAME1")));
        expectedDictMap.put("CODE2", Arrays.asList(createDictRes("CODE2", "NAME2")));

        // 设置mock行为
        when(functionAbility.listDictRelByBusinessLineBatch(any())).thenReturn(expectedDictMap);

        // 执行测试
        Map<String, List<DictRes>> result = functionProvider.listDictRelByBusinessLineBatch(queryReq);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedDictMap.size(), result.size());
        assertTrue(result.containsKey("CODE1"));
        assertTrue(result.containsKey("CODE2"));
        assertEquals(expectedDictMap.get("CODE1").size(), result.get("CODE1").size());
        assertEquals(expectedDictMap.get("CODE2").size(), result.get("CODE2").size());
    }

    @Test
    void listDictRelByBusinessLineBatch_EmptyCodeList_ReturnsEmptyMap() {
        // 准备测试数据
        DictBusinessLineBatchReq queryReq = new DictBusinessLineBatchReq();
        queryReq.setCode(Collections.emptyList());
        queryReq.setBusinessLine("TEST_BUSINESS_LINE");
        queryReq.setManageLine("TEST_MANAGE_LINE");
        queryReq.setFuncCode("TEST_FUNC_CODE");

        // 设置mock行为
        when(functionAbility.listDictRelByBusinessLineBatch(any())).thenReturn(Collections.emptyMap());

        // 执行测试
        Map<String, List<DictRes>> result = functionProvider.listDictRelByBusinessLineBatch(queryReq);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void listDictRelByBusinessLineBatch_NullRequest_ThrowsException() {
        // 执行测试并验证异常
        assertThrows(NullPointerException.class, () -> {
            functionProvider.listDictRelByBusinessLineBatch(null);
        });
    }

    @Test
    void listGlobalVal_ValidRequest_ReturnsPageData() {
        // 准备测试数据
        GlobalValQueryReq queryReq = new GlobalValQueryReq();
        queryReq.setManageType(1);
        queryReq.setBusinessLine("TEST_BUSINESS_LINE");
        queryReq.setKey("TEST_KEY");

        List<GlobalValRes> globalValList = Arrays.asList(
            createGlobalValRes("CODE1", "VALUE1"),
            createGlobalValRes("CODE2", "VALUE2")
        );
        PageData<GlobalValRes> expectedPageData = new PageData<>(globalValList, 10, 1, 2);

        // 设置mock行为
        when(functionAbility.listGlobalVal(any())).thenReturn(expectedPageData);

        // 执行测试
        PageData<GlobalValRes> result = functionProvider.listGlobalVal(queryReq);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedPageData.getList().size(), result.getList().size());
        assertEquals(expectedPageData.getTotal(), result.getTotal());
        assertEquals(expectedPageData.getPageSize(), result.getPageSize());
        assertEquals(expectedPageData.getPageNum(), result.getPageNum());
    }

    @Test
    void listGlobalVal_EmptyResult_ReturnsEmptyPageData() {
        // 准备测试数据
        GlobalValQueryReq queryReq = new GlobalValQueryReq();
        queryReq.setManageType(1);
        queryReq.setBusinessLine("TEST_BUSINESS_LINE");
        queryReq.setKey("TEST_KEY");

        // 设置mock行为
        PageData<GlobalValRes> emptyPageData = new PageData<>(Collections.emptyList(), 10, 1, 0);
        when(functionAbility.listGlobalVal(any())).thenReturn(emptyPageData);

        // 执行测试
        PageData<GlobalValRes> result = functionProvider.listGlobalVal(queryReq);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        assertEquals(0, result.getTotal());
        assertEquals(10, result.getPageSize());
        assertEquals(1, result.getPageNum());
    }

    @Test
    void listGlobalVal_NullRequest_ThrowsException() {
        // 执行测试并验证异常
        assertThrows(NullPointerException.class, () -> {
            functionProvider.listGlobalVal(null);
        });
    }

    @Test
    void listGlobalVal_InvalidPageSize_ReturnsDefaultPageSize() {
        // 准备测试数据
        GlobalValQueryReq queryReq = new GlobalValQueryReq();
        queryReq.setManageType(1);
        queryReq.setBusinessLine("TEST_BUSINESS_LINE");
        queryReq.setKey("TEST_KEY");
        queryReq.setPageSize(0); // 无效的页面大小

        List<GlobalValRes> globalValList = Arrays.asList(
            createGlobalValRes("CODE1", "VALUE1"),
            createGlobalValRes("CODE2", "VALUE2")
        );
        PageData<GlobalValRes> expectedPageData = new PageData<>(globalValList, 10, 1, 2);

        // 设置mock行为
        when(functionAbility.listGlobalVal(any())).thenReturn(expectedPageData);

        // 执行测试
        PageData<GlobalValRes> result = functionProvider.listGlobalVal(queryReq);

        // 验证结果
        assertNotNull(result);
        assertEquals(10, result.getPageSize()); // 应该使用默认页面大小
    }

    @Test
    void getGlobalVal_ValidRequest_ReturnsGlobalValList() {
        // 准备测试数据
        List<String> codes = Arrays.asList("CODE1", "CODE2");
        String businessLine = "TEST_BUSINESS_LINE";
        
        List<GlobalValRes> expectedList = Arrays.asList(
            createGlobalValRes("CODE1", "VALUE1"),
            createGlobalValRes("CODE2", "VALUE2")
        );

        // 设置mock行为
        when(functionAbility.getGlobalVal(codes, businessLine)).thenReturn(expectedList);

        // 执行测试
        List<GlobalValRes> result = functionProvider.getGlobalVal(codes, businessLine);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedList.size(), result.size());
        assertEquals(expectedList.get(0).getCode(), result.get(0).getCode());
        assertEquals(expectedList.get(0).getValue(), result.get(0).getValue());
        assertEquals(expectedList.get(1).getCode(), result.get(1).getCode());
        assertEquals(expectedList.get(1).getValue(), result.get(1).getValue());
    }

    @Test
    void getGlobalVal_EmptyCodeList_ThrowsException() {
        // 准备测试数据
        List<String> emptyCodes = Collections.emptyList();
        String businessLine = "TEST_BUSINESS_LINE";

        // 执行测试并验证异常
        assertThrows(ErrorCodeException.class, () -> {
            functionProvider.getGlobalVal(emptyCodes, businessLine);
        });
    }

    @Test
    void getGlobalVal_NullCodeList_ThrowsException() {
        // 准备测试数据
        String businessLine = "TEST_BUSINESS_LINE";

        // 执行测试并验证异常
        assertThrows(ErrorCodeException.class, () -> {
            functionProvider.getGlobalVal(null, businessLine);
        });
    }

    @Test
    void getGlobalVal_NoResults_ReturnsNull() {
        // 准备测试数据
        List<String> codes = Arrays.asList("CODE1", "CODE2");
        String businessLine = "TEST_BUSINESS_LINE";

        // 设置mock行为
        when(functionAbility.getGlobalVal(codes, businessLine)).thenReturn(null);

        // 执行测试
        List<GlobalValRes> result = functionProvider.getGlobalVal(codes, businessLine);

        // 验证结果
        assertNull(result);
    }

    @Test
    void listDownTask_ValidRequest_ReturnsPageData() {
        // 准备测试数据
        DownTaskQueryReq queryReq = new DownTaskQueryReq();
        queryReq.setPageNum(1);
        queryReq.setPageSize(10);

        List<DownTaskRes> expectedList = Arrays.asList(
            createDownTaskRes(1, "Task1", "SUCCESS"),
            createDownTaskRes(2, "Task2", "PENDING")
        );
        PageData<DownTaskRes> expectedPageData = new PageData<>(expectedList, 10, 1, 2);

        // 设置mock行为
        when(functionAbility.listDownTask(any())).thenReturn(expectedPageData);

        // 执行测试
        PageData<DownTaskRes> result = functionProvider.listDownTask(queryReq);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedPageData.getList().size(), result.getList().size());
        assertEquals(expectedPageData.getTotal(), result.getTotal());
        assertEquals(expectedPageData.getPageSize(), result.getPageSize());
        assertEquals(expectedPageData.getPageNum(), result.getPageNum());
    }

    @Test
    void listDownTask_WithKeyword_ReturnsFilteredResults() {
        // 准备测试数据
        DownTaskQueryReq queryReq = new DownTaskQueryReq();
        queryReq.setPageNum(1);
        queryReq.setPageSize(10);
        queryReq.setKey("Task1");

        List<DownTaskRes> expectedList = Arrays.asList(
            createDownTaskRes(1, "Task1", "SUCCESS")
        );
        PageData<DownTaskRes> expectedPageData = new PageData<>(expectedList, 10, 1, 1);

        // 设置mock行为
        when(functionAbility.listDownTask(any())).thenReturn(expectedPageData);

        // 执行测试
        PageData<DownTaskRes> result = functionProvider.listDownTask(queryReq);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        assertEquals("Task1", result.getList().get(0).getTaskName());
    }

    @Test
    void listDownTask_WithTaskStatus_ReturnsFilteredResults() {
        // 准备测试数据
        DownTaskQueryReq queryReq = new DownTaskQueryReq();
        queryReq.setPageNum(1);
        queryReq.setPageSize(10);
        queryReq.setTaskStatus("SUCCESS");

        List<DownTaskRes> expectedList = Arrays.asList(
            createDownTaskRes(1, "Task1", "SUCCESS")
        );
        PageData<DownTaskRes> expectedPageData = new PageData<>(expectedList, 10, 1, 1);

        // 设置mock行为
        when(functionAbility.listDownTask(any())).thenReturn(expectedPageData);

        // 执行测试
        PageData<DownTaskRes> result = functionProvider.listDownTask(queryReq);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        assertEquals("SUCCESS", result.getList().get(0).getTaskStatus());
    }

    @Test
    void listDownTask_EmptyResult_ReturnsEmptyPageData() {
        // 准备测试数据
        DownTaskQueryReq queryReq = new DownTaskQueryReq();
        queryReq.setPageNum(1);
        queryReq.setPageSize(10);

        PageData<DownTaskRes> expectedPageData = new PageData<>(Collections.emptyList(), 10, 1, 0);

        // 设置mock行为
        when(functionAbility.listDownTask(any())).thenReturn(expectedPageData);

        // 执行测试
        PageData<DownTaskRes> result = functionProvider.listDownTask(queryReq);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        assertEquals(0, result.getTotal());
    }

    @Test
    void listDownTask_NullRequest_ThrowsException() {
        // 执行测试并验证异常
        assertThrows(NullPointerException.class, () -> {
            functionProvider.listDownTask(null);
        });
    }

    private DictRes createDictRes(String code, String name) {
        DictRes dictRes = new DictRes();
        dictRes.setCode(code);
        dictRes.setName(name);
        return dictRes;
    }

    private GlobalValRes createGlobalValRes(String code, String value) {
        GlobalValRes globalValRes = new GlobalValRes();
        globalValRes.setCode(code);
        globalValRes.setValue(value);
        return globalValRes;
    }

    private DownTaskRes createDownTaskRes(int id, String taskName, String taskStatus) {
        DownTaskRes downTaskRes = new DownTaskRes();
        downTaskRes.setId(id);
        downTaskRes.setTaskName(taskName);
        downTaskRes.setTaskStatus(taskStatus);
        return downTaskRes;
    }
}
