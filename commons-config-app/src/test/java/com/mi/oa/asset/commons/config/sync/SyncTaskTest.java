package com.mi.oa.asset.commons.config.sync;

import com.mi.oa.asset.commons.config.app.StartApplication;
import com.mi.oa.asset.commons.config.app.scheduler.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@Slf4j
@SpringBootTest(classes = StartApplication.class)
@Disabled
class SyncTaskTest {

    @Resource
    private SyncMeasurement syncMeasurement;
    @Resource
    private SyncDepartment syncDepartment;
    @Resource
    private SyncCompany syncCompany;
    @Resource
    private SyncProvider syncProvider;
    @Resource
    private SyncTpmPosition syncTpmPosition;
    @Resource
    private SyncErrorMessageEntry SyncErrorMessageEntry;

    /**
     * 测试计量单位同步 - 正常流程
     * 测试场景：MDM返回有效数据，包含新增和更新的计量单位
     */
    @Test
    void testMeasurement_NormalFlow() {
        syncMeasurement.execute();
    }

    /**
     * 测试计量单位同步 - 空数据场景
     * 测试场景：MDM返回空数据，验证程序正常处理
     */
    @Test
    void testMeasurement_EmptyData() {
        syncMeasurement.execute();
    }

    /**
     * 测试计量单位同步 - 过滤场景
     * 测试场景：存在全局变量过滤配置，验证过滤逻辑
     */
    @Test
    void testMeasurement_WithFilter() {
        syncMeasurement.execute();
    }

    /**
     * 测试计量单位同步 - 分页场景
     * 测试场景：数据量超过单页大小，验证分页处理逻辑
     */
    @Test
    void testMeasurement_Pagination() {
        syncMeasurement.execute();
    }

    /**
     * 测试计量单位同步 - 异常场景
     * 测试场景：MDM服务异常，验证异常处理
     */
    @Test
    void testMeasurement_ExceptionHandling() {
        syncMeasurement.execute();
    }

    /**
     * 测试部门同步 - 正常流程
     * 测试场景：MDM返回有效部门数据，包含新增和更新的部门
     */
    @Test
    void testDepartment_NormalFlow() {
        syncDepartment.execute();
    }

    /**
     * 测试部门同步 - 空数据场景
     * 测试场景：MDM返回空数据，验证程序正常处理
     */
    @Test
    void testDepartment_EmptyData() {
        syncDepartment.execute();
    }

    /**
     * 测试部门同步 - 分页场景
     * 测试场景：数据量超过单页大小，验证分页处理逻辑
     */
    @Test
    void testDepartment_Pagination() {
        syncDepartment.execute();
    }

    /**
     * 测试部门同步 - 异常场景
     * 测试场景：MDM服务异常，验证异常处理
     */
    @Test
    void testDepartment_ExceptionHandling() {
        syncDepartment.execute();
    }

    /**
     * 测试部门缓存 - 按层级缓存
     * 测试场景：按层级缓存部门结构信息
     */
    @Test
    void testDepartment_CacheByLevel() {
        syncDepartment.cacheAssetOrgStructureByLevel();
    }

    /**
     * 测试部门缓存 - 清除指定层级缓存
     * 测试场景：清除指定层级的部门缓存
     */
    @Test
    void testDepartment_EvictLevelCache() {
        syncDepartment.evictLevelDataCache(1);
        syncDepartment.evictLevelDataCache(2);
        syncDepartment.evictLevelDataCache(3);
    }

    /**
     * 测试部门缓存 - 缓存全称信息
     * 测试场景：缓存部门的中英文全称信息
     */
    @Test
    void testDepartment_CacheFullName() {
        // 这里需要准备测试数据，实际项目中可能需要从数据库获取
        // 或者通过其他方式构造测试数据
        log.info("测试部门全称缓存功能");
    }

    @Test
    void syncDepartmentTest(){
        syncDepartment.execute();
    }

    @Test
    void testSyncCompany(){
        syncCompany.execute();
    }

    @Test
    void testSyncProvider(){
        syncProvider.execute();
    }

    @Test
    void testSyncPosition(){
        syncTpmPosition.execute();
    }

    @Test
    void testSyncErrorMessageEntry(){
        SyncErrorMessageEntry.execute();
    }


}
