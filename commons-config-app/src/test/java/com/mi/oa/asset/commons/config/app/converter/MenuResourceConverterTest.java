package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.menuresource.MenuRes;
import com.mi.oa.asset.commons.config.api.menuresource.ResourceRes;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.ResourceResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.ResourceTreeResp;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MenuResourceConverterTest {

    private MenuResourceConverter menuResourceConverter = new MenuResourceConverter() {
        @Override
        public MenuRes test(ResourceTreeResp treeResp) {
            if (treeResp == null) {
                return null;
            }
            MenuRes menuRes = new MenuRes();
            menuRes.setResourceList(resourceRespListToResourceResList(treeResp.getRelationResourceFlatDtoList()));
            menuRes.setResourceCode(treeResp.getResourceCode());
            menuRes.setResourceName(treeResp.getResourceName());
            menuRes.setDescription(treeResp.getDescription());
            menuRes.setMenuRoute(treeResp.getMenuRoute());
            menuRes.setMenuTitle(getMenuTitle(treeResp.getMenuTitle(), treeResp.getExt(), EAMConstants.CHINESE));
            menuRes.setIconUrl(treeResp.getIconUrl());
            menuRes.setIsShow(treeResp.getIsShow());
            menuRes.setParentCode(treeResp.getParentCode());
            menuRes.setSensitiveLevel(treeResp.getSensitiveLevel());
            menuRes.setDimensionCode(treeResp.getDimensionCode());
            menuRes.setSort(treeResp.getSort());
            menuRes.setDesc(converterDescription(treeResp.getDescription()));
            return menuRes;
        }

        @Override
        public MenuRes resourceTreeToRes(ResourceTreeResp treeResp) {
            if (treeResp == null) {
                return null;
            }
            MenuRes menuRes = new MenuRes();
            menuRes.setResourceList(resourceRespListToResourceResList(treeResp.getRelationResourceFlatDtoList()));
            menuRes.setResourceCode(treeResp.getResourceCode());
            menuRes.setResourceName(treeResp.getResourceName());
            menuRes.setDescription(treeResp.getDescription());
            menuRes.setMenuRoute(treeResp.getMenuRoute());
            menuRes.setMenuTitle(getMenuTitle(treeResp.getMenuTitle(), treeResp.getExt(), EAMConstants.CHINESE));
            menuRes.setIconUrl(treeResp.getIconUrl());
            menuRes.setIsShow(treeResp.getIsShow());
            menuRes.setParentCode(treeResp.getParentCode());
            menuRes.setSensitiveLevel(treeResp.getSensitiveLevel());
            menuRes.setDimensionCode(treeResp.getDimensionCode());
            menuRes.setSort(treeResp.getSort());
            menuRes.setDesc(converterDescription(treeResp.getDescription()));
            return menuRes;
        }

        @Override
        public MenuRes resourceTreeToRes(ResourceTreeResp treeResp, String eamLanguage) {
            if (treeResp == null) {
                return null;
            }
            MenuRes menuRes = new MenuRes();
            menuRes.setResourceList(resourceRespListToResourceResList(treeResp.getRelationResourceFlatDtoList()));
            menuRes.setResourceCode(treeResp.getResourceCode());
            menuRes.setResourceName(treeResp.getResourceName());
            menuRes.setDescription(treeResp.getDescription());
            menuRes.setMenuRoute(treeResp.getMenuRoute());
            menuRes.setMenuTitle(getMenuTitle(treeResp.getMenuTitle(), treeResp.getExt(), eamLanguage));
            menuRes.setIconUrl(treeResp.getIconUrl());
            menuRes.setIsShow(treeResp.getIsShow());
            menuRes.setParentCode(treeResp.getParentCode());
            menuRes.setSensitiveLevel(treeResp.getSensitiveLevel());
            menuRes.setDimensionCode(treeResp.getDimensionCode());
            menuRes.setSort(treeResp.getSort());
            menuRes.setDesc(converterDescription(treeResp.getDescription()));
            return menuRes;
        }

        @Override
        public List<MenuRes> resourceTreeListToResList(List<ResourceTreeResp> treeResp) {
            if (treeResp == null || treeResp.isEmpty()) {
                return null;
            }
            List<MenuRes> result = new ArrayList<>();
            for (ResourceTreeResp resp : treeResp) {
                result.add(resourceTreeToRes(resp));
            }
            return result;
        }

        @Override
        public List<MenuRes> resourceTreeListToResList(List<ResourceTreeResp> treeResp, String eamLanguage) {
            if (treeResp == null || treeResp.isEmpty()) {
                return null;
            }
            List<MenuRes> result = new ArrayList<>();
            for (ResourceTreeResp resp : treeResp) {
                result.add(resourceTreeToRes(resp, eamLanguage));
            }
            return result;
        }
    };

    private ResourceTreeResp mockTreeResp;
    private ResourceResp mockResourceResp;
    private List<ResourceTreeResp> mockTreeRespList;
    private List<ResourceResp> mockResourceRespList;
    private MenuRes mockMenuRes;
    private ResourceRes mockResourceRes;
    private Map<String, Object> extMap;

    @BeforeEach
    void setUp() {
        // 初始化 ResourceTreeResp
        mockTreeResp = new ResourceTreeResp();
        mockTreeResp.setResourceCode("TEST_CODE");
        mockTreeResp.setResourceName("Test Resource");
        mockTreeResp.setDescription("{\"key\":\"value\"}");
        mockTreeResp.setMenuRoute("/test/route");
        mockTreeResp.setMenuTitle("Default Title");
        mockTreeResp.setIconUrl("test-icon.png");
        mockTreeResp.setIsShow(Boolean.TRUE);
        mockTreeResp.setParentCode("PARENT_CODE");
        mockTreeResp.setSensitiveLevel(1);
        mockTreeResp.setDimensionCode("DIM_CODE");
        mockTreeResp.setSort(1);

        extMap = new HashMap<>();
        extMap.put("zh", "中文标题");
        extMap.put("en", "English Title");
        mockTreeResp.setExt(extMap);

        // 初始化 ResourceResp
        mockResourceResp = new ResourceResp();
        mockResourceResp.setResourceCode("RESOURCE_CODE");
        mockResourceResp.setResourceName("Resource Name");
        mockResourceResp.setDescription("Resource Description");

        // 设置关联资源列表
        mockResourceRespList = new ArrayList<>();
        mockResourceRespList.add(mockResourceResp);
        mockTreeResp.setRelationResourceFlatDtoList(mockResourceRespList);

        // 设置子资源
        mockTreeRespList = new ArrayList<>();
        mockTreeRespList.add(mockTreeResp);
        mockTreeResp.setChildren(mockTreeRespList);

        // 初始化 MenuRes
        mockMenuRes = new MenuRes();
        mockMenuRes.setResourceCode("TEST_CODE");
        mockMenuRes.setResourceName("Test Resource");
        mockMenuRes.setMenuTitle("中文标题");
        mockMenuRes.setMenuRoute("/test/route");
        mockMenuRes.setIconUrl("test-icon.png");
        mockMenuRes.setIsShow(Boolean.TRUE);
        mockMenuRes.setParentCode("PARENT_CODE");
        mockMenuRes.setSensitiveLevel(1);
        mockMenuRes.setDimensionCode("DIM_CODE");
        mockMenuRes.setSort(1);

        // 初始化 ResourceRes
        mockResourceRes = new ResourceRes();
        mockResourceRes.setResourceCode("RESOURCE_CODE");
        mockResourceRes.setResourceName("Resource Name");
        mockResourceRes.setDescription("Resource Description");
    }

    @Test
    void test_Test_Success() {
        // 准备测试数据
        List<ResourceResp> resourceList = new ArrayList<>();
        resourceList.add(mockResourceResp);
        mockTreeResp.setRelationResourceFlatDtoList(resourceList);

        // 执行测试
        MenuRes result = menuResourceConverter.test(mockTreeResp);

        // 验证结果
        assertNotNull(result);
        assertEquals("TEST_CODE", result.getResourceCode());
        assertEquals("Test Resource", result.getResourceName());
        assertEquals("/test/route", result.getMenuRoute());
        assertEquals("中文标题", result.getMenuTitle());
        assertEquals("test-icon.png", result.getIconUrl());
        assertEquals(Boolean.TRUE, result.getIsShow());
        assertEquals("PARENT_CODE", result.getParentCode());
        assertEquals(1, result.getSensitiveLevel());
        assertEquals("DIM_CODE", result.getDimensionCode());
        assertEquals(1, result.getSort());
        assertNotNull(result.getResourceList());
        assertEquals(1, result.getResourceList().size());
    }

    @Test
    void test_ResourceTreeToRes_Success() {
        // 准备测试数据
        List<ResourceResp> resourceList = new ArrayList<>();
        resourceList.add(mockResourceResp);
        mockTreeResp.setRelationResourceFlatDtoList(resourceList);

        // 执行测试
        MenuRes result = menuResourceConverter.resourceTreeToRes(mockTreeResp);

        // 验证结果
        assertNotNull(result);
        assertEquals("TEST_CODE", result.getResourceCode());
        assertEquals("Test Resource", result.getResourceName());
        assertEquals("/test/route", result.getMenuRoute());
        assertEquals("中文标题", result.getMenuTitle());
        assertEquals("test-icon.png", result.getIconUrl());
        assertEquals(Boolean.TRUE, result.getIsShow());
        assertEquals("PARENT_CODE", result.getParentCode());
        assertEquals(1, result.getSensitiveLevel());
        assertEquals("DIM_CODE", result.getDimensionCode());
        assertEquals(1, result.getSort());
        assertNotNull(result.getResourceList());
        assertEquals(1, result.getResourceList().size());
    }

    @Test
    void test_ResourceTreeToRes_WithEnglishLanguage() {
        // 准备测试数据
        List<ResourceResp> resourceList = new ArrayList<>();
        resourceList.add(mockResourceResp);
        mockTreeResp.setRelationResourceFlatDtoList(resourceList);

        // 执行测试
        MenuRes result = menuResourceConverter.resourceTreeToRes(mockTreeResp, "en");

        // 验证结果
        assertNotNull(result);
        assertEquals("English Title", result.getMenuTitle());
    }

    @Test
    void test_ResourceTreeToRes_NullInput() {
        MenuRes result = menuResourceConverter.resourceTreeToRes(null);
        assertNull(result);
    }

    @Test
    void test_ResourceTreeToRes_WithEmptyResourceList() {
        // 准备测试数据
        mockTreeResp.setRelationResourceFlatDtoList(new ArrayList<>());

        // 执行测试
        MenuRes result = menuResourceConverter.resourceTreeToRes(mockTreeResp);

        // 验证结果
        assertNotNull(result);
        assertNull(result.getResourceList());
    }

    @Test
    void test_ResourceTreeToRes_WithNullExt() {
        // 准备测试数据
        mockTreeResp.setExt(null);

        // 执行测试
        MenuRes result = menuResourceConverter.resourceTreeToRes(mockTreeResp);

        // 验证结果
        assertNotNull(result);
        assertEquals("Default Title", result.getMenuTitle());
    }

    @Test
    void test_ResourceTreeToRes_WithEmptyDescription() {
        // 准备测试数据
        mockTreeResp.setDescription(null);

        // 执行测试
        MenuRes result = menuResourceConverter.resourceTreeToRes(mockTreeResp);

        // 验证结果
        assertNotNull(result);
        assertNull(result.getDesc());
    }

    @Test
    void test_ResourceTreeListToResList_Success() {
        // 准备测试数据
        List<ResourceTreeResp> inputList = new ArrayList<>();
        inputList.add(mockTreeResp);

        // 执行测试
        List<MenuRes> result = menuResourceConverter.resourceTreeListToResList(inputList);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        MenuRes menuRes = result.get(0);
        assertEquals("TEST_CODE", menuRes.getResourceCode());
        assertEquals("Test Resource", menuRes.getResourceName());
        assertEquals("中文标题", menuRes.getMenuTitle());
    }

    @Test
    void test_ResourceTreeListToResList_WithMultipleItems() {
        // 准备测试数据
        List<ResourceTreeResp> inputList = new ArrayList<>();
        inputList.add(mockTreeResp);
        
        ResourceTreeResp secondTreeResp = new ResourceTreeResp();
        secondTreeResp.setResourceCode("TEST_CODE_2");
        secondTreeResp.setResourceName("Test Resource 2");
        secondTreeResp.setMenuTitle("Default Title 2");
        secondTreeResp.setExt(extMap);
        inputList.add(secondTreeResp);

        // 执行测试
        List<MenuRes> result = menuResourceConverter.resourceTreeListToResList(inputList);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("TEST_CODE", result.get(0).getResourceCode());
        assertEquals("TEST_CODE_2", result.get(1).getResourceCode());
    }

    @Test
    void test_ResourceTreeListToResList_WithLanguage() {
        // 准备测试数据
        List<ResourceTreeResp> inputList = new ArrayList<>();
        inputList.add(mockTreeResp);

        // 执行测试
        List<MenuRes> result = menuResourceConverter.resourceTreeListToResList(inputList, "en");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("English Title", result.get(0).getMenuTitle());
    }

    @Test
    void test_ResourceTreeListToResList_WithMultipleItemsAndLanguage() {
        // 准备测试数据
        List<ResourceTreeResp> inputList = new ArrayList<>();
        inputList.add(mockTreeResp);
        
        ResourceTreeResp secondTreeResp = new ResourceTreeResp();
        secondTreeResp.setResourceCode("TEST_CODE_2");
        secondTreeResp.setResourceName("Test Resource 2");
        secondTreeResp.setMenuTitle("Default Title 2");
        secondTreeResp.setExt(extMap);
        inputList.add(secondTreeResp);

        // 执行测试
        List<MenuRes> result = menuResourceConverter.resourceTreeListToResList(inputList, "en");

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("English Title", result.get(0).getMenuTitle());
        assertEquals("English Title", result.get(1).getMenuTitle());
    }

    @Test
    void test_ResourceTreeListToResList_EmptyInput() {
        List<MenuRes> result = menuResourceConverter.resourceTreeListToResList(new ArrayList<>());
        assertNull(result);
    }

    @Test
    void test_ResourceTreeListToResList_NullInput() {
        List<MenuRes> result = menuResourceConverter.resourceTreeListToResList(null);
        assertNull(result);
    }

    @Test
    void test_ResourceRespToResourceRes_Success() {
        ResourceRes result = menuResourceConverter.resourceRespToResourceRes(mockResourceResp);

        assertNotNull(result);
        assertEquals("RESOURCE_CODE", result.getResourceCode());
        assertEquals("Resource Name", result.getResourceName());
        assertEquals("Resource Description", result.getDescription());
    }

    @Test
    void test_ResourceRespToResourceRes_NullInput() {
        ResourceRes result = menuResourceConverter.resourceRespToResourceRes(null);
        assertNull(result);
    }

    @Test
    void test_ResourceRespListToResourceResList_Success() {
        List<ResourceResp> inputList = new ArrayList<>();
        inputList.add(mockResourceResp);

        List<ResourceRes> result = menuResourceConverter.resourceRespListToResourceResList(inputList);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("RESOURCE_CODE", result.get(0).getResourceCode());
    }

    @Test
    void test_ResourceRespListToResourceResList_EmptyInput() {
        List<ResourceRes> result = menuResourceConverter.resourceRespListToResourceResList(new ArrayList<>());
        assertNull(result);
    }

    @Test
    void test_ConverterDescription_Success() {
        String description = "{\"key\":\"value\"}";
        Object result = menuResourceConverter.converterDescription(description);

        assertNotNull(result);
        assertTrue(result instanceof Map);
        Map<String, Object> mapResult = (Map<String, Object>) result;
        assertEquals("value", mapResult.get("key"));
    }

    @Test
    void test_ConverterDescription_NullInput() {
        Object result = menuResourceConverter.converterDescription(null);
        assertNull(result);
    }

    @Test
    void test_GetMenuTitle_Chinese() {
        String result = menuResourceConverter.getMenuTitle("Default Title", extMap, EAMConstants.CHINESE);
        assertEquals("中文标题", result);
    }

    @Test
    void test_GetMenuTitle_English() {
        String result = menuResourceConverter.getMenuTitle("Default Title", extMap, "en");
        assertEquals("English Title", result);
    }

    @Test
    void test_GetMenuTitle_EmptyExt() {
        String result = menuResourceConverter.getMenuTitle("Default Title", null, EAMConstants.CHINESE);
        assertEquals("Default Title", result);
    }
}
