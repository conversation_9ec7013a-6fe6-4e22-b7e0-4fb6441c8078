package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyProvider;
import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyReq;
import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyRes;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;

/**
 * CountryCurrencyController单元测试类
 */
@ExtendWith(MockitoExtension.class)
class CountryCurrencyControllerTest {

    @InjectMocks
    private CountryCurrencyController controller;

    @Mock
    private CountryCurrencyProvider provider;

    @Test
    void byCountryCode() {
        String countryCode = "CHN";
        // 执行测试
        Result<List<CountryCurrencyRes>> result = controller.byCountryCode(countryCode);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getByCountryCode(countryCode);
    }

    @Test
    void detail() {
        Integer countryCurrencyId = 1;
        // 执行测试
        Result<CountryCurrencyRes> result = controller.info(countryCurrencyId);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getById(countryCurrencyId);
    }


    @Test
    void byCountryId() {
        Integer countryId = 1;
        // 执行测试
        Result<List<CountryCurrencyRes>> result = controller.byCountryId(countryId);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getByCountryId(countryId);
    }


    @Test
    void list() {
        // 执行测试
        Result<List<CountryCurrencyRes>> result = controller.list();
        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getCountryCurrencyList();
    }

    @Test
    void save() {
        // 准备数据
        CountryCurrencyReq req = new CountryCurrencyReq();
        req.setId(null);
        req.setCountryName("guojiaCS");
        req.setCountryCodeAlphaThree("GJC");


        // 执行测试
        Result<Integer> result = controller.edit(req);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).saveOrUpdate(req);
    }

//    @Test
//    void update() {
//        // 准备数据
//        CountryCurrencyReq req = new CountryCurrencyReq();
//        req.setId(5);
//        req.setCountryName("rn");
//        req.setCountryEnglishName("ern");
//        req.setCountryCodeAlphaThree("GJC");
//
//
//        // 执行测试
//        Result<Void> result = controller.edit(req);
//
//        // 验证结果
//        assertNotNull(result);
//        // Result类没有isSuccess方法，直接验证其他属性
//        assertEquals(0, result.getCode());
//
//        // 验证调用
//        verify(provider).saveOrUpdate(req);
//    }

    @Test
    void delete() {
        // 准备数据
        CountryCurrencyReq req = new CountryCurrencyReq();
        req.setId(null);

        // 执行测试
        Result<Void> result = controller.delete(req);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());
        // 验证调用
        verify(provider).removeByIds(Collections.singletonList(req.getId()));
    }

} 