package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.warehouse.*;
import com.mi.oa.asset.commons.config.app.web.controller.WarehouseController;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

/**
 * 已通过
 */
@ExtendWith(MockitoExtension.class)
class WarehouseControllerTest {

    @InjectMocks
    private WarehouseController warehouseController;

    @Mock
    private WarehouseProvider warehouseProvider;

    @Mock
    private WarehouseAreaProvider warehouseAreaProvider;

    @Test
    @DisplayName("findByBusinessLine方法应返回指定业务线的仓库列表")
    void findByBusinessLine_ShouldReturnWarehousesByBusinessLine() {
        // 准备测试数据
        String businessLine = "car";
        List<WarehouseRes> expectedWarehouses = Arrays.asList(
                createWarehouseRes(1, "WH001", "仓库1", businessLine),
                createWarehouseRes(2, "WH002", "仓库2", businessLine)
        );

        // 模拟warehouseProvider行为
        when(warehouseProvider.getByBusinessLine(businessLine)).thenReturn(expectedWarehouses);

        // 执行测试
        Result<List<WarehouseRes>> result = warehouseController.findByBusinessLine(businessLine);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(expectedWarehouses, result.getData());

        // 验证方法调用
        verify(warehouseProvider).getByBusinessLine(businessLine);
    }

    @Test
    @DisplayName("saveWarehouseArea方法应正确保存仓库区域信息并返回ID")
    void saveWarehouseArea_ShouldSaveAndReturnId() {
        // 准备测试数据
        WarehouseAreaReq req = new WarehouseAreaReq();
        req.setWarehouseCode("WH001");
        req.setWarehouseName("仓库1");
        req.setBusinessLine("car");
        req.setPriority(1);
        req.setCountryId("CN");

        Integer expectedId = 123;

        // 模拟warehouseAreaProvider行为
        when(warehouseAreaProvider.saveWarehouse(req)).thenReturn(expectedId);

        // 执行测试
        Result<Integer> result = warehouseController.saveWarehouseArea(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(expectedId, result.getData());

        // 验证方法调用
        verify(warehouseAreaProvider).saveWarehouse(req);
    }

    @Test
    @DisplayName("removeWarehouseArea方法应正确调用移除仓库区域服务")
    void removeWarehouseArea_ShouldRemoveWarehouseArea() {
        // 准备测试数据
        RemoveWarehouseAreaReq req = new RemoveWarehouseAreaReq();
        req.setWarehouseCode("WH001");
        req.setCountryId("CN");

        // 模拟doNothing行为
        doNothing().when(warehouseAreaProvider).removeWarehouse(req);

        // 执行测试
        Result<Void> result = warehouseController.removeWarehouseArea(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());

        // 验证方法调用
        verify(warehouseAreaProvider).removeWarehouse(req);
    }

    @Test
    @DisplayName("listByBizAndRegion方法应返回指定业务线和地区的仓库区域列表")
    void listByBizAndRegion_ShouldReturnWarehouseAreasByBusinessLineAndRegion() {
        // 准备测试数据
        List<String> businessLines = Collections.singletonList("car");
        String areaId = "CN-北京";
        String areaName = null;

        List<WarehouseAreaRes> expectedAreas = Arrays.asList(
                createWarehouseAreaRes(1, "WH001", "仓库1", "car", "CN", "北京"),
                createWarehouseAreaRes(2, "WH002", "仓库2", "car", "CN", "北京")
        );

        // 模拟warehouseAreaProvider行为
        when(warehouseAreaProvider.listByBizAndRegion(businessLines, areaId, areaName)).thenReturn(expectedAreas);

        // 执行测试
        Result<List<WarehouseAreaRes>> result = warehouseController.listByBizAndRegion(businessLines, areaId, areaName);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(expectedAreas, result.getData());

        // 验证方法调用
        verify(warehouseAreaProvider).listByBizAndRegion(businessLines, areaId, areaName);
    }

    @Test
    @DisplayName("searchWarehouseArea方法应根据关键词和业务线搜索仓库区域")
    void searchWarehouseArea_ShouldSearchWarehouseAreasByKeyWordAndBusinessLines() {
        // 准备测试数据
        String keyWord = "仓库";
        List<String> businessLines = Collections.singletonList("car");

        List<WarehouseAreaRes> expectedAreas = Arrays.asList(
                createWarehouseAreaRes(1, "WH001", "仓库1", "car", "CN", "北京"),
                createWarehouseAreaRes(2, "WH002", "仓库2", "car", "CN", "上海")
        );

        // 模拟warehouseAreaProvider行为
        when(warehouseAreaProvider.searchWarehouseArea(keyWord, businessLines)).thenReturn(expectedAreas);

        // 执行测试
        Result<List<WarehouseAreaRes>> result = warehouseController.searchWarehouseArea(keyWord, businessLines);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(expectedAreas, result.getData());

        // 验证方法调用
        verify(warehouseAreaProvider).searchWarehouseArea(keyWord, businessLines);
    }

    // 辅助方法：创建测试用的WarehouseRes对象
    private WarehouseRes createWarehouseRes(Integer id, String code, String name, String businessLine) {
        WarehouseRes warehouse = new WarehouseRes();
        warehouse.setId(id);
        warehouse.setHouseCode(code);
        warehouse.setHouseName(name);
        warehouse.setBusinessLine(businessLine);
        return warehouse;
    }

    // 辅助方法：创建测试用的WarehouseAreaRes对象
    private WarehouseAreaRes createWarehouseAreaRes(Integer id, String code, String name, String businessLine, String countryId, String areaName) {
        WarehouseAreaRes area = new WarehouseAreaRes();
        area.setId(id);
        area.setWarehouseCode(code);
        area.setWarehouseName(name);
        area.setBusinessLine(businessLine);
        area.setCountryId(countryId);
        area.setAreaName(areaName);
        return area;
    }
}