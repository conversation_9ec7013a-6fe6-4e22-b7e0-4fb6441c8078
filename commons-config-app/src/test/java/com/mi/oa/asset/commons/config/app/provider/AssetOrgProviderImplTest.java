package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgType;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgUnitQueryReq;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgUnitRes;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigField;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleInfoRes;
import com.mi.oa.asset.commons.config.app.ability.AssetOrgAbility;
import com.mi.oa.asset.commons.config.app.ability.BusinessRoleAbility;
import com.mi.oa.asset.commons.config.app.converter.AssetOrgConverter;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.assetorganization.repository.AssetOrgRepo;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgUnitQuery;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgStructure;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgStructureRes;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.MockedStatic;
import com.mi.oa.asset.commons.config.infra.common.CacheKey;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.mockStatic;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AssetOrgProviderImpl类的getAssetOrgUnitPageData方法单元测试
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ExtendWith(MockitoExtension.class)
class AssetOrgProviderImplTest {

    @InjectMocks
    private AssetOrgProviderImpl assetOrgProvider;

    @Mock
    private AssetOrgRepo assetOrgRepo;

    @Mock
    private AssetOrgConverter converter;

    @Mock
    private AssetOrgAbility assetOrgAbility;

    @Mock
    private BusinessRoleAbility businessRoleAbility;

    @BeforeEach
    void setUp() {
        // 测试前的初始化工作
    }

    @Test
    void getAssetOrgUnitPageData_normal() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .keyword("测试部门")
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .keyword("测试部门")
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "测试部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "测试部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "测试部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "测试部门2", "adm_emp")
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());
        assertEquals(pageSize, result.getPageSize());
        assertEquals(pageNum, result.getPageNum());
        assertEquals("ORG001", result.getList().get(0).getOrgCode());
        assertEquals("测试部门1", result.getList().get(0).getOrgName());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_empty() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .keyword("不存在的部门")
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .keyword("不存在的部门")
                .build();

        PageData<AssetOrgUnit> pageData = PageData.of(Collections.emptyList(), pageSize, pageNum, 0);
        PageData<AssetOrgUnitRes> expectedResult = PageData.of(Collections.emptyList(), pageSize, pageNum, 0);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        assertEquals(0, result.getTotal());
        assertEquals(pageSize, result.getPageSize());
        assertEquals(pageNum, result.getPageNum());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_id() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .orgIds(Arrays.asList(1, 2, 3))
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .orgIds(Arrays.asList(1, 2, 3))
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "部门2", "adm_emp")
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_code() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .orgCodes(Arrays.asList("ORG001", "ORG002"))
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .orgCodes(Arrays.asList("ORG001", "ORG002"))
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "部门2", "adm_emp")
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_parent() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .parentCode("PARENT001")
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .parentCode("PARENT001")
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "子部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "子部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "子部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "子部门2", "adm_emp")
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_businessLine() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLines(Arrays.asList("adm_emp", "adm_pub"))
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLines(Arrays.asList(BusinessLine.ADM_EMP, BusinessLine.ADM_PUB))
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_PUB)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "部门2", "adm_pub")
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_level() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .level(2)
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .level(2)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "二级部门1", BusinessLine.ADM_EMP, 2),
                createAssetOrgUnit(2, "ORG002", "二级部门2", BusinessLine.ADM_EMP, 2)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "二级部门1", "adm_emp", 2),
                createAssetOrgUnitRes(2, "ORG002", "二级部门2", "adm_emp", 2)
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_v_org() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .isVirtual(true)
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .isVirtual(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "虚拟部门1", BusinessLine.ADM_EMP, 1, true),
                createAssetOrgUnit(2, "ORG002", "虚拟部门2", BusinessLine.ADM_EMP, 1, true)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "虚拟部门1", "adm_emp", 1, true),
                createAssetOrgUnitRes(2, "ORG002", "虚拟部门2", "adm_emp", 1, true)
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_manage() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .isAssetManageOrg(true)
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "管理部门1", BusinessLine.ADM_EMP, 1, false, true),
                createAssetOrgUnit(2, "ORG002", "管理部门2", BusinessLine.ADM_EMP, 1, false, true)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "管理部门1", "adm_emp", 1, false, true),
                createAssetOrgUnitRes(2, "ORG002", "管理部门2", "adm_emp", 1, false, true)
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_use() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .isAssetUseOrg(true)
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .isAssetUseOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "使用部门1", BusinessLine.ADM_EMP, 1, false, false, true),
                createAssetOrgUnit(2, "ORG002", "使用部门2", BusinessLine.ADM_EMP, 1, false, false, true)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "使用部门1", "adm_emp", 1, false, false, true),
                createAssetOrgUnitRes(2, "ORG002", "使用部门2", "adm_emp", 1, false, false, true)
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_type() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .orgType("DEPT")
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .orgType(AssetOrgType.DEPARTMENT)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "部门2", "adm_emp")
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_list() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .orgTypes(Arrays.asList("DEPT", "TEAM"))
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .orgTypes(Arrays.asList(AssetOrgType.DEPARTMENT, AssetOrgType.OFFICE))
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "团队1", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "团队1", "adm_emp")
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_account() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .inUseCode("USER001")
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .inUseCode("USER001")
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "部门2", "adm_emp")
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_use_dept() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .inUseDeptCode("DEPT001")
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .inUseDeptCode("DEPT001")
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "部门2", "adm_emp")
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_condition() {
        // 准备数据 - 复杂查询条件
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .keyword("测试")
                .level(2)
                .isVirtual(false)
                .isAssetManageOrg(true)
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .keyword("测试")
                .level(2)
                .isVirtual(false)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "测试管理部门1", BusinessLine.ADM_EMP, 2, false, true),
                createAssetOrgUnit(2, "ORG002", "测试管理部门2", BusinessLine.ADM_EMP, 2, false, true)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "测试管理部门1", "adm_emp", 2, false, true),
                createAssetOrgUnitRes(2, "ORG002", "测试管理部门2", "adm_emp", 2, false, true)
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_null() {
        // 准备数据
        AssetOrgUnitQueryReq req = null;
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder().build();

        PageData<AssetOrgUnit> pageData = PageData.of(Collections.emptyList(), pageSize, pageNum, 0);
        PageData<AssetOrgUnitRes> expectedResult = PageData.of(Collections.emptyList(), pageSize, pageNum, 0);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        assertEquals(0, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_null_page() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .build();
        Integer pageNum = null;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .build();

        PageData<AssetOrgUnit> pageData = PageData.of(Collections.emptyList(), pageSize, 1, 0);
        PageData<AssetOrgUnitRes> expectedResult = PageData.of(Collections.emptyList(), pageSize, 1, 0);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        assertEquals(0, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_null_size() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .build();
        Integer pageNum = 1;
        Integer pageSize = null;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .build();

        PageData<AssetOrgUnit> pageData = PageData.of(Collections.emptyList(), 10, pageNum, 0);
        PageData<AssetOrgUnitRes> expectedResult = PageData.of(Collections.emptyList(), 10, pageNum, 0);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toPageData(eq(pageData), any())).thenReturn((PageData) expectedResult);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        assertEquals(0, result.getTotal());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toPageData(eq(pageData), any());
    }

    @Test
    void getAssetOrgUnitPageData_ch() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .keyword("测试部门")
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;
        String language = "zh-CN";

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .keyword("测试部门")
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "测试部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "测试部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "测试部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "测试部门2", "adm_emp")
        );

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toAssetOrgUnitRes(orgUnitList, language)).thenReturn(resList);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());
        assertEquals(pageSize, result.getPageSize());
        assertEquals(pageNum, result.getPageNum());
        assertEquals("ORG001", result.getList().get(0).getOrgCode());
        assertEquals("测试部门1", result.getList().get(0).getOrgName());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toAssetOrgUnitRes(orgUnitList, language);
    }

    @Test
    void getAssetOrgUnitPageData_en() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .keyword("测试部门")
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;
        String language = "en-US";

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .keyword("测试部门")
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "测试部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "测试部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "Test Department 1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "Test Department 2", "adm_emp")
        );

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toAssetOrgUnitRes(orgUnitList, language)).thenReturn(resList);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());
        assertEquals(pageSize, result.getPageSize());
        assertEquals(pageNum, result.getPageNum());
        assertEquals("ORG001", result.getList().get(0).getOrgCode());
        assertEquals("Test Department 1", result.getList().get(0).getOrgName());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toAssetOrgUnitRes(orgUnitList, language);
    }

    @Test
    void getAssetOrgUnitPageData_null_en() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .keyword("不存在的部门")
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;
        String language = "zh-CN";

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .keyword("不存在的部门")
                .build();

        PageData<AssetOrgUnit> pageData = PageData.of(Collections.emptyList(), pageSize, pageNum, 0);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toAssetOrgUnitRes(Collections.emptyList(), language)).thenReturn(Collections.emptyList());

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize, language);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        assertEquals(0, result.getTotal());
        assertEquals(pageSize, result.getPageSize());
        assertEquals(pageNum, result.getPageNum());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toAssetOrgUnitRes(Collections.emptyList(), language);
    }

    @Test
    void getAssetOrgUnitPageData_default() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .keyword("测试部门")
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;
        String language = null;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .keyword("测试部门")
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "测试部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "测试部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "测试部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "测试部门2", "adm_emp")
        );

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toAssetOrgUnitRes(orgUnitList, language)).thenReturn(resList);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());
        assertEquals(pageSize, result.getPageSize());
        assertEquals(pageNum, result.getPageNum());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toAssetOrgUnitRes(orgUnitList, language);
    }

    @Test
    void getAssetOrgUnitPageData_en_max() {
        // 准备数据 - 复杂查询条件
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .keyword("测试")
                .level(2)
                .isVirtual(false)
                .isAssetManageOrg(true)
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;
        String language = "zh-CN";

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .keyword("测试")
                .level(2)
                .isVirtual(false)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "测试管理部门1", BusinessLine.ADM_EMP, 2, false, true),
                createAssetOrgUnit(2, "ORG002", "测试管理部门2", BusinessLine.ADM_EMP, 2, false, true)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "测试管理部门1", "adm_emp", 2, false, true),
                createAssetOrgUnitRes(2, "ORG002", "测试管理部门2", "adm_emp", 2, false, true)
        );

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toAssetOrgUnitRes(orgUnitList, language)).thenReturn(resList);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAssetOrgUnitPageData(req, pageNum, pageSize, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());
        assertEquals(pageSize, result.getPageSize());
        assertEquals(pageNum, result.getPageNum());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toAssetOrgUnitRes(orgUnitList, language);
    }

    // 辅助方法：创建AssetOrgUnit对象
    private AssetOrgUnit createAssetOrgUnit(Integer orgId, String orgCode, String orgName, BusinessLine businessLine) {
        return createAssetOrgUnit(orgId, orgCode, orgName, businessLine, 1, false, false, false);
    }

    private AssetOrgUnit createAssetOrgUnit(Integer orgId, String orgCode, String orgName, BusinessLine businessLine, Integer level) {
        return createAssetOrgUnit(orgId, orgCode, orgName, businessLine, level, false, false, false);
    }

    private AssetOrgUnit createAssetOrgUnit(Integer orgId, String orgCode, String orgName, BusinessLine businessLine, Integer level, Boolean isVirtual) {
        return createAssetOrgUnit(orgId, orgCode, orgName, businessLine, level, isVirtual, false, false);
    }

    private AssetOrgUnit createAssetOrgUnit(Integer orgId, String orgCode, String orgName, BusinessLine businessLine, Integer level, Boolean isVirtual, Boolean isAssetManageOrg) {
        return createAssetOrgUnit(orgId, orgCode, orgName, businessLine, level, isVirtual, isAssetManageOrg, false);
    }

    private AssetOrgUnit createAssetOrgUnit(Integer orgId, String orgCode, String orgName, BusinessLine businessLine, Integer level, Boolean isVirtual, Boolean isAssetManageOrg, Boolean isAssetUseOrg) {
        return AssetOrgUnit.builder()
                .orgId(orgId)
                .orgCode(orgCode)
                .orgName(orgName)
                .businessLine(businessLine)
                .level(level)
                .isVirtual(isVirtual)
                .isAssetManageOrg(isAssetManageOrg)
                .isAssetUseOrg(isAssetUseOrg)
                .build();
    }

    // 辅助方法：创建AssetOrgUnitRes对象
    private AssetOrgUnitRes createAssetOrgUnitRes(Integer orgId, String orgCode, String orgName, String businessLine) {
        return createAssetOrgUnitRes(orgId, orgCode, orgName, businessLine, 1, false, false, false);
    }

    private AssetOrgUnitRes createAssetOrgUnitRes(Integer orgId, String orgCode, String orgName, String businessLine, Integer level) {
        return createAssetOrgUnitRes(orgId, orgCode, orgName, businessLine, level, false, false, false);
    }

    private AssetOrgUnitRes createAssetOrgUnitRes(Integer orgId, String orgCode, String orgName, String businessLine, Integer level, Boolean isVirtual) {
        return createAssetOrgUnitRes(orgId, orgCode, orgName, businessLine, level, isVirtual, false, false);
    }

    private AssetOrgUnitRes createAssetOrgUnitRes(Integer orgId, String orgCode, String orgName, String businessLine, Integer level, Boolean isVirtual, Boolean isAssetManageOrg) {
        return createAssetOrgUnitRes(orgId, orgCode, orgName, businessLine, level, isVirtual, isAssetManageOrg, false);
    }

    private AssetOrgUnitRes createAssetOrgUnitRes(Integer orgId, String orgCode, String orgName, String businessLine, Integer level, Boolean isVirtual, Boolean isAssetManageOrg, Boolean isAssetUseOrg) {
        AssetOrgUnitRes res = new AssetOrgUnitRes();
        res.setOrgId(orgId);
        res.setOrgCode(orgCode);
        res.setOrgName(orgName);
        res.setBusinessLine(businessLine);
        res.setLevel(level);
        res.setIsVirtual(isVirtual);
        res.setIsAssetManageOrg(isAssetManageOrg);
        res.setIsAssetUseOrg(isAssetUseOrg);
        return res;
    }

    private BusinessRoleInfoRes createBusinessRoleInfoRes(String roleCode, String roleName, String roleNameEn) {
        BusinessRoleInfoRes res = new BusinessRoleInfoRes();
        res.setRoleCode(roleCode);
        res.setRoleName(roleName);
        res.setRoleNameEn(roleNameEn);
        return res;
    }
    // ==================== 新增测试方法 ====================

    /**
     * 测试getOrgUnitById方法 - 正常场景（默认中文）
     */
    @Test
    void getOrgUnitById_normal() {
        // 准备数据
        Integer orgId = 1;
        AssetOrgUnit mockOrgUnit = createAssetOrgUnit(orgId, "ORG001", "测试部门", BusinessLine.ADM_EMP);
        AssetOrgUnitRes expectedRes = createAssetOrgUnitRes(orgId, "ORG001", "测试部门", "adm_emp");
        List<BusinessRoleInfoRes> businessRoleInfoList = Arrays.asList(
                createBusinessRoleInfoRes("ROLE001", "管理员", "Manager")
        );

        // 设置mock行为
        when(assetOrgAbility.loadOrgUnit(orgId)).thenReturn(mockOrgUnit);
        when(businessRoleAbility.getBusinessRoleInfo(mockOrgUnit, EAMConstants.CHINESE)).thenReturn(businessRoleInfoList);
        when(converter.toAssetOrgUnitRes(any(AssetOrgUnit.class), anyList())).thenReturn(expectedRes);

        // 执行测试
        AssetOrgUnitRes result = assetOrgProvider.getOrgUnitById(orgId);

        // 验证结果
        assertNotNull(result);
        assertEquals(orgId, result.getOrgId());
        assertEquals("ORG001", result.getOrgCode());
        assertEquals("测试部门", result.getOrgName());

        // 验证调用
        verify(assetOrgAbility).loadOrgUnit(orgId);
        verify(businessRoleAbility).getBusinessRoleInfo(mockOrgUnit, EAMConstants.CHINESE);
        verify(converter).toAssetOrgUnitRes(any(AssetOrgUnit.class), anyList());
    }

    /**
     * 测试getOrgUnitById方法 - 指定语言场景
     */
    @Test
    void getOrgUnitById_withLanguage() {
        // 准备数据
        Integer orgId = 1;
        String language = "en-US";
        AssetOrgUnit mockOrgUnit = createAssetOrgUnit(orgId, "ORG001", "测试部门", BusinessLine.ADM_EMP);
        AssetOrgUnitRes expectedRes = createAssetOrgUnitRes(orgId, "ORG001", "Test Department", "adm_emp");
        List<BusinessRoleInfoRes> businessRoleInfoList = Arrays.asList(
                createBusinessRoleInfoRes("ROLE001", "管理员", "Manager")
        );

        // 设置mock行为
        when(assetOrgAbility.loadOrgUnit(orgId)).thenReturn(mockOrgUnit);
        when(businessRoleAbility.getBusinessRoleInfo(mockOrgUnit, language)).thenReturn(businessRoleInfoList);
        when(converter.toAssetOrgUnitRes(mockOrgUnit, businessRoleInfoList)).thenReturn(expectedRes);

        // 执行测试
        AssetOrgUnitRes result = assetOrgProvider.getOrgUnitById(orgId, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(orgId, result.getOrgId());
        assertEquals("ORG001", result.getOrgCode());
        assertEquals("Test Department", result.getOrgName());

        // 验证调用
        verify(assetOrgAbility).loadOrgUnit(orgId);
        verify(businessRoleAbility).getBusinessRoleInfo(mockOrgUnit, language);
        verify(converter).toAssetOrgUnitRes(mockOrgUnit, businessRoleInfoList);
    }

    /**
     * 测试getOrgUnitById方法 - 空值场景
     */
    @Test
    void getOrgUnitById_nullOrgId() {
        // 准备数据
        Integer orgId = null;

        // 设置mock行为
        when(assetOrgAbility.loadOrgUnit(orgId)).thenReturn(null);
        when(businessRoleAbility.getBusinessRoleInfo(null, EAMConstants.CHINESE)).thenReturn(Collections.emptyList());
        when(converter.toAssetOrgUnitRes(isNull(), anyList())).thenReturn(null);

        // 执行测试
        AssetOrgUnitRes result = assetOrgProvider.getOrgUnitById(orgId);

        // 验证结果
        assertNull(result);

        // 验证调用
        verify(assetOrgAbility).loadOrgUnit(orgId);
        verify(businessRoleAbility).getBusinessRoleInfo(null, EAMConstants.CHINESE);
        verify(converter).toAssetOrgUnitRes(isNull(), anyList());
    }

    /**
     * 测试getAllotOrgList方法 - 资产管理组织场景
     */
    @Test
    void getAllotOrgList_assetManageOrg() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .isAssetManageOrg(true)
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "管理部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "管理部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "管理部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "管理部门2", "adm_emp")
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgAbility.getAllotOrgCodeList(query, AllotConfigField.IN_MANAGE_DEPT_CODE)).thenReturn(true);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toAssetOrgUnitRes(orgUnitList, EAMConstants.CHINESE)).thenReturn(resList);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAllotOrgList(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());
        assertEquals(pageSize, result.getPageSize());
        assertEquals(pageNum, result.getPageNum());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgAbility).getAllotOrgCodeList(query, AllotConfigField.IN_MANAGE_DEPT_CODE);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toAssetOrgUnitRes(orgUnitList, EAMConstants.CHINESE);
    }

    /**
     * 测试getAllotOrgList方法 - 资产使用组织场景
     */
    @Test
    void getAllotOrgList_assetUseOrg() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .isAssetUseOrg(true)
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .isAssetUseOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "使用部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "使用部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "使用部门1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "使用部门2", "adm_emp")
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgAbility.getAllotOrgCodeList(query, AllotConfigField.IN_USE_DEPT_CODE)).thenReturn(true);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toAssetOrgUnitRes(orgUnitList, EAMConstants.CHINESE)).thenReturn(resList);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAllotOrgList(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());
        assertEquals(pageSize, result.getPageSize());
        assertEquals(pageNum, result.getPageNum());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgAbility).getAllotOrgCodeList(query, AllotConfigField.IN_USE_DEPT_CODE);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toAssetOrgUnitRes(orgUnitList, EAMConstants.CHINESE);
    }

    /**
     * 测试getAllotOrgList方法 - 参数缺失异常场景
     */
    @Test
    void getAllotOrgList_missingParameter() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .build();

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);

        // 执行测试并验证异常
        assertThrows(ErrorCodeException.class, () -> {
            assetOrgProvider.getAllotOrgList(req, pageNum, pageSize);
        });

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgAbility, never()).getAllotOrgCodeList(any(), any());
        verify(assetOrgRepo, never()).orgUnitPageData(any(), any());
    }

    /**
     * 测试getAllotOrgList方法 - 无权限返回空列表场景
     */
    @Test
    void getAllotOrgList_noPermission() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .isAssetManageOrg(true)
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .isAssetManageOrg(true)
                .build();

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgAbility.getAllotOrgCodeList(query, AllotConfigField.IN_MANAGE_DEPT_CODE)).thenReturn(false);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAllotOrgList(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        assertEquals(0, result.getTotal());
        assertEquals(pageSize, result.getPageSize());
        assertEquals(pageNum, result.getPageNum());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgAbility).getAllotOrgCodeList(query, AllotConfigField.IN_MANAGE_DEPT_CODE);
        verify(assetOrgRepo, never()).orgUnitPageData(any(), any());
    }

    /**
     * 测试getAllotOrgList方法 - 指定语言场景
     */
    @Test
    void getAllotOrgList_withLanguage() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .isAssetManageOrg(true)
                .build();
        Integer pageNum = 1;
        Integer pageSize = 10;
        String language = "en-US";

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "管理部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "管理部门2", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, pageSize, pageNum, 2);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "Management Department 1", "adm_emp"),
                createAssetOrgUnitRes(2, "ORG002", "Management Department 2", "adm_emp")
        );

        PageData<AssetOrgUnitRes> expectedResult = PageData.of(resList, pageSize, pageNum, 2);

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgAbility.getAllotOrgCodeList(query, AllotConfigField.IN_MANAGE_DEPT_CODE)).thenReturn(true);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toAssetOrgUnitRes(orgUnitList, language)).thenReturn(resList);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAllotOrgList(req, pageNum, pageSize, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(2, result.getTotal());
        assertEquals(pageSize, result.getPageSize());
        assertEquals(pageNum, result.getPageNum());
        assertEquals("Management Department 1", result.getList().get(0).getOrgName());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgAbility).getAllotOrgCodeList(query, AllotConfigField.IN_MANAGE_DEPT_CODE);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toAssetOrgUnitRes(orgUnitList, language);
    }

    /**
     * 测试getAllotOrgList方法 - 空分页参数场景
     */
    @Test
    void getAllotOrgList_nullPageParams() {
        // 准备数据
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine("adm_emp")
                .isAssetManageOrg(true)
                .build();
        Integer pageNum = null;
        Integer pageSize = null;

        AssetOrgUnitQuery query = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "管理部门1", BusinessLine.ADM_EMP)
        );

        PageData<AssetOrgUnit> pageData = PageData.of(orgUnitList, 10, 1, 1);

        List<AssetOrgUnitRes> resList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "管理部门1", "adm_emp")
        );

        // 设置mock行为
        when(converter.toAssetOrgUnitQuery(req)).thenReturn(query);
        when(assetOrgAbility.getAllotOrgCodeList(query, AllotConfigField.IN_MANAGE_DEPT_CODE)).thenReturn(true);
        when(assetOrgRepo.orgUnitPageData(eq(query), any(PageRequest.class))).thenReturn(pageData);
        when(converter.toAssetOrgUnitRes(orgUnitList, EAMConstants.CHINESE)).thenReturn(resList);

        // 执行测试
        PageData<AssetOrgUnitRes> result = assetOrgProvider.getAllotOrgList(req, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        assertEquals(1, result.getTotal());
        assertEquals(10, result.getPageSize());
        assertEquals(1, result.getPageNum());

        // 验证调用
        verify(converter).toAssetOrgUnitQuery(req);
        verify(assetOrgAbility).getAllotOrgCodeList(query, AllotConfigField.IN_MANAGE_DEPT_CODE);
        verify(assetOrgRepo).orgUnitPageData(eq(query), any(PageRequest.class));
        verify(converter).toAssetOrgUnitRes(orgUnitList, EAMConstants.CHINESE);
    }

    /**
     * 测试getManageOrgUnit方法 - 默认中文场景
     */
    @Test
    void getManageOrgUnit_defaultLanguage() {
        // 准备数据
        String businessLineCode = "adm_emp";
        
        AssetOrgUnitQuery expectedQuery = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "管理部门1", BusinessLine.ADM_EMP, 1, false, true),
                createAssetOrgUnit(2, "ORG002", "管理部门2", BusinessLine.ADM_EMP, 1, false, true)
        );

        List<AssetOrgUnitRes> expectedResList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "管理部门1", "adm_emp", 1, false, true),
                createAssetOrgUnitRes(2, "ORG002", "管理部门2", "adm_emp", 1, false, true)
        );

        // 设置mock行为 - 当language是EAMConstants.CHINESE时，第三个参数应该是false
        when(assetOrgRepo.getOrgUnits(expectedQuery)).thenReturn(orgUnitList);
        when(converter.toAssetOrgUnitRes(orgUnitList, EAMConstants.CHINESE, false)).thenReturn(expectedResList);

        // 执行测试
        List<AssetOrgUnitRes> result = assetOrgProvider.getManageOrgUnit(businessLineCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("ORG001", result.get(0).getOrgCode());
        assertEquals("管理部门1", result.get(0).getOrgName());
        assertTrue(result.get(0).getIsAssetManageOrg());
        assertEquals("ORG002", result.get(1).getOrgCode());
        assertEquals("管理部门2", result.get(1).getOrgName());
        assertTrue(result.get(1).getIsAssetManageOrg());

        // 验证调用
        verify(assetOrgRepo).getOrgUnits(expectedQuery);
        verify(converter).toAssetOrgUnitRes(orgUnitList, EAMConstants.CHINESE, false);
    }

    /**
     * 测试getManageOrgUnit方法 - 指定中文语言场景
     */
    @Test
    void getManageOrgUnit_chineseLanguage() {
        // 准备数据
        String businessLineCode = "adm_emp";
        String language = "zh-CN";
        
        AssetOrgUnitQuery expectedQuery = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "管理部门1", BusinessLine.ADM_EMP, 1, false, true),
                createAssetOrgUnit(2, "ORG002", "管理部门2", BusinessLine.ADM_EMP, 1, false, true)
        );

        List<AssetOrgUnitRes> expectedResList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "管理部门1", "adm_emp", 1, false, true),
                createAssetOrgUnitRes(2, "ORG002", "管理部门2", "adm_emp", 1, false, true)
        );

        // 设置mock行为 - 当language是"zh-CN"时，第三个参数应该是false
        when(assetOrgRepo.getOrgUnits(expectedQuery)).thenReturn(orgUnitList);
        when(converter.toAssetOrgUnitRes(orgUnitList, language, false)).thenReturn(expectedResList);

        // 执行测试
        List<AssetOrgUnitRes> result = assetOrgProvider.getManageOrgUnit(businessLineCode, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("ORG001", result.get(0).getOrgCode());
        assertEquals("管理部门1", result.get(0).getOrgName());
        assertTrue(result.get(0).getIsAssetManageOrg());

        // 验证调用
        verify(assetOrgRepo).getOrgUnits(expectedQuery);
        verify(converter).toAssetOrgUnitRes(orgUnitList, language, false);
    }

    /**
     * 测试getManageOrgUnit方法 - 英文语言场景
     */
    @Test
    void getManageOrgUnit_englishLanguage() {
        // 准备数据
        String businessLineCode = "adm_emp";
        String language = "en-US";
        
        AssetOrgUnitQuery expectedQuery = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "管理部门1", BusinessLine.ADM_EMP, 1, false, true),
                createAssetOrgUnit(2, "ORG002", "管理部门2", BusinessLine.ADM_EMP, 1, false, true)
        );

        List<AssetOrgUnitRes> expectedResList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "Management Department 1", "adm_emp", 1, false, true),
                createAssetOrgUnitRes(2, "ORG002", "Management Department 2", "adm_emp", 1, false, true)
        );

        // 设置mock行为
        when(assetOrgRepo.getOrgUnits(expectedQuery)).thenReturn(orgUnitList);
        when(converter.toAssetOrgUnitRes(orgUnitList, language, true)).thenReturn(expectedResList);

        // 执行测试
        List<AssetOrgUnitRes> result = assetOrgProvider.getManageOrgUnit(businessLineCode, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("ORG001", result.get(0).getOrgCode());
        assertEquals("Management Department 1", result.get(0).getOrgName());
        assertTrue(result.get(0).getIsAssetManageOrg());
        assertEquals("ORG002", result.get(1).getOrgCode());
        assertEquals("Management Department 2", result.get(1).getOrgName());
        assertTrue(result.get(1).getIsAssetManageOrg());

        // 验证调用
        verify(assetOrgRepo).getOrgUnits(expectedQuery);
        verify(converter).toAssetOrgUnitRes(orgUnitList, language, true);
    }

    /**
     * 测试getManageOrgUnit方法 - 空结果场景
     */
    @Test
    void getManageOrgUnit_emptyResult() {
        // 准备数据
        String businessLineCode = "adm_emp";
        String language = "zh-CN";
        
        AssetOrgUnitQuery expectedQuery = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Collections.emptyList();
        List<AssetOrgUnitRes> expectedResList = Collections.emptyList();

        // 设置mock行为 - 当language是"zh-CN"时，第三个参数应该是false
        when(assetOrgRepo.getOrgUnits(expectedQuery)).thenReturn(orgUnitList);
        when(converter.toAssetOrgUnitRes(orgUnitList, language, false)).thenReturn(expectedResList);

        // 执行测试
        List<AssetOrgUnitRes> result = assetOrgProvider.getManageOrgUnit(businessLineCode, language);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用
        verify(assetOrgRepo).getOrgUnits(expectedQuery);
        verify(converter).toAssetOrgUnitRes(orgUnitList, language, false);
    }

    /**
     * 测试getManageOrgUnit方法 - 不同业务线场景
     */
    @Test
    void getManageOrgUnit_differentBusinessLine() {
        // 准备数据
        String businessLineCode = "pt_mobile";
        String language = "zh-CN";
        
        AssetOrgUnitQuery expectedQuery = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.PT_MOBILE)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "手机管理部门1", BusinessLine.PT_MOBILE, 1, false, true),
                createAssetOrgUnit(2, "ORG002", "手机管理部门2", BusinessLine.PT_MOBILE, 1, false, true)
        );

        List<AssetOrgUnitRes> expectedResList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "手机管理部门1", "pt_mobile", 1, false, true),
                createAssetOrgUnitRes(2, "ORG002", "手机管理部门2", "pt_mobile", 1, false, true)
        );

        // 设置mock行为 - 当language是"zh-CN"时，第三个参数应该是false
        when(assetOrgRepo.getOrgUnits(expectedQuery)).thenReturn(orgUnitList);
        when(converter.toAssetOrgUnitRes(orgUnitList, language, false)).thenReturn(expectedResList);

        // 执行测试
        List<AssetOrgUnitRes> result = assetOrgProvider.getManageOrgUnit(businessLineCode, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("ORG001", result.get(0).getOrgCode());
        assertEquals("手机管理部门1", result.get(0).getOrgName());
        assertEquals("pt_mobile", result.get(0).getBusinessLine());
        assertTrue(result.get(0).getIsAssetManageOrg());

        // 验证调用
        verify(assetOrgRepo).getOrgUnits(expectedQuery);
        verify(converter).toAssetOrgUnitRes(orgUnitList, language, false);
    }

    /**
     * 测试getManageOrgUnit方法 - 空语言参数场景
     */
    @Test
    void getManageOrgUnit_nullLanguage() {
        // 准备数据
        String businessLineCode = "adm_emp";
        String language = null;
        
        AssetOrgUnitQuery expectedQuery = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "管理部门1", BusinessLine.ADM_EMP, 1, false, true)
        );

        List<AssetOrgUnitRes> expectedResList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "管理部门1", "adm_emp", 1, false, true)
        );

        // 设置mock行为 - 当language是null时，第三个参数应该是true（因为!EAMConstants.CHINESE.equals(null)为true）
        when(assetOrgRepo.getOrgUnits(expectedQuery)).thenReturn(orgUnitList);
        when(converter.toAssetOrgUnitRes(orgUnitList, language, true)).thenReturn(expectedResList);

        // 执行测试
        List<AssetOrgUnitRes> result = assetOrgProvider.getManageOrgUnit(businessLineCode, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("ORG001", result.get(0).getOrgCode());
        assertEquals("管理部门1", result.get(0).getOrgName());

        // 验证调用
        verify(assetOrgRepo).getOrgUnits(expectedQuery);
        verify(converter).toAssetOrgUnitRes(orgUnitList, language, true);
    }

    /**
     * 测试getManageOrgUnit方法 - 空业务线编码场景
     */
    @Test
    void getManageOrgUnit_nullBusinessLineCode() {
        // 准备数据
        String businessLineCode = null;
        String language = "zh-CN";
        
        // 当 businessLineCode 为 null 时，BusinessLine.getByCode(null) 可能会返回 null 或抛出异常
        // 这里我们测试当业务线编码为空时的行为
        AssetOrgUnitQuery expectedQuery = AssetOrgUnitQuery.builder()
                .businessLine(null)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Collections.emptyList();
        List<AssetOrgUnitRes> expectedResList = Collections.emptyList();

        // 设置mock行为 - 当language是"zh-CN"时，第三个参数应该是false
        when(assetOrgRepo.getOrgUnits(expectedQuery)).thenReturn(orgUnitList);
        when(converter.toAssetOrgUnitRes(orgUnitList, language, false)).thenReturn(expectedResList);

        // 执行测试
        List<AssetOrgUnitRes> result = assetOrgProvider.getManageOrgUnit(businessLineCode, language);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用
        verify(assetOrgRepo).getOrgUnits(expectedQuery);
        verify(converter).toAssetOrgUnitRes(orgUnitList, language, false);
    }

    /**
     * 测试getManageOrgUnit方法 - 复杂业务线场景
     */
    @Test
    void getManageOrgUnit_complexBusinessLine() {
        // 准备数据
        String businessLineCode = "car";
        String language = "en-US";
        
        AssetOrgUnitQuery expectedQuery = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.CAR)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "CAR001", "汽车管理部门1", BusinessLine.CAR, 1, false, true),
                createAssetOrgUnit(2, "CAR002", "汽车管理部门2", BusinessLine.CAR, 2, false, true),
                createAssetOrgUnit(3, "CAR003", "汽车管理部门3", BusinessLine.CAR, 3, false, true)
        );

        List<AssetOrgUnitRes> expectedResList = Arrays.asList(
                createAssetOrgUnitRes(1, "CAR001", "Car Management Department 1", "car", 1, false, true),
                createAssetOrgUnitRes(2, "CAR002", "Car Management Department 2", "car", 2, false, true),
                createAssetOrgUnitRes(3, "CAR003", "Car Management Department 3", "car", 3, false, true)
        );

        // 设置mock行为
        when(assetOrgRepo.getOrgUnits(expectedQuery)).thenReturn(orgUnitList);
        when(converter.toAssetOrgUnitRes(orgUnitList, language, true)).thenReturn(expectedResList);

        // 执行测试
        List<AssetOrgUnitRes> result = assetOrgProvider.getManageOrgUnit(businessLineCode, language);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("CAR001", result.get(0).getOrgCode());
        assertEquals("Car Management Department 1", result.get(0).getOrgName());
        assertEquals(1, result.get(0).getLevel());
        assertEquals("CAR002", result.get(1).getOrgCode());
        assertEquals("Car Management Department 2", result.get(1).getOrgName());
        assertEquals(2, result.get(1).getLevel());
        assertEquals("CAR003", result.get(2).getOrgCode());
        assertEquals("Car Management Department 3", result.get(2).getOrgName());
        assertEquals(3, result.get(2).getLevel());

        // 验证调用
        verify(assetOrgRepo).getOrgUnits(expectedQuery);
        verify(converter).toAssetOrgUnitRes(orgUnitList, language, true);
    }

    /**
     * 测试getManageOrgUnit方法 - 默认语言与指定中文语言一致性
     */
    @Test
    void getManageOrgUnit_defaultVsChineseConsistency() {
        // 准备数据
        String businessLineCode = "adm_emp";
        
        AssetOrgUnitQuery expectedQuery = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.ADM_EMP)
                .isAssetManageOrg(true)
                .build();

        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "管理部门1", BusinessLine.ADM_EMP, 1, false, true)
        );

        List<AssetOrgUnitRes> expectedResList = Arrays.asList(
                createAssetOrgUnitRes(1, "ORG001", "管理部门1", "adm_emp", 1, false, true)
        );

        // 设置mock行为 - 为两次调用都设置相同的返回值
        // 注意：EAMConstants.CHINESE 和 "zh-CN" 都应该是中文，所以第三个参数都是false
        when(assetOrgRepo.getOrgUnits(expectedQuery)).thenReturn(orgUnitList);
        when(converter.toAssetOrgUnitRes(orgUnitList, EAMConstants.CHINESE, false)).thenReturn(expectedResList);
        when(converter.toAssetOrgUnitRes(orgUnitList, "zh-CN", false)).thenReturn(expectedResList);

        // 执行测试 - 默认语言
        List<AssetOrgUnitRes> resultDefault = assetOrgProvider.getManageOrgUnit(businessLineCode);
        
        // 执行测试 - 指定中文语言
        List<AssetOrgUnitRes> resultChinese = assetOrgProvider.getManageOrgUnit(businessLineCode, "zh-CN");

        // 验证结果一致性
        assertNotNull(resultDefault);
        assertNotNull(resultChinese);
        assertEquals(resultDefault.size(), resultChinese.size());
        assertEquals(resultDefault.get(0).getOrgCode(), resultChinese.get(0).getOrgCode());
        assertEquals(resultDefault.get(0).getOrgName(), resultChinese.get(0).getOrgName());

        // 验证调用 - 由于单参数方法内部会调用双参数方法，所以 getOrgUnits 被调用2次
        verify(assetOrgRepo, times(2)).getOrgUnits(expectedQuery);
    }

    /**
     * 测试getOrgStructureByCode方法 - 正常场景
     */
    @Test
    void getOrgStructureByCode_normal() {
        // 准备数据
        String orgCode = "ORG001";
        AssetOrgStructure mockOrgStructure = createAssetOrgStructure(orgCode, "测试部门", "Test Department", "PARENT001", 2);
        AssetOrgStructureRes expectedRes = createAssetOrgStructureRes(orgCode, "测试部门", "Test Department", "PARENT001", 2);
        String fullName = "技术部-测试部门";
        String fullNameEn = "Technology Department-Test Department";

        // 设置mock行为
        when(assetOrgAbility.loadStructure(orgCode)).thenReturn(mockOrgStructure);
        when(converter.toAssetOrgStructureRes(mockOrgStructure)).thenReturn(expectedRes);
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey(orgCode))).thenReturn(fullName);
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey(orgCode))).thenReturn(fullNameEn);

            // 执行测试
            AssetOrgStructureRes result = assetOrgProvider.getOrgStructureByCode(orgCode);

            // 验证结果
            assertNotNull(result);
            assertEquals(orgCode, result.getOrgCode());
            assertEquals("测试部门", result.getOrgName());
            assertEquals("Test Department", result.getOrgNameEn());
            assertEquals(fullName, result.getOrgFullName());
            assertEquals(fullNameEn, result.getOrgFullNameEn());

            // 验证调用
            verify(assetOrgAbility).loadStructure(orgCode);
            verify(converter).toAssetOrgStructureRes(mockOrgStructure);
            redisUtilsMock.verify(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey(orgCode)));
            redisUtilsMock.verify(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey(orgCode)));
        }
    }

    /**
     * 测试getOrgStructureByCode方法 - 组织结构不存在场景
     */
    @Test
    void getOrgStructureByCode_structureNotFound() {
        // 准备数据
        String orgCode = "NONEXISTENT";

        // 设置mock行为
        when(assetOrgAbility.loadStructure(orgCode)).thenThrow(new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "组织结构【NONEXISTENT】不存在"));

        // 执行测试并验证异常
        assertThrows(ErrorCodeException.class, () -> {
            assetOrgProvider.getOrgStructureByCode(orgCode);
        });

        // 验证调用
        verify(assetOrgAbility).loadStructure(orgCode);
        verify(converter, never()).toAssetOrgStructureRes(any(AssetOrgStructure.class));
    }

    /**
     * 测试getOrgStructureByCode方法 - Redis缓存为空场景
     */
    @Test
    void getOrgStructureByCode_redisCacheEmpty() {
        // 准备数据
        String orgCode = "ORG001";
        AssetOrgStructure mockOrgStructure = createAssetOrgStructure(orgCode, "测试部门", "Test Department", "PARENT001", 2);
        AssetOrgStructureRes expectedRes = createAssetOrgStructureRes(orgCode, "测试部门", "Test Department", "PARENT001", 2);

        // 设置mock行为
        when(assetOrgAbility.loadStructure(orgCode)).thenReturn(mockOrgStructure);
        when(converter.toAssetOrgStructureRes(mockOrgStructure)).thenReturn(expectedRes);
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey(orgCode))).thenReturn(null);
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey(orgCode))).thenReturn(null);

            // 执行测试
            AssetOrgStructureRes result = assetOrgProvider.getOrgStructureByCode(orgCode);

            // 验证结果
            assertNotNull(result);
            assertEquals(orgCode, result.getOrgCode());
            assertEquals("测试部门", result.getOrgName());
            assertNull(result.getOrgFullName());
            assertNull(result.getOrgFullNameEn());

            // 验证调用
            verify(assetOrgAbility).loadStructure(orgCode);
            verify(converter).toAssetOrgStructureRes(mockOrgStructure);
            redisUtilsMock.verify(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey(orgCode)));
            redisUtilsMock.verify(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey(orgCode)));
        }
    }

    /**
     * 测试getOrgStructureByCode方法 - 空组织编码场景
     */
    @Test
    void getOrgStructureByCode_nullOrgCode() {
        // 准备数据
        String orgCode = null;

        // 设置mock行为
        when(assetOrgAbility.loadStructure(orgCode)).thenThrow(new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "组织结构【null】不存在"));

        // 执行测试并验证异常
        assertThrows(ErrorCodeException.class, () -> {
            assetOrgProvider.getOrgStructureByCode(orgCode);
        });

        // 验证调用
        verify(assetOrgAbility).loadStructure(orgCode);
    }

    /**
     * 测试getBatchOrgStructureByCode方法 - 正常场景
     */
    @Test
    void getBatchOrgStructureByCode_normal() {
        // 准备数据
        List<String> orgCodes = Arrays.asList("ORG001", "ORG002");
        List<AssetOrgStructure> mockOrgStructures = Arrays.asList(
                createAssetOrgStructure("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2),
                createAssetOrgStructure("ORG002", "测试部门2", "Test Department 2", "PARENT001", 2)
        );
        List<AssetOrgStructureRes> expectedResList = Arrays.asList(
                createAssetOrgStructureRes("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2),
                createAssetOrgStructureRes("ORG002", "测试部门2", "Test Department 2", "PARENT001", 2)
        );

        // 设置mock行为
        when(assetOrgRepo.getOrgStructuresByCodes(orgCodes)).thenReturn(mockOrgStructures);
        when(converter.toAssetOrgStructureRes(mockOrgStructures)).thenReturn(expectedResList);
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG001"))).thenReturn("技术部-测试部门1");
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("ORG001"))).thenReturn("Technology Department-Test Department 1");
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG002"))).thenReturn("技术部-测试部门2");
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("ORG002"))).thenReturn("Technology Department-Test Department 2");

            // 执行测试
            List<AssetOrgStructureRes> result = assetOrgProvider.getBatchOrgStructureByCode(orgCodes);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("ORG001", result.get(0).getOrgCode());
            assertEquals("测试部门1", result.get(0).getOrgName());
            assertEquals("技术部-测试部门1", result.get(0).getOrgFullName());
            assertEquals("Technology Department-Test Department 1", result.get(0).getOrgFullNameEn());
            assertEquals("ORG002", result.get(1).getOrgCode());
            assertEquals("测试部门2", result.get(1).getOrgName());
            assertEquals("技术部-测试部门2", result.get(1).getOrgFullName());
            assertEquals("Technology Department-Test Department 2", result.get(1).getOrgFullNameEn());

            // 验证调用
            verify(assetOrgRepo).getOrgStructuresByCodes(orgCodes);
            verify(converter).toAssetOrgStructureRes(mockOrgStructures);
            redisUtilsMock.verify(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG001")));
            redisUtilsMock.verify(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("ORG001")));
            redisUtilsMock.verify(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG002")));
            redisUtilsMock.verify(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("ORG002")));
        }
    }

    /**
     * 测试getBatchOrgStructureByCode方法 - 空列表场景
     */
    @Test
    void getBatchOrgStructureByCode_emptyList() {
        // 准备数据
        List<String> orgCodes = Collections.emptyList();

        // 执行测试
        List<AssetOrgStructureRes> result = assetOrgProvider.getBatchOrgStructureByCode(orgCodes);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用
        verify(assetOrgRepo, never()).getOrgStructuresByCodes(any());
        verify(converter, never()).toAssetOrgStructureRes(anyList());
    }

    /**
     * 测试getBatchOrgStructureByCode方法 - null列表场景
     */
    @Test
    void getBatchOrgStructureByCode_nullList() {
        // 准备数据
        List<String> orgCodes = null;

        // 执行测试
        List<AssetOrgStructureRes> result = assetOrgProvider.getBatchOrgStructureByCode(orgCodes);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用
        verify(assetOrgRepo, never()).getOrgStructuresByCodes(any());
        verify(converter, never()).toAssetOrgStructureRes(anyList());
    }

    /**
     * 测试getBatchOrgStructureByCode方法 - 部分Redis缓存为空场景
     */
    @Test
    void getBatchOrgStructureByCode_partialRedisCacheEmpty() {
        // 准备数据
        List<String> orgCodes = Arrays.asList("ORG001", "ORG002");
        List<AssetOrgStructure> mockOrgStructures = Arrays.asList(
                createAssetOrgStructure("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2),
                createAssetOrgStructure("ORG002", "测试部门2", "Test Department 2", "PARENT001", 2)
        );
        List<AssetOrgStructureRes> expectedResList = Arrays.asList(
                createAssetOrgStructureRes("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2),
                createAssetOrgStructureRes("ORG002", "测试部门2", "Test Department 2", "PARENT001", 2)
        );

        // 设置mock行为
        when(assetOrgRepo.getOrgStructuresByCodes(orgCodes)).thenReturn(mockOrgStructures);
        when(converter.toAssetOrgStructureRes(mockOrgStructures)).thenReturn(expectedResList);
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG001"))).thenReturn("技术部-测试部门1");
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("ORG001"))).thenReturn(null);
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG002"))).thenReturn(null);
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("ORG002"))).thenReturn("Technology Department-Test Department 2");

            // 执行测试
            List<AssetOrgStructureRes> result = assetOrgProvider.getBatchOrgStructureByCode(orgCodes);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("技术部-测试部门1", result.get(0).getOrgFullName());
            assertNull(result.get(0).getOrgFullNameEn());
            assertNull(result.get(1).getOrgFullName());
            assertEquals("Technology Department-Test Department 2", result.get(1).getOrgFullNameEn());

            // 验证调用
            verify(assetOrgRepo).getOrgStructuresByCodes(orgCodes);
            verify(converter).toAssetOrgStructureRes(mockOrgStructures);
        }
    }

    /**
     * 测试getBatchOrgStructureByCode方法 - 单个组织编码场景
     */
    @Test
    void getBatchOrgStructureByCode_singleOrgCode() {
        // 准备数据
        List<String> orgCodes = Arrays.asList("ORG001");
        List<AssetOrgStructure> mockOrgStructures = Arrays.asList(
                createAssetOrgStructure("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2)
        );
        List<AssetOrgStructureRes> expectedResList = Arrays.asList(
                createAssetOrgStructureRes("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2)
        );

        // 设置mock行为
        when(assetOrgRepo.getOrgStructuresByCodes(orgCodes)).thenReturn(mockOrgStructures);
        when(converter.toAssetOrgStructureRes(mockOrgStructures)).thenReturn(expectedResList);
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG001"))).thenReturn("技术部-测试部门1");
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey("ORG001"))).thenReturn("Technology Department-Test Department 1");

            // 执行测试
            List<AssetOrgStructureRes> result = assetOrgProvider.getBatchOrgStructureByCode(orgCodes);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("ORG001", result.get(0).getOrgCode());
            assertEquals("测试部门1", result.get(0).getOrgName());
            assertEquals("技术部-测试部门1", result.get(0).getOrgFullName());
            assertEquals("Technology Department-Test Department 1", result.get(0).getOrgFullNameEn());

            // 验证调用
            verify(assetOrgRepo).getOrgStructuresByCodes(orgCodes);
            verify(converter).toAssetOrgStructureRes(mockOrgStructures);
        }
    }

    /**
     * 测试getBatchOrgStructureByCode方法 - 大量组织编码场景
     */
    @Test
    void getBatchOrgStructureByCode_largeList() {
        // 准备数据
        List<String> orgCodes = Arrays.asList("ORG001", "ORG002", "ORG003", "ORG004", "ORG005");
        List<AssetOrgStructure> mockOrgStructures = Arrays.asList(
                createAssetOrgStructure("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2),
                createAssetOrgStructure("ORG002", "测试部门2", "Test Department 2", "PARENT001", 2),
                createAssetOrgStructure("ORG003", "测试部门3", "Test Department 3", "PARENT001", 2),
                createAssetOrgStructure("ORG004", "测试部门4", "Test Department 4", "PARENT001", 2),
                createAssetOrgStructure("ORG005", "测试部门5", "Test Department 5", "PARENT001", 2)
        );
        List<AssetOrgStructureRes> expectedResList = Arrays.asList(
                createAssetOrgStructureRes("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2),
                createAssetOrgStructureRes("ORG002", "测试部门2", "Test Department 2", "PARENT001", 2),
                createAssetOrgStructureRes("ORG003", "测试部门3", "Test Department 3", "PARENT001", 2),
                createAssetOrgStructureRes("ORG004", "测试部门4", "Test Department 4", "PARENT001", 2),
                createAssetOrgStructureRes("ORG005", "测试部门5", "Test Department 5", "PARENT001", 2)
        );

        // 设置mock行为
        when(assetOrgRepo.getOrgStructuresByCodes(orgCodes)).thenReturn(mockOrgStructures);
        when(converter.toAssetOrgStructureRes(mockOrgStructures)).thenReturn(expectedResList);
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            // 模拟Redis返回数据
            for (int i = 1; i <= 5; i++) {
                String orgCode = "ORG00" + i;
                redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey(orgCode)))
                        .thenReturn("技术部-测试部门" + i);
                redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey(orgCode)))
                        .thenReturn("Technology Department-Test Department " + i);
            }

            // 执行测试
            List<AssetOrgStructureRes> result = assetOrgProvider.getBatchOrgStructureByCode(orgCodes);

            // 验证结果
            assertNotNull(result);
            assertEquals(5, result.size());
            for (int i = 0; i < 5; i++) {
                assertEquals("ORG00" + (i + 1), result.get(i).getOrgCode());
                assertEquals("测试部门" + (i + 1), result.get(i).getOrgName());
                assertEquals("技术部-测试部门" + (i + 1), result.get(i).getOrgFullName());
                assertEquals("Technology Department-Test Department " + (i + 1), result.get(i).getOrgFullNameEn());
            }

            // 验证调用
            verify(assetOrgRepo).getOrgStructuresByCodes(orgCodes);
            verify(converter).toAssetOrgStructureRes(mockOrgStructures);
        }
    }

    /**
     * 测试getBatchOrgStructureByCode方法 - 带业务线参数场景
     */
    @Test
    void getBatchOrgStructureByCode_withBusinessLine() {
        // 准备数据
        List<String> orgCodes = Arrays.asList("ORG001", "ORG002");
        BusinessLine businessLine = BusinessLine.ADM_EMP;
        List<AssetOrgStructure> mockOrgStructures = Arrays.asList(
                createAssetOrgStructure("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2, businessLine),
                createAssetOrgStructure("ORG002", "测试部门2", "Test Department 2", "PARENT001", 2, businessLine),
                createAssetOrgStructure("ORG003", "其他部门", "Other Department", "PARENT001", 2, BusinessLine.PT_MOBILE)
        );
        List<AssetOrgStructure> filteredStructures = Arrays.asList(
                createAssetOrgStructure("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2, businessLine),
                createAssetOrgStructure("ORG002", "测试部门2", "Test Department 2", "PARENT001", 2, businessLine)
        );
        List<AssetOrgStructureRes> expectedResList = Arrays.asList(
                createAssetOrgStructureRes("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2),
                createAssetOrgStructureRes("ORG002", "测试部门2", "Test Department 2", "PARENT001", 2)
        );

        // 设置mock行为
        when(assetOrgRepo.getOrgStructuresByCodes(orgCodes)).thenReturn(mockOrgStructures);
        when(converter.toAssetOrgStructureRes(filteredStructures)).thenReturn(expectedResList);
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG001"))).thenReturn("技术部-测试部门1");
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG002"))).thenReturn("技术部-测试部门2");

            // 执行测试
            List<AssetOrgStructureRes> result = assetOrgProvider.getBatchOrgStructureByCode(orgCodes, businessLine);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("ORG001", result.get(0).getOrgCode());
            assertEquals("ORG002", result.get(1).getOrgCode());

            // 验证调用
            verify(assetOrgRepo).getOrgStructuresByCodes(orgCodes);
            verify(converter).toAssetOrgStructureRes(filteredStructures);
            // 验证Redis调用
            redisUtilsMock.verify(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG001")));
            redisUtilsMock.verify(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG002")));
        }
    }

    /**
     * 测试getBatchOrgStructureByCode方法 - 带业务线参数空列表场景
     */
    @Test
    void getBatchOrgStructureByCode_withBusinessLineEmptyList() {
        // 准备数据
        List<String> orgCodes = Collections.emptyList();
        BusinessLine businessLine = BusinessLine.ADM_EMP;

        // 执行测试
        List<AssetOrgStructureRes> result = assetOrgProvider.getBatchOrgStructureByCode(orgCodes, businessLine);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用
        verify(assetOrgRepo, never()).getOrgStructuresByCodes(any());
        verify(converter, never()).toAssetOrgStructureRes(anyList());
    }

    /**
     * 测试getBatchOrgStructureByCode方法 - 带业务线参数null列表场景
     */
    @Test
    void getBatchOrgStructureByCode_withBusinessLineNullList() {
        // 准备数据
        List<String> orgCodes = null;
        BusinessLine businessLine = BusinessLine.ADM_EMP;

        // 执行测试
        List<AssetOrgStructureRes> result = assetOrgProvider.getBatchOrgStructureByCode(orgCodes, businessLine);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用
        verify(assetOrgRepo, never()).getOrgStructuresByCodes(any());
        verify(converter, never()).toAssetOrgStructureRes(anyList());
    }

    /**
     * 测试getBatchOrgStructureByCode方法 - 带业务线参数过滤场景
     */
    @Test
    void getBatchOrgStructureByCode_withBusinessLineFiltering() {
        // 准备数据
        List<String> orgCodes = Arrays.asList("ORG001", "ORG002", "ORG003");
        BusinessLine businessLine = BusinessLine.ADM_EMP;
        List<AssetOrgStructure> mockOrgStructures = Arrays.asList(
                createAssetOrgStructure("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2, businessLine),
                createAssetOrgStructure("ORG002", "测试部门2", "Test Department 2", "PARENT001", 2, null), // 业务线为空
                createAssetOrgStructure("ORG003", "其他部门", "Other Department", "PARENT001", 2, BusinessLine.PT_MOBILE) // 不同业务线
        );
        List<AssetOrgStructure> filteredStructures = Arrays.asList(
                createAssetOrgStructure("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2, businessLine),
                createAssetOrgStructure("ORG002", "测试部门2", "Test Department 2", "PARENT001", 2, null) // 业务线为空也会被包含
        );
        List<AssetOrgStructureRes> expectedResList = Arrays.asList(
                createAssetOrgStructureRes("ORG001", "测试部门1", "Test Department 1", "PARENT001", 2),
                createAssetOrgStructureRes("ORG002", "测试部门2", "Test Department 2", "PARENT001", 2)
        );

        // 设置mock行为
        when(assetOrgRepo.getOrgStructuresByCodes(orgCodes)).thenReturn(mockOrgStructures);
        when(converter.toAssetOrgStructureRes(filteredStructures)).thenReturn(expectedResList);
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG001"))).thenReturn("技术部-测试部门1");
            redisUtilsMock.when(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG002"))).thenReturn("技术部-测试部门2");

            // 执行测试
            List<AssetOrgStructureRes> result = assetOrgProvider.getBatchOrgStructureByCode(orgCodes, businessLine);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("ORG001", result.get(0).getOrgCode());
            assertEquals("ORG002", result.get(1).getOrgCode());

            // 验证调用
            verify(assetOrgRepo).getOrgStructuresByCodes(orgCodes);
            verify(converter).toAssetOrgStructureRes(filteredStructures);
            // 验证Redis调用
            redisUtilsMock.verify(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG001")));
            redisUtilsMock.verify(() -> RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey("ORG002")));
        }
    }

    // 辅助方法：创建AssetOrgStructure对象
    private AssetOrgStructure createAssetOrgStructure(String orgCode, String orgName, String orgNameEn, String parentCode, Integer level) {
        return createAssetOrgStructure(orgCode, orgName, orgNameEn, parentCode, level, null);
    }

    private AssetOrgStructure createAssetOrgStructure(String orgCode, String orgName, String orgNameEn, String parentCode, Integer level, BusinessLine businessLine) {
        return AssetOrgStructure.builder()
                .orgCode(orgCode)
                .orgName(orgName)
                .orgNameEn(orgNameEn)
                .parentCode(parentCode)
                .level(level)
                .businessLine(businessLine)
                .isVirtual(false)
                .defaultCostCenter("CC001")
                .build();
    }

    // 辅助方法：创建AssetOrgStructureRes对象
    private AssetOrgStructureRes createAssetOrgStructureRes(String orgCode, String orgName, String orgNameEn, String parentCode, Integer level) {
        AssetOrgStructureRes res = new AssetOrgStructureRes();
        res.setOrgCode(orgCode);
        res.setOrgName(orgName);
        res.setOrgNameEn(orgNameEn);
        res.setParentCode(parentCode);
        res.setLevel(level);
        res.setIsVirtual(false);
        res.setDefaultCostCenter("CC001");
        res.setBusinessLine("adm_emp");
        return res;
    }
}
