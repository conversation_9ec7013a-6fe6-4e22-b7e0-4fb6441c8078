package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxReq;
import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxRes;
import com.mi.oa.asset.commons.config.app.ability.CountryTaxAbility;
import com.mi.oa.asset.commons.config.app.converter.CountryTaxConverter;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryTaxDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryTaxRepo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CountryTaxProviderImplTest {
    @InjectMocks
    private CountryTaxProviderImpl provider;
    @Mock
    private CountryTaxRepo repo;
    @Mock
    private CountryTaxConverter converter;
    @Mock
    private CountryTaxAbility ability;

    @Test
    void getCountryTaxList_normal() {
        List<CountryTaxDo> doList = Collections.singletonList(new CountryTaxDo());
        List<CountryTaxRes> resList = Collections.singletonList(new CountryTaxRes());
        when(repo.searchAll()).thenReturn(doList);
        when(converter.listDoToRes(doList)).thenReturn(resList);
        List<CountryTaxRes> result = provider.getCountryTaxList();
        assertEquals(1, result.size());
    }

    @Test
    void getById_normal() {
        CountryTaxDo entity = new CountryTaxDo();
        when(repo.getById(1)).thenReturn(entity);
        CountryTaxRes res = new CountryTaxRes();
        when(converter.doToRes(entity)).thenReturn(res);
        assertEquals(res, provider.getById(1));
    }

    @Test
    void getById_null() {
        when(repo.getById(1)).thenReturn(null);
        assertNull(provider.getById(1));
    }

    @Test
    void saveOrUpdate_update() {
        CountryTaxReq req = new CountryTaxReq();
        CountryTaxDo entity = new CountryTaxDo();
        entity.setId(10);
        when(converter.reqToDo(req)).thenReturn(entity);
        doNothing().when(ability).check(entity);
        assertEquals(10, provider.saveOrUpdate(req));
        verify(repo).updateById(entity);
    }

    @Test
    void saveOrUpdate_insert() {
        CountryTaxReq req = new CountryTaxReq();
        CountryTaxDo entity = new CountryTaxDo();
        entity.setId(null);
        when(converter.reqToDo(req)).thenReturn(entity);
        doNothing().when(ability).check(entity);
        when(repo.save(entity)).thenReturn(99);
        assertEquals(99, provider.saveOrUpdate(req));
    }

    @Test
    void saveOrUpdate_checkException() {
        CountryTaxReq req = new CountryTaxReq();
        CountryTaxDo entity = new CountryTaxDo();
        when(converter.reqToDo(req)).thenReturn(entity);
        doThrow(new RuntimeException("check fail")).when(ability).check(entity);
        assertThrows(RuntimeException.class, () -> provider.saveOrUpdate(req));
    }

    @Test
    void removeByIds_normal() {
        doNothing().when(repo).deleteByIds(anyList());
        assertDoesNotThrow(() -> provider.removeByIds(Arrays.asList(1,2)));
    }

    @Test
    void byCountryId_normal() {
        List<CountryTaxDo> doList = Collections.singletonList(new CountryTaxDo());
        List<CountryTaxRes> resList = Collections.singletonList(new CountryTaxRes());
        when(repo.getByCountryId(1)).thenReturn(doList);
        when(converter.listDoToRes(doList)).thenReturn(resList);
        assertEquals(resList, provider.byCountryId(1));
    }

    @Test
    void byCountryId_empty() {
        when(repo.getByCountryId(1)).thenReturn(Collections.emptyList());
        assertNull(provider.byCountryId(1));
    }
} 