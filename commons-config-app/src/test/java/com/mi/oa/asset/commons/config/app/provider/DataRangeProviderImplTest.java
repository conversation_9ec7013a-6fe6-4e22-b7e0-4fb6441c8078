package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgProvider;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgUnitQueryReq;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgUnitRes;
import com.mi.oa.asset.commons.config.api.datarange.DataRangeProvider;
import com.mi.oa.asset.commons.config.api.datarange.DeptRangeRes;
import com.mi.oa.asset.commons.config.api.datarange.enums.DataRange;
import com.mi.oa.asset.commons.config.api.datarange.DataRangeReq;
import com.mi.oa.asset.commons.config.app.ability.DataRangeAbility;
import com.mi.oa.asset.commons.config.app.converter.DataRangeConverter;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.Warehouse;
import com.mi.oa.asset.eam.feign.client.HrodClient;
import com.mi.oa.asset.eam.feign.req.HrodEmployeeConditionReq;
import com.mi.oa.asset.eam.feign.res.HrodEmployeeRes;
import com.mi.oa.infra.oaucf.idm.api.IdmUserService;
import com.mi.oa.infra.oaucf.idm.api.rep.UserInfoDto;
import com.xiaomi.mit.api.PageData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

class DataRangeProviderImplTest {

    @InjectMocks
    private DataRangeProviderImpl dataRangeProvider;

    @Mock
    private DataRangeAbility dataRangeAbility;

    @Mock
    private IdmUserService idmUserService;

    @Mock
    private DataRangeConverter dataRangeConverter;

    @Mock
    private AssetOrgProvider provider;

    @Mock
    private HrodClient hrodClient;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getCostCenter_WhenUseDept_ShouldReturnCorrectCostCenter() {
        // 准备测试数据
        DataRangeReq req = new DataRangeReq();
        req.setDataRang(DataRange.USE_DEPT.getCode());
        req.setCompanyCode("TEST001");
        
        AssetOrgUnit orgUnit = AssetOrgUnit.builder()
                .costCenter("12345")
                .build();
        
        // 设置mock行为
        when(dataRangeAbility.getOrgUnitByCode(req)).thenReturn(orgUnit);
        
        // 执行测试
        String result = dataRangeProvider.getCostCenter(req);
        
        // 验证结果
        assertEquals("CTEST00112345", result);
    }

    @Test
    void getDeptList_WhenBusinessRole_ShouldReturnDeptList() {
        // 准备测试数据
        DataRangeReq req = new DataRangeReq();
        req.setDataRang(DataRange.BUSINESS_ROLE.getCode());
        
        AssetOrgUnit orgUnit = AssetOrgUnit.builder()
                .orgCode("DEPT001")
                .orgName("测试部门")
                .build();
        
        DeptRangeRes deptRangeRes = new DeptRangeRes();
        deptRangeRes.setDeptCode("DEPT001");
        deptRangeRes.setDeptName("测试部门");
        
        // 设置mock行为
        when(dataRangeAbility.getAssetOrgUnit(req, true)).thenReturn(Collections.singletonList(orgUnit));
        when(dataRangeConverter.toResList(Collections.singletonList(orgUnit)))
            .thenReturn(Collections.singletonList(deptRangeRes));
        
        // 执行测试
        List<DeptRangeRes> result = dataRangeProvider.getDeptList(req, true);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("DEPT001", result.get(0).getDeptCode());
    }

    @Test
    void getCompany_WhenUseDept_ShouldReturnCompany() {
        // 准备测试数据
        DataRangeReq req = new DataRangeReq();
        req.setDataRang(DataRange.USE_DEPT.getCode());
        req.setCompanyCode("COMP001");
        
        AssetOrgUnit orgUnit = AssetOrgUnit.builder()
                .companyCode("COMP001")
                .companyName("测试公司")
                .build();
        
        DeptRangeRes deptRangeRes = new DeptRangeRes();
        deptRangeRes.setCompanyCode("COMP001");
        deptRangeRes.setCompanyName("测试公司");
        
        // 设置mock行为
        when(dataRangeAbility.getOrgUnitByCode(req)).thenReturn(orgUnit);
        when(dataRangeConverter.toRes(orgUnit)).thenReturn(deptRangeRes);
        
        // 执行测试
        List<DeptRangeRes> result = dataRangeProvider.getCompany(req);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("COMP001", result.get(0).getCompanyCode());
    }

    @Test
    void getCostCenter_WhenEmployee_ShouldReturnCorrectCostCenter() {
        // 准备测试数据
        DataRangeReq req = new DataRangeReq();
        req.setDataRang(DataRange.EMPLOYEE.getCode());
        req.setUserName("testUser");
        
        UserInfoDto userInfo = new UserInfoDto();
        userInfo.setCostCt("EMP12345");
        
        // 设置mock行为
        when(dataRangeAbility.getUserInfo("testUser")).thenReturn(userInfo);
        
        // 执行测试
        String result = dataRangeProvider.getCostCenter(req);
        
        // 验证结果
        assertEquals("EMP12345", result);
    }

    @Test
    void getCostCenter_WhenCompanyCodeEmpty_ShouldReturnEmpty() {
        // 准备测试数据
        DataRangeReq req = new DataRangeReq();
        req.setDataRang(DataRange.USE_DEPT.getCode());
        req.setCompanyCode("");
        
        // 执行测试
        String result = dataRangeProvider.getCostCenter(req);
        
        // 验证结果
        assertEquals("", result);
    }

    @Test
    void getDeptList_WhenEmployee_ShouldReturnDeptList() {
        // 准备测试数据
        DataRangeReq req = new DataRangeReq();
        req.setDataRang(DataRange.EMPLOYEE.getCode());
        req.setUserName("testUser");
        
        UserInfoDto userInfo = new UserInfoDto();
        userInfo.setDeptId("DEPT001");
        
        AssetOrgUnit orgUnit = AssetOrgUnit.builder()
                .orgCode("DEPT001")
                .orgName("测试部门")
                .build();
        
        DeptRangeRes deptRangeRes = new DeptRangeRes();
        deptRangeRes.setDeptCode("DEPT001");
        deptRangeRes.setDeptName("测试部门");
        
        // 设置mock行为
        when(dataRangeAbility.getUserInfo("testUser")).thenReturn(userInfo);
        when(dataRangeAbility.getOrgUnitByCode(req)).thenReturn(orgUnit);
        when(dataRangeConverter.toRes(orgUnit)).thenReturn(deptRangeRes);
        
        // 执行测试
        List<DeptRangeRes> result = dataRangeProvider.getDeptList(req, true);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("DEPT001", result.get(0).getDeptCode());
    }

    @Test
    void getDeptList_WhenWarehouse_ShouldReturnDeptList() {
        // 准备测试数据
        DataRangeReq req = new DataRangeReq();
        req.setDataRang(DataRange.WAREHOUSE.getCode());
        req.setWarehouseCode("WH001");
        
        Warehouse warehouse = new Warehouse();
        warehouse.setDepartCode("DEPT001");
        
        AssetOrgUnit orgUnit = AssetOrgUnit.builder()
                .orgCode("DEPT001")
                .orgName("测试部门")
                .build();
        
        DeptRangeRes deptRangeRes = new DeptRangeRes();
        deptRangeRes.setDeptCode("DEPT001");
        deptRangeRes.setDeptName("测试部门");
        
        // 设置mock行为
        when(dataRangeAbility.getHouse("WH001")).thenReturn(warehouse);
        when(dataRangeAbility.getOrgUnitByCode(req)).thenReturn(orgUnit);
        when(dataRangeConverter.toRes(orgUnit)).thenReturn(deptRangeRes);
        
        // 执行测试
        List<DeptRangeRes> result = dataRangeProvider.getDeptList(req, true);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("DEPT001", result.get(0).getDeptCode());
    }

    @Test
    void getDeptList_WhenAll_ShouldReturnAllDeptList() {
        // 准备测试数据
        DataRangeReq req = new DataRangeReq();
        req.setDataRang(DataRange.ALL.getCode());
        req.setBusinessLine("TEST_LINE");
        req.setKey("测试");
        
        AssetOrgUnit orgUnit = AssetOrgUnit.builder()
                .orgCode("DEPT001")
                .orgName("测试部门")
                .build();
        
        DeptRangeRes deptRangeRes = new DeptRangeRes();
        deptRangeRes.setDeptCode("DEPT001");
        deptRangeRes.setDeptName("测试部门");
        
        AssetOrgUnitRes unitRes = new AssetOrgUnitRes();
        unitRes.setOrgCode("DEPT001");
        unitRes.setOrgName("测试部门");
        
        List<AssetOrgUnitRes> unitResList = Collections.singletonList(unitRes);
        PageData<AssetOrgUnitRes> pageData = new PageData<>(unitResList, 0, 0,0);
        
        // 设置mock行为
        when(dataRangeAbility.getAssetOrgUnit(req, true)).thenReturn(Collections.singletonList(orgUnit));
        when(dataRangeConverter.toResList(Collections.singletonList(orgUnit)))
            .thenReturn(Collections.singletonList(deptRangeRes));
        when(provider.getAssetOrgUnitPageData(any(), anyInt(), anyInt())).thenReturn(pageData);
        when(dataRangeConverter.unitToResList(unitResList))
            .thenReturn(Collections.singletonList(deptRangeRes));
        
        // 执行测试
        List<DeptRangeRes> result = dataRangeProvider.getDeptList(req, true);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.size() > 0);
        assertEquals("DEPT001", result.get(0).getDeptCode());
    }

    @Test
    void getCompany_WhenEmployee_ShouldReturnCompany() {
        // 准备测试数据
        DataRangeReq req = new DataRangeReq();
        req.setDataRang(DataRange.EMPLOYEE.getCode());
        req.setUserName("testUser");
        
        HrodEmployeeRes employeeRes = new HrodEmployeeRes();
        employeeRes.setMiCompanyCode("COMP001");
        employeeRes.setCompanyName("测试公司");
        
        // 设置mock行为
        when(hrodClient.getEmployeeByUserName(any())).thenReturn(employeeRes);
        
        // 执行测试
        List<DeptRangeRes> result = dataRangeProvider.getCompany(req);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("COMP001", result.get(0).getCompanyCode());
        assertEquals("测试公司", result.get(0).getCompanyName());
    }

    @Test
    void getCompany_WhenAll_ShouldReturnAllCompanies() {
        // 准备测试数据
        DataRangeReq req = new DataRangeReq();
        req.setDataRang(DataRange.ALL.getCode());
        req.setUseDeptCode("DEPT001");
        
        AssetOrgUnit orgUnit = AssetOrgUnit.builder()
                .companyCode("COMP001")
                .companyName("测试公司")
                .build();
        
        DeptRangeRes deptRangeRes = new DeptRangeRes();
        deptRangeRes.setCompanyCode("COMP001");
        deptRangeRes.setCompanyName("测试公司");
        
        // 设置mock行为
        when(dataRangeAbility.getOrgUnitByCode(req)).thenReturn(orgUnit);
        when(dataRangeConverter.toRes(orgUnit)).thenReturn(deptRangeRes);
        
        // 执行测试
        List<DeptRangeRes> result = dataRangeProvider.getCompany(req);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("COMP001", result.get(0).getCompanyCode());
    }
}
