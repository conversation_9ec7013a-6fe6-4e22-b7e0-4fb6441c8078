package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgProvider;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.CustomShareList;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.ShareRecordRepo;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.mi.oa.asset.eam.auth.data.DataPermissionService;
import com.mi.oa.asset.eam.dto.BaseRes;
import com.mi.oa.asset.eam.jxs.req.BaseQueryReq;
import com.mi.oa.asset.eam.jxs.res.ListDataRes;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRecord;
import com.mi.oa.asset.eam.jxs.utils.CommonUtils;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * AssetShareAbility单元测试类
 */
@ExtendWith(MockitoExtension.class)
class AssetShareAbilityTest {

    @InjectMocks
    private AssetShareAbility assetShareAbility;

    @Mock
    private UserInfoService userInfoService;

    @Mock
    private AssetOrgProvider assetOrgProvider;

    @Mock
    private ShareRecordRepo shareRecordRepo;

    @Mock
    private ShareConditionMergeAbility mergeAbility;

    @Mock
    private DataPermissionService dataPermissionService;

    private User mockUser;
    private BaseQueryReq mockReq;
    private static final String MOCK_USER_NAME = "testUser";
    private static final String MOCK_DEPT_CODE = "testDept";

    @BeforeEach
    void setUp() {
        mockUser = new User();
        mockUser.setUserName(MOCK_USER_NAME);
        mockUser.setDeptCode(MOCK_DEPT_CODE);

        mockReq = new BaseQueryReq();
        mockReq.setBusinessLine(Arrays.asList("testBusinessLine"));
        mockReq.setEamSource("testClient");

        // 设置通用mock行为，使用lenient()放宽限制
        lenient().doReturn(new com.mi.oa.asset.common.model.User()).when(userInfoService).getUserByUserName(anyString());
        lenient().when(assetOrgProvider.getParentCodes(anyString())).thenReturn(Collections.singletonList("dept1"));
    }

    /**
     * 测试customQueryShareAsset方法 - 正常场景
     * 当whereSql不为空时，应返回数据
     */
    @Test
    void customQueryShareAsset_WhenWhereSqlNotEmpty() {
        // 准备测试数据
        try (MockedStatic<AuthFacade> authFacadeMockedStatic = mockStatic(AuthFacade.class)) {
            authFacadeMockedStatic.when(AuthFacade::authedUserName).thenReturn(MOCK_USER_NAME);
            when(userInfoService.getUserByUserName(MOCK_USER_NAME)).thenReturn(mockUser);

            // 模拟查询结果
            List<Map<String, String>> mockData = new ArrayList<>();
            Map<String, String> asset1 = new HashMap<>();
            asset1.put(CommonConstant.ID, "1");
            asset1.put(CommonConstant.QUANTITY_CONSTANT, "5");
            mockData.add(asset1);

            ListDataRes<Map<String, String>> mockResponse = new ListDataRes<>();
            mockResponse.setList(mockData);
            mockResponse.setTotal(1);

            BaseRes<ListDataRes<Map<String, String>>> baseRes = new BaseRes<>();
            baseRes.setCode(0);
            baseRes.setData(mockResponse);

            // 执行测试
            ListDataRes<Map<String, String>> result = assetShareAbility.customQueryShareAsset(mockReq, "testScene");

            // 验证结果
            assertNotNull(result);
        }
    }

    /**
     * 测试customQueryShareAsset方法 - 边界场景
     * 当whereSql为空时，应返回空结果
     */
    @Test
    void customQueryShareAsset_WhenWhereSqlEmpty() {
        // 准备测试数据
        try (MockedStatic<AuthFacade> authFacadeMockedStatic = mockStatic(AuthFacade.class)) {
            authFacadeMockedStatic.when(AuthFacade::authedUserName).thenReturn(MOCK_USER_NAME);
            when(userInfoService.getUserByUserName(MOCK_USER_NAME)).thenReturn(mockUser);

            // 执行测试
            ListDataRes<Map<String, String>> result = assetShareAbility.customQueryShareAsset(mockReq, "testScene");

            // 验证结果
            assertNotNull(result);
            assertTrue(result.getList().isEmpty());
            assertEquals(0, result.getTotal());
        }
    }

    /**
     * 测试handleShareQuantity方法 - 正常场景
     * 验证共享数量处理逻辑
     */
    @Test
    void handleShareQuantity_ShouldProcessCorrectly() {
        // 准备测试数据
        List<Map<String, String>> data = new ArrayList<>();
        Map<String, String> item = new HashMap<>();
        item.put(CommonConstant.ID, "1");
        item.put(CommonConstant.QUANTITY_CONSTANT, "10");
        data.add(item);

        List<CustomShareList> shareLists = new ArrayList<>();
        CustomShareList shareList = new CustomShareList();
        shareList.setAssetId(1);
        shareList.setShareQuantity(5);
        shareLists.add(shareList);

        // 调用测试方法
        assetShareAbility.handleShareQuantity(data, shareLists);

        // 验证结果
        assertEquals("5", data.get(0).get(CommonConstant.SHARE_QUANTITY_CONSTANT));
    }

    /**
     * 测试handleShareQuantity方法 - 边界场景
     * 当共享清单为空时的处理
     */
    @Test
    void handleShareQuantity_WhenShareListEmpty() {
        // 准备测试数据
        List<Map<String, String>> data = new ArrayList<>();
        Map<String, String> item = new HashMap<>();
        item.put(CommonConstant.ID, "1");
        item.put(CommonConstant.QUANTITY_CONSTANT, "10");
        data.add(item);
        
        List<CustomShareList> shareLists = new ArrayList<>();
        
        // 调用测试方法
        assetShareAbility.handleShareQuantity(data, shareLists);

        // 验证结果
        assertEquals("10", data.get(0).get(CommonConstant.SHARE_QUANTITY_CONSTANT));
    }

    /**
     * 测试handleShareQuantity方法 - 特殊场景
     * 当有多个共享清单项指向同一资产时
     */
    @Test
    void handleShareQuantity_WhenMultipleShareItems() {
        // 准备测试数据
        List<Map<String, String>> data = new ArrayList<>();
        Map<String, String> item = new HashMap<>();
        item.put(CommonConstant.ID, "1");
        item.put(CommonConstant.QUANTITY_CONSTANT, "10");
        data.add(item);
        
        List<CustomShareList> shareLists = new ArrayList<>();
        
        // 添加两个指向同一资产的共享项
        CustomShareList shareList1 = new CustomShareList();
        shareList1.setAssetId(1);
        shareList1.setShareQuantity(3);
        shareLists.add(shareList1);
        
        CustomShareList shareList2 = new CustomShareList();
        shareList2.setAssetId(1);
        shareList2.setShareQuantity(5);
        shareLists.add(shareList2);
        
        // 调用测试方法
        assetShareAbility.handleShareQuantity(data, shareLists);

        // 验证结果 - 应该取最大值5
        assertEquals("5", data.get(0).get(CommonConstant.SHARE_QUANTITY_CONSTANT));
    }

    /**
     * 测试getShareWhereSql方法
     * 验证获取SQL条件的逻辑
     */
    @Test
    void getShareWhereSql_WhenNoShareRecords_ShouldReturnEmptyString() {
        // 准备数据
        try (MockedStatic<AuthFacade> authFacadeMockedStatic = mockStatic(AuthFacade.class)) {
            authFacadeMockedStatic.when(AuthFacade::authedUserName).thenReturn(MOCK_USER_NAME);
            lenient().doReturn(mockUser).when(userInfoService).getUserByUserName(anyString());

            String businessLine = "business1";
            String shareClient = "client1";
            lenient().when(shareRecordRepo.queryList(any(), anyString(), any(), anyString(), anyString()))
                    .thenReturn(Collections.emptyList());

            // 执行测试
            String result = assetShareAbility.getShareWhereSql(businessLine, shareClient);

            // 验证结果
            assertEquals("", result);
            verify(shareRecordRepo).queryList(
                    eq(Collections.singletonList(businessLine)),
                    eq(MOCK_USER_NAME),
                    eq(Collections.singletonList("dept1")),
                    isNull(),
                    eq(shareClient)
            );
        }
    }

    @Test
    void getShareWhereSql_WhenHasShareRecords_ShouldReturnEncodedSql() {
        // 准备数据
        try (MockedStatic<AuthFacade> authFacadeMockedStatic = mockStatic(AuthFacade.class)) {
            authFacadeMockedStatic.when(AuthFacade::authedUserName).thenReturn(MOCK_USER_NAME);
            when(userInfoService.getUserByUserName(MOCK_USER_NAME)).thenReturn(mockUser);

            String businessLine = "business1";
            String shareClient = "client1";
            ShareRecord record = new ShareRecord();
            record.setBusinessLine(businessLine);
            List<ShareRecord> records = Collections.singletonList(record);
            
            // 使用lenient()和更精确的参数匹配
            lenient().doReturn(records).when(shareRecordRepo).queryList(
                eq(Collections.singletonList(businessLine)),
                eq(MOCK_USER_NAME),
                eq(Collections.singletonList("dept1")),
                isNull(),
                eq(shareClient)
            );
            
            // 修改返回的SQL格式
            lenient().when(mergeAbility.buildShareWhereSql(anyString(), any(), any()))
                    .thenReturn("(business_line = 'business1' and id = 1)");

            // 执行测试
            String result = assetShareAbility.getShareWhereSql(businessLine, shareClient);

            // 验证结果
            assertNotNull(result);
            assertEquals(CommonUtils.encodeWhereSql("(business_line = 'business1' and id = 1)"), "TYHKGJ1c2luZXNzX2xpbmUgPSAnYnVzaW5lc3MxJyBhbmQgaWQgPSAxKQ==A=");
            verify(shareRecordRepo).queryList(
                    eq(Collections.singletonList(businessLine)),
                    eq(MOCK_USER_NAME),
                    eq(Collections.singletonList("dept1")),
                    isNull(),
                    eq(shareClient)
            );
        }
    }

    @Test
    void getShareWhereSql_WhenMultipleBusinessLines_ShouldReturnCombinedSql() {
        // 准备数据
        try (MockedStatic<AuthFacade> authFacadeMockedStatic = mockStatic(AuthFacade.class)) {
            authFacadeMockedStatic.when(AuthFacade::authedUserName).thenReturn(MOCK_USER_NAME);
            when(userInfoService.getUserByUserName(MOCK_USER_NAME)).thenReturn(mockUser);

            String businessLine = "business1;business2";
            String shareClient = "client1";
            ShareRecord record1 = new ShareRecord();
            record1.setBusinessLine("business1");
            ShareRecord record2 = new ShareRecord();
            record2.setBusinessLine("business2");
            List<ShareRecord> records = Arrays.asList(record1, record2);
            
            lenient().doReturn(records).when(shareRecordRepo).queryList(
                eq(Arrays.asList("business1", "business2")),
                eq(MOCK_USER_NAME),
                eq(Collections.singletonList("dept1")),
                isNull(),
                eq(shareClient)
            );
            
            lenient().when(mergeAbility.buildShareWhereSql(eq("business1"), any(), any()))
                    .thenReturn("id = 1");
            lenient().when(mergeAbility.buildShareWhereSql(eq("business2"), any(), any()))
                    .thenReturn("id = 2");

            // 执行测试
            String result = assetShareAbility.getShareWhereSql(businessLine, shareClient);

            // 验证结果
            assertNotNull(result);
            assertEquals(CommonUtils.encodeWhereSql("and (id = 1 or id = 2)"), "TYHYW5kIChpZCA9IDEgb3IgaWQgPSAyKQ==A=");
            verify(shareRecordRepo).queryList(
                    eq(Arrays.asList("business1", "business2")),
                    eq(MOCK_USER_NAME),
                    eq(Collections.singletonList("dept1")),
                    isNull(),
                    eq(shareClient)
            );
        }
    }
} 