package com.mi.oa.asset.commons.config.app.ability;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.asset.commons.config.api.function.DownTaskQueryReq;
import com.mi.oa.asset.commons.config.api.function.DownTaskRes;
import com.mi.oa.asset.commons.config.app.converter.DownTaskConverter;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.mi.oa.asset.eam.mybatis.DownloadTaskMapper;
import com.mi.oa.asset.eam.mybatis.DownloadTaskPo;
import com.mi.oa.asset.eam.mybatis.FunctionMapper;
import com.mi.oa.asset.eam.mybatis.FunctionPo;
import com.xiaomi.mit.api.PageData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FunctionAbilityExtraTest {

    @InjectMocks
    private FunctionAbility functionAbility;

    @Mock
    private DownloadTaskMapper downloadTaskMapper;

    @Mock
    private FunctionMapper functionMapper;

    @Mock
    private DownTaskConverter downTaskConverter;

    private static final String TEST_USER = "test_user";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // We need to inject mocks manually since FunctionAbility has many dependencies that we are not mocking here.
        // This is a simplified setup for the specific methods under test.
        // In a real scenario, you'd likely have a more comprehensive setup.
    }

    @Test
    void listDownTask_NonChineseLanguage_TranslatesTaskName() {
        // 准备测试数据
        DownTaskQueryReq query = new DownTaskQueryReq();
        query.setPageNum(1);
        query.setPageSize(10);
        query.setLanguage("en-US");

        DownloadTaskPo taskPo = createDownloadTaskPo(1, "amg_task_one_down", "SUCCESS");
        taskPo.setFuncCode("task_one");
        List<DownloadTaskPo> taskList = Collections.singletonList(taskPo);
        Page<DownloadTaskPo> page = new Page<>(1, 10);
        page.setRecords(taskList);
        page.setTotal(1);

        FunctionPo functionPo = createFunctionPo("task_one", "table_one");
        functionPo.setEnName("Task One");
        when(functionMapper.selectList(any())).thenReturn(Collections.singletonList(functionPo));

        // Let's assume the converter will be called with the modified page. We'll mock the final output.
        DownTaskRes taskRes = createDownTaskRes(1, "Task One download", "SUCCESS");
        PageData<DownTaskRes> expectedPageData = new PageData<>(Collections.singletonList(taskRes), 10, 1, 1);
        when(downTaskConverter.toPageData(any(Page.class), any(Function.class))).thenReturn((PageData) expectedPageData);


        // 设置mock行为
        try (MockedStatic<AuthFacade> authFacade = mockStatic(AuthFacade.class)) {
            authFacade.when(AuthFacade::authedUserName).thenReturn(TEST_USER);
            when(downloadTaskMapper.selectPage(any(), any())).thenReturn(page);

            // 执行测试
            PageData<DownTaskRes> result = functionAbility.listDownTask(query);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            assertEquals("Task One download", result.getList().get(0).getTaskName());

            // 验证调用
            verify(downloadTaskMapper).selectPage(any(), any());
            verify(functionMapper).selectList(any());
            verify(downTaskConverter).toPageData(any(Page.class), any(Function.class));
        }
    }

    @Test
    void getFunctionMap_NullOrEmptyList_ReturnsEmptyMap() {
        // 测试null列表
        Map<String, FunctionPo> resultForNull = functionAbility.getFunctionMap(null);
        assertNotNull(resultForNull);
        assertTrue(resultForNull.isEmpty());

        // 测试空列表
        Map<String, FunctionPo> resultForEmpty = functionAbility.getFunctionMap(Collections.emptyList());
        assertNotNull(resultForEmpty);
        assertTrue(resultForEmpty.isEmpty());

        // 验证functionMapper没有被调用
        verify(functionMapper, never()).selectList(any());
    }

    @Test
    void getFunctionMap_MapperReturnsEmpty_ReturnsEmptyMap() {
        // 准备测试数据
        List<String> codeList = Arrays.asList("code1", "code2");

        // 设置mock行为
        when(functionMapper.selectList(any())).thenReturn(Collections.emptyList());

        // 执行测试
        Map<String, FunctionPo> result = functionAbility.getFunctionMap(codeList);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用
        verify(functionMapper).selectList(any());
    }

    @Test
    void getFunctionMap_MapperReturnsFunctions_ReturnsFunctionMap() {
        // 准备测试数据
        List<String> codeList = Arrays.asList("func1", "func2");
        FunctionPo func1 = createFunctionPo("func1", "table1");
        FunctionPo func2 = createFunctionPo("func2", "table2");
        List<FunctionPo> functionList = Arrays.asList(func1, func2);

        // 设置mock行为
        when(functionMapper.selectList(any())).thenReturn(functionList);

        // 执行测试
        Map<String, FunctionPo> result = functionAbility.getFunctionMap(codeList);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("func1"));
        assertTrue(result.containsKey("func2"));
        assertEquals("table1", result.get("func1").getTableName());
        assertEquals("table2", result.get("func2").getTableName());

        // 验证调用
        verify(functionMapper).selectList(any());
    }

    // Helper methods to create test data
    private DownloadTaskPo createDownloadTaskPo(Integer id, String taskName, String taskStatus) {
        DownloadTaskPo po = new DownloadTaskPo();
        po.setId(id);
        po.setTaskName(taskName);
        po.setTaskStatus(taskStatus);
        return po;
    }

    private DownTaskRes createDownTaskRes(Integer id, String taskName, String taskStatus) {
        DownTaskRes res = new DownTaskRes();
        res.setId(id);
        res.setTaskName(taskName);
        res.setTaskStatus(taskStatus);
        return res;
    }

    private FunctionPo createFunctionPo(String code, String tableName) {
        FunctionPo po = new FunctionPo();
        po.setCode(code);
        po.setTableName(tableName);
        return po;
    }
}
