package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.businessline.BusinessLineRes;
import com.mi.oa.asset.commons.config.api.businessline.ManageLineRes;
import com.mi.oa.asset.commons.config.app.converter.BusinessLineConvertor;
import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.domain.common.entity.ManageLineDo;
import com.mi.oa.asset.commons.config.domain.common.repository.BusinessLineRepo;
import com.mi.oa.asset.commons.config.domain.common.repository.ManageLineRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

// AIGC START

@ExtendWith(MockitoExtension.class)
class BusinessLineProviderImplNewTest {

    @InjectMocks
    private BusinessLineProviderImpl businessLineProvider;

    @Mock
    private BusinessLineRepo businessLineRepo;

    @Mock
    private ManageLineRepo manageLineRepo;

    @Mock
    private BusinessLineConvertor businessLineConvertor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getBusinessLineList_Chinese() {
        // 准备测试数据
        List<BusinessLineDo> mockBusinessLineDos = Arrays.asList(
            createMockBusinessLineDo(1, "业务线1", "Business Line 1"),
            createMockBusinessLineDo(2, "业务线2", "Business Line 2")
        );
        List<BusinessLineRes> mockBusinessLineRes = Arrays.asList(
            createMockBusinessLineRes(1, "业务线1", "Business Line 1"),
            createMockBusinessLineRes(2, "业务线2", "Business Line 2")
        );

        // 设置mock行为
        when(businessLineRepo.searchAll()).thenReturn(mockBusinessLineDos);
        when(businessLineConvertor.doToResBatch(mockBusinessLineDos)).thenReturn(mockBusinessLineRes);

        // 执行测试
        List<BusinessLineRes> result = businessLineProvider.getBusinessLineList(EAMConstants.CHINESE);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("业务线1", result.get(0).getBusinessLineName());
        assertEquals("业务线2", result.get(1).getBusinessLineName());

        // 验证方法调用
        verify(businessLineRepo).searchAll();
        verify(businessLineConvertor).doToResBatch(mockBusinessLineDos);
    }

    @Test
    void getBusinessLineList_English() {
        // 准备测试数据
        List<BusinessLineDo> mockBusinessLineDos = Arrays.asList(
            createMockBusinessLineDo(1, "业务线1", "Business Line 1"),
            createMockBusinessLineDo(2, "业务线2", "Business Line 2")
        );
        List<BusinessLineRes> mockBusinessLineRes = Arrays.asList(
            createMockBusinessLineRes(1, "业务线1", "Business Line 1"),
            createMockBusinessLineRes(2, "业务线2", "Business Line 2")
        );

        // 设置mock行为
        when(businessLineRepo.searchAll()).thenReturn(mockBusinessLineDos);
        when(businessLineConvertor.doToResBatch(mockBusinessLineDos)).thenReturn(mockBusinessLineRes);

        // 执行测试
        List<BusinessLineRes> result = businessLineProvider.getBusinessLineList("en");

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Business Line 1", result.get(0).getBusinessLineName());
        assertEquals("Business Line 2", result.get(1).getBusinessLineName());
    }

    @Test
    void getManageLineList_Chinese() {
        // 准备测试数据
        List<ManageLineDo> mockManageLineDos = Arrays.asList(
            createMockManageLineDo("管理线1", "Manage Line 1"),
            createMockManageLineDo("管理线2", "Manage Line 2")
        );
        List<ManageLineRes> mockManageLineRes = Arrays.asList(
            createMockManageLineRes("管理线1", "Manage Line 1"),
            createMockManageLineRes("管理线2", "Manage Line 2")
        );

        // 设置mock行为
        when(manageLineRepo.getManageLine()).thenReturn(mockManageLineDos);
        when(businessLineConvertor.doToManageResBatch(mockManageLineDos)).thenReturn(mockManageLineRes);

        // 执行测试
        List<ManageLineRes> result = businessLineProvider.getManageLineList(EAMConstants.CHINESE);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("管理线1", result.get(0).getManageLineName());
        assertEquals("管理线2", result.get(1).getManageLineName());

        // 验证方法调用
        verify(manageLineRepo).getManageLine();
        verify(businessLineConvertor).doToManageResBatch(mockManageLineDos);
    }

    @Test
    void getManageLineList_English() {
        // 准备测试数据
        List<ManageLineDo> mockManageLineDos = Arrays.asList(
            createMockManageLineDo("管理线1", "Manage Line 1"),
            createMockManageLineDo("管理线2", "Manage Line 2")
        );
        List<ManageLineRes> mockManageLineRes = Arrays.asList(
            createMockManageLineRes("管理线1", "Manage Line 1"),
            createMockManageLineRes("管理线2", "Manage Line 2")
        );

        // 设置mock行为
        when(manageLineRepo.getManageLine()).thenReturn(mockManageLineDos);
        when(businessLineConvertor.doToManageResBatch(mockManageLineDos)).thenReturn(mockManageLineRes);

        // 执行测试
        List<ManageLineRes> result = businessLineProvider.getManageLineList("en");

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Manage Line 1", result.get(0).getManageLineName());
        assertEquals("Manage Line 2", result.get(1).getManageLineName());
    }

    // 辅助方法
    private BusinessLineDo createMockBusinessLineDo(Integer id, String name, String nameEn) {
        BusinessLineDo businessLineDo = new BusinessLineDo();
        businessLineDo.setBusinessLineId(id);
        businessLineDo.setBusinessLineName(name);
        businessLineDo.setBusinessLineNameEn(nameEn);
        return businessLineDo;
    }

    private BusinessLineRes createMockBusinessLineRes(Integer id, String name, String nameEn) {
        BusinessLineRes businessLineRes = new BusinessLineRes();
        businessLineRes.setBusinessLineId(id);
        businessLineRes.setBusinessLineName(name);
        businessLineRes.setBusinessLineNameEn(nameEn);
        return businessLineRes;
    }

    private ManageLineDo createMockManageLineDo(String name, String nameEn) {
        ManageLineDo manageLineDo = new ManageLineDo();
        manageLineDo.setManageLineName(name);
        manageLineDo.setManageLineNameEn(nameEn);
        return manageLineDo;
    }

    private ManageLineRes createMockManageLineRes(String name, String nameEn) {
        ManageLineRes manageLineRes = new ManageLineRes();
        manageLineRes.setManageLineName(name);
        manageLineRes.setManageLineNameEn(nameEn);
        return manageLineRes;
    }
}
// AIGC END