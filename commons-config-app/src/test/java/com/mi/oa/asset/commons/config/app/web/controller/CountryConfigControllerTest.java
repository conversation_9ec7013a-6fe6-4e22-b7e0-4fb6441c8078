package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigProvider;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigReq;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigRes;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;

/**
 * CountryConfigController单元测试类
 */
@ExtendWith(MockitoExtension.class)
class CountryConfigControllerTest {

    @InjectMocks
    private CountryConfigController controller;

    @Mock
    private CountryConfigProvider provider;

    /**
     * 测试delete方法 - 正常场景
     * 删除SKU，验证是否正确调用provider
     */
    @Test
    void listForRegionConfig() {
        // 准备数据
        CountryConfigRes req = new CountryConfigRes();
        req.setId(null);
        req.setCountryName("rn");
        req.setCountryEnglishName("ern");
        req.setCountryCodeAlphaThree("CHN");


        // 执行测试
        Result<List<CountryConfigRes>> result = controller.listForRegionConfig();

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getByRegionId(0);
    }

    @Test
    void detail() {
        Integer countryConfigId = 1;
        // 执行测试
        Result<CountryConfigRes> result = controller.info(countryConfigId);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getById(countryConfigId);
    }

    @Test
    void list() {
        // 执行测试
        Result<List<CountryConfigRes>> result = controller.list();
        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getCountryConfigList();
    }

    @Test
    void save() {
        // 准备数据
        CountryConfigReq req = new CountryConfigReq();
        req.setId(null);
        req.setCountryName("guojiaCS");
        req.setCountryEnglishName("guojiaCS");
        req.setCountryCodeAlphaThree("GJC");


        // 执行测试
        Result<Void> result = controller.edit(req);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).saveOrUpdate(req);
    }

//    @Test
//    void update() {
//        // 准备数据
//        CountryConfigReq req = new CountryConfigReq();
//        req.setId(5);
//        req.setCountryName("rn");
//        req.setCountryEnglishName("ern");
//        req.setCountryCodeAlphaThree("GJC");
//
//
//        // 执行测试
//        Result<Void> result = controller.edit(req);
//
//        // 验证结果
//        assertNotNull(result);
//        // Result类没有isSuccess方法，直接验证其他属性
//        assertEquals(0, result.getCode());
//
//        // 验证调用
//        verify(provider).saveOrUpdate(req);
//    }

    @Test
    void delete() {
        // 准备数据
        CountryConfigReq req = new CountryConfigReq();
        req.setId(null);

        // 执行测试
        Result<Void> result = controller.delete(req);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());
        // 验证调用
        verify(provider).removeByIds(Collections.singletonList(req.getId()));
    }

    @Test
    void getAllCountries() {
        // 执行测试
        Result<List<CountryConfigRes>> result = controller.getAllCountries();
        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getAllCountries();
    }

    @Test
    void getByThreeCode() {
        String threeCode = "CHN";

        // 执行测试
        Result<CountryConfigRes> result = controller.getByThreeCode(threeCode);
        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getByThreeCode(threeCode);
    }

} 