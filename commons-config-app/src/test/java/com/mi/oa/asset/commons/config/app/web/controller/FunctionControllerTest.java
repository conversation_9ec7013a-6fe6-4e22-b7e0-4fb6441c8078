package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.function.FunctionProvider;
import com.mi.oa.asset.commons.config.app.ability.FunctionAbility;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.mi.oa.asset.eam.feign.service.EamFuncBaseSyncService;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FunctionControllerTest {

    @InjectMocks
    private FunctionController functionController;

    @Mock
    private FunctionProvider functionProvider;

    @Mock
    private FunctionAbility functionAbility;

    @Mock
    private EamFuncBaseSyncService syncService;

    @Test
    void updateRedis_WhenUserIsAdminAndTimeIsNull_ShouldCallRedisSet() {
        // 准备数据
        String key = "test_key";
        String value = "test_value";
        Long time = null;
        String adminUser = "admin_user";
        String admins = "admin_user,other_admin";

        // 设置私有字段
        ReflectionTestUtils.setField(functionController, "admins", admins);

        // 模拟静态方法
        try (MockedStatic<AuthFacade> authFacadeMock = mockStatic(AuthFacade.class);
             MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            
            // 设置AuthFacade行为
            authFacadeMock.when(AuthFacade::authedUserName).thenReturn(adminUser);
            
            // 设置RedisUtils行为
            redisUtilsMock.when(() -> RedisUtils.set(key, value)).thenAnswer(invocation -> null);

            // 执行测试
            Result<Void> result = functionController.updateRedis(key, value, time);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccessful());
            
            // 验证RedisUtils.set被调用
            redisUtilsMock.verify(() -> RedisUtils.set(key, value), times(1));
            
            // 验证RedisUtils.setEx没有被调用
            redisUtilsMock.verify(() -> RedisUtils.setEx(anyString(), anyString(), anyLong(), any(TimeUnit.class)), never());
        }
    }

    @Test
    void updateRedis_WhenUserIsAdminAndTimeIsZero_ShouldCallRedisSet() {
        // 准备数据
        String key = "test_key";
        String value = "test_value";
        Long time = 0L;
        String adminUser = "admin_user";
        String admins = "admin_user,other_admin";

        // 设置私有字段
        ReflectionTestUtils.setField(functionController, "admins", admins);

        // 模拟静态方法
        try (MockedStatic<AuthFacade> authFacadeMock = mockStatic(AuthFacade.class);
             MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            
            // 设置AuthFacade行为
            authFacadeMock.when(AuthFacade::authedUserName).thenReturn(adminUser);
            
            // 设置RedisUtils行为
            redisUtilsMock.when(() -> RedisUtils.set(key, value)).thenAnswer(invocation -> null);

            // 执行测试
            Result<Void> result = functionController.updateRedis(key, value, time);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccessful());
            
            // 验证RedisUtils.set被调用
            redisUtilsMock.verify(() -> RedisUtils.set(key, value), times(1));
            
            // 验证RedisUtils.setEx没有被调用
            redisUtilsMock.verify(() -> RedisUtils.setEx(anyString(), anyString(), anyLong(), any(TimeUnit.class)), never());
        }
    }

    @Test
    void updateRedis_WhenUserIsAdminAndTimeIsPositive_ShouldCallRedisSetEx() {
        // 准备数据
        String key = "test_key";
        String value = "test_value";
        Long time = 3600L;
        String adminUser = "admin_user";
        String admins = "admin_user,other_admin";

        // 设置私有字段
        ReflectionTestUtils.setField(functionController, "admins", admins);

        // 模拟静态方法
        try (MockedStatic<AuthFacade> authFacadeMock = mockStatic(AuthFacade.class);
             MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            
            // 设置AuthFacade行为
            authFacadeMock.when(AuthFacade::authedUserName).thenReturn(adminUser);
            
            // 设置RedisUtils行为
            redisUtilsMock.when(() -> RedisUtils.setEx(key, value, time, TimeUnit.SECONDS)).thenAnswer(invocation -> null);

            // 执行测试
            Result<Void> result = functionController.updateRedis(key, value, time);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccessful());
            
            // 验证RedisUtils.setEx被调用
            redisUtilsMock.verify(() -> RedisUtils.setEx(key, value, time, TimeUnit.SECONDS), times(1));
            
            // 验证RedisUtils.set没有被调用
            redisUtilsMock.verify(() -> RedisUtils.set(anyString(), anyString()), never());
        }
    }

    @Test
    void updateRedis_WhenUserIsNotAdmin_ShouldReturnSuccessWithoutCallingRedis() {
        // 准备数据
        String key = "test_key";
        String value = "test_value";
        Long time = 3600L;
        String nonAdminUser = "normal_user";
        String admins = "admin_user,other_admin";

        // 设置私有字段
        ReflectionTestUtils.setField(functionController, "admins", admins);

        // 模拟静态方法
        try (MockedStatic<AuthFacade> authFacadeMock = mockStatic(AuthFacade.class);
             MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            
            // 设置AuthFacade行为
            authFacadeMock.when(AuthFacade::authedUserName).thenReturn(nonAdminUser);

            // 执行测试
            Result<Void> result = functionController.updateRedis(key, value, time);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccessful());
            
            // 验证RedisUtils方法没有被调用
            redisUtilsMock.verify(() -> RedisUtils.set(anyString(), anyString()), never());
            redisUtilsMock.verify(() -> RedisUtils.setEx(anyString(), anyString(), anyLong(), any(TimeUnit.class)), never());
        }
    }

    @Test
    void updateRedis_WhenAdminsIsEmpty_ShouldReturnSuccessWithoutCallingRedis() {
        // 准备数据
        String key = "test_key";
        String value = "test_value";
        Long time = 3600L;
        String adminUser = "admin_user";
        String admins = "";

        // 设置私有字段
        ReflectionTestUtils.setField(functionController, "admins", admins);

        // 模拟静态方法
        try (MockedStatic<AuthFacade> authFacadeMock = mockStatic(AuthFacade.class);
             MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            
            // 设置AuthFacade行为
            authFacadeMock.when(AuthFacade::authedUserName).thenReturn(adminUser);

            // 执行测试
            Result<Void> result = functionController.updateRedis(key, value, time);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccessful());
            
            // 验证RedisUtils方法没有被调用
            redisUtilsMock.verify(() -> RedisUtils.set(anyString(), anyString()), never());
            redisUtilsMock.verify(() -> RedisUtils.setEx(anyString(), anyString(), anyLong(), any(TimeUnit.class)), never());
        }
    }

    @Test
    void updateRedis_WhenUserIsAdminWithLargeTimeValue_ShouldCallRedisSetEx() {
        // 准备数据
        String key = "test_key";
        String value = "test_value";
        Long time = 86400L; // 24小时
        String adminUser = "admin_user";
        String admins = "admin_user,other_admin";

        // 设置私有字段
        ReflectionTestUtils.setField(functionController, "admins", admins);

        // 模拟静态方法
        try (MockedStatic<AuthFacade> authFacadeMock = mockStatic(AuthFacade.class);
             MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            
            // 设置AuthFacade行为
            authFacadeMock.when(AuthFacade::authedUserName).thenReturn(adminUser);
            
            // 设置RedisUtils行为
            redisUtilsMock.when(() -> RedisUtils.setEx(key, value, time, TimeUnit.SECONDS)).thenAnswer(invocation -> null);

            // 执行测试
            Result<Void> result = functionController.updateRedis(key, value, time);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccessful());
            
            // 验证RedisUtils.setEx被调用
            redisUtilsMock.verify(() -> RedisUtils.setEx(key, value, time, TimeUnit.SECONDS), times(1));
        }
    }

    @Test
    void updateRedis_WhenUserIsAdminWithEmptyKeyAndValue_ShouldCallRedisSet() {
        // 准备数据
        String key = "";
        String value = "";
        Long time = null;
        String adminUser = "admin_user";
        String admins = "admin_user,other_admin";

        // 设置私有字段
        ReflectionTestUtils.setField(functionController, "admins", admins);

        // 模拟静态方法
        try (MockedStatic<AuthFacade> authFacadeMock = mockStatic(AuthFacade.class);
             MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            
            // 设置AuthFacade行为
            authFacadeMock.when(AuthFacade::authedUserName).thenReturn(adminUser);
            
            // 设置RedisUtils行为
            redisUtilsMock.when(() -> RedisUtils.set(key, value)).thenAnswer(invocation -> null);

            // 执行测试
            Result<Void> result = functionController.updateRedis(key, value, time);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccessful());
            
            // 验证RedisUtils.set被调用
            redisUtilsMock.verify(() -> RedisUtils.set(key, value), times(1));
        }
    }

    @Test
    void updateRedis_WhenUserIsAdminWithSpecialCharacters_ShouldCallRedisSet() {
        // 准备数据
        String key = "test:key:with:colons";
        String value = "test_value_with_special_chars_!@#$%^&*()";
        Long time = null;
        String adminUser = "admin_user";
        String admins = "admin_user,other_admin";

        // 设置私有字段
        ReflectionTestUtils.setField(functionController, "admins", admins);

        // 模拟静态方法
        try (MockedStatic<AuthFacade> authFacadeMock = mockStatic(AuthFacade.class);
             MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            
            // 设置AuthFacade行为
            authFacadeMock.when(AuthFacade::authedUserName).thenReturn(adminUser);
            
            // 设置RedisUtils行为
            redisUtilsMock.when(() -> RedisUtils.set(key, value)).thenAnswer(invocation -> null);

            // 执行测试
            Result<Void> result = functionController.updateRedis(key, value, time);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccessful());
            
            // 验证RedisUtils.set被调用
            redisUtilsMock.verify(() -> RedisUtils.set(key, value), times(1));
        }
    }
}
