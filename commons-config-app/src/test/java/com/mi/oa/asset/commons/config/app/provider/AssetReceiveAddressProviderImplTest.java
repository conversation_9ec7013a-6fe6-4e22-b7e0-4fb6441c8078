package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.model.AdsDTO;
import com.mi.oa.asset.commons.config.app.converter.AssetReceiveAddressConverter;
import com.mi.oa.asset.commons.config.app.ability.AddressAbility;
import com.mi.oa.asset.commons.config.domain.address.enums.SubsetLevelEnum;
import com.mi.oa.asset.commons.config.domain.address.repository.AssetReceiveAddressRepo;
import com.mi.oa.asset.commons.config.infra.rpc.service.AddressServiceImpl;
import com.mi.oa.asset.eam.feign.client.AddressClient;
import com.xiaomi.mit.api.error.ErrorCodeException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AssetReceiveAddressProviderImplTest {

    @InjectMocks
    private AssetReceiveAddressProviderImpl assetReceiveAddressProvider;

    @Mock
    private AssetReceiveAddressConverter converter;

    @Mock
    private AssetReceiveAddressRepo assetReceiveAddressRepo;

    @Mock
    private AddressServiceImpl addressServiceImpl;

    @Mock
    private AddressClient addressClient;

    @Mock
    private AddressAbility addressAbility;

    private List<AdsDTO> mockProvinceList;
    private List<AdsDTO> mockCityList;
    private List<AdsDTO> mockDistrictList;
    private List<AdsDTO> mockStreetList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockProvinceList = new ArrayList<>();
        AdsDTO province1 = new AdsDTO();
        province1.setId(1);
        province1.setName("北京");
        mockProvinceList.add(province1);

        mockCityList = new ArrayList<>();
        AdsDTO city1 = new AdsDTO();
        city1.setId(2);
        city1.setName("朝阳区");
        mockCityList.add(city1);

        mockDistrictList = new ArrayList<>();
        AdsDTO district1 = new AdsDTO();
        district1.setId(3);
        district1.setName("三里屯");
        mockDistrictList.add(district1);

        mockStreetList = new ArrayList<>();
        AdsDTO street1 = new AdsDTO();
        street1.setId(4);
        street1.setName("工体北路");
        mockStreetList.add(street1);
    }

    @Test
    void qryAdsList_TypeEmpty_ThrowsException() {
        // 测试type为空的情况
        assertThrows(ErrorCodeException.class, () -> 
            assetReceiveAddressProvider.qryAdsList(null, 1)
        );
    }

    @Test
    void qryAdsList_Province_Success() {
        // 测试查询省份列表
        when(addressServiceImpl.getProvince(1)).thenReturn(mockProvinceList);

        List<AdsDTO> result = assetReceiveAddressProvider.qryAdsList(SubsetLevelEnum.PROVINCE.getName(), null);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("北京", result.get(0).getName());
    }

    @Test
    void qryAdsList_ProvinceWithParentId_Success() {
        // 测试查询指定parentId的省份列表
        when(addressServiceImpl.getProvince(2)).thenReturn(mockProvinceList);

        List<AdsDTO> result = assetReceiveAddressProvider.qryAdsList(SubsetLevelEnum.PROVINCE.getName(), 2);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("北京", result.get(0).getName());
    }

    @Test
    void qryAdsList_City_Success() {
        // 测试查询城市列表
        when(addressServiceImpl.getCity(1)).thenReturn(mockCityList);

        List<AdsDTO> result = assetReceiveAddressProvider.qryAdsList(SubsetLevelEnum.CITY.getName(), 1);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("朝阳区", result.get(0).getName());
    }

    @Test
    void qryAdsList_District_Success() {
        // 测试查询区县列表
        when(addressServiceImpl.getDistrict(2)).thenReturn(mockDistrictList);

        List<AdsDTO> result = assetReceiveAddressProvider.qryAdsList(SubsetLevelEnum.DISTRICT.getName(), 2);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("三里屯", result.get(0).getName());
    }

    @Test
    void qryAdsList_Street_Success() {
        // 测试查询街道列表
        when(addressServiceImpl.getStreet(3)).thenReturn(mockStreetList);

        List<AdsDTO> result = assetReceiveAddressProvider.qryAdsList(SubsetLevelEnum.STREET.getName(), 3);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("工体北路", result.get(0).getName());
    }

    @Test
    void qryAdsList_ProvinceWithMacau_Removed() {
        // 测试查询省份列表时移除澳门
        List<AdsDTO> provincesWithMacau = new ArrayList<>(mockProvinceList);
        AdsDTO macau = new AdsDTO();
        macau.setId(34);
        macau.setName("澳门");
        provincesWithMacau.add(macau);

        when(addressServiceImpl.getProvince(1)).thenReturn(provincesWithMacau);

        List<AdsDTO> result = assetReceiveAddressProvider.qryAdsList(SubsetLevelEnum.PROVINCE.getName(), null);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertFalse(result.stream().anyMatch(ads -> ads.getId() == 34));
    }

    @Test
    void qryAdsList_ProvinceWithIndia_Success() {
        // 测试查询印度省份列表
        List<AdsDTO> allProvinces = new ArrayList<>();
        AdsDTO india = new AdsDTO();
        india.setId(7607);
        india.setName("印度");
        allProvinces.add(india);
        AdsDTO other = new AdsDTO();
        other.setId(7608);
        other.setName("其他");
        allProvinces.add(other);

        when(addressServiceImpl.getProvince(28587)).thenReturn(allProvinces);

        List<AdsDTO> result = assetReceiveAddressProvider.qryAdsList(SubsetLevelEnum.PROVINCE.getName(), 28587);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(7607, result.get(0).getId());
        assertEquals("印度", result.get(0).getName());
    }
} 