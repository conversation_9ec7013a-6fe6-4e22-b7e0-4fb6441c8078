package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigRes;
import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyRes;
import com.mi.oa.asset.commons.config.api.regionconfig.EmployeeRegionCountryRes;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigReq;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryCurrencyDo;
import com.mi.oa.asset.commons.config.domain.international.entity.RegionConfigDo;
import com.mi.oa.asset.commons.config.domain.international.enums.DefaultDataEnums;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryConfigRepo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryCurrencyRepo;
import com.mi.oa.asset.commons.config.domain.international.repository.RegionConfigRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RegionConfigAbilityTest {
    @Spy
    @InjectMocks
    private RegionConfigAbility ability;
    @Mock
    private RegionConfigRepo regionConfigRepo;
    @Mock
    private CountryConfigRepo countryConfigRepo;
    @Mock
    private CountryCurrencyRepo currencyRepo;
    @Mock
    private com.mi.oa.asset.commons.config.app.converter.CountryConfigConverter countryConfigConverter;
    @Mock
    private com.mi.oa.asset.commons.config.app.converter.RegionConfigConverter regionConfigConverter;
    @Mock
    private com.mi.oa.asset.commons.config.app.converter.CountryCurrencyConverter currencyConverter;

    @Test
    void getRegionAndCountrysList_normal() {
        EmployeeRegionCountryRes res = new EmployeeRegionCountryRes();
        res.setId(1);
        List<EmployeeRegionCountryRes> input = Collections.singletonList(res);
        CountryConfigDo configDo = new CountryConfigDo();
        configDo.setId(10);
        configDo.setRegionId(1);
        List<CountryConfigDo> configDoList = Collections.singletonList(configDo);
        CountryConfigRes configRes = new CountryConfigRes();
        configRes.setId(10);
        configRes.setRegionId(1);
        List<CountryConfigRes> configResList = Collections.singletonList(configRes);
        CountryCurrencyDo currencyDo = new CountryCurrencyDo();
        currencyDo.setCountryConfigId(10);
        List<CountryCurrencyDo> currencyDoList = Collections.singletonList(currencyDo);
        CountryCurrencyRes currencyRes = new CountryCurrencyRes();
        currencyRes.setCountryConfigId(10);
        List<CountryCurrencyRes> currencyResList = Collections.singletonList(currencyRes);
        when(countryConfigRepo.getByRegionIds(anyList())).thenReturn(configDoList);
        when(countryConfigConverter.listDoToRes(configDoList)).thenReturn(configResList);
        when(currencyRepo.listCountryDefaultCurrency(anyList(), anyInt())).thenReturn(currencyDoList);
        when(currencyConverter.listDoToRes(currencyDoList)).thenReturn(currencyResList);
        List<EmployeeRegionCountryRes> result = ability.getRegionAndCountrysList(input);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getId());
        assertNotNull(result.get(0).getCountryConfigRes());
    }

    @Test
    void getRegionAndCountrysList_emptyInput() {
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.getRegionAndCountrysList(Collections.emptyList()));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
    }

    @Test
    void getRegionAndCountrysList_nullInput() {
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.getRegionAndCountrysList(null));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
    }

    @Test
    void getRegionAndCountrys() {
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.getRegionAndCountrys(null));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
    }

    @Test
    void getRegionAndCountrys_normal() {
        EmployeeRegionCountryRes input = new EmployeeRegionCountryRes();
        input.setId(2);
        CountryConfigDo configDo = new CountryConfigDo();
        configDo.setId(20);
        List<CountryConfigDo> configDoList = Collections.singletonList(configDo);
        CountryConfigRes configRes = new CountryConfigRes();
        configRes.setId(20);
        List<CountryConfigRes> configResList = Collections.singletonList(configRes);
        CountryCurrencyDo currencyDo = new CountryCurrencyDo();
        currencyDo.setCountryConfigId(20);
        when(countryConfigRepo.getByRegionId(2)).thenReturn(configDoList);
        when(countryConfigConverter.listDoToRes(configDoList)).thenReturn(configResList);
        when(currencyRepo.getCountryDefaultCurrency(anyInt(), anyInt())).thenReturn(currencyDo);
        when(currencyConverter.doToRes(currencyDo)).thenReturn(new CountryCurrencyRes());
        EmployeeRegionCountryRes result = ability.getRegionAndCountrys(input);
        assertNotNull(result);
        assertEquals(2, result.getId());
    }

    @Test
    void saveOrUpdate_emptyCountryList() {
        RegionConfigReq req = new RegionConfigReq();
        req.setCountryConfigIdList(Collections.emptyList());
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.saveOrUpdate(req));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
    }


    @Test
    void checkRegionCountry_countryNotExist() {
        when(countryConfigRepo.getById(anyInt())).thenReturn(null);
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.checkRegionCountry(Collections.singletonList(1), 1));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
    }

    @Test
    void checkRegionCountry_InOtherCountry() {
        when(countryConfigRepo.getById(22)).thenReturn(null);
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.checkRegionCountry(Collections.singletonList(22),25));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
    }

    @Test
    void checkRegionCountry_RegionMismatch() {
        CountryConfigDo mockConfig = new CountryConfigDo();
        mockConfig.setRegionId(30); // 不等于测试输入的25
        mockConfig.setCountryName("TestCountry");

        when(countryConfigRepo.getById(22)).thenReturn(mockConfig);
        ErrorCodeException ex = assertThrows(ErrorCodeException.class,
                () -> ability.checkRegionCountry(Collections.singletonList(22), 25));

        assertTrue(ex.getMessage().contains("在其他的地区归属下"));
    }


    @Test
    void deleteByIds_normal() {
        doNothing().when(regionConfigRepo).deleteByIds(anyList());
        when(countryConfigRepo.getByRegionIds(anyList())).thenReturn(Collections.emptyList());
        doNothing().when(countryConfigRepo).updateBatchById(anyList());
        assertDoesNotThrow(() -> ability.deleteByIds(Arrays.asList(1,2)));
    }

    @Test
    void saveReginCountry_normal() {
        List<Integer> ids = Arrays.asList(1,2);
        CountryConfigDo c1 = new CountryConfigDo();
        CountryConfigDo c2 = new CountryConfigDo();
        List<CountryConfigDo> configs = Arrays.asList(c1, c2);
        when(countryConfigRepo.getByIds(ids)).thenReturn(configs);
        doNothing().when(countryConfigRepo).updateBatchById(configs);
        assertDoesNotThrow(() -> ability.saveReginCountry(ids, 10));
    }

    @Test
    void updateReginIdByIds_normal() {
        CountryConfigDo c1 = new CountryConfigDo();
        c1.setRegionId(1);
        CountryConfigDo c2 = new CountryConfigDo();
        c2.setRegionId(2);
        List<CountryConfigDo> configs = Arrays.asList(c1, c2);
        doNothing().when(countryConfigRepo).updateBatchById(configs);
        assertDoesNotThrow(() -> ability.updateReginIdByIds(configs, 99));
        assertEquals(99, c1.getRegionId());
        assertEquals(99, c2.getRegionId());
    }

    @Test
    void saveOrUpdate_countryIdListEmpty_throwException() {
        RegionConfigReq req = new RegionConfigReq();
        req.setCountryConfigIdList(Collections.emptyList());
        try (MockedStatic<CollectionUtils> utils = Mockito.mockStatic(CollectionUtils.class)) {
            utils.when(() -> CollectionUtils.isEmpty(anyList())).thenReturn(true);
            ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.saveOrUpdate(req));
            assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
        }
    }

    @Test
    void saveOrUpdate_checkRegionCountryThrows() {
        RegionConfigReq req = new RegionConfigReq();
        req.setCountryConfigIdList(Arrays.asList(1,2));
        try (MockedStatic<CollectionUtils> utils = Mockito.mockStatic(CollectionUtils.class)) {
            utils.when(() -> CollectionUtils.isEmpty(anyList())).thenReturn(false);
            doThrow(new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "国家非法")).when(ability).checkRegionCountry(anyList(), anyInt());
            ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.saveOrUpdate(req));
            assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
        }
    }

    @Test
    void saveOrUpdate_newRegion() {
        RegionConfigReq req = new RegionConfigReq();
        req.setCountryConfigIdList(Arrays.asList(1,2));
        req.setId(null);
        RegionConfigDo entity = new RegionConfigDo();
        entity.setId(null);
        when(regionConfigConverter.reqToDo(req)).thenReturn(entity);
        when(regionConfigRepo.save(entity)).thenReturn(100);
        try (MockedStatic<CollectionUtils> utils = Mockito.mockStatic(CollectionUtils.class)) {
            utils.when(() -> CollectionUtils.isEmpty(anyList())).thenReturn(false);
            doNothing().when(ability).checkRegionCountry(anyList(), anyInt());
            doNothing().when(ability).saveReginCountry(anyList(), anyInt());
            assertDoesNotThrow(() -> ability.saveOrUpdate(req));
            verify(ability).saveReginCountry(Arrays.asList(1,2), 100);
        }
    }

//    @Test
//    void saveOrUpdate_updateRegion_byRegionIdEmpty_throwException() {
//        RegionConfigReq req = new RegionConfigReq();
//        req.setCountryConfigIdList(Arrays.asList(1,2));
//        req.setId(10);
//        RegionConfigDo entity = new RegionConfigDo();
//        entity.setId(10);
//        when(regionConfigConverter.reqToDo(req)).thenReturn(entity);
//        when(regionConfigRepo.updateById(entity)).thenReturn(1);
//        when(countryConfigRepo.getByRegionId(10)).thenReturn(Collections.emptyList());
//        try (MockedStatic<CollectionUtils> utils = Mockito.mockStatic(CollectionUtils.class)) {
//            utils.when(() -> CollectionUtils.isEmpty(anyList())).thenReturn(true);
//            doNothing().when(ability).checkRegionCountry(anyList(), anyInt());
//            ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> ability.saveOrUpdate(req));
//            assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
//        }
//    }

    @Test
    void saveOrUpdate_updateRegion_noChange() {
        RegionConfigReq req = new RegionConfigReq();
        req.setCountryConfigIdList(Arrays.asList(1,2));
        req.setId(10);
        RegionConfigDo entity = new RegionConfigDo();
        entity.setId(10);
        when(regionConfigConverter.reqToDo(req)).thenReturn(entity);
        when(regionConfigRepo.updateById(entity)).thenReturn(1);
        CountryConfigDo c1 = new CountryConfigDo(); c1.setId(1);
        CountryConfigDo c2 = new CountryConfigDo(); c2.setId(2);
        List<CountryConfigDo> byRegionId = Arrays.asList(c1, c2);
        when(countryConfigRepo.getByRegionId(10)).thenReturn(byRegionId);
        List<Integer> alreadyBoundIdList = Arrays.asList(1,2);
        try (MockedStatic<CollectionUtils> utils = Mockito.mockStatic(CollectionUtils.class)) {
            utils.when(() -> CollectionUtils.isEmpty(byRegionId)).thenReturn(false);
            utils.when(() -> CollectionUtils.isEqualCollection(alreadyBoundIdList, req.getCountryConfigIdList())).thenReturn(true);
            doNothing().when(ability).checkRegionCountry(anyList(), anyInt());
            assertDoesNotThrow(() -> ability.saveOrUpdate(req));
        }
    }

    @Test
    void saveOrUpdate_updateRegion_addCountry() {
        RegionConfigReq req = new RegionConfigReq();
        req.setCountryConfigIdList(Arrays.asList(1,2,3));
        req.setId(10);
        RegionConfigDo entity = new RegionConfigDo();
        entity.setId(10);
        when(regionConfigConverter.reqToDo(req)).thenReturn(entity);
        when(regionConfigRepo.updateById(entity)).thenReturn(1);
        CountryConfigDo c1 = new CountryConfigDo(); c1.setId(1);
        CountryConfigDo c2 = new CountryConfigDo(); c2.setId(2);
        List<CountryConfigDo> byRegionId = Arrays.asList(c1, c2);
        when(countryConfigRepo.getByRegionId(10)).thenReturn(byRegionId);
        List<Integer> alreadyBoundIdList = Arrays.asList(1,2);
        try (MockedStatic<CollectionUtils> utils = Mockito.mockStatic(CollectionUtils.class)) {
            utils.when(() -> CollectionUtils.isEmpty(byRegionId)).thenReturn(false);
            utils.when(() -> CollectionUtils.isEqualCollection(alreadyBoundIdList, req.getCountryConfigIdList())).thenReturn(false);
            utils.when(() -> CollectionUtils.isNotEmpty(anyList())).thenReturn(true);
            doNothing().when(ability).checkRegionCountry(anyList(), anyInt());
            doNothing().when(ability).saveReginCountry(anyList(), anyInt());
            doNothing().when(ability).updateReginIdByIds(anyList(), anyInt());
            assertDoesNotThrow(() -> ability.saveOrUpdate(req));
            verify(ability).saveReginCountry(req.getCountryConfigIdList(), 10);
        }
    }

    @Test
    void saveOrUpdate_updateRegion_removeCountry() {
        RegionConfigReq req = new RegionConfigReq();
        req.setCountryConfigIdList(Arrays.asList(1));
        req.setId(10);
        RegionConfigDo entity = new RegionConfigDo();
        entity.setId(10);
        when(regionConfigConverter.reqToDo(req)).thenReturn(entity);
        when(regionConfigRepo.updateById(entity)).thenReturn(1);
        CountryConfigDo c1 = new CountryConfigDo(); c1.setId(1);
        CountryConfigDo c2 = new CountryConfigDo(); c2.setId(2);
        List<CountryConfigDo> byRegionId = Arrays.asList(c1, c2);
        when(countryConfigRepo.getByRegionId(10)).thenReturn(byRegionId);
        List<Integer> alreadyBoundIdList = Arrays.asList(1,2);
        try (MockedStatic<CollectionUtils> utils = Mockito.mockStatic(CollectionUtils.class)) {
            utils.when(() -> CollectionUtils.isEmpty(byRegionId)).thenReturn(false);
            utils.when(() -> CollectionUtils.isEqualCollection(alreadyBoundIdList, req.getCountryConfigIdList())).thenReturn(false);
            utils.when(() -> CollectionUtils.isNotEmpty(anyList())).thenReturn(true);
            doNothing().when(ability).checkRegionCountry(anyList(), anyInt());
            doNothing().when(ability).saveReginCountry(anyList(), anyInt());
            doNothing().when(ability).updateReginIdByIds(anyList(), anyInt());
            assertDoesNotThrow(() -> ability.saveOrUpdate(req));
            verify(ability).updateReginIdByIds(anyList(), eq(0));
        }
    }
} 