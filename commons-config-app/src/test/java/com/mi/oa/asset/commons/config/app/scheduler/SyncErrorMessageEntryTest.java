package com.mi.oa.asset.commons.config.app.scheduler;

import com.mi.oa.asset.commons.config.app.ability.TranslateAbility;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * SyncErrorMessageEntry 单元测试
 * <AUTHOR>
 * @Date 2025/5/21
 */
@ExtendWith(MockitoExtension.class)
class SyncErrorMessageEntryTest {

    @InjectMocks
    private SyncErrorMessageEntry syncErrorMessageEntry;

    @Mock
    private TranslateAbility translateAbility;

    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }

    @Test
    @DisplayName("execute_正常执行_应该成功调用翻译能力并记录日志")
    void execute_NormalExecution_ShouldCallTranslateAbilityAndLogSuccess() {
        // 准备测试数据 - 模拟正常执行
        doNothing().when(translateAbility).pullDataToRedis(false);

        // 执行测试
        assertDoesNotThrow(() -> syncErrorMessageEntry.execute());

        // 验证结果
        verify(translateAbility, times(1)).pullDataToRedis(false);
        verifyNoMoreInteractions(translateAbility);
    }

    @Test
    @DisplayName("execute_翻译能力抛出异常_应该抛出异常并记录日志")
    void execute_TranslateAbilityThrowsException_ShouldThrowExceptionAndLogError() {
        // 准备测试数据 - 模拟翻译能力抛出异常
        String errorMessage = "pull data to redis fail";
        doThrow(new ErrorCodeException(ErrorCodes.FORBIDDEN, errorMessage))
            .when(translateAbility).pullDataToRedis(false);

        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, 
            () -> syncErrorMessageEntry.execute());

        // 验证结果
        assertEquals(ErrorCodes.FORBIDDEN, exception.getErrorCode());
        assertEquals(errorMessage, exception.getMessage());
        verify(translateAbility, times(1)).pullDataToRedis(false);
        verifyNoMoreInteractions(translateAbility);
    }

    @Test
    @DisplayName("execute_翻译能力抛出运行时异常_应该抛出异常")
    void execute_TranslateAbilityThrowsRuntimeException_ShouldThrowException() {
        // 准备测试数据 - 模拟翻译能力抛出运行时异常
        RuntimeException runtimeException = new RuntimeException("Unexpected error");
        doThrow(runtimeException).when(translateAbility).pullDataToRedis(false);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, 
            () -> syncErrorMessageEntry.execute());

        // 验证结果
        assertEquals("Unexpected error", exception.getMessage());
        verify(translateAbility, times(1)).pullDataToRedis(false);
        verifyNoMoreInteractions(translateAbility);
    }

    @Test
    @DisplayName("execute_多次调用_每次都应该成功执行")
    void execute_MultipleCalls_ShouldExecuteSuccessfullyEachTime() {
        // 准备测试数据
        doNothing().when(translateAbility).pullDataToRedis(false);

        // 执行测试 - 多次调用
        assertDoesNotThrow(() -> syncErrorMessageEntry.execute());
        assertDoesNotThrow(() -> syncErrorMessageEntry.execute());
        assertDoesNotThrow(() -> syncErrorMessageEntry.execute());

        // 验证结果 - 每次调用都应该执行一次
        verify(translateAbility, times(3)).pullDataToRedis(false);
        verifyNoMoreInteractions(translateAbility);
    }

    @Test
    @DisplayName("execute_验证IS_SG常量值_应该传递false给翻译能力")
    void execute_VerifyIsSgConstant_ShouldPassFalseToTranslateAbility() {
        // 准备测试数据
        doNothing().when(translateAbility).pullDataToRedis(any(Boolean.class));

        // 执行测试
        syncErrorMessageEntry.execute();

        // 验证结果 - 确保传递的是false
        verify(translateAbility, times(1)).pullDataToRedis(false);
        verifyNoMoreInteractions(translateAbility);
    }
} 