package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.app.ability.TranslateColumnAbility;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TranslateColumnProviderImplTest {

    @InjectMocks
    private TranslateColumnProviderImpl translateColumnProvider;

    @Mock
    private TranslateColumnAbility translateColumnAbility;

    @BeforeEach
    void setUp() {
        // 不需要额外的设置，MockitoExtension 会自动处理
    }

    @Test
    void translateColumn_Success() throws IOException {
        // 准备测试数据
        MultipartFile file = new MockMultipartFile(
            "test.xlsx",
            "test.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "test data".getBytes()
        );

        // 执行测试
        translateColumnProvider.translateColumn(file);

        // 验证结果
        verify(translateColumnAbility, times(1)).translateColumn(file);
    }

    @Test
    void translateDict_Success() throws IOException {
        // 准备测试数据
        MultipartFile file = new MockMultipartFile(
            "test.xlsx",
            "test.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "test data".getBytes()
        );

        // 执行测试
        translateColumnProvider.translateDict(file);

        // 验证结果
        verify(translateColumnAbility, times(1)).translateDict(file);
    }
}
