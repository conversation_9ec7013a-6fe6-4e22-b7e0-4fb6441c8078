package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxProvider;
import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxReq;
import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxRes;
import com.xiaomi.mit.api.Result;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;

/**
 * CountryTaxController单元测试类
 */
@ExtendWith(MockitoExtension.class)
class CountryTaxControllerTest {

    @InjectMocks
    private CountryTaxController controller;

    @Mock
    private CountryTaxProvider provider;

    @Test
    void detail() {
        Integer countryTaxId = 1;
        // 执行测试
        Result<CountryTaxRes> result = controller.info(countryTaxId);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getById(countryTaxId);
    }


    @Test
    void byCountryId() {
        Integer countryId = 1;
        // 执行测试
        Result<List<CountryTaxRes>> result = controller.byCountryId(countryId);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).byCountryId(countryId);
    }


    @Test
    void list() {
        // 执行测试
        Result<List<CountryTaxRes>> result = controller.list();
        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getCountryTaxList();
    }

    @Test
    void save() {
        // 准备数据
        CountryTaxReq req = new CountryTaxReq();
        req.setId(null);
        req.setCountryName("guojiaCS");
        req.setCountryCodeAlphaThree("GJC");


        // 执行测试
        Result<Integer> result = controller.edit(req);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).saveOrUpdate(req);
    }

//    @Test
//    void update() {
//        // 准备数据
//        CountryTaxReq req = new CountryTaxReq();
//        req.setId(5);
//        req.setCountryName("rn");
//        req.setCountryEnglishName("ern");
//        req.setCountryCodeAlphaThree("GJC");
//
//
//        // 执行测试
//        Result<Void> result = controller.edit(req);
//
//        // 验证结果
//        assertNotNull(result);
//        // Result类没有isSuccess方法，直接验证其他属性
//        assertEquals(0, result.getCode());
//
//        // 验证调用
//        verify(provider).saveOrUpdate(req);
//    }

    @Test
    void delete() {
        // 准备数据
        CountryTaxReq req = new CountryTaxReq();
        req.setId(null);

        // 执行测试
        Result<Void> result = controller.delete(req);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());
        // 验证调用
        verify(provider).removeByIds(Collections.singletonList(req.getId()));
    }

} 