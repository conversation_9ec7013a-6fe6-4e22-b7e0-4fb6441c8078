package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.domain.warehouse.entity.WarehouseArea;
import com.mi.oa.asset.commons.config.infra.database.dataobject.WarehouseAreaPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.WarehouseAreaMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.WarehouseAreaConvertor;
import com.mi.oa.asset.commons.config.infra.repository.impl.WarehouseAreaRepoImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WarehouseAreaRepoImplTest {

    @InjectMocks
    private WarehouseAreaRepoImpl warehouseAreaRepo;

    @Mock
    private WarehouseAreaMapper baseMapper;

    @Mock
    private WarehouseAreaConvertor converter;

    private WarehouseArea warehouseArea;
    private WarehouseAreaPo warehouseAreaPo;

    @BeforeEach
    void setUp() {
        warehouseArea = new WarehouseArea();
        warehouseArea.setId(1);
        warehouseArea.setWarehouseCode("WH001");
        warehouseArea.setWarehouseName("测试仓库");
        warehouseArea.setBusinessLine("MI");
        warehouseArea.setPriority(1);
        warehouseArea.setCountryId("1");
        warehouseArea.setProvinceId("2");
        warehouseArea.setCityId("3");
        warehouseArea.setAreaId("4");
        warehouseArea.setStreetId("5");
        warehouseArea.setAreaName("测试区域");

        warehouseAreaPo = new WarehouseAreaPo();
        warehouseAreaPo.setId(1);
        warehouseAreaPo.setWarehouseCode("WH001");
        warehouseAreaPo.setWarehouseName("测试仓库");
        warehouseAreaPo.setBusinessLine("MI");
        warehouseAreaPo.setPriority(1);
        warehouseAreaPo.setCountryId("1");
        warehouseAreaPo.setProvinceId("2");
        warehouseAreaPo.setCityId("3");
        warehouseAreaPo.setAreaId("4");
        warehouseAreaPo.setStreetId("5");
        warehouseAreaPo.setAreaName("测试区域");
    }

    @Test
    @DisplayName("保存仓库 - 新增场景")
    void saveWarehouse_Insert() {
        // 准备测试数据
        when(baseMapper.selectOne(any())).thenReturn(null);
        when(converter.toIndiaWarehousePo(warehouseArea)).thenReturn(warehouseAreaPo);
        when(baseMapper.insert(any())).thenReturn(1);

        // 执行测试
        Integer result = warehouseAreaRepo.saveWarehouse(warehouseArea);

        // 验证结果
        assertEquals(1, result);
        verify(baseMapper).selectOne(any());
        verify(baseMapper).insert(any());
        verify(converter).toIndiaWarehousePo(warehouseArea);
    }

    @Test
    @DisplayName("保存仓库 - 更新场景")
    void saveWarehouse_Update() {
        // 准备测试数据
        when(baseMapper.selectOne(any())).thenReturn(warehouseAreaPo);
        when(converter.toIndiaWarehousePo(warehouseArea)).thenReturn(warehouseAreaPo);
        when(baseMapper.updateById(any())).thenReturn(1);

        // 执行测试
        Integer result = warehouseAreaRepo.saveWarehouse(warehouseArea);

        // 验证结果
        assertEquals(1, result);
        verify(baseMapper).selectOne(any());
        verify(baseMapper).updateById(any());
        verify(converter).toIndiaWarehousePo(warehouseArea);
    }

    @Test
    @DisplayName("删除仓库")
    void removeWarehouse() {
        // 准备测试数据
        String warehouseCode = "WH001";
        String countryId = "1";
        String provinceId = "2";
        String cityId = "3";
        String areaId = "4";
        String streetId = "5";

        when(baseMapper.delete(any())).thenReturn(1);

        // 执行测试
        warehouseAreaRepo.removeWarehouse(warehouseCode, countryId, provinceId, cityId, areaId, streetId);

        // 验证结果
        verify(baseMapper).delete(any());
    }

//    @Test
//    @DisplayName("获取仓库区域列表")
//    void listByBizAndRegion() {
//        // 准备测试数据
//        List<String> businessLines = Arrays.asList("MI", "TV");
//        List<WarehouseAreaPo> poList = Collections.singletonList(warehouseAreaPo);
//        List<WarehouseArea> expectedList = Collections.singletonList(warehouseArea);
//
//        when(baseMapper.selectList(any())).thenReturn(poList);
//        when(converter.toDoList(poList)).thenReturn(expectedList);
//
//        // 执行测试
//        List<WarehouseArea> result = warehouseAreaRepo.listByBizAndRegion(businessLines, "1", "2", "3", "4", "5");
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(1, result.size());
//        assertEquals(warehouseArea.getWarehouseCode(), result.get(0).getWarehouseCode());
//        verify(baseMapper).selectList(any());
//        verify(converter).toDoList(poList);
//    }

    @Test
    @DisplayName("搜索仓库区域")
    void searchWarehouseArea() {
        // 准备测试数据
        String keyword = "测试";
        List<String> businessLines = Arrays.asList("MI", "TV");
        List<WarehouseAreaPo> poList = Collections.singletonList(warehouseAreaPo);
        List<WarehouseArea> expectedList = Collections.singletonList(warehouseArea);

        when(baseMapper.selectList(any())).thenReturn(poList);
        when(converter.toDoList(poList)).thenReturn(expectedList);

        // 执行测试
        List<WarehouseArea> result = warehouseAreaRepo.searchWarehouseArea(keyword, businessLines);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(warehouseArea.getWarehouseCode(), result.get(0).getWarehouseCode());
        verify(baseMapper).selectList(any());
        verify(converter).toDoList(poList);
    }
} 