package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.warehouse.RemoveWarehouseAreaReq;
import com.mi.oa.asset.commons.config.api.warehouse.WarehouseAreaReq;
import com.mi.oa.asset.commons.config.api.warehouse.WarehouseAreaRes;
import com.mi.oa.asset.commons.config.app.provider.WarehouseAreaProviderImpl;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.WarehouseArea;
import com.mi.oa.asset.commons.config.domain.warehouse.repository.WarehouseAreaRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WarehouseAreaProviderImplTest {

    @InjectMocks
    private WarehouseAreaProviderImpl warehouseAreaProvider;

    @Mock
    private WarehouseAreaRepo warehouseAreaRepo;

    @Test
    @DisplayName("保存仓库 - 成功场景")
    void saveWarehouse_Success() {
        // 准备测试数据
        WarehouseAreaReq req = new WarehouseAreaReq();
        req.setWarehouseCode("WH001");
        req.setWarehouseName("测试仓库");
        req.setBusinessLine("MI");
        req.setPriority(1);
        req.setAreaId("1-2-3-4-5");

        when(warehouseAreaRepo.saveWarehouse(any(WarehouseArea.class))).thenReturn(1);

        // 执行测试
        Integer result = warehouseAreaProvider.saveWarehouse(req);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result);
        verify(warehouseAreaRepo).saveWarehouse(any(WarehouseArea.class));
    }

    @Test
    @DisplayName("保存仓库 - 缺少必填字段")
    void saveWarehouse_MissingRequiredFields() {
        // 准备测试数据
        WarehouseAreaReq req = new WarehouseAreaReq();

        // 执行测试并验证异常
        assertThrows(NullPointerException.class, () -> warehouseAreaProvider.saveWarehouse(req));
        verifyNoInteractions(warehouseAreaRepo);
    }

    @Test
    @DisplayName("保存仓库 - 优先级非法")
    void saveWarehouse_InvalidPriority() {
        // 准备测试数据
        WarehouseAreaReq req = new WarehouseAreaReq();
        req.setWarehouseCode("WH001");
        req.setWarehouseName("测试仓库");
        req.setBusinessLine("MI");
        req.setPriority(0);

        // 执行测试并验证异常
        assertThrows(IllegalArgumentException.class, () -> warehouseAreaProvider.saveWarehouse(req));
        verifyNoInteractions(warehouseAreaRepo);
    }

    @Test
    @DisplayName("删除仓库 - 成功场景")
    void removeWarehouse_Success() {
        // 准备测试数据
        RemoveWarehouseAreaReq req = new RemoveWarehouseAreaReq();
        req.setWarehouseCode("WH001");
        req.setAreaId("1-2-3-4-5");

        // 执行测试
        warehouseAreaProvider.removeWarehouse(req);

        // 验证结果
        verify(warehouseAreaRepo).removeWarehouse(
            eq("WH001"),
            eq("1"), eq("2"), eq("3"), eq("4"), eq("5")
        );
    }



    @Test
    @DisplayName("获取仓库区域列表 - 业务线为空")
    void listByBizAndRegion_EmptyBusinessLines() {
        // 执行测试并验证异常
        assertThrows(ErrorCodeException.class,
            () -> warehouseAreaProvider.listByBizAndRegion(null, "1-2-3", null));
        assertThrows(ErrorCodeException.class,
            () -> warehouseAreaProvider.listByBizAndRegion(Collections.emptyList(), "1-2-3", null));
        verifyNoInteractions(warehouseAreaRepo);
    }

    @Test
    @DisplayName("搜索仓库区域 - 成功场景")
    void searchWarehouseArea_Success() {
        // 准备测试数据
        String keyword = "测试";
        List<String> businessLines = Arrays.asList("MI", "TV");

        WarehouseArea area = new WarehouseArea();
        area.setWarehouseCode("WH001");
        area.setWarehouseName("测试仓库");
        area.setBusinessLine("MI");
        area.setPriority(1);

        when(warehouseAreaRepo.searchWarehouseArea(keyword, businessLines))
            .thenReturn(Collections.singletonList(area));

        // 执行测试
        List<WarehouseAreaRes> result = warehouseAreaProvider.searchWarehouseArea(keyword, businessLines);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("WH001", result.get(0).getWarehouseCode());
        assertEquals("测试仓库", result.get(0).getWarehouseName());
        verify(warehouseAreaRepo).searchWarehouseArea(keyword, businessLines);
    }

    @Test
    @DisplayName("搜索仓库区域 - 业务线为空")
    void searchWarehouseArea_EmptyBusinessLines() {
        // 执行测试并验证异常
        assertThrows(ErrorCodeException.class,
            () -> warehouseAreaProvider.searchWarehouseArea("测试", null));
        assertThrows(ErrorCodeException.class,
            () -> warehouseAreaProvider.searchWarehouseArea("测试", Collections.emptyList()));
        verifyNoInteractions(warehouseAreaRepo);
    }


    @Test
    void listByBizAndRegion_WithEmptyBusinessLines_ShouldThrowException() {
        // 准备数据
        List<String> emptyBusinessLines = Collections.emptyList();

        // 执行测试并验证结果
        ErrorCodeException exception = assertThrows(ErrorCodeException.class,
                () -> warehouseAreaProvider.listByBizAndRegion(emptyBusinessLines, "CN-11-01", null));

        assertEquals(ErrorCodes.BAD_PARAMETER, exception.getErrorCode());
        assertEquals("业务线不能为空", exception.getMessage());

        // 验证mock对象没有被调用
        verifyNoInteractions(warehouseAreaRepo);
    }

    @Test
    void listByBizAndRegion_WithNullBusinessLines_ShouldThrowException() {
        // 执行测试并验证结果
        ErrorCodeException exception = assertThrows(ErrorCodeException.class,
                () -> warehouseAreaProvider.listByBizAndRegion(null, "CN-11-01", null));

        assertEquals(ErrorCodes.BAD_PARAMETER, exception.getErrorCode());
        assertEquals("业务线不能为空", exception.getMessage());

        // 验证mock对象没有被调用
        verifyNoInteractions(warehouseAreaRepo);
    }

    @Test
    void listByBizAndRegion_WithNullRegion_ShouldPassNullParams() {
        // 准备数据
        List<String> businessLines = Collections.singletonList("BIZ_LINE_1");
        List<WarehouseArea> mockAreas = new ArrayList<>();

        // 设置mock行为
        when(warehouseAreaRepo.listByBizAndRegion(
                eq(businessLines), isNull(), isNull(), isNull(), isNull(), isNull(), isNull()))
                .thenReturn(mockAreas);

        // 执行测试
        List<WarehouseAreaRes> result = warehouseAreaProvider.listByBizAndRegion(businessLines, null, null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证mock方法被正确调用
        verify(warehouseAreaRepo).listByBizAndRegion(
                eq(businessLines), isNull(), isNull(), isNull(), isNull(), isNull(), isNull());
    }

    @Test
    void listByBizAndRegion_WithEmptyRegion_ShouldPassNullParams() {
        // 准备数据
        List<String> businessLines = Collections.singletonList("BIZ_LINE_1");
        List<WarehouseArea> mockAreas = new ArrayList<>();

        // 设置mock行为
        when(warehouseAreaRepo.listByBizAndRegion(
                eq(businessLines), isNull(), isNull(), isNull(), isNull(), isNull(), isNull()))
                .thenReturn(mockAreas);

        // 执行测试
        List<WarehouseAreaRes> result = warehouseAreaProvider.listByBizAndRegion(businessLines, "", null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证mock方法被正确调用
        verify(warehouseAreaRepo).listByBizAndRegion(
                eq(businessLines), isNull(), isNull(), isNull(), isNull(), isNull(), isNull());
    }

    @Test
    void listByBizAndRegion_WithValidRegion_ShouldSplitRegionCorrectly() {
        // 准备数据
        List<String> businessLines = Collections.singletonList("BIZ_LINE_1");
        String region = "CN-11-01-0001-00001";
        List<WarehouseArea> mockAreas = new ArrayList<>();

        // 设置mock行为
        when(warehouseAreaRepo.listByBizAndRegion(
                eq(businessLines), eq("CN"), eq("11"), eq("01"), eq("0001"), eq("00001"), isNull()))
                .thenReturn(mockAreas);

        // 执行测试
        List<WarehouseAreaRes> result = warehouseAreaProvider.listByBizAndRegion(businessLines, region, null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证mock方法被正确调用
        verify(warehouseAreaRepo).listByBizAndRegion(
                eq(businessLines), eq("CN"), eq("11"), eq("01"), eq("0001"), eq("00001"), isNull());
    }

    @Test
    void listByBizAndRegion_WithPartialRegion_ShouldSplitRegionCorrectly() {
        // 准备数据
        List<String> businessLines = Collections.singletonList("BIZ_LINE_1");
        String region = "CN-11-01";
        List<WarehouseArea> mockAreas = new ArrayList<>();

        // 设置mock行为
        when(warehouseAreaRepo.listByBizAndRegion(
                eq(businessLines), eq("CN"), eq("11"), eq("01"), isNull(), isNull(), isNull()))
                .thenReturn(mockAreas);

        // 执行测试
        List<WarehouseAreaRes> result = warehouseAreaProvider.listByBizAndRegion(businessLines, region, null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证mock方法被正确调用
        verify(warehouseAreaRepo).listByBizAndRegion(
                eq(businessLines), eq("CN"), eq("11"), eq("01"), isNull(), isNull(), isNull());
    }

    @Test
    void listByBizAndRegion_WithValidData_ShouldReturnSortedResults() {
        // 准备数据
        List<String> businessLines = Arrays.asList("BIZ_LINE_1", "BIZ_LINE_2");
        String region = "CN-11";
        String areaName = "测试区域";

        // 创建测试数据
        WarehouseArea area1 = new WarehouseArea();
        area1.setId(1);
        area1.setWarehouseCode("WH001");
        area1.setWarehouseName("仓库1");
        area1.setBusinessLine("BIZ_LINE_1");
        area1.setPriority(3);
        area1.setCountryId("CN");
        area1.setProvinceId("11");
        area1.setCityId(null);
        area1.setAreaId(null);
        area1.setStreetId(null);
        area1.setAreaName("北京");

        WarehouseArea area2 = new WarehouseArea();
        area2.setId(2);
        area2.setWarehouseCode("WH002");
        area2.setWarehouseName("仓库2");
        area2.setBusinessLine("BIZ_LINE_2");
        area2.setPriority(1);
        area2.setCountryId("CN");
        area2.setProvinceId("11");
        area2.setCityId("01");
        area2.setAreaId(null);
        area2.setStreetId(null);
        area2.setAreaName("北京市");

        List<WarehouseArea> mockAreas = Arrays.asList(area1, area2);

        // 设置mock行为
        when(warehouseAreaRepo.listByBizAndRegion(
                eq(businessLines), eq("CN"), eq("11"), isNull(), isNull(), isNull(), eq(areaName)))
                .thenReturn(mockAreas);

        // 执行测试
        List<WarehouseAreaRes> result = warehouseAreaProvider.listByBizAndRegion(businessLines, region, areaName);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证结果按优先级排序
        assertEquals(1, result.get(0).getPriority());
        assertEquals(3, result.get(1).getPriority());

        // 验证第一个结果的字段
        assertEquals(2, result.get(0).getId());
        assertEquals("WH002", result.get(0).getWarehouseCode());
        assertEquals("仓库2", result.get(0).getWarehouseName());
        assertEquals("BIZ_LINE_2", result.get(0).getBusinessLine());
        assertEquals("CN-11-01", result.get(0).getAreaId());
        assertEquals("北京市", result.get(0).getAreaName());

        // 验证第二个结果的字段
        assertEquals(1, result.get(1).getId());
        assertEquals("WH001", result.get(1).getWarehouseCode());
        assertEquals("仓库1", result.get(1).getWarehouseName());
        assertEquals("BIZ_LINE_1", result.get(1).getBusinessLine());
        assertEquals("CN-11", result.get(1).getAreaId());
        assertEquals("北京", result.get(1).getAreaName());

        // 验证mock方法被正确调用
        verify(warehouseAreaRepo).listByBizAndRegion(
                eq(businessLines), eq("CN"), eq("11"), isNull(), isNull(), isNull(), eq(areaName));
    }

    @Test
    void listByBizAndRegion_WithNullWarehouseArea_ShouldSkipNullItems() {
        // 准备数据
        List<String> businessLines = Collections.singletonList("BIZ_LINE_1");

        WarehouseArea area = new WarehouseArea();
        area.setId(1);
        area.setWarehouseCode("WH001");
        area.setWarehouseName("仓库1");
        area.setBusinessLine("BIZ_LINE_1");
        area.setPriority(1);

        // 包含一个null元素
        List<WarehouseArea> mockAreas = Arrays.asList(area, null);

        // 设置mock行为
        when(warehouseAreaRepo.listByBizAndRegion(
                eq(businessLines), isNull(), isNull(), isNull(), isNull(), isNull(), isNull()))
                .thenReturn(mockAreas);

        // 执行测试
        List<WarehouseAreaRes> result = warehouseAreaProvider.listByBizAndRegion(businessLines, null, null);

        // 验证结果 - 应该跳过null元素
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getId());
        assertEquals("WH001", result.get(0).getWarehouseCode());
    }

    @Test
    void listByBizAndRegion_AreaCompositionTest_ShouldCorrectlyComposeAreaId() {
        // 准备数据
        List<String> businessLines = Collections.singletonList("BIZ_LINE_1");

        // 测试不同组合的区域ID
        WarehouseArea area1 = new WarehouseArea();
        area1.setId(1);
        area1.setWarehouseCode("WH001");
        area1.setWarehouseName("仓库1");
        area1.setBusinessLine("BIZ_LINE_1");
        area1.setPriority(1);
        area1.setCountryId("CN");
        area1.setProvinceId(null);
        area1.setCityId(null);
        area1.setAreaId(null);
        area1.setStreetId(null);

        WarehouseArea area2 = new WarehouseArea();
        area2.setId(2);
        area2.setWarehouseCode("WH002");
        area2.setWarehouseName("仓库2");
        area2.setBusinessLine("BIZ_LINE_1");
        area2.setPriority(2);
        area2.setCountryId(null);
        area2.setProvinceId(null);
        area2.setCityId(null);
        area2.setAreaId(null);
        area2.setStreetId(null);

        List<WarehouseArea> mockAreas = Arrays.asList(area1, area2);

        // 设置mock行为
        when(warehouseAreaRepo.listByBizAndRegion(
                eq(businessLines), isNull(), isNull(), isNull(), isNull(), isNull(), isNull()))
                .thenReturn(mockAreas);

        // 执行测试
        List<WarehouseAreaRes> result = warehouseAreaProvider.listByBizAndRegion(businessLines, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个结果 - 只有国家ID
        assertEquals("CN", result.get(0).getAreaId());

        // 验证第二个结果 - 所有ID都为null，应该返回null
        assertNull(result.get(1).getAreaId());
    }
}