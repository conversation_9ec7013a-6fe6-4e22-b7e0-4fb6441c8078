package com.mi.oa.asset.commons.config.app.util;

import com.mi.oa.asset.commons.config.app.util.entity.ErrorMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class PullErrorMessageEntryUtilTest {

    @InjectMocks
    private PullErrorMessageEntryUtil pullErrorMessageEntryUtil;

    private Path tempDir;

    @BeforeEach
    void setUp() throws IOException {
        // 创建临时目录用于测试
        tempDir = Files.createTempDirectory("test-java-files");
    }

    @Test
    @DisplayName("generateUUIDKey_相同输入_返回相同UUID")
    void generateUUIDKey_SameInput_ReturnsSameUUID() {
        // 准备数据
        String input = "测试错误信息";

        // 执行测试
        String result1 = PullErrorMessageEntryUtil.generateUUIDKey(input);
        String result2 = PullErrorMessageEntryUtil.generateUUIDKey(input);

        // 验证结果
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(result1, result2);
        assertTrue(result1.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"));
    }

    @Test
    @DisplayName("generateUUIDKey_不同输入_返回不同UUID")
    void generateUUIDKey_DifferentInput_ReturnsDifferentUUID() {
        // 准备数据
        String input1 = "测试错误信息1";
        String input2 = "测试错误信息2";

        // 执行测试
        String result1 = PullErrorMessageEntryUtil.generateUUIDKey(input1);
        String result2 = PullErrorMessageEntryUtil.generateUUIDKey(input2);

        // 验证结果
        assertNotEquals(result1, result2);
    }

    @Test
    @DisplayName("generateUUIDKey_空字符串_返回有效UUID")
    void generateUUIDKey_EmptyString_ReturnsValidUUID() {
        // 准备数据
        String input = "";

        // 执行测试
        String result = PullErrorMessageEntryUtil.generateUUIDKey(input);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"));
    }

//    @Test
//    @DisplayName("getJavaFilePaths_有效路径_返回Java文件列表")
//    void getJavaFilePaths_ValidPath_ReturnsJavaFiles() throws IOException {
//        // 准备数据 - 创建测试Java文件
//        Path javaFile1 = tempDir.resolve("TestClass1.java");
//        Path javaFile2 = tempDir.resolve("TestClass2.java");
//        Path nonJavaFile = tempDir.resolve("test.txt");
//
//        Files.write(javaFile1, "public class TestClass1 {}".getBytes());
//        Files.write(javaFile2, "public class TestClass2 {}".getBytes());
//        Files.write(nonJavaFile, "test content".getBytes());
//
//        // 执行测试
//        List<String> result = PullErrorMessageEntryUtil.getJavaFilePaths(tempDir);
//
//        // 验证结果
//        assertNotNull(result);
//        assertTrue(result.size() >= 2);
//        assertTrue(result.stream().anyMatch(path -> path.endsWith("TestClass1.java")));
//        assertTrue(result.stream().anyMatch(path -> path.endsWith("TestClass2.java")));
//        assertFalse(result.stream().anyMatch(path -> path.endsWith("test.txt")));
//    }

//    @Test
//    @DisplayName("getJavaFilePaths_无效路径_返回空列表")
//    void getJavaFilePaths_InvalidPath_ReturnsEmptyList() {
//        // 准备数据
//        Path invalidPath = Paths.get("/non/existent/path");
//
//        // 执行测试
//        List<String> result = PullErrorMessageEntryUtil.getJavaFilePaths(invalidPath);
//
//        // 验证结果
//        assertNotNull(result);
//        assertTrue(result.isEmpty());
//    }
//
//    @Test
//    @DisplayName("getJavaFilePaths_空目录_返回空列表")
//    void getJavaFilePaths_EmptyDirectory_ReturnsEmptyList() throws IOException {
//        // 准备数据
//        Path emptyDir = Files.createTempDirectory("empty-test");
//
//        // 执行测试
//        List<String> result = PullErrorMessageEntryUtil.getJavaFilePaths(emptyDir);
//
//        // 验证结果
//        assertNotNull(result);
//        assertTrue(result.isEmpty());
//    }

//    @Test
//    @DisplayName("getThrowStmtMsg_包含ErrorCodeException的文件_返回错误信息")
//    void getThrowStmtMsg_FileWithErrorCodeException_ReturnsErrorMessages() throws IOException {
//        // 准备数据
//        String javaContent = "public class TestClass {\n" +
//                "    public void testMethod() {\n" +
//                "        throw new ErrorCodeException(\"用户不存在\");\n" +
//                "        throw new ErrorCodeException(\"参数错误\");\n" +
//                "    }\n" +
//                "}";
//        Path javaFile = tempDir.resolve("TestClass.java");
//        Files.write(javaFile, javaContent.getBytes());
//
//        // 执行测试
//        List<String> result = PullErrorMessageEntryUtil.getThrowStmtMsg(javaFile.toString());
//
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(2, result.size());
//        assertTrue(result.stream().anyMatch(msg -> msg.contains("用户不存在")));
//        assertTrue(result.stream().anyMatch(msg -> msg.contains("参数错误")));
//    }

    @Test
    @DisplayName("getThrowStmtMsg_不包含ErrorCodeException的文件_返回空列表")
    void getThrowStmtMsg_FileWithoutErrorCodeException_ReturnsEmptyList() throws IOException {
        // 准备数据
        String javaContent = "public class TestClass {\n" +
                "    public void testMethod() {\n" +
                "        System.out.println(\"Hello World\");\n" +
                "    }\n" +
                "}";
        Path javaFile = tempDir.resolve("TestClass.java");
        Files.write(javaFile, javaContent.getBytes());

        // 执行测试
        List<String> result = PullErrorMessageEntryUtil.getThrowStmtMsg(javaFile.toString());

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

//    @Test
//    @DisplayName("getThrowStmtMsg_无效文件路径_抛出异常")
//    void getThrowStmtMsg_InvalidFilePath_ThrowsException() {
//        // 准备数据
//        String invalidPath = "/non/existent/file.java";
//
//        // 执行测试并验证异常
//        assertThrows(IllegalArgumentException.class, () -> {
//            PullErrorMessageEntryUtil.getThrowStmtMsg(invalidPath);
//        });
//    }
//
//    @Test
//    @DisplayName("extractAndSplitChineseMessages_包含中文消息_返回分割后的词条")
//    void extractAndSplitChineseMessages_WithChineseMessages_ReturnsSplitEntries() {
//        // 准备数据
//        Set<String> errorMessages = new HashSet<>();
//        errorMessages.add("throw new ErrorCodeException(\"用户不存在，请检查用户名\")");
//        errorMessages.add("throw new ErrorCodeException(\"参数错误：%s\")");
//        errorMessages.add("throw new ErrorCodeException(\"系统异常：{0}\")");
//
//        // 执行测试
//        Set<String> result = PullErrorMessageEntryUtil.extractAndSplitChineseMessages(errorMessages);
//
//        // 验证结果
//        assertNotNull(result);
//        assertTrue(result.contains("用户不存在"));
//        assertTrue(result.contains("请检查用户名"));
//        assertTrue(result.contains("参数错误"));
//        assertTrue(result.contains("系统异常"));
//    }

    @Test
    @DisplayName("extractAndSplitChineseMessages_包含format方法_返回分割后的词条")
    void extractAndSplitChineseMessages_WithFormatMethod_ReturnsSplitEntries() {
        // 准备数据
        Set<String> errorMessages = new HashSet<>();
        errorMessages.add("throw new ErrorCodeException(String.format(\"用户%s不存在\", username))");

        // 执行测试
        Set<String> result = PullErrorMessageEntryUtil.extractAndSplitChineseMessages(errorMessages);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("用户"));
        assertTrue(result.contains("不存在"));
    }

//    @Test
//    @DisplayName("extractAndSplitChineseMessages_包含特殊字符_返回清理后的词条")
//    void extractAndSplitChineseMessages_WithSpecialCharacters_ReturnsCleanedEntries() {
//        // 准备数据
//        Set<String> errorMessages = new HashSet<>();
//        errorMessages.add("throw new ErrorCodeException(\"【错误】用户不存在！\")");
//
//        // 执行测试
//        Set<String> result = PullErrorMessageEntryUtil.extractAndSplitChineseMessages(errorMessages);
//
//        // 验证结果
//        assertNotNull(result);
//        assertTrue(result.contains("错误"));
//        assertTrue(result.contains("用户不存在"));
//    }

    @Test
    @DisplayName("extractAndSplitChineseMessages_空集合_返回空集合")
    void extractAndSplitChineseMessages_EmptySet_ReturnsEmptySet() {
        // 准备数据
        Set<String> errorMessages = new HashSet<>();

        // 执行测试
        Set<String> result = PullErrorMessageEntryUtil.extractAndSplitChineseMessages(errorMessages);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("extractAndSplitChineseMessages_不包含中文_返回空集合")
    void extractAndSplitChineseMessages_NoChinese_ReturnsEmptySet() {
        // 准备数据
        Set<String> errorMessages = new HashSet<>();
        errorMessages.add("throw new ErrorCodeException(\"User not found\")");

        // 执行测试
        Set<String> result = PullErrorMessageEntryUtil.extractAndSplitChineseMessages(errorMessages);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("extractAndSplitChineseMessages_包含边缘标点_返回清理后的词条")
    void extractAndSplitChineseMessages_WithEdgePunctuation_ReturnsCleanedEntries() {
        // 准备数据
        Set<String> errorMessages = new HashSet<>();
        errorMessages.add("throw new ErrorCodeException(\"，用户不存在。\")");

        // 执行测试
        Set<String> result = PullErrorMessageEntryUtil.extractAndSplitChineseMessages(errorMessages);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("用户不存在"));
    }

    @Test
    @DisplayName("exportToExcel_有效数据_成功导出")
    @Disabled
    void exportToExcel_ValidData_ExportsSuccessfully() {
        // 准备数据
        List<ErrorMessage> dataList = new ArrayList<>();
        dataList.add(new ErrorMessage("key1", "用户不存在"));
        dataList.add(new ErrorMessage("key2", "参数错误"));
        String fileName = "test_export.xlsx";

        // 执行测试
        assertDoesNotThrow(() -> {
            PullErrorMessageEntryUtil.exportToExcel(dataList, fileName);
        });

        // 验证文件是否创建
        File file = new File(fileName);
        assertTrue(file.exists());
        
        // 清理测试文件
        file.delete();
    }

    @Test
    @DisplayName("exportToExcel_空数据_成功导出")
    @Disabled
    void exportToExcel_EmptyData_ExportsSuccessfully() {
        // 准备数据
        List<ErrorMessage> dataList = new ArrayList<>();
        String fileName = "test_empty_export.xlsx";

        // 执行测试
        assertDoesNotThrow(() -> {
            PullErrorMessageEntryUtil.exportToExcel(dataList, fileName);
        });

        // 验证文件是否创建
        File file = new File(fileName);
        assertTrue(file.exists());
        
        // 清理测试文件
        file.delete();
    }

    @Test
    @DisplayName("pullErrorMessageEntry_有效路径_成功处理")
    @Disabled
    void pullErrorMessageEntry_ValidPath_ProcessesSuccessfully() throws IOException {
        // 准备数据 - 创建包含ErrorCodeException的测试文件
        String javaContent = "public class TestClass {\n" +
                "    public void testMethod() {\n" +
                "        throw new ErrorCodeException(\"用户不存在\");\n" +
                "    }\n" +
                "}";
        Path javaFile = tempDir.resolve("TestClass.java");
        Files.write(javaFile, javaContent.getBytes());

        // 执行测试
        assertDoesNotThrow(() -> {
            pullErrorMessageEntryUtil.pullErrorMessageEntry(tempDir.toString());
        });
    }

//    @Test
//    @DisplayName("pullErrorMessageEntry_无效路径_抛出异常")
//    void pullErrorMessageEntry_InvalidPath_ThrowsException() {
//        // 准备数据
//        String invalidPath = "/non/existent/path";
//
//        // 执行测试并验证异常
//        assertThrows(RuntimeException.class, () -> {
//            pullErrorMessageEntryUtil.pullErrorMessageEntry(invalidPath);
//        });
//    }
} 