package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.assetorganization.repository.AssetOrgRepo;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgUnitQuery;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AssetOrgProviderImpl类的refreshOrgUnit方法单元测试
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ExtendWith(MockitoExtension.class)
class AssetOrgProviderImplRefreshOrgUnitTest {

    @InjectMocks
    private AssetOrgProviderImpl assetOrgProvider;

    @Mock
    private AssetOrgRepo assetOrgRepo;

    @BeforeEach
    void setUp() {
        // 测试前的初始化工作
    }

    @Test
    void refreshOrgUnit_WithSpecificBusinessLines_ShouldProcessCorrectly() {
        // 准备数据
        List<String> businessLines = Arrays.asList("adm_emp", "adm_pub");
        
        List<AssetOrgUnit> orgUnitList1 = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_EMP)
        );
        
        List<AssetOrgUnit> orgUnitList2 = Arrays.asList(
                createAssetOrgUnit(3, "ORG003", "部门3", BusinessLine.ADM_PUB),
                createAssetOrgUnit(4, "ORG004", "部门4", BusinessLine.ADM_PUB)
        );
        
        PageData<AssetOrgUnit> pageData1 = PageData.of(orgUnitList1, 200, 2, 4);
        PageData<AssetOrgUnit> pageData2 = PageData.of(orgUnitList2, 200, 3, 4);
        // 第三页不会被调用，因为第二页后done=4，total=4，循环结束

        // 设置mock行为
        when(assetOrgRepo.orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class)))
                .thenReturn(pageData1)
                .thenReturn(pageData2);
        
        // 设置refreshOrgUnitPath中调用的方法
        when(assetOrgRepo.getOrgUnits(any(AssetOrgUnitQuery.class)))
                .thenReturn(orgUnitList1)
                .thenReturn(orgUnitList2);

        // 执行测试
        assetOrgProvider.refreshOrgUnit(businessLines);

        // 验证结果
        verify(assetOrgRepo, times(2)).orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class));
        verify(assetOrgRepo, times(4)).updateOrgUnit(any(AssetOrgUnit.class));
    }

    @Test
    void refreshOrgUnit_WithEmptyBusinessLines_ShouldProcessAllBusinessLines() {
        // 准备数据
        List<String> businessLines = Collections.emptyList();
        
        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_PUB),
                createAssetOrgUnit(3, "ORG003", "部门3", BusinessLine.CAR)
        );
        
        PageData<AssetOrgUnit> pageData1 = PageData.of(orgUnitList, 200, 2, 3);
        // 第二页不会被调用，因为第一页后done=3，total=3，循环结束

        // 设置mock行为
        when(assetOrgRepo.orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class)))
                .thenReturn(pageData1);
        
        when(assetOrgRepo.getOrgUnits(any(AssetOrgUnitQuery.class)))
                .thenReturn(orgUnitList);

        // 执行测试
        assetOrgProvider.refreshOrgUnit(businessLines);

        // 验证结果
        verify(assetOrgRepo, times(1)).orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class));
        verify(assetOrgRepo, times(3)).updateOrgUnit(any(AssetOrgUnit.class));
    }

    @Test
    void refreshOrgUnit_WithNullBusinessLines_ShouldProcessAllBusinessLines() {
        // 准备数据
        List<String> businessLines = null;
        
        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_PUB)
        );
        
        PageData<AssetOrgUnit> pageData1 = PageData.of(orgUnitList, 200, 2, 2);
        // 第二页不会被调用，因为第一页后done=2，total=2，循环结束

        // 设置mock行为
        when(assetOrgRepo.orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class)))
                .thenReturn(pageData1);
        
        when(assetOrgRepo.getOrgUnits(any(AssetOrgUnitQuery.class)))
                .thenReturn(orgUnitList);

        // 执行测试
        assetOrgProvider.refreshOrgUnit(businessLines);

        // 验证结果
        verify(assetOrgRepo, times(1)).orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class));
        verify(assetOrgRepo, times(2)).updateOrgUnit(any(AssetOrgUnit.class));
    }

    @Test
    void refreshOrgUnit_WithSingleBusinessLine_ShouldProcessCorrectly() {
        // 准备数据
        List<String> businessLines = Arrays.asList("adm_emp");
        
        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_EMP)
        );
        
        PageData<AssetOrgUnit> pageData1 = PageData.of(orgUnitList, 200, 2, 2);
        // 第二页不会被调用，因为第一页后done=2，total=2，循环结束

        // 设置mock行为
        when(assetOrgRepo.orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class)))
                .thenReturn(pageData1);
        
        when(assetOrgRepo.getOrgUnits(any(AssetOrgUnitQuery.class)))
                .thenReturn(orgUnitList);

        // 执行测试
        assetOrgProvider.refreshOrgUnit(businessLines);

        // 验证结果
        verify(assetOrgRepo, times(1)).orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class));
        verify(assetOrgRepo, times(2)).updateOrgUnit(any(AssetOrgUnit.class));
    }

    @Test
    void refreshOrgUnit_WithMultiplePages_ShouldProcessAllPages() {
        // 准备数据
        List<String> businessLines = Arrays.asList("adm_emp");
        
        List<AssetOrgUnit> orgUnitList1 = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_EMP)
        );
        
        List<AssetOrgUnit> orgUnitList2 = Arrays.asList(
                createAssetOrgUnit(3, "ORG003", "部门3", BusinessLine.ADM_EMP),
                createAssetOrgUnit(4, "ORG004", "部门4", BusinessLine.ADM_EMP)
        );
        
        List<AssetOrgUnit> orgUnitList3 = Arrays.asList(
                createAssetOrgUnit(5, "ORG005", "部门5", BusinessLine.ADM_EMP)
        );
        
        PageData<AssetOrgUnit> pageData1 = PageData.of(orgUnitList1, 200, 2, 5);
        PageData<AssetOrgUnit> pageData2 = PageData.of(orgUnitList2, 200, 3, 5);
        PageData<AssetOrgUnit> pageData3 = PageData.of(orgUnitList3, 200, 4, 5);
        // 第四页不会被调用，因为第三页后done=5，total=5，循环结束

        // 设置mock行为
        when(assetOrgRepo.orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class)))
                .thenReturn(pageData1)
                .thenReturn(pageData2)
                .thenReturn(pageData3);
        
        when(assetOrgRepo.getOrgUnits(any(AssetOrgUnitQuery.class)))
                .thenReturn(orgUnitList1)
                .thenReturn(orgUnitList2)
                .thenReturn(orgUnitList3);

        // 执行测试
        assetOrgProvider.refreshOrgUnit(businessLines);

        // 验证结果
        verify(assetOrgRepo, times(3)).orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class));
        verify(assetOrgRepo, times(5)).updateOrgUnit(any(AssetOrgUnit.class));
    }

    @Test
    void refreshOrgUnit_WithEmptyResult_ShouldNotProcess() {
        // 准备数据
        List<String> businessLines = Arrays.asList("adm_emp");
        
        PageData<AssetOrgUnit> pageData = PageData.of(Collections.emptyList(), 200, 2, 0);

        // 设置mock行为
        when(assetOrgRepo.orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class)))
                .thenReturn(pageData);

        // 执行测试
        assetOrgProvider.refreshOrgUnit(businessLines);

        // 验证结果
        verify(assetOrgRepo, times(1)).orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class));
        verify(assetOrgRepo, never()).updateOrgUnit(any(AssetOrgUnit.class));
    }

    @Test
    void refreshOrgUnit_WithInvalidBusinessLine_ShouldHandleGracefully() {
        // 准备数据
        List<String> businessLines = Arrays.asList("invalid_business_line");
        
        PageData<AssetOrgUnit> pageData = PageData.of(Collections.emptyList(), 200, 2, 0);

        // 设置mock行为
        when(assetOrgRepo.orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class)))
                .thenReturn(pageData);

        // 执行测试
        assetOrgProvider.refreshOrgUnit(businessLines);

        // 验证结果
        verify(assetOrgRepo, times(1)).orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class));
        verify(assetOrgRepo, never()).updateOrgUnit(any(AssetOrgUnit.class));
    }

    @Test
    void refreshOrgUnit_WithMixedValidAndInvalidBusinessLines_ShouldProcessValidOnes() {
        // 准备数据
        List<String> businessLines = Arrays.asList("adm_emp", "invalid_line", "adm_pub");
        
        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_PUB)
        );
        
        PageData<AssetOrgUnit> pageData1 = PageData.of(orgUnitList, 200, 2, 2);
        // 第二页不会被调用，因为第一页后done=2，total=2，循环结束

        // 设置mock行为
        when(assetOrgRepo.orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class)))
                .thenReturn(pageData1);
        
        when(assetOrgRepo.getOrgUnits(any(AssetOrgUnitQuery.class)))
                .thenReturn(orgUnitList);

        // 执行测试
        assetOrgProvider.refreshOrgUnit(businessLines);

        // 验证结果
        verify(assetOrgRepo, times(1)).orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class));
        verify(assetOrgRepo, times(2)).updateOrgUnit(any(AssetOrgUnit.class));
    }

    @Test
    void refreshOrgUnit_WithLargeDataSet_ShouldProcessInBatches() {
        // 准备数据
        List<String> businessLines = Arrays.asList("adm_emp");
        
        // 创建大量数据
        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_EMP),
                createAssetOrgUnit(3, "ORG003", "部门3", BusinessLine.ADM_EMP),
                createAssetOrgUnit(4, "ORG004", "部门4", BusinessLine.ADM_EMP),
                createAssetOrgUnit(5, "ORG005", "部门5", BusinessLine.ADM_EMP)
        );
        
        PageData<AssetOrgUnit> pageData1 = PageData.of(orgUnitList, 200, 2, 5);
        // 第二页不会被调用，因为第一页后done=5，total=5，循环结束

        // 设置mock行为
        when(assetOrgRepo.orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class)))
                .thenReturn(pageData1);
        
        when(assetOrgRepo.getOrgUnits(any(AssetOrgUnitQuery.class)))
                .thenReturn(orgUnitList);

        // 执行测试
        assetOrgProvider.refreshOrgUnit(businessLines);

        // 验证结果
        verify(assetOrgRepo, times(1)).orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class));
        verify(assetOrgRepo, times(5)).updateOrgUnit(any(AssetOrgUnit.class));
    }

    @Test
    void refreshOrgUnit_WithDuplicateBusinessLines_ShouldProcessCorrectly() {
        // 准备数据
        List<String> businessLines = Arrays.asList("adm_emp", "adm_emp", "adm_pub");
        
        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_PUB)
        );
        
        PageData<AssetOrgUnit> pageData1 = PageData.of(orgUnitList, 200, 2, 2);
        // 第二页不会被调用，因为第一页后done=2，total=2，循环结束

        // 设置mock行为
        when(assetOrgRepo.orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class)))
                .thenReturn(pageData1);
        
        when(assetOrgRepo.getOrgUnits(any(AssetOrgUnitQuery.class)))
                .thenReturn(orgUnitList);

        // 执行测试
        assetOrgProvider.refreshOrgUnit(businessLines);

        // 验证结果
        verify(assetOrgRepo, times(1)).orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class));
        verify(assetOrgRepo, times(2)).updateOrgUnit(any(AssetOrgUnit.class));
    }

    @Test
    void refreshOrgUnit_WithSpecialCharactersInBusinessLine_ShouldHandleGracefully() {
        // 准备数据
        List<String> businessLines = Arrays.asList("adm_emp", "test-line", "test_line");
        
        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP)
        );
        
        PageData<AssetOrgUnit> pageData1 = PageData.of(orgUnitList, 200, 2, 1);
        // 第二页不会被调用，因为第一页后done=1，total=1，循环结束

        // 设置mock行为
        when(assetOrgRepo.orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class)))
                .thenReturn(pageData1);
        
        when(assetOrgRepo.getOrgUnits(any(AssetOrgUnitQuery.class)))
                .thenReturn(orgUnitList);

        // 执行测试
        assetOrgProvider.refreshOrgUnit(businessLines);

        // 验证结果
        verify(assetOrgRepo, times(1)).orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class));
        verify(assetOrgRepo, times(1)).updateOrgUnit(any(AssetOrgUnit.class));
    }

    @Test
    void refreshOrgUnit_WithCaseSensitiveBusinessLines_ShouldProcessCorrectly() {
        // 准备数据
        List<String> businessLines = Arrays.asList("ADM_EMP", "adm_pub");
        
        List<AssetOrgUnit> orgUnitList = Arrays.asList(
                createAssetOrgUnit(1, "ORG001", "部门1", BusinessLine.ADM_EMP),
                createAssetOrgUnit(2, "ORG002", "部门2", BusinessLine.ADM_PUB)
        );
        
        PageData<AssetOrgUnit> pageData1 = PageData.of(orgUnitList, 200, 2, 2);
        // 第二页不会被调用，因为第一页后done=2，total=2，循环结束

        // 设置mock行为
        when(assetOrgRepo.orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class)))
                .thenReturn(pageData1);
        
        when(assetOrgRepo.getOrgUnits(any(AssetOrgUnitQuery.class)))
                .thenReturn(orgUnitList);

        // 执行测试
        assetOrgProvider.refreshOrgUnit(businessLines);

        // 验证结果
        verify(assetOrgRepo, times(1)).orgUnitPageData(any(AssetOrgUnitQuery.class), any(PageRequest.class));
        verify(assetOrgRepo, times(2)).updateOrgUnit(any(AssetOrgUnit.class));
    }

    // 辅助方法
    private AssetOrgUnit createAssetOrgUnit(Integer orgId, String orgCode, String orgName, BusinessLine businessLine) {
        return AssetOrgUnit.builder()
                .orgId(orgId)
                .orgCode(orgCode)
                .orgName(orgName)
                .businessLine(businessLine)
                .build();
    }
} 