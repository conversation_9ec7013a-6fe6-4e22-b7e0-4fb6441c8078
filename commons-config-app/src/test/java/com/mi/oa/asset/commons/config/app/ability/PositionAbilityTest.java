package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.position.PositionTreeRes;
import com.mi.oa.asset.commons.config.app.converter.PositionConverter;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import com.mi.oa.asset.commons.config.domain.position.entity.Position;
import com.mi.oa.asset.commons.config.domain.position.entity.PositionSpacePark;
import com.mi.oa.asset.commons.config.domain.position.repository.PositionSpaceParkRepo;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.space.rep.SpaceBuildingVO;
import com.mi.oa.infra.oaucf.space.rep.SpaceFloorVO;
import com.mi.oa.infra.oaucf.space.rep.Space<PERSON>ark<PERSON>;
import com.mi.oa.infra.oaucf.space.service.SpaceParkService;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.mockito.stubbing.Answer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * PositionAbility的单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PositionAbilityTest {

    @InjectMocks
    private PositionAbility positionAbility;

    @Mock
    private SpaceParkService spaceParkService;

    @Mock
    private PositionSpaceParkRepo positionSpaceParkRepo;
    
    @Mock
    private PositionConverter positionConverter;

    // 测试中使用的常量和共享变量
    private static final int CODE_SUCCESS = 0; // 模拟BaseResp.CODE_SUCCESS值
    private List<SpaceParkVO> mockParkList;
    private BaseResp<List<SpaceParkVO>> successResp;
    private BaseResp<List<SpaceParkVO>> failureResp;

    @BeforeEach
    void setUp() {
        // 设置成功响应
        successResp = new BaseResp<>();
        successResp.setCode(CODE_SUCCESS);

        // 设置失败响应
        failureResp = new BaseResp<>();
        failureResp.setCode(CODE_SUCCESS - 1); // 非成功状态码
        failureResp.setMessage("API调用失败");

        // 创建模拟园区数据
        mockParkList = createMockParkData();
        
        // 正确设置转换器，防止无限递归
        // 对于子节点的处理，确保不会递归到父节点
        doAnswer(invocation -> {
            List<PositionSpacePark> parks = invocation.getArgument(0);
            if (parks == null || parks.isEmpty()) {
                return Collections.emptyList();
            }
            
            return parks.stream()
                .map(park -> {
                    return PositionTreeRes.builder()
                        .positionId(park.getPositionId())
                        .positionCode(park.getPositionCode())
                        .positionName(park.getPositionName())
                        .positionNameEn(park.getPositionNameEn())
                        .parentCode(park.getParentCode())
                        // 重要：确保不添加子节点，防止无限递归
                        .subList(new ArrayList<>())
                        .build();
                })
                .collect(Collectors.toList());
        }).when(positionConverter).parkToPositionTreeResList(anyList());
    }

    /**
     * 创建模拟的园区数据
     */
    private List<SpaceParkVO> createMockParkData() {
        List<SpaceParkVO> parkList = new ArrayList<>();

        // 创建园区
        SpaceParkVO park = new SpaceParkVO();
        park.setParkCode("P001");
        park.setParkName("测试园区");

        // 创建楼宇
        List<SpaceBuildingVO> buildings = new ArrayList<>();
        SpaceBuildingVO building = new SpaceBuildingVO();
        building.setBuildingCode("B001");
        building.setBuildingName("测试楼宇");

        // 创建楼层
        List<SpaceFloorVO> floors = new ArrayList<>();
        SpaceFloorVO floor = new SpaceFloorVO();
        floor.setFloorCode("F001");
        floor.setFloorName("测试楼层");

        // 组装数据结构
        floors.add(floor);
        building.setFloorList(floors);
        buildings.add(building);
        park.setBuildingList(buildings);
        parkList.add(park);

        return parkList;
    }

    /**
     * 测试syncSpacePosition方法的正常流程
     * 场景：API调用成功并返回有效数据
     */
    @Test
    void syncSpacePosition_Success_CorrectDataFlow() {
        // 准备数据
        successResp.setData(mockParkList);
        when(spaceParkService.getParkBuildingFloorTree()).thenReturn(successResp);

        // 模拟部分数据在数据库中已存在
        PositionSpacePark existingPark = new PositionSpacePark();
        existingPark.setPositionCode("P001");

        when(positionSpaceParkRepo.listPositionSpaceParks(anyList()))
                .thenReturn(Collections.singletonList(existingPark));

        // 执行测试
        positionAbility.syncSpacePosition();

        // 验证结果
        verify(spaceParkService).getParkBuildingFloorTree();
        verify(positionSpaceParkRepo).listPositionSpaceParks(anyList());

        // 捕获和验证保存的数据
        ArgumentCaptor<List<PositionSpacePark>> parkCaptor = ArgumentCaptor.forClass(List.class);
        verify(positionSpaceParkRepo).savePositionSpaceParks(parkCaptor.capture());

        List<PositionSpacePark> savedParks = parkCaptor.getValue();
        assertEquals(3, savedParks.size()); // 园区、楼宇、楼层

        // 验证层次结构
        PositionSpacePark savedPark = findPositionByCode(savedParks, "P001");
        PositionSpacePark savedBuilding = findPositionByCode(savedParks, "B001");
        PositionSpacePark savedFloor = findPositionByCode(savedParks, "F001");

        assertNotNull(savedPark);
        assertNotNull(savedBuilding);
        assertNotNull(savedFloor);

        assertEquals("0", savedPark.getParentCode());
        assertEquals("P001", savedBuilding.getParentCode());
        assertEquals("B001", savedFloor.getParentCode());
    }

    /**
     * 查找指定编码的位置对象
     */
    private PositionSpacePark findPositionByCode(List<PositionSpacePark> parks, String code) {
        return parks.stream()
                .filter(p -> code.equals(p.getPositionCode()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 测试syncSpacePosition方法处理API调用失败的情况
     * 场景：SpaceParkService返回错误状态码
     */
    @Test
    void syncSpacePosition_ApiFailure_ThrowsException() {
        // 准备数据 - API调用失败
        when(spaceParkService.getParkBuildingFloorTree()).thenReturn(failureResp);

        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () ->
                positionAbility.syncSpacePosition()
        );

        // 验证异常信息
        assertEquals(ErrorCodes.BAD_REQUEST, exception.getErrorCode());
        assertEquals("API调用失败", exception.getMessage());

        // 验证后续方法未被调用
        verify(positionSpaceParkRepo, never()).listPositionSpaceParks(anyList());
        verify(positionSpaceParkRepo, never()).savePositionSpaceParks(anyList());
    }

    /**
     * 测试syncSpacePosition方法处理空数据的情况
     * 场景：API调用成功但返回空列表
     */
    @Test
    void syncSpacePosition_EmptyData_HandlesGracefully() {
        // 准备数据 - 空列表
        successResp.setData(Collections.emptyList());
        when(spaceParkService.getParkBuildingFloorTree()).thenReturn(successResp);
        when(positionSpaceParkRepo.listPositionSpaceParks(anyList())).thenReturn(Collections.emptyList());

        // 执行测试
        positionAbility.syncSpacePosition();

        // 验证结果
        ArgumentCaptor<List<PositionSpacePark>> parkCaptor = ArgumentCaptor.forClass(List.class);
        verify(positionSpaceParkRepo).savePositionSpaceParks(parkCaptor.capture());

        List<PositionSpacePark> savedParks = parkCaptor.getValue();
        assertTrue(savedParks.isEmpty());
    }

    /**
     * 测试buildPositionSpaceParks方法构建正确的位置层次结构
     * 场景：验证从SpaceParkVO到PositionSpacePark的转换逻辑
     */
    @Test
    void buildPositionSpaceParks_ComplexStructure_CorrectHierarchy() {
        // 准备更复杂的测试数据
        List<SpaceParkVO> parks = new ArrayList<>();

        // 园区1 - 有两个楼宇，第一个楼宇有两个楼层
        SpaceParkVO park1 = new SpaceParkVO();
        park1.setParkCode("P001");
        park1.setParkName("园区1");

        List<SpaceBuildingVO> park1Buildings = new ArrayList<>();

        // 楼宇1
        SpaceBuildingVO building1 = new SpaceBuildingVO();
        building1.setBuildingCode("B001");
        building1.setBuildingName("楼宇1");

        List<SpaceFloorVO> building1Floors = new ArrayList<>();

        SpaceFloorVO floor1 = new SpaceFloorVO();
        floor1.setFloorCode("F001");
        floor1.setFloorName("1楼");
        building1Floors.add(floor1);

        SpaceFloorVO floor2 = new SpaceFloorVO();
        floor2.setFloorCode("F002");
        floor2.setFloorName("2楼");
        building1Floors.add(floor2);

        building1.setFloorList(building1Floors);
        park1Buildings.add(building1);

        // 楼宇2 - 没有楼层
        SpaceBuildingVO building2 = new SpaceBuildingVO();
        building2.setBuildingCode("B002");
        building2.setBuildingName("楼宇2");
        building2.setFloorList(Collections.emptyList());
        park1Buildings.add(building2);

        park1.setBuildingList(park1Buildings);
        parks.add(park1);

        // 园区2 - 没有楼宇
        SpaceParkVO park2 = new SpaceParkVO();
        park2.setParkCode("P002");
        park2.setParkName("园区2");
        park2.setBuildingList(Collections.emptyList());
        parks.add(park2);

        // 执行测试
        List<PositionSpacePark> result = positionAbility.buildPositionSpaceParks(parks);

        // 验证结果
        assertEquals(6, result.size()); // 2个园区 + 2个楼宇 + 2个楼层 = 6个位置

        // 验证层次结构
        PositionSpacePark p1 = findPositionByCode(result, "P001");
        PositionSpacePark p2 = findPositionByCode(result, "P002");
        PositionSpacePark b1 = findPositionByCode(result, "B001");
        PositionSpacePark b2 = findPositionByCode(result, "B002");
        PositionSpacePark f1 = findPositionByCode(result, "F001");
        PositionSpacePark f2 = findPositionByCode(result, "F002");

        assertEquals("0", p1.getParentCode());
        assertEquals("0", p2.getParentCode());
        assertEquals("P001", b1.getParentCode());
        assertEquals("P001", b2.getParentCode());
        assertEquals("B001", f1.getParentCode());
        assertEquals("B001", f2.getParentCode());
    }

    /**
     * 测试buildPositionSpaceParks方法处理空输入
     * 场景：空列表作为输入
     */
    @Test
    void buildPositionSpaceParks_EmptyInput_ReturnsEmptyList() {
        // 执行测试
        List<PositionSpacePark> result = positionAbility.buildPositionSpaceParks(Collections.emptyList());

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试buildPositionSpaceParks方法处理只有园区没有楼宇的情况
     * 场景：园区没有楼宇作为输入
     */
    @Test
    void buildPositionSpaceParks_ParkWithoutBuildings_OnlyCreatesPark() {
        // 准备数据
        List<SpaceParkVO> parks = new ArrayList<>();
        SpaceParkVO park = new SpaceParkVO();
        park.setParkCode("P001");
        park.setParkName("测试园区");
        park.setBuildingList(Collections.emptyList());
        parks.add(park);

        // 执行测试
        List<PositionSpacePark> result = positionAbility.buildPositionSpaceParks(parks);

        // 验证结果
        assertEquals(1, result.size());
        PositionSpacePark resultPark = result.get(0);
        assertEquals("P001", resultPark.getPositionCode());
        assertEquals("测试园区", resultPark.getPositionName());
        assertEquals("0", resultPark.getParentCode());
    }

    /**
     * 测试listPositionTree方法，确保正确处理树结构
     * 注意：该测试不会导致堆栈溢出，因为我们已经正确模拟了positionConverter.parkToPositionTreeResList
     */
    @Test
    void listPositionTree_NormalData_CorrectTreeStructure() {
        // 准备数据
        List<SpaceParkVO> parks = createComplexTestData();
        
        // 模拟位置空间公园数据
        when(positionSpaceParkRepo.listPositionSpaceParks(anyList())).thenReturn(Collections.emptyList());
        
        // 执行测试
        List<PositionTreeRes> result = positionAbility.listPositionTree(parks);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size()); // 2个根园区
    }

    /**
     * 创建更复杂的测试数据，用于测试树结构
     */
    private List<SpaceParkVO> createComplexTestData() {
        List<SpaceParkVO> parks = new ArrayList<>();

        // 园区1
        SpaceParkVO park1 = new SpaceParkVO();
        park1.setParkCode("P001");
        park1.setParkName("园区1");

        List<SpaceBuildingVO> buildings1 = new ArrayList<>();
        SpaceBuildingVO building1 = new SpaceBuildingVO();
        building1.setBuildingCode("B001");
        building1.setBuildingName("楼宇1");
        
        List<SpaceFloorVO> floors1 = new ArrayList<>();
        SpaceFloorVO floor1 = new SpaceFloorVO();
        floor1.setFloorCode("F001");
        floor1.setFloorName("1楼");
        floors1.add(floor1);
        building1.setFloorList(floors1);
        
        buildings1.add(building1);
        park1.setBuildingList(buildings1);
        parks.add(park1);
        
        // 园区2
        SpaceParkVO park2 = new SpaceParkVO();
        park2.setParkCode("P002");
        park2.setParkName("园区2");
        park2.setBuildingList(Collections.emptyList());
        parks.add(park2);
        
        return parks;
    }

    @Test
    public void testSetStdPositionPath() {
        // 创建测试数据，模拟导入的位置数据
        List<Position> positions = new ArrayList<>();
        
        // 根节点
        Position root = Position.builder()
                .positionId(6034)
                .positionCode("Xiaomi")
                .positionName("Xiaomi Science and Technology Park 22")
                .parentCode("0")
                .businessLine(BusinessLine.INTEL)
                .dataSource(DataCreateSource.SYS_SPACE)
                .build();
        positions.add(root);
        
        // 子节点
        Position child1 = Position.builder()
                .positionId(6035)
                .positionCode("Xiaomi-A")
                .positionName("Building A")
                .parentCode("Xiaomi")
                .businessLine(BusinessLine.INTEL)
                .dataSource(DataCreateSource.SYS_SPACE)
                .build();
        positions.add(child1);
        
        Position child2 = Position.builder()
                .positionId(6036)
                .positionCode("Xiaomi-B")
                .positionName("Building B")
                .parentCode("Xiaomi")
                .businessLine(BusinessLine.INTEL)
                .dataSource(DataCreateSource.SYS_SPACE)
                .build();
        positions.add(child2);
        
        // 调用修复后的方法
        positionAbility.setStdPositionPath(positions);
        
        // 验证结果
        assertEquals("0-6034", root.getPositionPath());
        assertEquals("0-6034-6035", child1.getPositionPath());
        assertEquals("0-6034-6036", child2.getPositionPath());
        
        // 验证没有 null 值
        assertNotNull(root.getPositionPath());
        assertNotNull(child1.getPositionPath());
        assertNotNull(child2.getPositionPath());
        assertFalse(root.getPositionPath().contains("null"));
        assertFalse(child1.getPositionPath().contains("null"));
        assertFalse(child2.getPositionPath().contains("null"));
    }
}