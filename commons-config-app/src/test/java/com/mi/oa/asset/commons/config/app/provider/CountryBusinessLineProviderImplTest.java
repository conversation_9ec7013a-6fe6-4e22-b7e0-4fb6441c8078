package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineReq;
import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineRes;
import com.mi.oa.asset.commons.config.app.converter.CountryBusinessLineConverter;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryBusinessLineDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryBusinessLineRepo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CountryBusinessLineProviderImplTest {
    @InjectMocks
    private CountryBusinessLineProviderImpl provider;
    @Mock
    private CountryBusinessLineRepo repo;
    @Mock
    private CountryBusinessLineConverter converter;

    @Test
    void getCountryRegionList_normal() {
        List<CountryBusinessLineDo> doList = Collections.singletonList(new CountryBusinessLineDo());
        List<CountryBusinessLineRes> resList = Collections.singletonList(new CountryBusinessLineRes());
        when(repo.searchAll()).thenReturn(doList);
        when(converter.listDoToRes(doList)).thenReturn(resList);
        List<CountryBusinessLineRes> result = provider.getCountryRegionList();
        assertEquals(1, result.size());
    }

    @Test
    void getById_normal() {
        CountryBusinessLineDo entity = new CountryBusinessLineDo();
        when(repo.getById(1)).thenReturn(entity);
        CountryBusinessLineRes res = new CountryBusinessLineRes();
        when(converter.doToRes(entity)).thenReturn(res);
        assertEquals(res, provider.getById(1));
    }

    @Test
    void getById_null() {
        when(repo.getById(1)).thenReturn(null);
        assertNull(provider.getById(1));
    }

    @Test
    void getCountryId_normal() {
        List<CountryBusinessLineDo> doList = Collections.singletonList(new CountryBusinessLineDo());
        List<CountryBusinessLineRes> resList = Collections.singletonList(new CountryBusinessLineRes());
        when(repo.getByCountryId(1)).thenReturn(doList);
        when(converter.listDoToRes(doList)).thenReturn(resList);
        assertEquals(resList, provider.getCountryId(1));
    }

    @Test
    void getCountryId_null() {
        when(repo.getByCountryId(1)).thenReturn(null);
        assertNull(provider.getCountryId(1));
    }

    @Test
    void getByBusinessLine_normal() {
        List<CountryBusinessLineDo> doList = Collections.singletonList(new CountryBusinessLineDo());
        List<CountryBusinessLineRes> resList = Collections.singletonList(new CountryBusinessLineRes());
        when(repo.getByBusinessLine("BL")).thenReturn(doList);
        when(converter.listDoToRes(doList)).thenReturn(resList);
        assertEquals(resList, provider.getByBusinessLine("BL"));
    }

    @Test
    void getByBusinessLine_null() {
        when(repo.getByBusinessLine("BL")).thenReturn(null);
        assertNull(provider.getByBusinessLine("BL"));
    }

    @Test
    void saveOrUpdate_update() {
        CountryBusinessLineReq req = new CountryBusinessLineReq();
        CountryBusinessLineDo entity = new CountryBusinessLineDo();
        entity.setId(10);
        when(converter.reqToDo(req)).thenReturn(entity);
        assertEquals(10, provider.saveOrUpdate(req));
        verify(repo).updateById(entity);
    }

    @Test
    void saveOrUpdate_insert() {
        CountryBusinessLineReq req = new CountryBusinessLineReq();
        CountryBusinessLineDo entity = new CountryBusinessLineDo();
        entity.setId(null);
        when(converter.reqToDo(req)).thenReturn(entity);
        when(repo.save(entity)).thenReturn(99);
        assertEquals(99, provider.saveOrUpdate(req));
    }

    @Test
    void removeByIds_normal() {
        doNothing().when(repo).deleteByIds(anyList());
        assertDoesNotThrow(() -> provider.removeByIds(Arrays.asList(1,2)));
    }
} 