package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.businessline.ManageLineRes;
import com.mi.oa.asset.commons.config.app.converter.BusinessLineConvertor;
import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.domain.common.entity.ManageLineDo;
import com.mi.oa.asset.commons.config.domain.common.repository.BusinessLineRepo;
import com.mi.oa.asset.commons.config.domain.common.repository.ManageLineRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class BusinessLineProviderImplTest {

    @InjectMocks
    private BusinessLineProviderImpl businessLineProvider;

    @Mock
    private BusinessLineRepo businessLineRepo;

    @Mock
    private ManageLineRepo manageLineRepo;

    @Mock
    private BusinessLineConvertor businessLineConvertor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getManageLine_WhenManageLineIsBlank_ShouldReturnNull() {
        // 测试空字符串
        assertNull(businessLineProvider.getManageLine(""));
        // 测试null
        assertNull(businessLineProvider.getManageLine(null));
    }

    @Test
    void getManageLine_WhenManageLineExists_ShouldReturnManageLineRes() {
        // 准备测试数据
        String manageLine = "TEST_LINE";
        ManageLineDo manageLineDo = new ManageLineDo();
        manageLineDo.setManageLine(manageLine);
        manageLineDo.setManageLineName("测试线");

        BusinessLineDo businessLineDo = new BusinessLineDo();
        businessLineDo.setManageLineCode(manageLine);
        List<BusinessLineDo> businessLineDos = Collections.singletonList(businessLineDo);
        
        // 设置mock行为
        when(manageLineRepo.getManageLine(Collections.singletonList(manageLine)))
                .thenReturn(Collections.singletonList(manageLineDo));
        when(businessLineRepo.getBusinessLine(Collections.singletonList(manageLine)))
                .thenReturn(businessLineDos);
        when(businessLineConvertor.doToResBatch(businessLineDos))
                .thenReturn(Collections.emptyList());

        // 执行测试
        ManageLineRes result = businessLineProvider.getManageLine(manageLine);

        // 验证结果
        assertNotNull(result);
        assertEquals(manageLine, result.getManageLine());
        assertEquals("测试线", result.getManageLineName());
        
        // 验证方法调用
        verify(manageLineRepo).getManageLine(Collections.singletonList(manageLine));
        verify(businessLineRepo).getBusinessLine(Collections.singletonList(manageLine));
        verify(businessLineConvertor).doToResBatch(businessLineDos);
    }

    @Test
    void getManageLines_WhenManageLinesExist_ShouldReturnManageLineResList() {
        // 准备测试数据
        List<String> manageLines = Arrays.asList("LINE1", "LINE2");
        List<ManageLineDo> manageLineDos = Arrays.asList(
                createManageLineDo("LINE1", "线1"),
                createManageLineDo("LINE2", "线2")
        );
        
        List<BusinessLineDo> businessLineDos = Arrays.asList(
                createBusinessLineDo("LINE1"),
                createBusinessLineDo("LINE2")
        );

        // 设置mock行为
        when(manageLineRepo.getManageLine(manageLines)).thenReturn(manageLineDos);
        when(businessLineRepo.getBusinessLine(manageLines)).thenReturn(businessLineDos);
        when(businessLineConvertor.doToResBatch(any())).thenReturn(Collections.emptyList());

        // 执行测试
        List<ManageLineRes> results = businessLineProvider.getManageLines(manageLines);

        // 验证结果
        assertNotNull(results);
        assertEquals(2, results.size());
        assertEquals("LINE1", results.get(0).getManageLine());
        assertEquals("线1", results.get(0).getManageLineName());
        assertEquals("LINE2", results.get(1).getManageLine());
        assertEquals("线2", results.get(1).getManageLineName());

        // 验证方法调用
        verify(manageLineRepo).getManageLine(manageLines);
        verify(businessLineRepo).getBusinessLine(manageLines);
        verify(businessLineConvertor, times(2)).doToResBatch(any());
    }

    private ManageLineDo createManageLineDo(String code, String name) {
        ManageLineDo manageLineDo = new ManageLineDo();
        manageLineDo.setManageLine(code);
        manageLineDo.setManageLineName(name);
        return manageLineDo;
    }

    private BusinessLineDo createBusinessLineDo(String manageLineCode) {
        BusinessLineDo businessLineDo = new BusinessLineDo();
        businessLineDo.setManageLineCode(manageLineCode);
        return businessLineDo;
    }
}
