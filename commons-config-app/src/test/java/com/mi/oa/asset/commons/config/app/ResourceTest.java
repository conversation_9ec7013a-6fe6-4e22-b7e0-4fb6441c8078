package com.mi.oa.asset.commons.config.app;

import com.mi.oa.asset.commons.config.api.auth.QueryRemoteDataResourceReq;
import com.mi.oa.asset.commons.config.api.auth.ResourceDimensionRemoteResp;
import com.mi.oa.asset.commons.config.app.ability.AssetAuthAbility;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = StartApplication.class)
@Disabled
public class ResourceTest {

    @Resource
    private AssetAuthAbility assetAuthAbility;


    @Test
    public void testListAssetFuncAuthData() {
        QueryRemoteDataResourceReq resourceReq = new QueryRemoteDataResourceReq();
        resourceReq.setDimensionCode("pt_warehouse");
        resourceReq.setPageSize("100");
        resourceReq.setPageNum("1");
        ResourceDimensionRemoteResp response = assetAuthAbility.listAssetFuncAuthData(resourceReq);
        assertNotNull(response);
    }


    @Test
    public void testListTreeAssetFuncAuthData() {
        QueryRemoteDataResourceReq resourceReq = new QueryRemoteDataResourceReq();
        resourceReq.setDimensionCode("ASSET_CATEGORY");
        ResourceDimensionRemoteResp response = assetAuthAbility.listAssetFuncAuthData(resourceReq);
        assertNotNull(response);
    }


}