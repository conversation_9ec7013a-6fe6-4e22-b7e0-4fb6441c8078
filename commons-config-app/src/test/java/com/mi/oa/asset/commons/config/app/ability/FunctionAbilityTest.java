package com.mi.oa.asset.commons.config.app.ability;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.oa.asset.common.enums.ColumnUseWay;
import com.mi.oa.asset.commons.config.api.function.DownTaskQueryReq;
import com.mi.oa.asset.commons.config.api.function.DownTaskRes;
import com.mi.oa.asset.commons.config.app.converter.DownTaskConverter;
import com.mi.oa.asset.commons.config.app.converter.FunctionConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.GlobalValPo;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.mi.oa.asset.eam.mybatis.DownloadTaskPo;
import com.mi.oa.asset.eam.mybatis.DownloadTaskMapper;
import com.xiaomi.mit.api.PageData;
import com.mi.oa.asset.commons.config.api.function.FuncFieldRes;
import com.mi.oa.asset.commons.config.app.converter.DictConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.DictPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.DictMapper;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.ArgumentMatchers;
import org.mockito.MockitoAnnotations;
import com.mi.oa.asset.commons.config.api.function.DictRes;
import com.mi.oa.asset.eam.mybatis.FuncColumnPo;
import com.mi.oa.asset.eam.mybatis.FuncColumnMapper;
import com.mi.oa.asset.eam.mybatis.FunctionPo;
import com.mi.oa.asset.eam.mybatis.FunctionMapper;
import com.mi.oa.asset.commons.config.api.function.FieldImportReq;
import com.mi.oa.asset.commons.config.domain.function.enums.FuncManageType;
import com.mi.oa.asset.commons.config.domain.function.repository.FunColumnRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.mi.oa.asset.commons.config.api.function.DictBusinessLineQueryReq;
import com.mi.oa.asset.commons.config.api.function.DictBusinessLineBatchReq;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FuncDictPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.FuncDictMapper;
import com.mi.oa.asset.commons.config.api.function.GlobalValRes;
import com.mi.oa.asset.commons.config.app.converter.GlobalValConverter;
import com.mi.oa.asset.commons.config.infra.database.mapper.GlobalValMapper;
import org.junit.jupiter.api.AfterEach;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FunctionAbilityTest {

    @InjectMocks
    private FunctionAbility functionAbility;

    @Mock
    private DownTaskConverter downTaskConverter;

    @Mock
    private DownloadTaskMapper downloadTaskMapper;

    @Mock
    private DictMapper dictMapper;

    @Mock
    private FunctionConverter functionConverter;

    @Mock
    private DictConverter dictConverter;

    @Mock
    private FuncColumnMapper funcColumnMapper;

    @Mock
    private FunctionMapper functionMapper;

    @Mock
    private FunColumnRepo funColumnRepo;

    @Mock
    private FuncDictMapper funcDictMapper;

    @Mock
    private GlobalValMapper globalValMapper;

    @Mock
    private GlobalValConverter globalValConverter;

    private MockedStatic<RedisUtils> redisUtilsMockedStatic;

    private static final String TEST_USER = "test_user";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        redisUtilsMockedStatic = mockStatic(RedisUtils.class);
        redisUtilsMockedStatic.when(() -> RedisUtils.keys(anyString()))
            .thenReturn(Collections.emptySet());
        redisUtilsMockedStatic.when(() -> RedisUtils.delete(any(Set.class)))
            .thenAnswer(invocation -> null);
    }

    @AfterEach
    void tearDown() {
        if (redisUtilsMockedStatic != null) {
            redisUtilsMockedStatic.close();
        }
    }

    @Test
    void fillDictList_WhenFieldResListIsEmpty_ShouldDoNothing() {
        // 准备测试数据
        List<FuncFieldRes> fieldResList = new ArrayList<>();

        // 执行测试
        functionAbility.fillDictList(fieldResList);

        // 验证结果
        verify(dictMapper, never()).selectList(any());
        verify(dictConverter, never()).poToResList(any());
    }

    @Test
    void fillDictList_WhenNoControlCode_ShouldDoNothing() {
        // 准备测试数据
        List<FuncFieldRes> fieldResList = new ArrayList<>();
        FuncFieldRes fieldRes = new FuncFieldRes();
        fieldRes.setControlCode(null);
        fieldResList.add(fieldRes);

        // 执行测试
        functionAbility.fillDictList(fieldResList);

        // 验证结果
        verify(dictMapper, never()).selectList(any());
        verify(dictConverter, never()).poToResList(any());
    }

    @Test
    void fillDictList_WhenHasControlCode_ShouldFillDictList() {
        // 准备测试数据
        List<FuncFieldRes> fieldResList = new ArrayList<>();
        FuncFieldRes fieldRes1 = new FuncFieldRes();
        fieldRes1.setControlCode("CONTROL_CODE_1");
        fieldResList.add(fieldRes1);

        FuncFieldRes fieldRes2 = new FuncFieldRes();
        fieldRes2.setControlCode("CONTROL_CODE_2");
        fieldResList.add(fieldRes2);

        // 模拟字典数据
        List<DictPo> dictPoList = new ArrayList<>();
        DictPo dictPo1 = new DictPo();
        dictPo1.setParentCode("CONTROL_CODE_1");
        dictPo1.setCode("CODE_1");
        dictPo1.setName("NAME_1");
        dictPoList.add(dictPo1);

        DictPo dictPo2 = new DictPo();
        dictPo2.setParentCode("CONTROL_CODE_2");
        dictPo2.setCode("CODE_2");
        dictPo2.setName("NAME_2");
        dictPoList.add(dictPo2);

        // 模拟行为
        when(dictMapper.selectList(any())).thenReturn(dictPoList);
        
        List<DictRes> dictResList = new ArrayList<>();
        DictRes dictRes1 = new DictRes();
        dictRes1.setCode("CODE_1");
        dictRes1.setName("NAME_1");
        dictResList.add(dictRes1);
        
        DictRes dictRes2 = new DictRes();
        dictRes2.setCode("CODE_2");
        dictRes2.setName("NAME_2");
        dictResList.add(dictRes2);
        
        when(dictConverter.poToResList(any())).thenReturn(dictResList);

        // 执行测试
        functionAbility.fillDictList(fieldResList);

        // 验证结果
        verify(dictMapper).selectList(any());
        verify(dictConverter, times(2)).poToResList(any());
    }

    @Test
    void fillDictList_WhenDictListIsEmpty_ShouldDoNothing() {
        // 准备测试数据
        List<FuncFieldRes> fieldResList = new ArrayList<>();
        FuncFieldRes fieldRes = new FuncFieldRes();
        fieldRes.setControlCode("CONTROL_CODE");
        fieldResList.add(fieldRes);

        // 模拟行为
        when(dictMapper.selectList(any())).thenReturn(new ArrayList<>());

        // 执行测试
        functionAbility.fillDictList(fieldResList);

        // 验证结果
        verify(dictMapper).selectList(any());
        verify(dictConverter, never()).poToResList(any());
    }

    @Test
    void importField_BasicImport_ShouldSucceed() {
        // mock functionConverter.copy 行为，避免 NPE
        when(functionConverter.copy(any(FuncColumnPo.class))).thenAnswer(invocation -> {
            FuncColumnPo po = invocation.getArgument(0);
            FuncColumnPo copy = new FuncColumnPo();
            copy.setId(po.getId());
            copy.setCode(po.getCode());
            copy.setFuncCode(po.getFuncCode());
            copy.setUseWay(po.getUseWay());
            return copy;
        });
        // 准备测试数据
        FieldImportReq importReq = new FieldImportReq();
        importReq.setFuncCode("TEST_FUNC");
        importReq.setBusinessLine("TEST_LINE");
        importReq.setUseWay(ColumnUseWay.FUNC_COLUMN.getCode()); // 功能列字段
        importReq.setFieldIds(Arrays.asList(1, 2));

        List<FuncColumnPo> columnList = Arrays.asList(
            createFuncColumnPo(1, "FIELD1", "TEST_FUNC", ColumnUseWay.FUNC_COLUMN.getCode()),
            createFuncColumnPo(2, "FIELD2", "TEST_FUNC", ColumnUseWay.FUNC_COLUMN.getCode())
        );

        // 设置mock行为
        when(funcColumnMapper.selectList(any())).thenReturn(columnList);
        
        FunctionPo functionPo = createFunctionPo("TEST_FUNC", "TEST_TABLE");
        when(functionMapper.selectOne(any())).thenReturn(functionPo);
        
        doNothing().when(funColumnRepo).batchSave(any());
        
        // 设置 RedisUtils 的行为
        Set<String> keys = Collections.singleton("test_key");
        redisUtilsMockedStatic.when(() -> RedisUtils.keys("FUN_COL_SQL:TEST_FUNC:*"))
            .thenReturn(keys);
        redisUtilsMockedStatic.when(() -> RedisUtils.delete(keys))
            .thenAnswer(invocation -> null);

        // 执行测试
        functionAbility.importField(importReq);

        // 验证结果
        verify(funcColumnMapper, times(2)).selectList(any());
        verify(functionMapper).selectOne(any());
        verify(funColumnRepo).batchSave(any());
        
        // 验证 RedisUtils 的调用
        redisUtilsMockedStatic.verify(() -> RedisUtils.keys("FUN_COL_SQL:TEST_FUNC:*"));
        redisUtilsMockedStatic.verify(() -> RedisUtils.delete(keys));
    }

    @Test
    void importField_WithInvalidParams_ShouldThrowException() {
        // 准备测试数据
        FieldImportReq importReq = new FieldImportReq();
        importReq.setFuncCode(""); // 使用空字符串代替 null
        importReq.setBusinessLine("TEST_LINE");
        importReq.setUseWay(1);
        importReq.setFieldIds(Arrays.asList(1, 2));

        // 执行测试并验证异常
        assertThrows(ErrorCodeException.class, () -> {
            functionAbility.importField(importReq);
        });
    }

    @Test
    void importField_WithSystemManageType_ShouldHandleCorrectly() {
        // mock functionConverter.copy 行为，避免 NPE
        when(functionConverter.copy(any(FuncColumnPo.class))).thenAnswer(invocation -> {
            FuncColumnPo po = invocation.getArgument(0);
            FuncColumnPo copy = new FuncColumnPo();
            copy.setId(po.getId());
            copy.setCode(po.getCode());
            copy.setFuncCode(po.getFuncCode());
            copy.setUseWay(po.getUseWay());
            return copy;
        });
        // 准备测试数据
        FieldImportReq importReq = new FieldImportReq();
        importReq.setFuncCode("TEST_FUNC");
        importReq.setBusinessLine("TEST_LINE");
        importReq.setManageType(FuncManageType.SYSTEM.getCode());
        importReq.setUseWay(ColumnUseWay.FUNC_COLUMN.getCode()); // 修改为功能列字段
        importReq.setFieldIds(Arrays.asList(1));

        List<FuncColumnPo> columnList = Arrays.asList(
            createFuncColumnPo(1, "FIELD1", "TEST_FUNC", ColumnUseWay.FUNC_COLUMN.getCode())
        );

        // 设置mock行为
        when(funcColumnMapper.selectList(any())).thenReturn(columnList);
        
        FunctionPo functionPo = createFunctionPo("TEST_FUNC", "TEST_TABLE");
        when(functionMapper.selectOne(any())).thenReturn(functionPo);
        
        doNothing().when(funColumnRepo).batchSave(any());

        // 执行测试
        functionAbility.importField(importReq);

        // 验证结果
        verify(funcColumnMapper, times(2)).selectList(any());
        verify(functionMapper).selectOne(any());
        verify(funColumnRepo).batchSave(any());
    }

    @Test
    void importField_WithFormUseWay_ShouldTransformCode() {
        // mock functionConverter.copy 行为，避免 NPE
        when(functionConverter.copy(any(FuncColumnPo.class))).thenAnswer(invocation -> {
            FuncColumnPo po = invocation.getArgument(0);
            FuncColumnPo copy = new FuncColumnPo();
            copy.setId(po.getId());
            copy.setCode(po.getCode());
            copy.setFuncCode(po.getFuncCode());
            copy.setUseWay(po.getUseWay());
            return copy;
        });
        // 准备测试数据
        FieldImportReq importReq = new FieldImportReq();
        importReq.setFuncCode("TEST_FUNC");
        importReq.setBusinessLine("TEST_LINE");
        importReq.setUseWay(ColumnUseWay.FUNC_FORM.getCode()); // 表单列字段
        importReq.setFieldIds(Arrays.asList(1));

        List<FuncColumnPo> columnList = Arrays.asList(
            createFuncColumnPo(1, "TEST_FIELD", "TEST_FUNC", ColumnUseWay.FUNC_FORM.getCode())
        );

        // 设置mock行为
        when(funcColumnMapper.selectList(any())).thenReturn(columnList);
        
        FunctionPo functionPo = createFunctionPo("TEST_FUNC", "TEST_TABLE");
        when(functionMapper.selectOne(any())).thenReturn(functionPo);
        
        doNothing().when(funColumnRepo).batchSave(any());

        // 执行测试
        functionAbility.importField(importReq);

        // 验证结果
        verify(funcColumnMapper, times(2)).selectList(any());
        verify(functionMapper).selectOne(any());
        verify(funColumnRepo).batchSave(any());
    }

    @Test
    void deleteField_BasicDelete_ShouldSucceed() {
        // 准备测试数据
        List<Integer> ids = Arrays.asList(1, 2);
        List<FuncColumnPo> columnList = Arrays.asList(
            createFuncColumnPo(1, "FIELD1", "TEST_FUNC", ColumnUseWay.FUNC_COLUMN.getCode()),
            createFuncColumnPo(2, "FIELD2", "TEST_FUNC", ColumnUseWay.FUNC_COLUMN.getCode())
        );

        // 设置mock行为
        when(funcColumnMapper.selectBatchIds(ids)).thenReturn(columnList);
        when(funcColumnMapper.deleteBatchIds(ids)).thenReturn(2);
        
        // 设置 RedisUtils 的行为
        Set<String> keys = Collections.singleton("test_key");
        redisUtilsMockedStatic.when(() -> RedisUtils.keys("FUN_COL_SQL:TEST_FUNC:*"))
            .thenReturn(keys);
        redisUtilsMockedStatic.when(() -> RedisUtils.delete(keys))
            .thenAnswer(invocation -> null);

        // 执行测试
        functionAbility.deleteField(ids);

        // 验证结果
        verify(funcColumnMapper).selectBatchIds(ids);
        verify(funcColumnMapper).deleteBatchIds(ids);
        
        // 验证 RedisUtils 的调用
        redisUtilsMockedStatic.verify(() -> RedisUtils.keys("FUN_COL_SQL:TEST_FUNC:*"));
        redisUtilsMockedStatic.verify(() -> RedisUtils.delete(keys));
    }

    @Test
    void deleteField_WithEmptyList_ShouldDoNothing() {
        // 准备测试数据
        List<Integer> ids = new ArrayList<>();

        // 设置mock行为
        when(funcColumnMapper.selectBatchIds(ids)).thenReturn(Collections.emptyList());

        // 执行测试
        functionAbility.deleteField(ids);

        // 验证结果
        verify(funcColumnMapper).selectBatchIds(ids);
        verify(funcColumnMapper, never()).deleteBatchIds(any());
    }

    @Test
    void deleteField_WithTableUseWay_ShouldNotDeleteCache() {
        // 准备测试数据
        List<Integer> ids = Arrays.asList(1);
        List<FuncColumnPo> columnList = Arrays.asList(
            createFuncColumnPo(1, "FIELD1", "TEST_FUNC", ColumnUseWay.TABLE.getCode())
        );

        // 设置mock行为
        when(funcColumnMapper.selectBatchIds(ids)).thenReturn(columnList);
        when(funcColumnMapper.deleteBatchIds(ids)).thenReturn(1);

        // 执行测试
        functionAbility.deleteField(ids);

        // 验证结果
        verify(funcColumnMapper).selectBatchIds(ids);
        verify(funcColumnMapper).deleteBatchIds(ids);
        
        // 验证 RedisUtils 没有被调用
        redisUtilsMockedStatic.verify(() -> RedisUtils.keys(anyString()), never());
        redisUtilsMockedStatic.verify(() -> RedisUtils.delete(any(Set.class)), never());
    }

    @Test
    void deleteField_WithFormUseWay_ShouldNotDeleteCache() {
        // 准备测试数据
        List<Integer> ids = Arrays.asList(1);
        List<FuncColumnPo> columnList = Arrays.asList(
            createFuncColumnPo(1, "FIELD1", "TEST_FUNC", ColumnUseWay.FUNC_FORM.getCode())
        );

        // 设置mock行为
        when(funcColumnMapper.selectBatchIds(ids)).thenReturn(columnList);
        when(funcColumnMapper.deleteBatchIds(ids)).thenReturn(1);

        // 执行测试
        functionAbility.deleteField(ids);

        // 验证结果
        verify(funcColumnMapper).selectBatchIds(ids);
        verify(funcColumnMapper).deleteBatchIds(ids);
        
        // 验证 RedisUtils 没有被调用
        redisUtilsMockedStatic.verify(() -> RedisUtils.keys(anyString()), never());
        redisUtilsMockedStatic.verify(() -> RedisUtils.delete(any(Set.class)), never());
    }

    @Test
    void listDictRelByBusinessLine_ValidRequest_ReturnsDictList() {
        // 准备测试数据
        DictBusinessLineQueryReq queryReq = new DictBusinessLineQueryReq();
        queryReq.setCode("TEST_CODE");
        queryReq.setBusinessLine("TEST_BUSINESS_LINE");
        queryReq.setManageLine("TEST_MANAGE_LINE");
        queryReq.setFuncCode("TEST_FUNC_CODE");

        List<FuncDictPo> funcDictList = Arrays.asList(
            createFuncDictPo("TEST_CODE", "TEST_PARENT", "TEST_BUSINESS_LINE", "TEST_FUNC_CODE", com.mi.oa.asset.common.enums.ManageType.BUSINESS_LINE.getCode())
        );

        List<DictPo> dictList = Arrays.asList(
            createDictPo("TEST_CODE", "TEST_NAME", "TEST_PARENT")
        );

        List<DictRes> expectedDictList = Arrays.asList(
            createDictRes("TEST_CODE", "TEST_NAME")
        );

        // 设置mock行为
        when(funcDictMapper.selectList(any())).thenReturn(funcDictList);
        when(dictMapper.selectList(any())).thenReturn(dictList);
        when(dictConverter.poToResList(any())).thenReturn(expectedDictList);

        // 执行测试
        List<DictRes> result = functionAbility.listDictRelByBusinessLine(queryReq);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedDictList.size(), result.size());
        assertEquals(expectedDictList.get(0).getCode(), result.get(0).getCode());
        assertEquals(expectedDictList.get(0).getName(), result.get(0).getName());

        // 验证调用
        verify(funcDictMapper).selectList(any());
        verify(dictMapper).selectList(any());
        verify(dictConverter).poToResList(any());
    }

    @Test
    void listDictRelByBusinessLine_WithEmptyResult_ReturnsEmptyList() {
        // 准备测试数据
        DictBusinessLineQueryReq queryReq = new DictBusinessLineQueryReq();
        queryReq.setCode("TEST_CODE");
        queryReq.setBusinessLine("TEST_BUSINESS_LINE");
        queryReq.setManageLine("TEST_MANAGE_LINE");
        queryReq.setFuncCode("TEST_FUNC_CODE");

        // 设置mock行为
        when(funcDictMapper.selectList(any())).thenReturn(Collections.emptyList());
        when(dictMapper.selectList(any())).thenReturn(Collections.emptyList());

        // 执行测试
        List<DictRes> result = functionAbility.listDictRelByBusinessLine(queryReq);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用
        verify(funcDictMapper).selectList(any());
        verify(dictMapper).selectList(any());
        verify(dictConverter, never()).poToResList(any());
    }

    @Test
    void listDictRelByBusinessLine_WithInvalidParams_ThrowsException() {
        // 准备测试数据
        DictBusinessLineQueryReq queryReq = new DictBusinessLineQueryReq();
        queryReq.setCode("TEST_CODE");
        // 不设置 businessLine 和 manageLine

        // 执行测试并验证异常
        assertThrows(ErrorCodeException.class, () -> {
            functionAbility.listDictRelByBusinessLine(queryReq);
        });
    }

    @Test
    void listDictRelByBusinessLineBatch_ValidRequest_ReturnsDictMap() {
        // 准备测试数据
        DictBusinessLineBatchReq queryReq = new DictBusinessLineBatchReq();
        queryReq.setCode(Arrays.asList("CODE1", "CODE2"));
        queryReq.setBusinessLine("TEST_BUSINESS_LINE");
        queryReq.setManageLine("TEST_MANAGE_LINE");
        queryReq.setFuncCode("TEST_FUNC_CODE");

        List<FuncDictPo> funcDictList = Arrays.asList(
            createFuncDictPo("CODE1", "PARENT1", "TEST_BUSINESS_LINE", "TEST_FUNC_CODE", com.mi.oa.asset.common.enums.ManageType.BUSINESS_LINE.getCode()),
            createFuncDictPo("CODE2", "PARENT2", "TEST_BUSINESS_LINE", "TEST_FUNC_CODE", com.mi.oa.asset.common.enums.ManageType.BUSINESS_LINE.getCode())
        );

        List<DictPo> dictList = Arrays.asList(
            createDictPo("CODE1", "NAME1", "PARENT1"),
            createDictPo("CODE2", "NAME2", "PARENT2")
        );

        // 设置mock行为
        when(funcDictMapper.selectList(any())).thenReturn(funcDictList);
        when(dictMapper.selectList(any())).thenReturn(dictList);
        when(dictConverter.funcPoToRes(any())).thenAnswer(invocation -> {
            FuncDictPo po = invocation.getArgument(0);
            DictRes res = new DictRes();
            res.setCode(po.getCode());
            res.setName(po.getCode().equals("CODE1") ? "NAME1" : "NAME2");
            return res;
        });

        // 执行测试
        Map<String, List<DictRes>> result = functionAbility.listDictRelByBusinessLineBatch(queryReq);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("PARENT1"));
        assertTrue(result.containsKey("PARENT2"));
        assertEquals(1, result.get("PARENT1").size());
        assertEquals(1, result.get("PARENT2").size());
        assertEquals("NAME1", result.get("PARENT1").get(0).getName());
        assertEquals("NAME2", result.get("PARENT2").get(0).getName());

        // 验证调用
        verify(funcDictMapper).selectList(any());
        verify(dictMapper).selectList(any());
        verify(dictConverter, times(2)).funcPoToRes(any());
    }

    @Test
    void listDictRelByBusinessLineBatch_WithEmptyResult_ReturnsEmptyMap() {
        // 准备测试数据
        DictBusinessLineBatchReq queryReq = new DictBusinessLineBatchReq();
        queryReq.setCode(Arrays.asList("CODE1", "CODE2"));
        queryReq.setBusinessLine("TEST_BUSINESS_LINE");
        queryReq.setManageLine("TEST_MANAGE_LINE");
        queryReq.setFuncCode("TEST_FUNC_CODE");

        // 设置mock行为
        when(funcDictMapper.selectList(any())).thenReturn(Collections.emptyList());
        when(dictMapper.selectList(any())).thenReturn(Collections.emptyList());

        // 执行测试
        Map<String, List<DictRes>> result = functionAbility.listDictRelByBusinessLineBatch(queryReq);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用
        verify(funcDictMapper).selectList(any());
        verify(dictMapper).selectList(any());
        verify(dictConverter, never()).funcPoToRes(any());
    }

    @Test
    void listDictRelByBusinessLineBatch_WithInvalidParams_ThrowsException() {
        // 准备测试数据
        DictBusinessLineBatchReq queryReq = new DictBusinessLineBatchReq();
        queryReq.setCode(Collections.emptyList());
        queryReq.setBusinessLine("TEST_BUSINESS_LINE");
        queryReq.setManageLine("TEST_MANAGE_LINE");
        queryReq.setFuncCode("TEST_FUNC_CODE");

        // 执行测试并验证异常
        assertThrows(ErrorCodeException.class, () -> {
            functionAbility.listDictRelByBusinessLineBatch(queryReq);
        });
    }

    @Test
    void getGlobalVal_ValidRequest_ReturnsGlobalVal() {
        String key = "TEST_KEY";
        String businessLine = "TEST_BUSINESS_LINE";
        Integer manageType = 1;

        GlobalValPo po = new GlobalValPo();
        po.setCode(key);
        po.setBusinessLine(businessLine);
        po.setManageType(manageType);
        po.setValue("TEST_VALUE");

        // 修改 mock 行为，返回非空列表
        when(globalValMapper.selectList(any())).thenReturn(Collections.singletonList(po));

        GlobalValRes expectedRes = new GlobalValRes();
        expectedRes.setCode(key);
        expectedRes.setBusinessLine(businessLine);
        expectedRes.setManageType(manageType);
        expectedRes.setValue("TEST_VALUE");
        when(globalValConverter.poToResList(any())).thenReturn(Collections.singletonList(expectedRes));

        List<GlobalValRes> result = functionAbility.getGlobalVal(Collections.singletonList(key), businessLine);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(expectedRes.getCode(), result.get(0).getCode());
        assertEquals(expectedRes.getValue(), result.get(0).getValue());

        verify(globalValMapper).selectList(any());
        verify(globalValConverter).poToResList(any());
    }

    @Test
    void getGlobalVal_InvalidParam_ThrowsException() {
        assertThrows(ErrorCodeException.class, () -> {
            functionAbility.getGlobalVal(null, "TEST_BUSINESS_LINE");
        });
        assertThrows(ErrorCodeException.class, () -> {
            functionAbility.getGlobalVal(Collections.singletonList("TEST_KEY"), null);
        });
    }

    @Test
    void getDictList_WithValidCodeList_ReturnsGroupedDictList() {
        // 准备测试数据
        List<String> codeList = Arrays.asList("PARENT1", "PARENT2");
        
        List<DictPo> dictList = Arrays.asList(
            createDictPo("CODE1", "NAME1", "PARENT1"),
            createDictPo("CODE2", "NAME2", "PARENT1"),
            createDictPo("CODE3", "NAME3", "PARENT2")
        );

        // 设置mock行为
        when(dictMapper.selectList(any())).thenReturn(dictList);
        when(dictConverter.poToResList(any())).thenAnswer(invocation -> {
            List<DictPo> pos = invocation.getArgument(0);
            return pos.stream().map(po -> {
                DictRes res = new DictRes();
                res.setCode(po.getCode());
                res.setName(po.getName());
                res.setParentCode(po.getParentCode());
                return res;
            }).collect(Collectors.toList());
        });

        // 执行测试
        Map<String, List<DictRes>> result = functionAbility.getDictList(codeList);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("PARENT1"));
        assertTrue(result.containsKey("PARENT2"));
        assertEquals(2, result.get("PARENT1").size());
        assertEquals(1, result.get("PARENT2").size());
        
        // 验证PARENT1下的字典值
        List<DictRes> parent1List = result.get("PARENT1");
        assertEquals("CODE1", parent1List.get(0).getCode());
        assertEquals("NAME1", parent1List.get(0).getName());
        assertEquals("CODE2", parent1List.get(1).getCode());
        assertEquals("NAME2", parent1List.get(1).getName());
        
        // 验证PARENT2下的字典值
        List<DictRes> parent2List = result.get("PARENT2");
        assertEquals("CODE3", parent2List.get(0).getCode());
        assertEquals("NAME3", parent2List.get(0).getName());
    }

    @Test
    void getDictList_WithEmptyCodeList_ReturnsEmptyMap() {
        // 准备测试数据
        List<String> codeList = new ArrayList<>();

        // 执行测试
        Map<String, List<DictRes>> result = functionAbility.getDictList(codeList);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getDictList_WithNoMatchingDicts_ReturnsEmptyMap() {
        // 准备测试数据
        List<String> codeList = Arrays.asList("PARENT1", "PARENT2");
        
        // 设置mock行为 - 返回空列表
        when(dictMapper.selectList(any())).thenReturn(new ArrayList<>());

        // 执行测试
        Map<String, List<DictRes>> result = functionAbility.getDictList(codeList);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    private FuncColumnPo createFuncColumnPo(Integer id, String code, String funcCode, Integer useWay) {
        FuncColumnPo po = new FuncColumnPo();
        po.setId(id);
        po.setCode(code);
        po.setFuncCode(funcCode);
        po.setUseWay(useWay);
        return po;
    }

    private FunctionPo createFunctionPo(String code, String tableName) {
        FunctionPo po = new FunctionPo();
        po.setCode(code);
        po.setTableName(tableName);
        return po;
    }

    private FuncDictPo createFuncDictPo(String code, String parentCode, String businessLine, String funcCode, Integer manageType) {
        FuncDictPo po = new FuncDictPo();
        po.setCode(code);
        po.setParentCode(parentCode);
        po.setBusinessLine(businessLine);
        po.setFuncCode(funcCode);
        po.setValid(1);
        po.setManageType(manageType);
        return po;
    }

    private DictPo createDictPo(String code, String name, String parentCode) {
        DictPo po = new DictPo();
        po.setCode(code);
        po.setName(name);
        po.setParentCode(parentCode);
        return po;
    }

    private DictRes createDictRes(String code, String name) {
        DictRes res = new DictRes();
        res.setCode(code);
        res.setName(name);
        return res;
    }
} 
