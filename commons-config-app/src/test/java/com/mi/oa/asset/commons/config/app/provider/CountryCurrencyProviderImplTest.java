package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyReq;
import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyRes;
import com.mi.oa.asset.commons.config.app.ability.CountryCurrencyAbility;
import com.mi.oa.asset.commons.config.app.converter.CountryCurrencyConverter;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryCurrencyDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryCurrencyRepo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CountryCurrencyProviderImplTest {
    @InjectMocks
    private CountryCurrencyProviderImpl provider;
    @Mock
    private CountryCurrencyRepo repo;
    @Mock
    private CountryCurrencyConverter converter;
    @Mock
    private CountryCurrencyAbility ability;

    @Test
    void getCountryCurrencyList_normal() {
        List<CountryCurrencyDo> doList = Collections.singletonList(new CountryCurrencyDo());
        List<CountryCurrencyRes> resList = Collections.singletonList(new CountryCurrencyRes());
        when(repo.searchAll()).thenReturn(doList);
        when(converter.listDoToRes(doList)).thenReturn(resList);
        List<CountryCurrencyRes> result = provider.getCountryCurrencyList();
        assertEquals(1, result.size());
    }

    @Test
    void getById_normal() {
        CountryCurrencyDo entity = new CountryCurrencyDo();
        when(repo.getById(1)).thenReturn(entity);
        CountryCurrencyRes res = new CountryCurrencyRes();
        when(converter.doToRes(entity)).thenReturn(res);
        assertEquals(res, provider.getById(1));
    }

    @Test
    void getById_null() {
        when(repo.getById(1)).thenReturn(null);
        assertNull(provider.getById(1));
    }

    @Test
    void saveOrUpdate_update() {
        CountryCurrencyReq req = new CountryCurrencyReq();
        CountryCurrencyDo entity = new CountryCurrencyDo();
        entity.setId(10);
        when(converter.reqToDo(req)).thenReturn(entity);
        doNothing().when(ability).check(entity);
        assertEquals(10, provider.saveOrUpdate(req));
        verify(repo).updateById(entity);
    }

    @Test
    void saveOrUpdate_insert() {
        CountryCurrencyReq req = new CountryCurrencyReq();
        CountryCurrencyDo entity = new CountryCurrencyDo();
        entity.setId(null);
        when(converter.reqToDo(req)).thenReturn(entity);
        doNothing().when(ability).check(entity);
        when(repo.save(entity)).thenReturn(99);
        assertEquals(99, provider.saveOrUpdate(req));
    }

    @Test
    void saveOrUpdate_checkException() {
        CountryCurrencyReq req = new CountryCurrencyReq();
        CountryCurrencyDo entity = new CountryCurrencyDo();
        when(converter.reqToDo(req)).thenReturn(entity);
        doThrow(new RuntimeException("check fail")).when(ability).check(entity);
        assertThrows(RuntimeException.class, () -> provider.saveOrUpdate(req));
    }

    @Test
    void removeByIds_normal() {
        doNothing().when(repo).deleteByIds(anyList());
        assertDoesNotThrow(() -> provider.removeByIds(Arrays.asList(1,2)));
    }

    @Test
    void getByCountryId_normal() {
        List<CountryCurrencyDo> doList = Collections.singletonList(new CountryCurrencyDo());
        List<CountryCurrencyRes> resList = Collections.singletonList(new CountryCurrencyRes());
        when(repo.getByCountryId(1)).thenReturn(doList);
        when(converter.listDoToRes(doList)).thenReturn(resList);
        assertEquals(resList, provider.getByCountryId(1));
    }

    @Test
    void getByCountryId_empty() {
        when(repo.getByCountryId(1)).thenReturn(Collections.emptyList());
        assertNull(provider.getByCountryId(1));
    }

    @Test
    void getByCountryCode_normal() {
        List<CountryCurrencyDo> doList = Collections.singletonList(new CountryCurrencyDo());
        List<CountryCurrencyRes> resList = Collections.singletonList(new CountryCurrencyRes());
        when(repo.getByCountryCode("CN")).thenReturn(doList);
        when(converter.listDoToRes(doList)).thenReturn(resList);
        assertEquals(resList, provider.getByCountryCode("CN"));
    }

    @Test
    void getByCountryCode_empty() {
        when(repo.getByCountryCode("CN")).thenReturn(Collections.emptyList());
        assertNull(provider.getByCountryCode("CN"));
    }
} 