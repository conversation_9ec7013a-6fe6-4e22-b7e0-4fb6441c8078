package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleInfoRes;
import com.mi.oa.asset.commons.config.app.converter.BusinessRoleConverter;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRole;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRoleUser;
import com.mi.oa.asset.commons.config.domain.businessrole.repository.BusinessRoleRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * BusinessRoleAbility单元测试类
 * 测试getBusinessRoleInfo方法的各种场景
 */
@ExtendWith(MockitoExtension.class)
class BusinessRoleAbilityTest {

    @InjectMocks
    private BusinessRoleAbility businessRoleAbility;

    @Mock
    private BusinessRoleRepo businessRoleRepo;

    @Mock
    private BusinessRoleConverter converter;

    private AssetOrgUnit mockOrgUnit;
    private List<BusinessRoleUser> mockBusinessRoleUsers;
    private List<BusinessRole> mockBusinessRoles;
    private List<User> mockUsers;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockOrgUnit = AssetOrgUnit.builder()
                .orgCode("TEST_ORG_001")
                .orgName("测试组织")
                .businessLine(BusinessLine.ADM_EMP)
                .build();

        // 创建模拟的业务角色用户数据
        mockBusinessRoleUsers = Arrays.asList(
                BusinessRoleUser.builder()
                        .id(1)
                        .roleCode("ROLE_ADMIN")
                        .roleName("管理员")
                        .userCode("user001")
                        .userName("张三")
                        .deptName("技术部")
                        .orgCode("TEST_ORG_001")
                        .businessLine(BusinessLine.ADM_EMP)
                        .build(),
                BusinessRoleUser.builder()
                        .id(2)
                        .roleCode("ROLE_ADMIN")
                        .roleName("管理员")
                        .userCode("user002")
                        .userName("李四")
                        .deptName("技术部")
                        .orgCode("TEST_ORG_001")
                        .businessLine(BusinessLine.ADM_EMP)
                        .build(),
                BusinessRoleUser.builder()
                        .id(3)
                        .roleCode("ROLE_USER")
                        .roleName("普通用户")
                        .userCode("user003")
                        .userName("王五")
                        .deptName("运营部")
                        .orgCode("TEST_ORG_001")
                        .businessLine(BusinessLine.ADM_EMP)
                        .build()
        );

        // 创建模拟的业务角色数据
        mockBusinessRoles = Arrays.asList(
                new BusinessRole() {{
                    setRoleCode("ROLE_ADMIN");
                    setRoleName("管理员");
                    setRoleNameEn("Administrator");
                    setRoleDesc("系统管理员角色");
                    setRoleDescEn("System Administrator Role");
                }},
                new BusinessRole() {{
                    setRoleCode("ROLE_USER");
                    setRoleName("普通用户");
                    setRoleNameEn("User");
                    setRoleDesc("普通用户角色");
                    setRoleDescEn("Normal User Role");
                }}
        );

        // 创建模拟的用户数据
        mockUsers = Arrays.asList(
                new User() {{
                    setUserName("user001");
                    setDisplayName("张三");
                    setDeptName("技术部");
                }},
                new User() {{
                    setUserName("user002");
                    setDisplayName("李四");
                    setDeptName("技术部");
                }},
                new User() {{
                    setUserName("user003");
                    setDisplayName("王五");
                    setDeptName("运营部");
                }}
        );
    }

    /**
     * 测试getBusinessRoleInfo方法 - 中文语言场景
     * 验证中文环境下角色名称和描述的正确设置
     */
    @Test
    void getBusinessRoleInfo_ChineseLanguage_ShouldReturnCorrectData() {
        // 准备测试数据
        when(businessRoleRepo.getRoleByOrgCodeAndBusiness("TEST_ORG_001", BusinessLine.ADM_EMP))
                .thenReturn(mockBusinessRoleUsers);
        when(businessRoleRepo.findByCodes(Arrays.asList("ROLE_ADMIN", "ROLE_USER")))
                .thenReturn(mockBusinessRoles);
        
        // 为不同角色设置不同的用户列表
        List<User> adminUsers = Arrays.asList(mockUsers.get(0), mockUsers.get(1)); // 张三、李四
        List<User> userUsers = Collections.singletonList(mockUsers.get(2)); // 王五
        
        when(converter.toUserList(argThat(users -> {
            if (users.size() == 2) {
                // ROLE_ADMIN 有2个用户
                return users.stream().allMatch(u -> "ROLE_ADMIN".equals(u.getRoleCode()));
            } else if (users.size() == 1) {
                // ROLE_USER 有1个用户
                return users.stream().allMatch(u -> "ROLE_USER".equals(u.getRoleCode()));
            }
            return false;
        }))).thenAnswer(invocation -> {
            List<BusinessRoleUser> users = invocation.getArgument(0);
            if (users.size() == 2) {
                return adminUsers;
            } else if (users.size() == 1) {
                return userUsers;
            }
            return Collections.emptyList();
        });

        // 执行测试
        List<BusinessRoleInfoRes> result = businessRoleAbility.getBusinessRoleInfo(mockOrgUnit, EAMConstants.CHINESE);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证管理员角色
        BusinessRoleInfoRes adminRole = result.stream()
                .filter(role -> "ROLE_ADMIN".equals(role.getRoleCode()))
                .findFirst()
                .orElse(null);
        assertNotNull(adminRole);
        assertEquals("ROLE_ADMIN", adminRole.getRoleCode());
        assertEquals("管理员", adminRole.getRoleName());
        assertEquals("系统管理员角色", adminRole.getRoleDesc());
        assertNotNull(adminRole.getUsers());
        assertEquals(2, adminRole.getUsers().size());

        // 验证普通用户角色
        BusinessRoleInfoRes userRole = result.stream()
                .filter(role -> "ROLE_USER".equals(role.getRoleCode()))
                .findFirst()
                .orElse(null);
        assertNotNull(userRole);
        assertEquals("ROLE_USER", userRole.getRoleCode());
        assertEquals("普通用户", userRole.getRoleName());
        assertEquals("普通用户角色", userRole.getRoleDesc());
        assertNotNull(userRole.getUsers());
        assertEquals(1, userRole.getUsers().size());

        // 验证方法调用
        verify(businessRoleRepo).getRoleByOrgCodeAndBusiness("TEST_ORG_001", BusinessLine.ADM_EMP);
        verify(businessRoleRepo).findByCodes(Arrays.asList("ROLE_ADMIN", "ROLE_USER"));
        verify(converter, times(2)).toUserList(anyList());
    }

    /**
     * 测试getBusinessRoleInfo方法 - 英文语言场景
     * 验证英文环境下角色名称和描述的正确设置
     */
    @Test
    void getBusinessRoleInfo_EnglishLanguage_ShouldReturnCorrectData() {
        // 准备测试数据
        when(businessRoleRepo.getRoleByOrgCodeAndBusiness("TEST_ORG_001", BusinessLine.ADM_EMP))
                .thenReturn(mockBusinessRoleUsers);
        when(businessRoleRepo.findByCodes(Arrays.asList("ROLE_ADMIN", "ROLE_USER")))
                .thenReturn(mockBusinessRoles);
        
        // 为不同角色设置不同的用户列表
        List<User> adminUsers = Arrays.asList(mockUsers.get(0), mockUsers.get(1)); // 张三、李四
        List<User> userUsers = Collections.singletonList(mockUsers.get(2)); // 王五
        
        when(converter.toUserList(argThat(users -> {
            if (users.size() == 2) {
                // ROLE_ADMIN 有2个用户
                return users.stream().allMatch(u -> "ROLE_ADMIN".equals(u.getRoleCode()));
            } else if (users.size() == 1) {
                // ROLE_USER 有1个用户
                return users.stream().allMatch(u -> "ROLE_USER".equals(u.getRoleCode()));
            }
            return false;
        }))).thenAnswer(invocation -> {
            List<BusinessRoleUser> users = invocation.getArgument(0);
            if (users.size() == 2) {
                return adminUsers;
            } else if (users.size() == 1) {
                return userUsers;
            }
            return Collections.emptyList();
        });

        // 执行测试
        List<BusinessRoleInfoRes> result = businessRoleAbility.getBusinessRoleInfo(mockOrgUnit, EAMConstants.ENGLISH);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证管理员角色（英文）
        BusinessRoleInfoRes adminRole = result.stream()
                .filter(role -> "ROLE_ADMIN".equals(role.getRoleCode()))
                .findFirst()
                .orElse(null);
        assertNotNull(adminRole);
        assertEquals("ROLE_ADMIN", adminRole.getRoleCode());
        assertEquals("Administrator", adminRole.getRoleName());
        assertEquals("System Administrator Role", adminRole.getRoleDesc());

        // 验证普通用户角色（英文）
        BusinessRoleInfoRes userRole = result.stream()
                .filter(role -> "ROLE_USER".equals(role.getRoleCode()))
                .findFirst()
                .orElse(null);
        assertNotNull(userRole);
        assertEquals("ROLE_USER", userRole.getRoleCode());
        assertEquals("User", userRole.getRoleName());
        assertEquals("Normal User Role", userRole.getRoleDesc());
    }

    /**
     * 测试getBusinessRoleInfo方法 - 空数据场景
     * 当没有业务角色用户数据时，应返回空列表
     */
    @Test
    void getBusinessRoleInfo_EmptyData_ShouldReturnEmptyList() {
        // 准备测试数据
        when(businessRoleRepo.getRoleByOrgCodeAndBusiness("TEST_ORG_001", BusinessLine.ADM_EMP))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<BusinessRoleInfoRes> result = businessRoleAbility.getBusinessRoleInfo(mockOrgUnit, EAMConstants.CHINESE);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(businessRoleRepo).getRoleByOrgCodeAndBusiness("TEST_ORG_001", BusinessLine.ADM_EMP);
        verify(businessRoleRepo, never()).findByCodes(anyList());
        verify(converter, never()).toUserList(anyList());
    }

    /**
     * 测试getBusinessRoleInfo方法 - 角色信息缺失场景
     * 当角色信息不存在时，应使用默认的空角色对象
     */
    @Test
    void getBusinessRoleInfo_MissingRoleInfo_ShouldUseDefaultRole() {
        // 准备测试数据 - 角色信息不存在
        when(businessRoleRepo.getRoleByOrgCodeAndBusiness("TEST_ORG_001", BusinessLine.ADM_EMP))
                .thenReturn(mockBusinessRoleUsers);
        when(businessRoleRepo.findByCodes(Arrays.asList("ROLE_ADMIN", "ROLE_USER")))
                .thenReturn(Collections.emptyList());
        when(converter.toUserList(anyList())).thenReturn(mockUsers);

        // 执行测试
        List<BusinessRoleInfoRes> result = businessRoleAbility.getBusinessRoleInfo(mockOrgUnit, EAMConstants.CHINESE);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证角色信息为空
        BusinessRoleInfoRes adminRole = result.stream()
                .filter(role -> "ROLE_ADMIN".equals(role.getRoleCode()))
                .findFirst()
                .orElse(null);
        assertNotNull(adminRole);
        assertEquals("ROLE_ADMIN", adminRole.getRoleCode());
        assertNull(adminRole.getRoleName());
        assertNull(adminRole.getRoleDesc());
        assertNotNull(adminRole.getUsers());
    }

    /**
     * 测试getBusinessRoleInfo方法 - 英文名称为空场景
     * 当英文名称为空时，应正确处理
     */
    @Test
    void getBusinessRoleInfo_EmptyEnglishNames_ShouldHandleCorrectly() {
        // 准备测试数据 - 英文名称为空
        List<BusinessRole> rolesWithEmptyEnglish = Arrays.asList(
                new BusinessRole() {{
                    setRoleCode("ROLE_ADMIN");
                    setRoleName("管理员");
                    setRoleNameEn(null);
                    setRoleDesc("系统管理员角色");
                    setRoleDescEn(null);
                }}
        );

        List<BusinessRoleUser> singleRoleUsers = Collections.singletonList(
                BusinessRoleUser.builder()
                        .id(1)
                        .roleCode("ROLE_ADMIN")
                        .userCode("user001")
                        .userName("张三")
                        .orgCode("TEST_ORG_001")
                        .businessLine(BusinessLine.ADM_EMP)
                        .build()
        );

        when(businessRoleRepo.getRoleByOrgCodeAndBusiness("TEST_ORG_001", BusinessLine.ADM_EMP))
                .thenReturn(singleRoleUsers);
        when(businessRoleRepo.findByCodes(Collections.singletonList("ROLE_ADMIN")))
                .thenReturn(rolesWithEmptyEnglish);
        when(converter.toUserList(anyList())).thenReturn(Collections.singletonList(mockUsers.get(0)));

        // 执行测试
        List<BusinessRoleInfoRes> result = businessRoleAbility.getBusinessRoleInfo(mockOrgUnit, EAMConstants.ENGLISH);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("ROLE_ADMIN", result.get(0).getRoleCode());
        assertNull(result.get(0).getRoleName());
        assertNull(result.get(0).getRoleDesc());
    }

    /**
     * 测试getBusinessRoleInfo方法 - 单角色多用户场景
     * 验证单个角色下多个用户的正确分组
     */
    @Test
    void getBusinessRoleInfo_SingleRoleMultipleUsers_ShouldGroupCorrectly() {
        // 准备测试数据 - 只有一个角色，多个用户
        List<BusinessRoleUser> singleRoleUsers = Arrays.asList(
                BusinessRoleUser.builder()
                        .id(1)
                        .roleCode("ROLE_ADMIN")
                        .userCode("user001")
                        .userName("张三")
                        .orgCode("TEST_ORG_001")
                        .businessLine(BusinessLine.ADM_EMP)
                        .build(),
                BusinessRoleUser.builder()
                        .id(2)
                        .roleCode("ROLE_ADMIN")
                        .userCode("user002")
                        .userName("李四")
                        .orgCode("TEST_ORG_001")
                        .businessLine(BusinessLine.ADM_EMP)
                        .build()
        );

        when(businessRoleRepo.getRoleByOrgCodeAndBusiness("TEST_ORG_001", BusinessLine.ADM_EMP))
                .thenReturn(singleRoleUsers);
        when(businessRoleRepo.findByCodes(Collections.singletonList("ROLE_ADMIN")))
                .thenReturn(Collections.singletonList(mockBusinessRoles.get(0)));
        when(converter.toUserList(anyList())).thenReturn(Arrays.asList(mockUsers.get(0), mockUsers.get(1)));

        // 执行测试
        List<BusinessRoleInfoRes> result = businessRoleAbility.getBusinessRoleInfo(mockOrgUnit, EAMConstants.CHINESE);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("ROLE_ADMIN", result.get(0).getRoleCode());
        assertEquals("管理员", result.get(0).getRoleName());
        assertEquals(2, result.get(0).getUsers().size());
    }

    /**
     * 测试getBusinessRoleInfo方法 - 异常场景
     * 当数据库查询抛出异常时的处理
     */
    @Test
    void getBusinessRoleInfo_DatabaseException_ShouldHandleException() {
        // 准备测试数据 - 模拟数据库异常
        when(businessRoleRepo.getRoleByOrgCodeAndBusiness("TEST_ORG_001", BusinessLine.ADM_EMP))
                .thenThrow(new RuntimeException("Database connection failed"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            businessRoleAbility.getBusinessRoleInfo(mockOrgUnit, EAMConstants.CHINESE);
        });

        // 验证方法调用
        verify(businessRoleRepo).getRoleByOrgCodeAndBusiness("TEST_ORG_001", BusinessLine.ADM_EMP);
        verify(businessRoleRepo, never()).findByCodes(anyList());
        verify(converter, never()).toUserList(anyList());
    }

    /**
     * 测试getBusinessRoleInfo方法 - 不同业务线场景
     * 验证不同业务线下的数据处理
     */
    @Test
    void getBusinessRoleInfo_DifferentBusinessLine_ShouldProcessCorrectly() {
        // 准备测试数据 - 使用不同的业务线
        AssetOrgUnit differentBusinessOrg = AssetOrgUnit.builder()
                .orgCode("TEST_ORG_002")
                .orgName("测试组织2")
                .businessLine(BusinessLine.CAR)
                .build();

        when(businessRoleRepo.getRoleByOrgCodeAndBusiness("TEST_ORG_002", BusinessLine.CAR))
                .thenReturn(mockBusinessRoleUsers);
        when(businessRoleRepo.findByCodes(Arrays.asList("ROLE_ADMIN", "ROLE_USER")))
                .thenReturn(mockBusinessRoles);
        when(converter.toUserList(anyList())).thenReturn(mockUsers);

        // 执行测试
        List<BusinessRoleInfoRes> result = businessRoleAbility.getBusinessRoleInfo(differentBusinessOrg, EAMConstants.CHINESE);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证方法调用
        verify(businessRoleRepo).getRoleByOrgCodeAndBusiness("TEST_ORG_002", BusinessLine.CAR);
    }
}
