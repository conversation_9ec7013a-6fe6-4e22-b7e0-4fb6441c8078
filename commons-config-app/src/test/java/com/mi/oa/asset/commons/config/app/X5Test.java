package com.mi.oa.asset.commons.config.app;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mi.oa.asset.commons.config.domain.position.valobj.TpmMdmItem;
import com.mi.oa.asset.commons.config.domain.position.valobj.TpmPage;
import com.mi.oa.asset.commons.config.infra.rpc.utils.X5HttpUtil;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.xiaomi.core.auth.x5.X5AppInfo;
import com.xiaomi.core.auth.x5.X5Response;
import com.xiaomi.core.auth.x5.X5Utils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.List;
@Disabled
public class X5Test {

    @Test
    void testTpmMdmX5() throws Exception {

        X5AppInfo x5AppInfo = new X5AppInfo("hyper_tpm", "b7348926-16b9-4c32-bb04-eb26288feaeb","queryMdmForEAM");
        String reqData = X5Utils.buildX5RequestBody(x5AppInfo, "{\n" +
                "    \"mdmType\": \"workstation\",\n" +
                "    \"pageNum\": 1,\n" +
                "    \"pageSize\": 10\n" +
                "}");
        System.out.println("x5请求体：" + reqData);
        String response = X5HttpUtil.doX5Post("https://ac-mid.test.mi.com/tpm/x5/eam/mdm", reqData);
        System.out.println("x5请求返回：" + response);
        TypeReference<X5Response<TpmPage<List<TpmMdmItem>>>> typeReference = new TypeReference<X5Response<TpmPage<List<TpmMdmItem>>>>() {
        };
        X5Response<TpmPage<List<TpmMdmItem>>> result = JacksonUtils.json2TypeReference(response, typeReference);
        System.out.println("解析：" + JacksonUtils.bean2Json(result));
    }
}
