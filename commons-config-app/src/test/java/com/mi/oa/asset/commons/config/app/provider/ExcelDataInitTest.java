package com.mi.oa.asset.commons.config.app.provider;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.common.enums.AssetUseStatus;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.CheckStatus;
import com.mi.oa.asset.common.enums.SpecialCate;
import com.mi.oa.asset.commons.config.api.assetcategory.ImportStdCategoryReq;
import com.mi.oa.asset.commons.config.api.user.UserInfoRes;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.app.StartApplication;
import com.mi.oa.asset.commons.config.app.ability.AssetCategoryAbility;
import com.mi.oa.asset.commons.config.domain.assetcategory.repository.AssetCategoryRepo;
import com.mi.oa.asset.commons.config.domain.common.repository.CommonDataRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.OrganizationStructurePo;
import com.mi.oa.asset.commons.config.infra.database.mapper.OrganizationStructurePoMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@SpringBootTest(classes = StartApplication.class)
@Disabled
class ExcelDataInitTest {

    @Autowired
    UserInfoService userInfoService;

    @Autowired
    OrganizationStructurePoMapper organizationStructurePoMapper;

    @Autowired
    private AssetCategoryRepo assetCategoryRepo;

    @Autowired
    private CommonDataRepo commonDataRepo;

    @Autowired
    private AssetCategoryAbility assetCategoryAbility;


    @Test
    public void testExcelToSql2() throws FileNotFoundException {
        // 1. 获取Excel文件路径
        File file = ResourceUtils.getFile("classpath:category.xlsx"); // 假设文件放在resources目录下

        // 2. 读取Excel数据
        List<Category> dataList = new ArrayList<>();
        EasyExcel.read(file, Category.class, new PageReadListener<Category>(dataList::addAll))
                .sheet("资产分类初始化")
                .doRead();


        List<ImportStdCategoryReq.Item> items = new ArrayList<>();
        for (Category category : dataList) {
            ImportStdCategoryReq.Item item = new ImportStdCategoryReq.Item();
            item.setPurchaseCatalogCode(category.getCateCode());
            item.setPurchaseCatalogName(category.getCateName());
            item.setLevel(category.getLevel());
            item.setParentCode(category.getParentCateCode());
            item.setSapCateCode(category.getSapCode());
            item.setSapCateName(category.getSapName());
            item.setUseYear(category.getYear());
            items.add(item);
        }

        ImportStdCategoryReq req = new ImportStdCategoryReq();
        req.setBusinessLine(BusinessLine.HEA_FACTORY.getCode());
        req.setItems(items);
        System.out.println("分类参数："+ JacksonUtils.toJson(req));
    }
    @Data
    public static class Category {
        @ExcelProperty("分类编码")
        private String cateCode;
        @ExcelProperty("分类名称")
        private String cateName;
        @ExcelProperty("上级分类")
        private String parentCateCode;
        @ExcelProperty("层级")
        private Integer level;
        @ExcelProperty("SAP资产分类编码")
        private String sapCode;
        @ExcelProperty("SAP资产分类")
        private String sapName;
        @ExcelProperty("使用年限")
        private int year;
    }

    @Test
    public void testExcelToSql() throws FileNotFoundException {
        // 1. 获取Excel文件路径
        File file = ResourceUtils.getFile("classpath:assets.xlsx"); // 假设文件放在resources目录下

        // 2. 读取Excel数据
        List<Asset> dataList = new ArrayList<>();
        EasyExcel.read(file, Asset.class, new PageReadListener<Asset>(dataList::addAll))
                .sheet("")
                .doRead();

        // 3. 生成并打印SQL语句
        generateInsertSql(dataList);
    }

    private void generateInsertSql(List<Asset> dataList) {
        String tableName = "amg_asset_account"; // 替换为你的实际表名
        Set<String> userCodes = dataList.stream().map(Asset::getUserCode).collect(Collectors.toSet());

        List<UserInfoRes> userInfos = userInfoService.getUsersByUserNames(new ArrayList<>(userCodes));
        Map<String, String> userCodeMap = userInfos.stream().collect(Collectors.toMap(UserInfoRes::getUserName, UserInfoRes::getDisplayName));


        List<String> deptCodes = dataList.stream().map(Asset::getUserDeptCode).collect(Collectors.toList());
        List<String> manageDeptCodes = dataList.stream().map(Asset::getManageDeptCode).collect(Collectors.toList());

        List<OrganizationStructurePo> organizationStructurePos = organizationStructurePoMapper.selectList(Wrappers.lambdaQuery(OrganizationStructurePo.class)
                .eq(OrganizationStructurePo::getOrgCode, deptCodes.addAll(manageDeptCodes)));

        Map<String, String> deptCodeMap = organizationStructurePos.stream().collect(Collectors.toMap(OrganizationStructurePo::getOrgCode, OrganizationStructurePo::getOrgName));

        for (Asset asset : dataList) {
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO ").append(tableName).append(" (");
            sql.append("asset_code, asset_name, asset_cate_code,asset_cate_name, asset_type, use_status, ");
            sql.append("check_status, sn, business_line, special_cate, country, ");
            sql.append("user_code,user_name, user_dept_code,user_dept_name, manage_dept_code, manage_dept_name,company_code,company_name, quantity");
            sql.append(") VALUES (");

            // 添加值部分
            sql.append(formatValue(asset.getAssetCode())).append(", ");
            sql.append(formatValue(asset.getAssetName())).append(", ");
            sql.append(formatValue(asset.getAssetCateCode())).append(", ");
            sql.append(formatValue(asset.getAssetType())).append(", ");
            sql.append(formatValue(asset.getUserCode())).append(", ");
            sql.append(formatValue(AssetUseStatus.getByDesc(asset.getUseStatus()).getCode())).append(", ");
            sql.append(formatValue(CheckStatus.getByDesc(asset.getUseStatus()).getCode())).append(", ");
            sql.append(formatValue(asset.getSn())).append(", ");
            sql.append(formatValue(BusinessLine.HEA_FACTORY.getCode())).append(", ");
            sql.append(formatValue(SpecialCate.getByDesc(asset.getUseStatus()).getCode())).append(", ");
            sql.append(formatValue(asset.getCountry())).append(", ");
            sql.append(formatValue(asset.getUserName())).append(", ");
            sql.append(formatValue(userCodeMap.get(asset.getUserName()))).append(", ");
            sql.append(formatValue(asset.getUserDeptCode())).append(", ");
            sql.append(formatValue(deptCodeMap.get(asset.getUserDeptCode()))).append(", ");
            sql.append(formatValue(asset.getManageDeptCode())).append(", ");
            sql.append(formatValue(deptCodeMap.get(asset.getManageDeptCode()))).append(", ");
            sql.append(formatValue(asset.getCompanyCode())).append(", ");
            sql.append(formatValue(asset.getCompanyName())).append(", ");
            sql.append(asset.getQuantity() != null ? asset.getQuantity() : "1");

            sql.append(");");

            System.out.println(sql.toString());
        }
    }

    private String formatValue(String value) {
        return value != null ? "'" + value.replace("'", "''") + "'" : "";
    }

    @Data
    public static class Asset {
        //资产编码	资产名称	设备分类	资产类型	使用状态	验收状态	SN	业务线	专项分类	国家	资产责任人	使用部门	管理部门	公司	数量
        @ExcelProperty("资产编码")
        private String assetCode;

        @ExcelProperty("资产名称")
        private String assetName;

        @ExcelProperty("设备分类")
        private String assetCateCode;
        @ExcelIgnore()
        private String assetCateName;

        @ExcelProperty("资产类型")
        private String assetType;

        @ExcelProperty("使用状态")
        private String useStatus;

        @ExcelProperty("验收状态")
        private String checkStatus;

        @ExcelProperty("SN")
        private String sn;

        @ExcelProperty("业务线")
        private String businessLine;

        @ExcelProperty("专项分类")
        private String specialCate;

        @ExcelProperty("国家")
        private String country;

        @ExcelProperty("资产责任人")
        private String userCode;

        @ExcelIgnore()
        private String userName;

        @ExcelProperty("使用部门")
        private String userDeptCode;
        @ExcelIgnore()
        private String userDeptName;

        @ExcelProperty("管理部门")
        private String manageDeptCode;
        @ExcelIgnore()
        private String manageDeptName;

        @ExcelProperty("公司")
        private String companyCode;

        @ExcelIgnore()
        private String companyName;

        @ExcelProperty("数量")
        private Integer quantity;

    }
}
