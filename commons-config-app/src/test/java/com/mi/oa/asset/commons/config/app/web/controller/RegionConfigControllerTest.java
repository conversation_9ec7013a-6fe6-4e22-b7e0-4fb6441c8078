package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.regionconfig.EmployeeRegionCountryRes;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigProvider;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigReq;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigRes;
import com.mi.oa.asset.commons.config.app.ability.RegionConfigAbility;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xiaomi.mit.api.error.ErrorCodeException;

/**
 * RegionConfigController单元测试类
 */
@ExtendWith(MockitoExtension.class)
class RegionConfigControllerTest {

    @InjectMocks
    private RegionConfigController controller;

    @Mock
    private RegionConfigProvider provider;

    @Mock
    private RegionConfigAbility assetSkuAbility;
    /**
     * 测试delete方法 - 正常场景
     * 删除SKU，验证是否正确调用provider
     */
    @Test
    void listEmployeeRegionCountry() {
        // 准备数据
        RegionConfigRes req = new RegionConfigRes();
        req.setId(null);
        req.setRegionName("rn");
        req.setRegionEnglishName("ern");
        req.setRegionSortOrder(3);


        // 执行测试
        Result<List<EmployeeRegionCountryRes>> result = controller.listEmployeeRegionCountry();

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getRegionAndCountrysList();
    }

    @Test
    void listEmployeeRegionCountry_providerReturnsNull() {
        when(provider.getRegionAndCountrysList()).thenReturn(null);
        Result<List<EmployeeRegionCountryRes>> result = controller.listEmployeeRegionCountry();
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(null, result.getData());
    }

    @Test
    void listEmployeeRegionCountry_providerReturnsEmptyList() {
        when(provider.getRegionAndCountrysList()).thenReturn(Collections.emptyList());
        Result<List<EmployeeRegionCountryRes>> result = controller.listEmployeeRegionCountry();
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(0, result.getData().size());
    }

    @Test
    void listEmployeeRegionCountry_providerReturnsSingle() {
        EmployeeRegionCountryRes res = new EmployeeRegionCountryRes();
        when(provider.getRegionAndCountrysList()).thenReturn(Collections.singletonList(res));
        Result<List<EmployeeRegionCountryRes>> result = controller.listEmployeeRegionCountry();
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(1, result.getData().size());
    }

    @Test
    void detail() {
        Integer regionConfigId = 1;
        // 执行测试
        Result<EmployeeRegionCountryRes> result = controller.info(regionConfigId);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getRegionAndCountrys(regionConfigId);
    }

    @Test
    void detail_providerReturnsNull() {
        when(provider.getRegionAndCountrys(1)).thenReturn(null);
        Result<EmployeeRegionCountryRes> result = controller.info(1);
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(null, result.getData());
    }

    @Test
    void list() {
        // 执行测试
        Result<List<RegionConfigRes>> result = controller.list();
        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).getRegionConfigList();
    }

    @Test
    void list_providerThrowsException() {
        when(provider.getRegionConfigList()).thenThrow(new RuntimeException("mock异常3"));
        assertThrows(RuntimeException.class, () -> controller.list());
    }

    @Test
    void list_providerReturnsNull() {
        when(provider.getRegionConfigList()).thenReturn(null);
        Result<List<RegionConfigRes>> result = controller.list();
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(null, result.getData());
    }

    @Test
    void list_providerReturnsEmptyList() {
        when(provider.getRegionConfigList()).thenReturn(Collections.emptyList());
        Result<List<RegionConfigRes>> result = controller.list();
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals(0, result.getData().size());
    }

    @Test
    void save() {
        // 准备数据
        RegionConfigReq req = new RegionConfigReq();
        req.setId(null);
        req.setRegionName("rn");
        req.setRegionEnglishName("ern");
        req.setRegionSortOrder(3);


        // 执行测试
        Result<Void> result = controller.edit(req);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());

        // 验证调用
        verify(provider).saveOrUpdate(req);
    }

//    @Test
//    void edit_providerThrowsException() {
//        RegionConfigReq req = new RegionConfigReq();
//        req.setId(null);
//        req.setRegionName("rn");
//        req.setRegionEnglishName("ern");
//        req.setRegionSortOrder(3);
//        when(provider.saveOrUpdate(req)).thenThrow(new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请选择地区下的国家"));
//        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> controller.edit(req));
//        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getMessage());
//    }

//    @Test
//    void edit_nullReq() {
//        // 允许为null时的行为（如NPE或自定义异常）
//        assertThrows(NullPointerException.class, () -> controller.edit(null));
//    }

    @Test
    void delete() {
        // 准备数据
        RegionConfigReq req = new RegionConfigReq();
        req.setId(null);

        // 执行测试
        Result<Void> result = controller.delete(req);

        // 验证结果
        assertNotNull(result);
        // Result类没有isSuccess方法，直接验证其他属性
        assertEquals(0, result.getCode());
        // 验证调用
        verify(provider).removeByIds(Collections.singletonList(req.getId()));
    }

    @Test
    void delete_nullReq() {
        assertThrows(NullPointerException.class, () -> controller.delete(null));
    }



} 