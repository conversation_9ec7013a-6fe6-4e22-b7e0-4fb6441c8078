package com.mi.oa.asset.commons.config.app.ability;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.commons.config.api.datarange.DataRangeReq;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.Warehouse;
import com.mi.oa.asset.commons.config.infra.database.dataobject.OrganizationUnitPo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.WarehousePo;
import com.mi.oa.asset.commons.config.infra.database.mapper.OrganizationUnitPoMapper;
import com.mi.oa.asset.commons.config.infra.database.mapper.WarehouseMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AssetOrgRepoConverter;
import com.mi.oa.asset.commons.config.infra.repository.converter.WarehouseConverter;
import com.mi.oa.asset.commons.config.infra.repository.service.AssetOrgUnitService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.idm.api.IdmAccountV2Service;
import com.mi.oa.infra.oaucf.idm.api.IdmUserService;
import com.mi.oa.infra.oaucf.idm.api.enums.AccountStatusEnum;
import com.mi.oa.infra.oaucf.idm.api.rep.AccountInfoDto;
import com.mi.oa.infra.oaucf.idm.api.rep.Resp;
import com.mi.oa.infra.oaucf.idm.api.rep.UserInfoDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class DataRangeAbilityTest {

    @InjectMocks
    private DataRangeAbility dataRangeAbility;

    @Mock
    private AssetOrgUnitService orgUnitService;

    @Mock
    private AssetOrgRepoConverter orgConverter;

    @Mock
    private IdmAccountV2Service idmAccountV2Service;

    @Mock
    private IdmUserService idmUserService;

    @Mock
    private OrganizationUnitPoMapper organizationUnitPoMapper;

    @Mock
    private WarehouseMapper warehouseMapper;

    @Mock
    private WarehouseConverter warehouseConverter;

    @Mock
    private AssetOrgRepoConverter assetOrgRepoConverter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getOrgUnitByCode_WhenUseDeptCodeIsEmpty_ShouldReturnNull() {
        DataRangeReq req = new DataRangeReq();
        req.setUseDeptCode("");
        
        AssetOrgUnit result = dataRangeAbility.getOrgUnitByCode(req);
        
        assertNull(result);
    }

    @Test
    void getOrgUnitByCode_WhenOrgUnitExists_ShouldReturnOrgUnit() {
        DataRangeReq req = new DataRangeReq();
        req.setUseDeptCode("TEST_CODE");
        req.setBusinessLine("TEST_LINE");

        OrganizationUnitPo orgUnitPo = new OrganizationUnitPo();
        AssetOrgUnit expectedOrgUnit = AssetOrgUnit.builder()
                .orgCode("TEST_CODE")
                .orgName("Test Org")
                .build();

        when(orgUnitService.list(any())).thenReturn(Collections.singletonList(orgUnitPo));
        when(orgConverter.toAssetOrgUnit(orgUnitPo)).thenReturn(expectedOrgUnit);

        AssetOrgUnit result = dataRangeAbility.getOrgUnitByCode(req);

        assertNotNull(result);
        assertEquals(expectedOrgUnit, result);
    }

    @Test
    void getUserInfo_WhenAccountNotFound_ShouldReturnNull() {
        String userName = "testUser";
        when(idmAccountV2Service.getAccount(userName)).thenReturn(new BaseResp<>());

        UserInfoDto result = dataRangeAbility.getUserInfo(userName);

        assertNull(result);
    }

    @Test
    void getUserInfo_WhenAccountDisabled_ShouldReturnNull() {
        String userName = "testUser";
        AccountInfoDto accountInfo = new AccountInfoDto();
        accountInfo.setAccountStatus(AccountStatusEnum.DISABLE);
        
        BaseResp<AccountInfoDto> baseResp = new BaseResp<>();
        baseResp.setData(accountInfo);
        
        when(idmAccountV2Service.getAccount(userName)).thenReturn(baseResp);

        UserInfoDto result = dataRangeAbility.getUserInfo(userName);

        assertNull(result);
    }

    @Test
    void getHouse_WhenHouseCodeExists_ShouldReturnWarehouse() {
        String houseCode = "TEST_HOUSE";
        WarehousePo warehousePo = new WarehousePo();
        Warehouse expectedWarehouse = new Warehouse();

        when(warehouseMapper.selectList(any())).thenReturn(Collections.singletonList(warehousePo));
        when(warehouseConverter.toDo(warehousePo)).thenReturn(expectedWarehouse);

        Warehouse result = dataRangeAbility.getHouse(houseCode);

        assertNotNull(result);
        assertEquals(expectedWarehouse, result);
    }
}
