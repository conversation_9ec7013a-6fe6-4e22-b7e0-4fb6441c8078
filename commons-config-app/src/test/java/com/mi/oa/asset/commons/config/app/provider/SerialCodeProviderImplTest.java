package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.serialcode.RollingType;
import com.mi.oa.asset.commons.config.api.serialcode.SerialCodeProvider;
import com.mi.oa.asset.commons.config.app.StartApplication;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;


@Slf4j
@SpringBootTest(classes = StartApplication.class)
@Disabled
class SerialCodeProviderImplTest {
    @Resource
    private SerialCodeProvider serialCodeProvider;

    private ThreadPoolExecutor customThreadPool = new ThreadPoolExecutor(5, 10, 60,
            TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000000));


    @Test
    void genSerialCode() {
        String id = serialCodeProvider.genSerialCode("mi-", RollingType.YMD, 4);
        System.out.println("生成ID:" + id);
    }

    @Test
    void testGenSerialCodesNum() {

        String indexCode = "ASSET_CODE_GENERATE:adm_pub:6";

        // 启用异步并发处理，避免dubbo接口超时
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            String prefix = "TEST-" + (i % 3 + 1);
            int num = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                genSerialCodesNum(prefix, indexCode, num);
            }, customThreadPool);
            futureList.add(future);
        }
        // 等待线程全部执行完成处理返回
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        try {
            allFutures.get();
        } catch (InterruptedException | ExecutionException e) {
            // 重新中断当前线程
            Thread.currentThread().interrupt();
            log.info("batchGenSerialCodes error:{}", e);
            throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, "生成流水号失败");
        }
    }

    private static final String LOCK_KEY = "serial:%s:%s";
    private static final int LOCK_TIMEOUT = 6; // 锁超时时间
    private static final int WAIT_TIME = 3; // 等待时间
    private static final int MAX_ATTEMPTS = 10; // 最大重试次数

    @Resource
    private RedissonClient redissonClient;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void genSerialCodesNum(String prefix, String indexCode, int num) {
        String key = String.format(LOCK_KEY, prefix, indexCode);
        int attempts = 0;

        while (attempts < MAX_ATTEMPTS) {
            boolean isLockAcquired = tryLock(key, TimeUnit.SECONDS, WAIT_TIME, LOCK_TIMEOUT);
            if (isLockAcquired) {
                try {
                    // 执行业务逻辑
                    System.out.println(num + "获取锁，执行业务逻辑");
                    Thread.sleep(1000); // 模拟业务逻辑执行
                    System.out.println(num + "业务执行完成");
                } catch (InterruptedException e) {
                    e.printStackTrace();
                } finally {
                    // 确保释放锁
                    unlock(key);
                }
                break;
            } else {
                System.out.println(num + "等待锁");
                attempts++;
                try {
                    Thread.sleep(1500); // 等待一段时间后重试
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

            }
        }
        if (attempts >= MAX_ATTEMPTS) {
            System.out.println(num + "达到最大尝试次数，还未获取锁");
        }
    }

    public boolean tryLock(String lockKey, TimeUnit unit, long waitTime, long leaseTime) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (InterruptedException e) {
            log.error("tryLock:{}", e.getMessage(), e);
            return false;
        }
    }

    public void unlock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.isLocked()) {
                lock.unlock();
            }
        } catch (IllegalMonitorStateException e) {
            log.error("unlock:{}", e.getMessage(), e);
        }
    }
}