package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.assetquit.AssetQuitProvider;
import com.mi.oa.asset.commons.config.app.StartApplication;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(classes = StartApplication.class)
@Disabled
class AssetQuitProviderImplTest {

    @Resource
    private AssetQuitProvider assetQuitProvider;

    @Test
    void isQuitUser() {
        System.out.println("是否已离职和待离职状态：" + assetQuitProvider.isQuitUser("dengzhibin"));
    }
}