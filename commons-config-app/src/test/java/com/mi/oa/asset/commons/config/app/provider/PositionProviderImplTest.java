package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.position.PositionTreeRes;
import com.mi.oa.asset.commons.config.app.ability.PositionAbility;
import com.mi.oa.asset.commons.config.app.converter.PositionConverter;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.space.rep.SpaceBuildingVO;
import com.mi.oa.infra.oaucf.space.rep.SpaceFloorVO;
import com.mi.oa.infra.oaucf.space.rep.SpaceParkVO;
import com.mi.oa.infra.oaucf.space.service.SpaceParkService;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.mi.oa.asset.common.enums.EAMConstants.CHINESE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * PositionProviderImpl的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class PositionProviderImplTest {

    @InjectMocks
    private PositionProviderImpl positionProvider;

    @Mock
    private SpaceParkService spaceParkService;

    @Mock
    private PositionConverter positionConverter;

    @Mock
    private PositionAbility positionAbility;

    // 测试中使用的常量和共享变量
    private static final int CODE_SUCCESS = 0; // 模拟BaseResp.CODE_SUCCESS值
    private List<SpaceParkVO> mockParkList;
    private BaseResp<List<SpaceParkVO>> successResp;
    private BaseResp<List<SpaceParkVO>> failureResp;
    private List<PositionTreeRes> mockTreeResList;

    @BeforeEach
    void setUp() {
        // 设置成功响应
        successResp = new BaseResp<>();
        successResp.setCode(CODE_SUCCESS);

        // 设置失败响应
        failureResp = new BaseResp<>();
        failureResp.setCode(CODE_SUCCESS - 1); // 非成功状态码
        failureResp.setMessage("API调用失败");

        // 创建模拟园区数据
        mockParkList = createMockParkData();
        
        // 创建模拟树形结构返回数据
        mockTreeResList = new ArrayList<>();
        PositionTreeRes treeRes = PositionTreeRes.builder()
                .positionCode("P001")
                .positionName("测试园区")
                .subList(new ArrayList<>())
                .build();
        mockTreeResList.add(treeRes);
    }

    /**
     * 创建模拟的园区数据
     */
    private List<SpaceParkVO> createMockParkData() {
        List<SpaceParkVO> parkList = new ArrayList<>();

        // 创建园区
        SpaceParkVO park = new SpaceParkVO();
        park.setParkCode("P001");
        park.setParkName("测试园区");

        // 创建楼宇
        List<SpaceBuildingVO> buildings = new ArrayList<>();
        SpaceBuildingVO building = new SpaceBuildingVO();
        building.setBuildingCode("B001");
        building.setBuildingName("测试楼宇");

        // 创建楼层
        List<SpaceFloorVO> floors = new ArrayList<>();
        SpaceFloorVO floor = new SpaceFloorVO();
        floor.setFloorCode("F001");
        floor.setFloorName("测试楼层");

        // 组装数据结构
        floors.add(floor);
        building.setFloorList(floors);
        buildings.add(building);
        park.setBuildingList(buildings);
        parkList.add(park);

        return parkList;
    }

    /**
     * 测试stdPositionTree方法的正常流程 - 中文环境
     * 场景：API调用成功，语言为中文，使用converter直接转换
     */
    @Test
    void stdPositionTree_ChineseLanguage_UsesConverter() {
        // 准备数据
        successResp.setData(mockParkList);
        when(spaceParkService.getParkBuildingFloorTree()).thenReturn(successResp);
        when(positionConverter.stdParkToPositionTreeRes(mockParkList)).thenReturn(mockTreeResList);

        // 执行测试
        List<PositionTreeRes> result = positionProvider.stdPositionTree(CHINESE);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockTreeResList, result);
        
        // 验证方法调用
        verify(spaceParkService).getParkBuildingFloorTree();
        verify(positionConverter).stdParkToPositionTreeRes(mockParkList);
        verify(positionAbility, never()).listPositionTree(any()); // 不应调用positionAbility
    }

    /**
     * 测试stdPositionTree方法的正常流程 - 英文环境
     * 场景：API调用成功，语言为英文，使用positionAbility处理
     */
    @Test
    void stdPositionTree_EnglishLanguage_UsesPositionAbility() {
        // 准备数据
        String englishLanguage = "en-US";
        successResp.setData(mockParkList);
        when(spaceParkService.getParkBuildingFloorTree()).thenReturn(successResp);
        when(positionAbility.listPositionTree(mockParkList)).thenReturn(mockTreeResList);

        // 执行测试
        List<PositionTreeRes> result = positionProvider.stdPositionTree(englishLanguage);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockTreeResList, result);
        
        // 验证方法调用
        verify(spaceParkService).getParkBuildingFloorTree();
        verify(positionAbility).listPositionTree(mockParkList);
        verify(positionConverter, never()).stdParkToPositionTreeRes((SpaceParkVO) any()); // 不应调用converter
    }

    /**
     * 测试stdPositionTree方法处理API调用失败的情况
     * 场景：SpaceParkService返回错误状态码
     */
    @Test
    void stdPositionTree_ApiFailure_ThrowsException() {
        // 准备数据 - API调用失败
        when(spaceParkService.getParkBuildingFloorTree()).thenReturn(failureResp);

        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () ->
                positionProvider.stdPositionTree(CHINESE)
        );

        // 验证异常信息
        assertEquals(ErrorCodes.BAD_REQUEST, exception.getErrorCode());
        assertEquals("API调用失败", exception.getMessage());

        // 验证后续方法未被调用
        verify(positionConverter, never()).stdParkToPositionTreeRes((SpaceParkVO) any());
        verify(positionAbility, never()).listPositionTree(any());
    }

    /**
     * 测试stdPositionTree方法处理空数据的情况
     * 场景：API调用成功但返回空列表
     */
    @Test
    void stdPositionTree_EmptyData_HandlesGracefully() {
        // 准备数据 - 空列表
        successResp.setData(Collections.emptyList());
        when(spaceParkService.getParkBuildingFloorTree()).thenReturn(successResp);
        when(positionConverter.stdParkToPositionTreeRes(Collections.emptyList())).thenReturn(Collections.emptyList());

        // 执行测试
        List<PositionTreeRes> result = positionProvider.stdPositionTree(CHINESE);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试stdPositionTree方法处理空语言参数的情况
     * 场景：语言参数为null，应默认使用converter处理
     */
    @Test
    void stdPositionTree_NullLanguage_UsesConverter() {
        // 准备数据
        successResp.setData(mockParkList);
        when(spaceParkService.getParkBuildingFloorTree()).thenReturn(successResp);
        when(positionConverter.stdParkToPositionTreeRes(mockParkList)).thenReturn(mockTreeResList);

        // 执行测试
        List<PositionTreeRes> result = positionProvider.stdPositionTree(null);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockTreeResList, result);
        
        // 验证方法调用
        verify(spaceParkService).getParkBuildingFloorTree();
        verify(positionConverter).stdParkToPositionTreeRes(mockParkList);
        verify(positionAbility, never()).listPositionTree(any()); // 不应调用positionAbility
    }
} 