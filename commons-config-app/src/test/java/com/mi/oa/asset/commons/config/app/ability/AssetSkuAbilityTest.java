package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.account.api.assetaccount.AssetAccountProvider;
import com.mi.oa.asset.account.api.assetaccount.res.AssetAccountRes;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.serialcode.SerialCodeProvider;
import com.mi.oa.asset.commons.config.app.converter.AssetSkuConverter;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.AssetCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.repository.AssetCategoryRepo;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSku;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSkuManage;
import com.mi.oa.asset.commons.config.domain.assetsku.repository.AssetSkuRepo;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryConfigRepo;
import com.mi.oa.asset.commons.config.infra.repository.impl.AssetSkuManageRepoImpl;
import com.xiaomi.mit.api.error.ErrorCodeException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 已通过
 */
@ExtendWith(MockitoExtension.class)
class AssetSkuAbilityTest {

    @InjectMocks
    private AssetSkuAbility assetSkuAbility;

    @Mock
    private CountryConfigRepo countryConfigRepo;
    
    @Mock
    private SerialCodeProvider serialCodeProvider;
    
    @Mock
    private AssetCategoryAbility assetCategoryAbility;
    
    @Mock
    private AssetSkuRepo assetSkuRepo;
    
    @Mock
    private AssetAccountProvider assetAccountProvider;
    
    @Mock
    private AssetCategoryRepo assetCategoryRepo;
    
    @Mock
    private AssetSkuManageRepoImpl assetSkuManageRepo;
    
    @Mock
    private AssetSkuConverter converter;

    @Test
    @DisplayName("当国家代码有效时应返回null")
    void validateCountryCode_WhenCountryCodeValid_ShouldReturnNull() throws Exception {
        // 获取私有方法
        Method validateCountryCodeMethod = AssetSkuAbility.class.getDeclaredMethod("validateCountryCode", String.class, String.class);
        validateCountryCodeMethod.setAccessible(true);

        // 模拟有效的国家代码
        CountryConfigDo countryConfigDo = new CountryConfigDo();
        countryConfigDo.setCountryCodeAlphaThree("CHN");
        countryConfigDo.setCountryName("中国");

        List<CountryConfigDo> countryConfigs = Collections.singletonList(countryConfigDo);

        // 模拟repo行为 - 返回非空列表表示国家代码存在
        when(countryConfigRepo.getByThreeCode(Collections.singletonList("CHN"))).thenReturn(countryConfigs);

        // 执行测试
        String result = (String) validateCountryCodeMethod.invoke(assetSkuAbility, "CHN" , "en-US");

        // 验证结果
        assertNull(result);

        // 验证countryConfigRepo.getByThreeCode被调用
        verify(countryConfigRepo).getByThreeCode(Collections.singletonList("CHN"));
    }
    
    @Test
    @DisplayName("当国家代码为空时应返回错误信息")
    void validateCountryCode_WhenCountryCodeEmpty_ShouldReturnErrorMessage() throws Exception {
        // 获取私有方法
        Method validateCountryCodeMethod = AssetSkuAbility.class.getDeclaredMethod("validateCountryCode", String.class, String.class);
        validateCountryCodeMethod.setAccessible(true);

        // 执行测试 - 英文环境
        String resultEn = (String) validateCountryCodeMethod.invoke(assetSkuAbility, "", "en-US");
        
        // 执行测试 - 中文环境
        String resultZh = (String) validateCountryCodeMethod.invoke(assetSkuAbility, null, "zh-CN");

        // 验证结果
        assertEquals("Country code cannot be empty", resultEn);
        assertEquals("国家编码不能为空", resultZh);
        
        // 验证countryConfigRepo.getByThreeCode未被调用
        verify(countryConfigRepo, never()).getByThreeCode(anyList());
    }
    
    @Test
    @DisplayName("当国家代码无效时应返回错误信息")
    void validateCountryCode_WhenCountryCodeInvalid_ShouldReturnErrorMessage() throws Exception {
        // 获取私有方法
        Method validateCountryCodeMethod = AssetSkuAbility.class.getDeclaredMethod("validateCountryCode", String.class, String.class);
        validateCountryCodeMethod.setAccessible(true);

        // 模拟无效的国家代码 - 返回空列表
        when(countryConfigRepo.getByThreeCode(Collections.singletonList("XYZ"))).thenReturn(Collections.emptyList());

        // 执行测试 - 英文环境
        String resultEn = (String) validateCountryCodeMethod.invoke(assetSkuAbility, "XYZ", "en-US");
        
        // 执行测试 - 中文环境
        String resultZh = (String) validateCountryCodeMethod.invoke(assetSkuAbility, "XYZ", "zh-CN");

        // 验证结果
        assertEquals("Country code XYZ does not exist", resultEn);
        assertEquals("国家编码XYZ不存在", resultZh);
        
        // 验证countryConfigRepo.getByThreeCode被调用
        verify(countryConfigRepo, times(2)).getByThreeCode(Collections.singletonList("XYZ"));
    }

    
    @Test
    @DisplayName("生成单个SKU编码 - 已有编码")
    void genSkuCode_SingleSku_ExistingCode() {
        // 准备数据
        AssetSku assetSku = new AssetSku();
        assetSku.setSkuCode("EXISTING");
        
        // 执行测试
        assetSkuAbility.genSkuCode(assetSku);
        
        // 验证结果
        assertEquals("EXISTING", assetSku.getSkuCode());
        verify(serialCodeProvider, never()).genSerialCodeWithIndex(anyString(), anyInt(), anyString());
    }
    
    @Test
    @DisplayName("生成单个SKU编码 - 未配置前缀")
    void genSkuCode_SingleSku_NoPrefix() {
        // 准备数据
        AssetSku assetSku = new AssetSku();
        assetSku.setBusinessLine(null); // 未配置业务线

        verify(serialCodeProvider, never()).genSerialCodeWithIndex(anyString(), anyInt(), anyString());
    }


    
    @Test
    @DisplayName("检查参数 - 管理信息为空")
    void checkParams_EmptyManages() {
        // 准备数据
        AssetSku assetSku = new AssetSku();
        assetSku.setManages(Collections.emptyList());
        
        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            assetSkuAbility.checkParams(assetSku);
        });
        
        assertEquals("管理信息不能为空", exception.getMessage());
    }
    
    @Test
    @DisplayName("检查参数 - 管理信息业务线重复")
    void checkParams_DuplicateBusinessLine() {
        // 准备数据
        AssetSku assetSku = new AssetSku();
        List<AssetSkuManage> manages = new ArrayList<>();
        
        AssetSkuManage manage1 = new AssetSkuManage();
        manage1.setBusinessLine(BusinessLine.ADM_PUB);
        manages.add(manage1);
        
        AssetSkuManage manage2 = new AssetSkuManage();
        manage2.setBusinessLine(BusinessLine.ADM_PUB); // 重复的业务线
        manages.add(manage2);
        
        assetSku.setManages(manages);
        
        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            assetSkuAbility.checkParams(assetSku);
        });
        
        assertEquals("管理信息业务线不允许重复", exception.getMessage());
    }
    
    @Test
    @DisplayName("检查参数 - 更新场景SKU不存在")
    void checkParams_UpdateNonExistingSku() {
        // 准备数据
        AssetSku assetSku = new AssetSku();
        assetSku.setSkuId(1);
        List<AssetSkuManage> manages = new ArrayList<>();
        
        AssetSkuManage manage = new AssetSkuManage();
        manage.setBusinessLine(BusinessLine.ADM_PUB);
        manages.add(manage);
        
        assetSku.setManages(manages);
        
        // 模拟依赖
        when(assetSkuRepo.getAssetSku(1)).thenReturn(null);
        
        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            assetSkuAbility.checkParams(assetSku);
        });
        
        assertEquals("无效参数", exception.getMessage());
        verify(assetSkuRepo).getAssetSku(1);
    }


    @Test
    @DisplayName("检查参数 - 成功场景")
    void checkParams_Success() {
        // 准备数据
        AssetSku assetSku = new AssetSku();
        assetSku.setSkuCode("NEW_CODE");
        List<AssetSkuManage> manages = new ArrayList<>();
        
        AssetSkuManage manage1 = new AssetSkuManage();
        manage1.setBusinessLine(BusinessLine.ADM_PUB);
        manages.add(manage1);
        
        AssetSkuManage manage2 = new AssetSkuManage();
        manage2.setBusinessLine(BusinessLine.ADM_EMP);
        manages.add(manage2);
        
        assetSku.setManages(manages);
        
        // 模拟依赖
        when(assetSkuRepo.isExists("NEW_CODE")).thenReturn(false);
        
        // 执行测试 - 不应抛出异常
        assertDoesNotThrow(() -> {
            assetSkuAbility.checkParams(assetSku);
        });
        
        verify(assetSkuRepo).isExists("NEW_CODE");
    }
    
    @Test
    @DisplayName("加载资产分类管理信息 - 成功场景")
    void loadAssetCategoryManage_Success() {
        // 准备数据
        AssetSku assetSku = new AssetSku();
        List<AssetSkuManage> manages = new ArrayList<>();
        
        AssetSkuManage manage1 = new AssetSkuManage();
        manage1.setCateId(1);
        manages.add(manage1);
        
        AssetSkuManage manage2 = new AssetSkuManage();
        manage2.setCateId(2);
        manages.add(manage2);
        
        assetSku.setManages(manages);
        
        List<AssetCategory> categories = new ArrayList<>();
        AssetCategory category1 = AssetCategory.builder().build();
        category1.setCateId(1);
        category1.setCateCode("CATE001");
        category1.setCateName("分类1");
        categories.add(category1);
        
        AssetCategory category2 = AssetCategory.builder().build();
        category2.setCateId(2);
        category2.setCateCode("CATE002");
        category2.setCateName("分类2");
        categories.add(category2);
        
        // 模拟依赖
        when(assetCategoryAbility.loadAssetCategorys(Arrays.asList(1, 2))).thenReturn(categories);
        
        // 执行测试
        assetSkuAbility.loadAssetCategoryManage(assetSku);
        
        // 验证结果
        assertEquals(1, assetSku.getCateId());
        assertEquals("CATE001", assetSku.getCateCode());
        assertEquals("分类1", assetSku.getCateName());
        
        assertEquals(1, assetSku.getManages().get(0).getCateId());
        assertEquals("CATE001", assetSku.getManages().get(0).getCateCode());
        assertEquals("分类1", assetSku.getManages().get(0).getCateName());
        
        assertEquals(2, assetSku.getManages().get(1).getCateId());
        assertEquals("CATE002", assetSku.getManages().get(1).getCateCode());
        assertEquals("分类2", assetSku.getManages().get(1).getCateName());
        
        verify(assetCategoryAbility).loadAssetCategorys(Arrays.asList(1, 2));
    }
    
    @Test
    @DisplayName("加载资产分类管理信息 - 分类不存在")
    void loadAssetCategoryManage_CategoryNotFound() {
        // 准备数据
        AssetSku assetSku = new AssetSku();
        List<AssetSkuManage> manages = new ArrayList<>();
        
        AssetSkuManage manage = new AssetSkuManage();
        manage.setCateId(1);
        manages.add(manage);
        
        assetSku.setManages(manages);
        
        // 模拟依赖 - 返回空列表表示分类不存在
        when(assetCategoryAbility.loadAssetCategorys(Collections.singletonList(1))).thenReturn(Collections.emptyList());
        
        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            assetSkuAbility.loadAssetCategoryManage(assetSku);
        });
        
        assertEquals("管理信息中分类不存在:1", exception.getMessage());
        verify(assetCategoryAbility).loadAssetCategorys(Collections.singletonList(1));
    }
    
    @Test
    @DisplayName("检查是否有SKU")
    void hasSku() {
        // 准备数据
        List<Integer> cateIds = Arrays.asList(1, 2, 3);
        
        // 模拟依赖
        when(assetSkuRepo.getAssetSkuCountsByCategory(cateIds)).thenReturn(5L);
        
        // 执行测试
        boolean result = assetSkuAbility.hasSku(cateIds);
        
        // 验证结果
        assertTrue(result);
        verify(assetSkuRepo).getAssetSkuCountsByCategory(cateIds);
    }
    
    @Test
    @DisplayName("检查是否有SKU - 无SKU")
    void hasSku_NoSku() {
        // 准备数据
        List<Integer> cateIds = Arrays.asList(1, 2, 3);
        
        // 模拟依赖
        when(assetSkuRepo.getAssetSkuCountsByCategory(cateIds)).thenReturn(0L);
        
        // 执行测试
        boolean result = assetSkuAbility.hasSku(cateIds);
        
        // 验证结果
        assertFalse(result);
        verify(assetSkuRepo).getAssetSkuCountsByCategory(cateIds);
    }
    
    @Test
    @DisplayName("检查是否可以删除 - 有关联资产台账")
    void checkCanDelete_WithAssetAccounts() {
        // 准备数据
        AssetSku sku = new AssetSku();
        sku.setSkuCode("SKU001");
        sku.setBusinessLine(BusinessLine.ADM_PUB);
        
        List<AssetAccountRes> assetAccounts = new ArrayList<>();
        assetAccounts.add(new AssetAccountRes());
        
        // 模拟依赖
        when(assetAccountProvider.getBySkuCode("SKU001", BusinessLine.ADM_PUB.getCode())).thenReturn(assetAccounts);
        
        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            assetSkuAbility.checkCanDelete(sku);
        });
        
        assertEquals("请先移除物料数据中关联的资产台账", exception.getMessage());
        verify(assetAccountProvider).getBySkuCode("SKU001", BusinessLine.ADM_PUB.getCode());
    }
    
    @Test
    @DisplayName("检查是否可以删除 - 无关联资产台账")
    void checkCanDelete_NoAssetAccounts() {
        // 准备数据
        AssetSku sku = new AssetSku();
        sku.setSkuCode("SKU001");
        sku.setBusinessLine(BusinessLine.ADM_PUB);
        
        // 模拟依赖
        when(assetAccountProvider.getBySkuCode("SKU001", BusinessLine.ADM_PUB.getCode())).thenReturn(Collections.emptyList());
        
        // 执行测试 - 不应抛出异常
        assertDoesNotThrow(() -> {
            assetSkuAbility.checkCanDelete(sku);
        });
        
        verify(assetAccountProvider).getBySkuCode("SKU001", BusinessLine.ADM_PUB.getCode());
    }
    
    @Test
    @DisplayName("处理SKU信息 - 匹配业务线")
    void handleSkuInfo_MatchingBusinessLine() {
        // 准备数据
        List<AssetSku> assetSkuList = new ArrayList<>();
        AssetSku sku = new AssetSku();
        sku.setSkuId(1);
        assetSkuList.add(sku);

        List<String> businessLineCodes = Arrays.asList("adm_pub", "adm_it");

        List<AssetSkuManage> manages = new ArrayList<>();
        AssetSkuManage manage1 = new AssetSkuManage();
        manage1.setCateId(100);
        manage1.setCateCode("CATE001");
        manage1.setCateName("分类1");
        BusinessLine mockBusinessLine1 = mock(BusinessLine.class);
        when(mockBusinessLine1.getCode()).thenReturn("adm_pub");
        manage1.setBusinessLine(mockBusinessLine1);
        manages.add(manage1);

        AssetSkuManage manage2 = new AssetSkuManage();
        manage2.setCateId(200);
        manage2.setCateCode("CATE002");
        manage2.setCateName("分类2");
        BusinessLine mockBusinessLine2 = mock(BusinessLine.class);
        when(mockBusinessLine2.getCode()).thenReturn("adm_it");
        manage2.setBusinessLine(mockBusinessLine2);
        manages.add(manage2);

        Map<Integer, List<AssetSkuManage>> manageMap = new HashMap<>();
        manageMap.put(1, manages);

        // 模拟依赖
        when(assetSkuManageRepo.getAssetSkusManages(Collections.singletonList(1))).thenReturn(manageMap);

        // 执行测试
        assetSkuAbility.handleSkuInfo(assetSkuList, businessLineCodes);

        // 验证结果
        assertEquals(100, sku.getCateId());
        assertEquals("CATE001", sku.getCateCode());
        assertEquals("分类1", sku.getCateName());
        assertEquals(mockBusinessLine1, sku.getBusinessLine());
        assertEquals(2, sku.getBusinessLines().size());
        assertTrue(sku.getBusinessLines().contains(mockBusinessLine1));
        assertTrue(sku.getBusinessLines().contains(mockBusinessLine2));

        verify(assetSkuManageRepo).getAssetSkusManages(Collections.singletonList(1));
    }

    @Test
    @DisplayName("处理SKU信息 - 无匹配业务线")
    void handleSkuInfo_NoMatchingBusinessLine() {
        // 准备数据
        List<AssetSku> assetSkuList = new ArrayList<>();
        AssetSku sku = new AssetSku();
        sku.setSkuId(1);
        assetSkuList.add(sku);

        List<String> businessLineCodes = Arrays.asList("adm_pub", "adm_it");

        List<AssetSkuManage> manages = new ArrayList<>();
        AssetSkuManage manage = new AssetSkuManage();
        manage.setCateId(100);
        manage.setCateCode("CATE001");
        manage.setCateName("分类1");
        BusinessLine mockBusinessLine = mock(BusinessLine.class);
        when(mockBusinessLine.getCode()).thenReturn("other_line");
        manage.setBusinessLine(mockBusinessLine);
        manages.add(manage);

        Map<Integer, List<AssetSkuManage>> manageMap = new HashMap<>();
        manageMap.put(1, manages);

        // 模拟依赖
        when(assetSkuManageRepo.getAssetSkusManages(Collections.singletonList(1))).thenReturn(manageMap);

        // 执行测试
        assetSkuAbility.handleSkuInfo(assetSkuList, businessLineCodes);

        // 验证结果
        assertNull(sku.getCateId());
        assertNull(sku.getCateCode());
        assertNull(sku.getCateName());
        assertNull(sku.getBusinessLine());
        assertEquals(0, sku.getBusinessLines().size());

        verify(assetSkuManageRepo).getAssetSkusManages(Collections.singletonList(1));
    }

    @Test
    @DisplayName("设置管理信息的SKU ID")
    void setManageIds_ShouldSetSkuIdForManages() {
        // 准备数据
        List<AssetSku> assetSkus = new ArrayList<>();
        
        AssetSku sku1 = new AssetSku();
        sku1.setSkuId(1);
        sku1.setSkuCode("SKU001");
        List<AssetSkuManage> manages1 = new ArrayList<>();
        AssetSkuManage manage1 = new AssetSkuManage();
        manages1.add(manage1);
        sku1.setManages(manages1);
        assetSkus.add(sku1);
        
        AssetSku sku2 = new AssetSku();
        sku2.setSkuId(2);
        sku2.setSkuCode("SKU002");
        List<AssetSkuManage> manages2 = new ArrayList<>();
        AssetSkuManage manage2 = new AssetSkuManage();
        manages2.add(manage2);
        sku2.setManages(manages2);
        assetSkus.add(sku2);
        
        // 执行测试
        List<AssetSkuManage> result = assetSkuAbility.setManageIds(assetSkus);
        
        // 验证结果
        assertEquals(2, result.size());
        assertEquals(1, result.get(0).getSkuId());
        assertEquals(2, result.get(1).getSkuId());
    }





    @Test
    @DisplayName("加载交期天数 - 正常情况")
    void loadDeliveryDays_ShouldSetDeliveryDays() {
        // 准备数据
        List<String> businessLineCodes = Collections.singletonList("adm_pub");
        List<Integer> cateIds = Arrays.asList(1, 2, 3);
        
        List<AssetSku> assetSkus = new ArrayList<>();
        AssetSku sku1 = new AssetSku();
        sku1.setSkuId(1);
        sku1.setCateId(1);
        assetSkus.add(sku1);
        
        AssetSku sku2 = new AssetSku();
        sku2.setSkuId(2);
        sku2.setCateId(2);
        assetSkus.add(sku2);
        
        List<AssetCategory> categories = new ArrayList<>();
        AssetCategory category1 = AssetCategory.builder().build();
        category1.setCateId(1);
        category1.setDeliveryDays(10);
        categories.add(category1);
        
        AssetCategory category2 = AssetCategory.builder().build();
        category2.setCateId(2);
        category2.setDeliveryDays(20);
        categories.add(category2);
        
        // 模拟依赖
        when(assetCategoryRepo.getAssetCategories(cateIds)).thenReturn(categories);
        
        // 执行测试
        assetSkuAbility.loadDeliveryDays(businessLineCodes, cateIds, assetSkus);
        
        // 验证结果
        assertEquals(10, sku1.getDeliveryDays());
        assertEquals(20, sku2.getDeliveryDays());
        verify(assetCategoryRepo).getAssetCategories(cateIds);
    }
    
    @Test
    @DisplayName("加载交期天数 - 多个业务线")
    void loadDeliveryDays_MultipleBusinessLines_ShouldNotSetDeliveryDays() {
        // 准备数据
        List<String> businessLineCodes = Arrays.asList("adm_pub", "adm_it");
        List<Integer> cateIds = Arrays.asList(1, 2, 3);
        
        List<AssetSku> assetSkus = new ArrayList<>();
        AssetSku sku = new AssetSku();
        sku.setSkuId(1);
        sku.setCateId(1);
        assetSkus.add(sku);
        
        // 执行测试
        assetSkuAbility.loadDeliveryDays(businessLineCodes, cateIds, assetSkus);
        
        // 验证结果
        assertNull(sku.getDeliveryDays());
        verify(assetCategoryRepo, never()).getAssetCategories(anyList());
    }
    
    @Test
    @DisplayName("加载交期天数 - 空分类ID列表")
    void loadDeliveryDays_EmptyCateIds_ShouldNotSetDeliveryDays() {
        // 准备数据
        List<String> businessLineCodes = Collections.singletonList("adm_pub");
        List<Integer> cateIds = Collections.emptyList();
        
        List<AssetSku> assetSkus = new ArrayList<>();
        AssetSku sku = new AssetSku();
        sku.setSkuId(1);
        sku.setCateId(1);
        assetSkus.add(sku);
        
        // 执行测试
        assetSkuAbility.loadDeliveryDays(businessLineCodes, cateIds, assetSkus);
        
        // 验证结果
        assertNull(sku.getDeliveryDays());
        verify(assetCategoryRepo, never()).getAssetCategories(anyList());
    }
} 