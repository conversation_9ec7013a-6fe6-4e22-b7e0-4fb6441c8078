package com.mi.oa.asset.commons.config.app.provider;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigReq;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigRes;
import com.mi.oa.asset.commons.config.app.ability.CountryConfigAbility;
import com.mi.oa.asset.commons.config.app.converter.CountryConfigConverter;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryConfigRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CountryConfigProviderImplTest {
    @InjectMocks
    private CountryConfigProviderImpl provider;
    @Mock
    private CountryConfigRepo repo;
    @Mock
    private CountryConfigConverter converter;
    @Mock
    private CountryConfigAbility ability;

    @Test
    void getCountryConfigList_normal() {
        List<CountryConfigDo> doList = Collections.singletonList(new CountryConfigDo());
        List<CountryConfigRes> resList = Collections.singletonList(new CountryConfigRes());
        when(repo.searchAll()).thenReturn(doList);
        when(converter.listDoToRes(doList)).thenReturn(resList);
        when(ability.getCountryConfigList(resList)).thenReturn(resList);
        List<CountryConfigRes> result = provider.getCountryConfigList();
        assertEquals(1, result.size());
    }

    @Test
    void getByRegionId_normal() {
        List<CountryConfigDo> doList = Collections.singletonList(new CountryConfigDo());
        List<CountryConfigRes> resList = Collections.singletonList(new CountryConfigRes());
        when(repo.getByRegionId(1)).thenReturn(doList);
        when(converter.listDoToRes(doList)).thenReturn(resList);
        assertEquals(resList, provider.getByRegionId(1));
    }

    @Test
    void getAllCountries_normal() {
        List<CountryConfigDo> doList = Collections.singletonList(new CountryConfigDo());
        List<CountryConfigRes> resList = Collections.singletonList(new CountryConfigRes());
        when(repo.searchAll()).thenReturn(doList);
        when(converter.listDoToRes(doList)).thenReturn(resList);
        assertEquals(resList, provider.getAllCountries());
    }

    @Test
    void getAllCountries_empty() {
        when(repo.searchAll()).thenReturn(Collections.emptyList());
        assertEquals(Collections.emptyList(), provider.getAllCountries());
    }

    @Test
    void getByThreeCode_normal() {
        CountryConfigDo do1 = new CountryConfigDo();
        CountryConfigRes res = new CountryConfigRes();
        when(repo.getByCountryCodeAlphaThree("CHN")).thenReturn(do1);
        when(converter.doToRes(do1)).thenReturn(res);
        when(ability.getById(res)).thenReturn(res);
        assertEquals(res, provider.getByThreeCode("CHN"));
    }

    @Test
    void getByThreeCode_nullParam() {
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> provider.getByThreeCode(null));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
    }

    @Test
    void getByThreeCode_notFound() {
        when(repo.getByCountryCodeAlphaThree("AAA")).thenReturn(null);
        assertNull(provider.getByThreeCode("AAA"));
    }

    @Test
    void getById_normal() {
        CountryConfigDo do1 = new CountryConfigDo();
        CountryConfigRes res = new CountryConfigRes();
        when(repo.getById(1)).thenReturn(do1);
        when(converter.doToRes(do1)).thenReturn(res);
        when(ability.getById(res)).thenReturn(res);
        assertEquals(res, provider.getById(1));
    }

    @Test
    void getById_notFound() {
        when(repo.getById(1)).thenReturn(null);
        assertNull(provider.getById(1));
    }

    @Test
    void saveOrUpdate_normal() {
        CountryConfigReq req = new CountryConfigReq();
        doNothing().when(ability).saveOrUpdate(req);
        assertDoesNotThrow(() -> provider.saveOrUpdate(req));
    }

    @Test
    void removeByIds_normal() {
        doNothing().when(ability).deleteByIds(anyList());
        assertDoesNotThrow(() -> provider.removeByIds(Arrays.asList(1,2)));
    }

    @Test
    void removeByIds_empty() {
        ErrorCodeException ex = assertThrows(ErrorCodeException.class, () -> provider.removeByIds(Collections.emptyList()));
        assertEquals(ErrorCodes.BAD_PARAMETER, ex.getErrorCode());
    }
} 