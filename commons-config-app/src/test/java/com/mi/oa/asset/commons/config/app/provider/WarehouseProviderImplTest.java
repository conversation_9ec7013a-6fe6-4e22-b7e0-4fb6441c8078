package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.warehouse.SaveWarehouseReq;
import com.mi.oa.asset.commons.config.api.warehouse.WarehouseRes;
import com.mi.oa.asset.commons.config.app.ability.AddressAbility;
import com.mi.oa.asset.commons.config.app.ability.WarehouseAbility;
import com.mi.oa.asset.commons.config.app.converter.AppWarehouseConverter;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.Warehouse;
import com.mi.oa.asset.commons.config.domain.warehouse.repository.WarehouseRepo;
import com.mi.oa.asset.commons.config.domain.warehouse.valobj.WarehouseData;
import com.mi.oa.asset.eam.utils.ExcelUtils;
import com.mi.oa.asset.excel.enums.ExcelLanguageContextlEnum;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @Date 2024/9/3 19:45
 */
@ExtendWith(MockitoExtension.class)
class WarehouseProviderImplTest {

    @InjectMocks
    private WarehouseProviderImpl warehouseProvider;

    @Mock
    private AppWarehouseConverter converter;

    @Mock
    private WarehouseRepo warehouseRepo;

    @Mock
    private AddressAbility addressAbility;

    @Mock
    private WarehouseAbility warehouseAbility;

    @Mock
    private MultipartFile mockFile;

    @Test
    @DisplayName("保存仓库 - 新增场景 - 成功")
    void saveWarehouse_Create_ShouldSaveWarehouse() {
        // 准备数据
        SaveWarehouseReq req = new SaveWarehouseReq();
        req.setHouseCode("W001");
        
        Warehouse warehouse = new Warehouse();
        warehouse.setId(1);
        
        // 模拟依赖
        when(converter.toWarehouse(req)).thenReturn(warehouse);
        when(warehouseRepo.findByCode("W001")).thenReturn(null);
        
        // 执行测试
        Integer result = warehouseProvider.saveWarehouse(req);
        
        // 验证结果
        assertEquals(1, result);
        verify(warehouseRepo).saveWarehouse(warehouse);
    }
    
    @Test
    @DisplayName("保存仓库 - 更新场景 - 成功")
    void saveWarehouse_Update_ShouldUpdateWarehouse() {
        // 准备数据
        SaveWarehouseReq req = new SaveWarehouseReq();
        req.setId(1);
        req.setHouseCode("W001");
        
        Warehouse warehouse = new Warehouse();
        warehouse.setId(1);
        
        Warehouse existingWarehouse = new Warehouse();
        existingWarehouse.setId(1);
        
        // 模拟依赖
        when(converter.toWarehouse(req)).thenReturn(warehouse);
        when(warehouseRepo.findByCode("W001")).thenReturn(existingWarehouse);
        
        // 执行测试
        Integer result = warehouseProvider.saveWarehouse(req);
        
        // 验证结果
        assertEquals(1, result);
        verify(warehouseRepo).saveWarehouse(warehouse);
    }
    
    @Test
    @DisplayName("保存仓库 - 更新场景 - 仓库编码已存在但ID不同")
    void saveWarehouse_Update_WithDuplicateCode_ShouldThrowException() {
        // 准备数据
        SaveWarehouseReq req = new SaveWarehouseReq();
        req.setId(1);
        req.setHouseCode("W001");
        
        Warehouse warehouse = new Warehouse();
        warehouse.setId(1);
        
        Warehouse existingWarehouse = new Warehouse();
        existingWarehouse.setId(2); // 不同的ID
        
        // 模拟依赖
        when(converter.toWarehouse(req)).thenReturn(warehouse);
        when(warehouseRepo.findByCode("W001")).thenReturn(existingWarehouse);
        
        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            warehouseProvider.saveWarehouse(req);
        });
        
        // 验证结果
        assertEquals(ErrorCodes.BAD_PARAMETER, exception.getErrorCode());
        assertEquals("仓库编码不能重复", exception.getMessage());
    }
    
    @Test
    @DisplayName("保存仓库 - 新增场景 - 仓库编码已存在")
    void saveWarehouse_Create_WithDuplicateCode_ShouldThrowException() {
        // 准备数据
        SaveWarehouseReq req = new SaveWarehouseReq();
        req.setHouseCode("W001");
        
        Warehouse warehouse = new Warehouse();
        
        Warehouse existingWarehouse = new Warehouse();
        existingWarehouse.setId(1);
        
        // 模拟依赖
        when(converter.toWarehouse(req)).thenReturn(warehouse);
        when(warehouseRepo.findByCode("W001")).thenReturn(existingWarehouse);
        
        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            warehouseProvider.saveWarehouse(req);
        });
        
        // 验证结果
        assertEquals(ErrorCodes.BAD_PARAMETER, exception.getErrorCode());
        assertEquals("仓库编码不能重复", exception.getMessage());
    }
    
    @Test
    @DisplayName("保存仓库 - 仓库编码为空")
    void saveWarehouse_EmptyCode_ShouldThrowException() {
        // 准备数据
        SaveWarehouseReq req = new SaveWarehouseReq();
        req.setHouseCode("");
        
        Warehouse warehouse = new Warehouse();
        
        // 模拟依赖
        when(converter.toWarehouse(req)).thenReturn(warehouse);
        
        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            warehouseProvider.saveWarehouse(req);
        });
        
        // 验证结果
        assertEquals(ErrorCodes.BAD_PARAMETER, exception.getErrorCode());
        assertEquals("仓库编码不能为空", exception.getMessage());
    }
    
    @Test
    @DisplayName("删除仓库 - 成功")
    void deleteWarehouse_ShouldDeleteWarehouse() {
        // 准备数据
        List<Integer> ids = Arrays.asList(1, 2, 3);
        
        // 执行测试
        warehouseProvider.deleteWarehouse(ids);
        
        // 验证结果
        verify(warehouseRepo).deleteWarehouse(ids);
    }
    
    @Test
    @DisplayName("删除仓库 - 空ID列表")
    void deleteWarehouse_EmptyIds_ShouldNotCallRepo() {
        // 准备数据
        List<Integer> emptyIds = Collections.emptyList();
        
        // 执行测试
        warehouseProvider.deleteWarehouse(emptyIds);
        
        // 验证结果
        verify(warehouseRepo, never()).deleteWarehouse(anyList());
    }
    
    @Test
    @DisplayName("根据编码查询仓库 - 找到仓库")
    void findByCode_FoundWarehouse_ShouldReturnWarehouseRes() {
        // 准备数据
        String code = "W001";
        Warehouse warehouse = new Warehouse();
        warehouse.setId(1);
        
        List<Warehouse> warehouseList = Collections.singletonList(warehouse);
        
        WarehouseRes warehouseRes = new WarehouseRes();
        warehouseRes.setId(1);
        
        // 模拟依赖
        when(warehouseRepo.getByCode(code)).thenReturn(warehouseList);
        when(converter.toWarehouseRes(warehouse)).thenReturn(warehouseRes);
        
        // 执行测试
        WarehouseRes result = warehouseProvider.findByCode(code);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getId());
        verify(warehouseRepo).getByCode(code);
        verify(converter).toWarehouseRes(warehouse);
    }
    
    @Test
    @DisplayName("根据编码查询仓库 - 未找到仓库")
    void findByCode_NotFoundWarehouse_ShouldReturnNull() {
        // 准备数据
        String code = "W001";
        
        // 模拟依赖
        when(warehouseRepo.getByCode(code)).thenReturn(Collections.emptyList());
        
        // 执行测试
        WarehouseRes result = warehouseProvider.findByCode(code);
        
        // 验证结果
        assertNull(result);
        verify(warehouseRepo).getByCode(code);
        verify(converter, never()).toWarehouseRes(any());
    }
    
    @Test
    @DisplayName("根据编码列表查询仓库 - 找到仓库")
    void findByCodes_FoundWarehouses_ShouldReturnWarehouseResList() {
        // 准备数据
        List<String> codes = Arrays.asList("W001", "W002");
        
        List<Warehouse> warehouseList = new ArrayList<>();
        Warehouse warehouse1 = new Warehouse();
        warehouse1.setId(1);
        warehouseList.add(warehouse1);
        
        List<WarehouseRes> warehouseResList = new ArrayList<>();
        WarehouseRes warehouseRes1 = new WarehouseRes();
        warehouseRes1.setId(1);
        warehouseResList.add(warehouseRes1);
        
        // 模拟依赖
        when(warehouseRepo.findByCodes(codes)).thenReturn(warehouseList);
        when(converter.toWarehouseResList(warehouseList)).thenReturn(warehouseResList);
        
        // 执行测试
        List<WarehouseRes> result = warehouseProvider.findByCodes(codes);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getId());
        verify(warehouseRepo).findByCodes(codes);
        verify(converter).toWarehouseResList(warehouseList);
    }
    
    @Test
    @DisplayName("根据编码列表查询仓库 - 空编码列表")
    void findByCodes_EmptyCodes_ShouldReturnEmptyList() {
        // 准备数据
        List<String> emptyCodes = Collections.emptyList();
        
        // 执行测试
        List<WarehouseRes> result = warehouseProvider.findByCodes(emptyCodes);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(warehouseRepo, never()).findByCodes(anyList());
        verify(converter, never()).toWarehouseResList(anyList());
    }
    
    @Test
    @DisplayName("根据业务线和服务类型查询仓库 - 成功")
    void getBatchByServiceType_ShouldReturnWarehouseResList() {
        // 准备数据
        String businessLine = "adm_pub";
        String serviceType = "service1";
        String key = "keyword";
        
        List<Warehouse> warehouseList = new ArrayList<>();
        Warehouse warehouse = new Warehouse();
        warehouse.setId(1);
        warehouseList.add(warehouse);
        
        List<WarehouseRes> warehouseResList = new ArrayList<>();
        WarehouseRes warehouseRes = new WarehouseRes();
        warehouseRes.setId(1);
        warehouseResList.add(warehouseRes);
        
        // 模拟依赖
        when(warehouseRepo.getBatchByServiceType(businessLine, serviceType, key)).thenReturn(warehouseList);
        when(converter.toWarehouseResList(warehouseList)).thenReturn(warehouseResList);
        
        // 执行测试
        List<WarehouseRes> result = warehouseProvider.getBatchByServiceType(businessLine, serviceType, key);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getId());
        verify(warehouseRepo).getBatchByServiceType(businessLine, serviceType, key);
        verify(converter).toWarehouseResList(warehouseList);
    }
    
    @Test
    @DisplayName("根据业务线和服务类型查询仓库 - 业务线为空")
    void getBatchByServiceType_EmptyBusinessLine_ShouldThrowException() {
        // 准备数据
        String businessLine = "";
        String serviceType = "service1";
        String key = "keyword";
        
        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            warehouseProvider.getBatchByServiceType(businessLine, serviceType, key);
        });
        
        // 验证结果
        assertEquals(ErrorCodes.BAD_PARAMETER, exception.getErrorCode());
        assertEquals("查询参数必填", exception.getMessage());
    }
    
    @Test
    @DisplayName("根据业务线和服务类型查询仓库 - 服务类型为空")
    void getBatchByServiceType_EmptyServiceType_ShouldThrowException() {
        // 准备数据
        String businessLine = "adm_pub";
        String serviceType = "";
        String key = "keyword";
        
        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            warehouseProvider.getBatchByServiceType(businessLine, serviceType, key);
        });
        
        // 验证结果
        assertEquals(ErrorCodes.BAD_PARAMETER, exception.getErrorCode());
        assertEquals("查询参数必填", exception.getMessage());
    }
    
    @Test
    @DisplayName("导入仓库 - 成功")
    void importWarehouse_ShouldImportWarehouse() throws Exception {
        // 准备数据
        InputStream inputStream = mock(InputStream.class);
        
        List<WarehouseData> warehouseDataList = new ArrayList<>();
        WarehouseData warehouseData = new WarehouseData();
        warehouseData.setArea("北京市");
        warehouseDataList.add(warehouseData);
        
        List<Warehouse> warehouseList = new ArrayList<>();
        Warehouse warehouse = new Warehouse();
        warehouse.setArea("北京市");
        warehouseList.add(warehouse);
        
        Map<String, String> addressMap = new HashMap<>();
        addressMap.put("北京市", "110000");
        
        // 模拟依赖
        try (MockedStatic<ExcelUtils> excelUtilsMock = mockStatic(ExcelUtils.class)) {
            when(mockFile.getInputStream()).thenReturn(inputStream);
            excelUtilsMock.when(() -> ExcelUtils.readExcel(eq(inputStream), eq(WarehouseData.class), eq(2)))
                    .thenReturn(warehouseDataList);
            when(converter.excelToHouses(warehouseDataList, ExcelLanguageContextlEnum.current()))
                    .thenReturn(warehouseList);
            when(addressAbility.getAreaCodeMapByAddress(anyList())).thenReturn(addressMap);
            
            // 执行测试
            warehouseProvider.importWarehouse(mockFile);
            
            // 验证结果
            verify(warehouseAbility).checkImport(warehouseList);
            verify(warehouseAbility).fillHouseInfo(warehouse);
            verify(addressAbility).getAreaCodeMapByAddress(anyList());
            verify(warehouseRepo).saveWarehouse(warehouseList);
        }
    }
    
    @Test
    @DisplayName("导入仓库 - 数据为空")
    void importWarehouse_EmptyData_ShouldThrowException() throws Exception {
        // 准备数据
        InputStream inputStream = mock(InputStream.class);
        List<WarehouseData> emptyList = Collections.emptyList();
        
        // 模拟依赖
        try (MockedStatic<ExcelUtils> excelUtilsMock = mockStatic(ExcelUtils.class)) {
            when(mockFile.getInputStream()).thenReturn(inputStream);
            excelUtilsMock.when(() -> ExcelUtils.readExcel(eq(inputStream), eq(WarehouseData.class), eq(2)))
                    .thenReturn(emptyList);
            
            // 执行测试并验证异常
            ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
                warehouseProvider.importWarehouse(mockFile);
            });
            
            // 验证结果
            assertEquals(ErrorCodes.BAD_PARAMETER, exception.getErrorCode());
            assertEquals("导入参数不能为空", exception.getMessage());
        }
    }
    
    @Test
    @DisplayName("根据业务线获取仓库 - 成功")
    void getByBusinessLine_ShouldReturnWarehouseResList() {
        // 准备数据
        String businessLine = "adm_pub";
        
        List<Warehouse> warehouseList = new ArrayList<>();
        Warehouse warehouse = new Warehouse();
        warehouse.setId(1);
        warehouseList.add(warehouse);
        
        List<WarehouseRes> warehouseResList = new ArrayList<>();
        WarehouseRes warehouseRes = new WarehouseRes();
        warehouseRes.setId(1);
        warehouseResList.add(warehouseRes);
        
        // 模拟依赖
        when(warehouseRepo.getByBusinessLine(businessLine)).thenReturn(warehouseList);
        when(converter.toWarehouseResList(warehouseList)).thenReturn(warehouseResList);
        
        // 执行测试
        List<WarehouseRes> result = warehouseProvider.getByBusinessLine(businessLine);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getId());
        verify(warehouseRepo).getByBusinessLine(businessLine);
        verify(converter).toWarehouseResList(warehouseList);
    }
    
    @Test
    @DisplayName("根据业务线获取仓库 - 业务线为空")
    void getByBusinessLine_EmptyBusinessLine_ShouldThrowException() {
        // 准备数据
        String businessLine = "";
        
        // 执行测试并验证异常
        ErrorCodeException exception = assertThrows(ErrorCodeException.class, () -> {
            warehouseProvider.getByBusinessLine(businessLine);
        });
        
        // 验证结果
        assertEquals(ErrorCodes.BAD_PARAMETER, exception.getErrorCode());
        assertEquals("业务线不能为空", exception.getMessage());
    }
}
