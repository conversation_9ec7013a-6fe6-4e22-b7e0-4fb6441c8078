package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.app.StartApplication;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(classes = StartApplication.class)
@Disabled
class PositionAbilityAppTest {

    @Resource
    private PositionAbility positionAbility;

    @Test
    void testSyncSpacePosition() {
        positionAbility.syncSpacePosition();
    }

}