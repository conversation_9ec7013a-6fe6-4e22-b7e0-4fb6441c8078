server:
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: application/json

spring:
  application:
    name: asset-commons-config
  profiles:
    active: dev
    include: config, city, coderule
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  cloud:
    nacos:
      discovery:
        server-addr: http://${nacos.host}
        namespace: ${nacos.namespace}
        username: ${nacos.username}
        password: ${nacos.password}
        group: ${nacos.group}
#      config:
#        enabled: false
#        server-addr: http://${nacos.host}
#        namespace: ${nacos.namespace}
#        username: ${nacos.username}
#        password: ${nacos.password}
#        group: ${nacos.group}
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${datasource.mysql.asset.host}/asset?characterEncoding=utf8&autoReconnect=true&failOverReadOnly=false&serverTimezone=Asia/Shanghai&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true&zeroDateTimeBehavior=CONVERT_TO_NULL
    username: ${datasource.mysql.asset.username}
    password: ${datasource.mysql.asset.password}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 10
      auto-commit: true
      max-lifetime: 60000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
    password@kc-sid: oa-asset.g
    # 连接超时时间（毫秒）
    timeout: 2000
    # Redis默认情况下有16个分片，这里配置具体使用的分片，默认是0
    database: 0
    # 连接池最大连接数（使用负值表示没有限制） 默认 8
    lettuce:
      shutdown-timeout: 2000ms
      pool:
        max-active: 20
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-wait: 2000
        # 连接池中的最大空闲连接 默认 8
        max-idle: 20
        # 连接池中的最小空闲连接 默认 0
        min-idle: 2
  mi:
    plan:
      auto-startup: true
      country-domain: CN
      env: ${mi-plan.env}
      project-uid: ${mi-plan.project-uid}

  freemarker:
    cache: false
    charset: UTF-8
    suffix: ftl
    check-template-location: true
    template-loader-path: classpath:/template/

oaucf:
  cache:
    enable: true
    corePoolSize: 40                # 异步线程的核心线程数
    maxPoolSize: 40                 # 最大线程数
    keepAliveSeconds: 60            # 保持活跃时间 秒
    queueCapacity: 500              # 线程队列的大小
    debug: true                     # 开启调试模式，可以打出执行的日志
  workbench:
    host: ${workbench.host}
    app-id: ${workbench.app-code}
    app-secret: ${workbench.app-secret}
  auth:
    env: ${auth.env}
    appId: ${auth.appId}
    appSecret: ${auth.appSecret}
    appSecret@kc-sid: oa-asset.g
    authority:
      #是否开启权限功能
      enable: true
      #应用编码
      appCode: ${auth.authority.appCode}
      #账户体系
      accountType: "intra"
      #授权服务URL
      url: ${auth.authority.url}
      #权限服务URL
      adminUrl: ${auth.authority.adminUrl}
      #权限申请服务URL
      applyUrl: ${auth.authority.applyUrl}
      #是否开启本地缓存
      cache:
        enable: false
      useClient: false
      #是否开启方法权限保护
      methodSecurityEnable: true
      #方法跳过
      methodSecuritySkip: ${auth.authority.methodSecuritySkip}
    token:
      remoteCheck: true
        # 远程认证服务的 base URL
      # 测试环境: https://workbench-ee.test.mioffice.cn
      # 生产环境: https://workbench.mioffice.cn
      remoteHost: ${auth.token.remoteHost}
      # Token 的过期时间 (天)
      days: 1
      excludes: ${auth.token.excludes}
    auth:
      #应用认证配置
      app:
        #是否开启应用认证
        enable: true
        #应用认证方式:local 本地，remote 远程
        type: remote
        #远程认证：远程认证域名
        remoteHost: ${auth.auth.app.remoteHost}
        #认证路径（可以不填）
        openAuthPath:
  idm-api:
    enabled: true
    env: ${idm.env}
    url: ${idm.url}
    app-id: ${idm.app-id}
    app-secret: ${idm.app-secret}
    useClient: false
  fds:
    accessKey: ${fds.accessKey}
    accessSecret: ${fds.accessSecret}
    bucketName: eam-objects
    fdsDomain: https://${fds.host}
    fdsEndpoint: ${fds.host}
    enableCdnForDownload: false
    enableHttps: true
  space:
    enabled: true
    url: ${space.url}
    appId: ${workbench.app-code}
    appSecret: ${workbench.app-secret}
    open-auth-path: /space-be-admin-api/v1/auth/open
  bpm:
    enabled: true
    url: ${bpm.url}
    connectTimeout: 10
    readTimeout: 60
    useClient: false

dubbo: 
  scan: 
    base-packages: com.mi.oa.asset.commons.config
  registry: 
    address: nacos://${nacos.host}?username=${nacos.username}&namespace=${nacos.namespace}&password=${nacos.password}&group=${nacos.group}
  protocol:
    id: dubbo
    name: dubbo
    port: -1
  provider: 
    retries: 1
    validation: true
    filter: errorcodeExceptionFilter, dubboAuthFilter, -exception,
  consumer:
    filter: dubboAuthFilter, -exception,

eam:
  jxs:
    enable: true
    url: ${eam.url}
    app-id: ${eam.app-id}
    app-secret: ${eam.app-secret}
  mybatis-plus:
    auto-fill-operator: true
  hrod:
    url: ${hrod.host}
    appId: ${hrod.appId}
    appSecret: ${hrod.appKey}

management:
  server:
    port: 10119
  endpoints:
    web:
      exposure:
        include: "metrics,prometheus,health" # 只暴露相关 endpoint
    enabled-by-default: false #禁用所有端点
  endpoint:
    metrics:           # 单独打开 metrics 监控指标信息
      enabled: true
    prometheus:        # prometheus 指标采集数据
      enabled: true
    health:            # 健康检查 url
      show-details: never      # 不显示健康详情
      show-components: never
      enabled: true
  metrics:
    tags:
      env: dev
      application: ${spring.application.name}
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
      maximum-expected-value:
        http.server.requests: 10000
      minimum-expected-value:
        http.server.requests: 5
      sla:
        http.server.requests: 5, 10, 25, 50, 75, 100, 250, 500, 750, 1000, 2500, 5000, 7500, 10000
  health:
    nacos-discovery:
      enabled: false
    redis:
      enabled: false