# 测试环境、正式环境配置在nacos
data-auth:
  enable: true
  enableDict: true
  oldFuncIds:
    - asset_account1
  noBusinessLineBusinessType:
    - asset_share_list
  noBusinessLineFuncCode:
    - asset_country_currency
    - amg_region_config
    - amg_country_config
    - amg_country_currency
    - amg_country_tax
# 列字段默认配置

# manageLine: 管理线，多个管理线使用,分隔
# 列字段属性枚举值：
# HIDDEN("0", "不勾选"),
# SHOW("1", "勾选（可取消）"),
# DISABLED("2", "必须勾选"),
fieldmanage:
  # 默认字段配置 (优先配置在这里，出现多个管理线的配置，再配置到下面fieldConfigs
  defaultFieldConfigs:
    - funId: asset_account
      fieldConfig:
        assetCateCode|2;assetCode|2;assetName|2;assetType|1;skuCode|1;skuName|1;quantity|1;locationCode|2;useStatus|2;checkStatus|2;intactStatus|2;companyCode|1;useDeptCode|2;
        manageDeptCode|1;userCode|2;specialCate|0;brand|2;color|1;size|1;configParam|1;texture|1;applicableUserNum|1;currency|1;sapAssetCode|1;sapCateCode|0;
        centerCode|0;originValue|1;netValue|1;depValue|1;price|1;providerCode|1;sn|1;loanUserCode|0;loanDate|0;expectedReturnDate|0;periodMonth|1;periodDate|1;
        newOld|1;country|1;budgetCode|0;purchaseNo|0;contractCode|0;wbsCode|0;buyDate|1;checkDate|1;batchId|1;remark|1;createTime|0;rfid|1;

    - funId: inventory_plan
      fieldConfig:
        recordNo|2;planName|2;planStatus|2;execWay|1;planPercentage|1;endTime|1;manageDeptCode|1;isDraw|1;drawCode|0;deptCode|1;
        userCode|2;createUser|0;createTime|0;businessLine|2;pyAuditWay|0;supervisionWay|1;

    - funId: inventory_task
      fieldConfig:
        recordNo|2;planName|2;taskStatus|2;execWay|1;taskProgress|1;planEndTime|1;userCode|2;planUserCode|2;supervisorUserCode|1;financeUserCode|1;
        purchaserUserCode|1;businessLine|2;totalQuantity|1;locationCode|0;

    - funId: inventory_report
      fieldConfig:
        recordNo|2;planName|2;reportStatus|2;planEndTime|1;userCode|2;submitTime|0;businessLine|2;totalQuantity|1;ypQuantity|1;
        pyQuantity|1;pkQuantity|1;

    - funId: asset_allot #资产调拨列表列配置
      fieldConfig:
        recordNo|2;allotType|2;recordStatus|2;inUserName|2;inUseDeptCode|1;inManageDeptCode|1;inCompanyCode|1;inLocationCode|1;reason|1;createUser|2;createTime|1;
        deptCode|1;source|1;businessLine|1;

    - funId: asset_allot_abnormal  # 调拨异常列表列配置，funId为虚拟的
      fieldConfig:
        recordNo|2;allotType|2;recordStatus|2;inUserName|2;inUseDeptCode|1;inManageDeptCode|1;inCompanyCode|1;inLocationCode|1;reason|1;createUser|2;createTime|1;
        deptCode|1;source|1;businessLine|1;sapMsg|2;

    - funId: idle_enable
      fieldConfig:
        recordNo|2;businessType|2;recordStatus|2;useDeptCode|2;reason|1;createUser|2;createTime|1;deptCode|1;businessLine|1;

    - funId: entry_be_purchase
      fieldConfig:
        recordNo|2;purchaseStatus|2;contractCode|2;purchaseDisplayName|1;providerName|1;purchaseWay|1;orderSource|1;company|1;createTime|1;totalAmount|1;currency|1;
        createUserName|1;deptName|1;businessLine|1;country|1;

    - funId: entry_be_requirement
      fieldConfig:
        recordNo|2;requirementStatus|2;purchaseWay|2;applyUseType|1;applyReasonType|1;useDeptName|1;approvalTime|2;receiverName|2;remark|1;createUser|2;deptName|1;createTime|2;
        businessLine|1;currency|1;country|1;

    - funId: asset_dispose
      fieldConfig:
        recordNo|2;disposeType|2;recordStatus|2;submitStatus|2;applicantUserName|2;applicantDeptCode|1;disposeReason|1;disposeDate|1;createUser|1;
        createDeptCode|1;createTime|1;updateTime|1;businessLine|1;disposeQuantity|1;saleAmount|2;compensateAmount|2;liquidateAmount|2;source|2;

    - funId: asset_dispose_abnormal
      fieldConfig:
        recordNo|2;disposeType|2;recordStatus|2;submitStatus|2;applicantUserName|2;applicantDeptCode|1;disposeReason|1;disposeDate|1;createUser|1;
        createDeptCode|1;createTime|1;updateTime|1;businessLine|1;disposeQuantity|1;saleAmount|2;compensateAmount|2;liquidateAmount|2;source|2;sapType|2;sapMsg|2;

    - funId: asset_dispose_item  #资产处置明细列表列配置
      fieldConfig:
        assetCode|2|资产编码;assetName|2|资产名称;SKU|2|SKU编码;skuName|2|SKU名称;userDisplayName|1|责任人;sn|2|出厂SN;availableQuantity|2|可处置数量;disposeQuantity|2|处置数量;
        saleAmount|2|出售金额;assigneeDisplayName|2|受让人;compensateAmount|2|赔偿金额;compensateDisplayName|2|赔偿人;liquidateAmount|2|清理费用;
        liquidateDisplayName|2|清理人;centerCode|1|成本中心;originValue|1|资产原值;netValue|1|净值;remark|1|备注;

    - funId: asset_dispose_type  #资产处置类型
      fieldConfig:
        recordNo|2;disposeType|2;businessCate|2;remark|2;recordStatus|2;businessLine|2;

    - funId: entry_be_purchase_item  # 我的订单列表列配置
      fieldConfig:
        recordNo|2;purchaseItemStatus|2;applicantUserName|1;purchaseName|2;materialType|1;brand|2;model|2;purchaseUserName|1;providerCode|1;
        contractCode|2;purchaseQuantity|2;settledQuantity|2;acceptanceUserName|2;requirementNo|1;requireDate|1;costCenter|1;remark|1;purchaseDate|1;
        purchaseWay|1;createUser|1;businessLine|1;settledAmount|1;currency|1;budgetCode|1;wbsCode|1;totalAmount|1;unTaxAmount|1;id|1;

    - funId: entry_be_purchase_item  # 采购订单列表列配置
      fieldConfig:
        recordNo|2;purchaseItemStatus|2;applicantUserName|1;purchaseName|2;materialType|1;brand|2;model|2;purchaseUserName|1;providerCode|1;
        contractCode|2;purchaseQuantity|2;settledQuantity|2;acceptanceUserName|2;requirementNo|1;requireDate|1;costCenter|1;remark|1;purchaseDate|1;
        purchaseWay|1;createUser|1;businessLine|1;settledAmount|1;currency|1;budgetCode|1;wbsCode|1;totalAmount|1;unTaxAmount|1;id|1;

    - funId: receive_apply  # 领用列表列配置
      fieldConfig:
        recordNo|2;execStatus|2;recordStatus|2;applicantUserName|2;applicantEmpCode|1;approvedDate|2;applicantDeptCode|2;createDeptCode|1;
        businessKey|0;businessLine|2;recordSource|1;warehouseCode|0;assetManageUserName|0;assetManageEmail|0;receiveWay|0;receiveType|0;
        applyUseId|0;applyUse|0;applyReasonId|0;applyReason|0;consigneeUserName|0;consigneePhone|0;consigneeEmail|0;consigneeAddress|0;ruleName|0;
        ruleResult|0;country|0;currency|0;remark|0;createUser|2;updateUser|0;createTime|1;updateTime|0

    - funId: amg_stock_asset  # 资产入库列表列配置
      fieldConfig:
        recordStatus|2;recordNo|2;businessDate|1;businessLine|2;createUser|2;createDeptName|2;createTime|2;approvalTime|1;businessType|2;warehouseName|1;sourceOrderNo|1;sourceOrderType|1;contractCode|1;providerName|1;companyName|1;currency|1;purchaseDisplayName|1;totalQuantity|1;totalAmount|1;stockRemark|1;

    - funId: stock_asset_item  # 资产入库明细列配置
      fieldConfig:
        rowStatus|2|行状态;assetCode|2|资产编码;sn|2|出厂SN;skuCode|1|SKU;assetName|2|资产名称;availableQuantity|2|数量;assetCateName|2|设备分类;sapCateName|1|SAP资产分类;
        originValue|2|资产原值;locationName|1|位置;manageDeptName|2|管理部门;useDeptName|2|使用部门;userDisplayName|2|责任人;centerCode|2|成本中心;brand|1|品牌;model|1|型号;sapType|2|sap状态;sapMsg|2|sap消息;

    - funId: amg_stock_return_item  # 退还入库明细列配置
      fieldConfig:
        rowStatus|2|行状态;sn|2|出厂SN;skuCode|2|SKU;skuName|2|物料名称;assetCode|2|资产编码;assetName|2|资产名称;notifyQuantity|2|通知收货数量;stockQuantity|2|入库数量;stockArea|2|库区;

    - funId: amg_stock_acceptance_item  # 验收入库明细列配置
      fieldConfig:
        id|1;rowId|1;skuCode|1;skuName|1;sn|1;notifyQuantity|1;stockQuantity|1;taxPrice|1;price|1;taxRate|1;centerCode|1;applyUser|1;stockArea|1;periodDate|1;

    - funId: amg_stock_account # 全局库存列表列配置
      fieldConfig:
        warehouseName|2;skuCode|2;skuName|2;model|2;deviceType|1;brand|1;measureCode|1;miSkuCode|1;miGoodsId|1;config|1;country|1;
        totalQuantity|2;uncheckQuantity|2;availableQuantity|2;lockQuantity|2;amount|1;id|1;

    - funId: amg_stock_batch # 批次库存列表列配置
      fieldConfig:
        warehouseName|2;stockArea|2;skuCode|2;skuName|2;model|2;assetCode|2;imei|1;laserCode|1;sn|2;batchNo|1;newOld|2;stockType|1;intactStatus|1;deviceType|1;
        brand|1;spec|1;measureCode|1;miSkuCode|1;miGoodsId|1;config|1;country|1;
        totalQuantity|2;uncheckQuantity|2;availableQuantity|2;lockQuantity|2;amount|1;taxPrice|1;id|1;
        projectCode|1;projectName|1;rfid|1;

    - funId: amg_stock_sn # 串号库存列表列配置
      fieldConfig:
        warehouseName|2;skuCode|2;skuName|2;model|2;assetCode|2;imei|1;laserCode|1;sn|2;batchNo|1;newOld|2;stockType|1;intactStatus|1;deviceType|1;
        brand|1;spec|1;measureCode|1;miSkuCode|1;miGoodsId|1;config|1;country|1;
        totalQuantity|2;uncheckQuantity|2;availableQuantity|2;lockQuantity|2;amount|1;taxPrice|1;id|1;
        projectCode|1;projectName|1;rfid|1;

    - funId: amg_stock_record # 库存流水列表列配置
      fieldConfig:
        warehouseName|2;skuCode|2;skuName|2;assetCode|1;model|2;imei|1;laserCode|1;sn|1;batchNo|1;newOld|1;stockType|1;intactStatus|1;deviceType|1;
        brand|1;measureCode|1;miSkuCode|1;miGoodsId|1;config|1;country|1;billType|2;businessSource|2;businessNo|2;updateTime|2;updateUserName|2;
        openingQuantity|2;changQuantity|2;taxPrice|1;amount|1;id|1;projectCode|1;projectName|1;rfid|1;

    - funId: amg_stock_received # 收货入库列配置
      fieldConfig:
        recordStatus|2;recordNo|2;businessSource|1;createTime|2;providerCode|2;businessNo|1;businessLine|1;receivedQuantity|2;nonReceivedQuantity|2;closeQuantity|2;projectCode|1;projectPhase|1;warehouseCode|2;

    - funId: amg_stock_acceptance # 验收入库列配置
      fieldConfig:
        recordStatus|2;recordNo|2;businessType|1;businessNo|1;businessSource|1;stockDate|1;createTime|1;createUser|1;
        updateTime|1;updateUser|1;warehouseCode|2;providerCode|1;projectCode|1;projectPhase|1;stockRemark|2;notifyQuantity|2;quantity|2;receivedQuantity|2;

    - funId: amg_stock_collect # 领用出库列配置
      fieldConfig:
        recordStatus|2;recordNo|2;businessLine|1;applyUserName|2;applyPersonId|1;useDeptName|1;createUserName|1;createDeptName|1;createTime|1;
        businessSource|1;businessType|1;useWay|1;sourcePort|1;sourceNo|1;remarks|2;collectWay|2;warehouseCode|2;applyQuantity|1;stockQuantity|1;

    - funId: amg_stock_return # 退还入库列配置
      fieldConfig:
        recordStatus|2;recordNo|2;businessLine|1;applyUser|2;applyPersonId|2;applyDeptCode|2;createUser|2;createDeptCode|1;
        createTime|1;businessNo|1;businessSource|1;returnReason|1;returnWay|1;returnWarehouseCode|2;expressCompany|1;expressNo|1;remarks|2;
    - funId: project_cfg # 项目配置
      fieldConfig:
        recordStatus|2;enabled|2;businessLine|2;projectCode|2;projectName|2;parentProjectCode|2;source|2;
    - funId: reservation_record # 预约打卡记录
      fieldConfig:
        recordNo|2;recordStatus|2;reservationType|2;reservationStartTime|2;reservationEndTime|2;reservationDuration|1;
        actualStartTime|1;actualEndTime|1;useDuration|1;assetQuantity|1;createUser|1;create_user_name|2;createDeptCode|1;createDeptName|2;createTime|2;

    - funId: reservation_asset # 设备打卡记录
      fieldConfig:
        assetCode|2;assetName|2;recordNo|2;recordStatus|2;reservationType|2;reservationStartTime|2;reservationEndTime|2;reservationDuration|2;
        actualStartTime|2;actualEndTime|2;useDuration|2;useDeptCode|2;useDeptName|2;createUser|2;create_user_name|2;createDeptCode|2;createDeptName|2;createTime|2;

    - funId: asset_share_record # 共享资产记录
      fieldConfig:
        isValid|2;recordNo|2;recordTitle|2;memo|2;shareScene|2;shareDeptName|2;createUser|1;createUserName|1;createDeptCode|1;createDeptName|1;createTime|1;updateTime|1;businessLine|1;

    - funId: custom_share_list # 自定义共享清单
      fieldConfig:
        assetCode|2|资产编码;assetName|2|资产名称;userName|2|责任人;assetQuantity|2|台账数量;shareQuantity|2|共享数量;userCode|0|责任人账号;originValue|0|资产原值;netValue|0|资产净值;

    - funId: asset_share_list # 共享清单
      fieldConfig:
        shareQuantity|2;assetCateCode|2;assetCode|2;assetName|2;assetType|1;skuCode|1;quantity|1;locationCode|2;useStatus|2;checkStatus|2;intactStatus|2;companyCode|1;useDeptCode|2;
        manageDeptCode|1;userCode|2;specialCate|0;brand|2;color|1;size|1;configParam|1;texture|1;applicableUserNum|1;currency|1;sapAssetCode|1;sapCateCode|0;
        centerCode|0;originValue|1;netValue|1;depValue|1;price|1;providerCode|1;sn|1;loanUserCode|0;loanDate|0;expectedReturnDate|0;periodMonth|1;periodDate|1;
        newOld|1;country|1;budgetCode|0;purchaseNo|0;contractCode|0;wbsCode|0;buyDate|1;checkDate|1;batchId|1;remark|1;createTime|0;

  fieldConfigs:
    - manageLine: adm        # 行政
      funId: asset_account   # funId
      fieldConfig:
        assetCateCode|2;assetCode|2;assetName|2;assetType|1;skuCode|1;skuName|1;quantity|1;locationCode|2;useStatus|2;checkStatus|2;intactStatus|2;companyCode|1;useDeptCode|2;
        manageDeptCode|1;userCode|2;specialCate|0;brand|2;color|1;size|1;configParam|1;texture|1;applicableUserNum|1;currency|1;sapAssetCode|1;sapCateCode|0;
        centerCode|0;originValue|1;netValue|1;depValue|1;price|1;providerCode|1;sn|1;loanUserCode|0;loanDate|0;expectedReturnDate|0;periodMonth|1;periodDate|1;
        newOld|1;country|1;budgetCode|0;purchaseNo|0;contractCode|0;wbsCode|0;buyDate|1;checkDate|1;batchId|1;remark|1;createTime|0;
    - manageLine: car_asset    # 汽车
      funId: asset_account   # funId
      fieldConfig:
        assetCateCode|2;assetCode|2;assetName|2;assetType|1;skuCode|1;skuName|1;quantity|1;locationCode|2;useStatus|2;checkStatus|2;intactStatus|2;companyCode|1;useDeptCode|2;
        manageDeptCode|1;userCode|2;specialCate|0;brand|2;color|1;size|1;configParam|1;texture|1;applicableUserNum|1;currency|1;sapAssetCode|1;sapCateCode|0;
        centerCode|0;originValue|1;netValue|1;depValue|1;price|1;providerCode|1;sn|1;loanUserCode|0;loanDate|0;expectedReturnDate|0;periodMonth|1;periodDate|1;
    - manageLine: car_asset    # 汽车
      funId: stock_asset_item  # 资产入库明细列配置
      fieldConfig:
        rowStatus|2|行状态;assetCode|2|资产编码;sn|2|出厂SN;skuCode|1|SKU;assetName|2|资产名称;assetCateName|2|设备分类;rowId|1|采购订单行号;remark|1|备注;manageDeptName|2|管理部门;
        useDeptName|2|使用部门;userDisplayName|2|责任人;companyName|1|所属公司;availableQuantity|2|数量;brand|1|品牌;model|1|型号;
        vehicleCode|1|车辆编号;vehicleType|1|车型;vehiclePlateNo|1|车牌号;vehiclePowerType|1|动力类型;color|1|颜色;insuranceCompany|1|保险公司;insuranceStartDate|1|保险生效日期;insuranceEndDate|1|保险到期日期;
        address|1|位置;originValue|2|资产原值;sapCateName|1|SAP资产分类;centerCode|2|成本中心;

workbench:
  enable: true
  deptlist:
    - HW
  deptblacklist:
    - HW000075
  whitelist:
    - chendaiquan
    - huyuetian
    - liaohongxing
    - qinyanshan
    - minpan
    - v-xiaoman
    - p-dengbin8
    - p-xujiawei7
    - p-longju
    - p-guoqiang11
    - p-wangaolin
    - v-lvjianqiao
    - v-xiaoman

#不区分业务线订阅的功能，多个通过;分割
not-distinguish-business-subscribe:
  funCode: asset_share_list

cache:
  admins: chendaiquan