server:
  port: 9091

nacos:
  host: cnbj1-nacos.api.xiaomi.net:80
  namespace: info_mims_asset_prod
  username: info_mims_asset_prod
  password@kc-sid: oa-asset.g
  password: GBC0DenEy2bKMYI2DstbsvZiGBLauU57nOxAGrVWOGzhR5rv7QEYEDr2UvWCJ0YMmHgTm9vJK48YFCr9mJD4E3iQF+k0Ofu/jiWXSqUbAA==
  group: ASSET_GROUP

redis:
  host: cache01.b2c.srv
  port: 5100
  password: GDAHqQapGuT7ihbOMXt3hkgeBLAYoqWlxpoqG/xEGkewJ3BbEQwxbOIchpXy5OH1rA4YEgbCEtQQekHFpF6OOtU3lIxKARgQsrOBhYvmTXmuQ5Tm0NbqeRgU6vF+z9y+Iiu3nTy+GkG9xcbywFAA

datasource:
  mysql:
    asset:
      host: asset.mysql01.b2c.srv:3438
      username: kc_asset_wn
      password@kc-sid: oa-asset.g
      password: GDCVsgudJ2jsnrUk3H_lqddAACm5OjddygxO02zUkXlteAWFU8636Rs0ejCXU_y1iB4YEsfi-OzqrkY0lvHLOu6Nc0DZARgQ3U-s6VB-QQiv-7wcR8GrGBgU7xjpGq8pEE_mj4gfMRD61zpGpZsA

eam:
  url: https://eam.asset.mioffice.cn
  app-id: jxstar
  app-secret@kc-sid: oa-asset.g
  app-secret: GBBkPaPrDvHxxBK08F4rBluIGBIxKXH42pdD6YF/erQN8zliFAEYEFY8hGfPeU1FsQ5VgIjpjV8YFH0uvY2FTcCHHZuLjuesysaVTepAAA==
  x5:
    appId: eam_v2_config
    appKey@kc-sid: oa-asset.g # 用来加密password的sid
    appKey: GCBlyehoKYJINhKJTULYkOObQ7s9nPeor2Jn7SAxge/3+xgSUnDdDzJaQ2OQv6XqRt18yesBGBBbkqMwX75ClZZnw7+OUa4nGBTuBAfxkzlgoXtASHc+FvuPsbP2cQA=
    method: /api/x5/register-role
  mdm:
    url: https://mdm.be.mi.com/getMaster/x5
    app-id: eam
    app-secret@kc-sid: oa-asset.g
    app-secret: GDBQ7oGnwu3UMh1owIm8WDwFHSvSTMGiThcT/wgGDnC5lOWkX/g++pjVxz6AZPwmBq8YEuy89A8gf03spxOCg3LboZ2iARgQa+nEqc7STM+tKVgCwT38/xgUa2PxHa7sc/sNHDorFJ12PqYsXaIA
    query-key:
      company: 476e2b7c9cee4260b8bdbdf545aa0602
      department: 73ed95d30c0e4d219df47f32199d0aac
      supplier: c519638299154387814d320aab8a81cf
      purchaseCatalog: 83524a87e5bb487a9122311e4dff9002
      purchaseItem: 717ee82f2a2a45128a5ce0525adbc1b3
      measurementUnit: 3c9bf68eade248dfb4cedfdc03fe6fc2
    integrate:
      host: http://mdm.be.mi.com/integrate/x5
      appId: MDM-EAM
      appKey@kc-sid: oa-asset.g # 用来加密password的sid
      appKey: GDAHzo4Mlvw6Gw8TmTrFGDHULYsoKgc1zM2FIinFjcZ7ao6oXW5me7inyFWH+/cF1nMYErUqq549rk7ysO3q1rQNs4dVARgQJOxI8CMlSQORWDnyZjolbRgUBbKb0hfcO9VY6/PzXMcBB8myjYQA
  ads:
    url: http://wps.be.xiaomi.com
    appId: xm_1002
    appSecret@kc-sid: oa-asset.g
    appSecret: GDBqfio450DLg/jAut7JE80dNFjm40x82MVL7TSiwOY/0uIGE+H/7dN1GZn1OxuD1OUYEoV4cY9VaUYrlFXUlw5XuynPARgQXfObR5QrS4mCAdUkilnN3RgU3Z/F+NOdpT9AliomrP1aOPvgow8A
    method: parse
  ecp:
    enabled: true
    url: https://ecp.mier.mi.com/ecp-server-api
    appId: ecp_openApi_api
    appSecret@kc-sid: oa-asset.g # 用来加密password的sid
    appSecret: GDBdyIAluDxG23wC5EkDD0fm4Zm2Xqgb+iKTqd+lJJtZg/d5y8t8W3Wnyvw0iHf4b0UYEtUHaWC5h044pqcBJ3uDmQzBARgQjqV8hF3hSomN8OjX+c4lChgUSasInjKUofOZL4/51AlXsighyj0A
# 工作台
workbench:
  host: https://workbench.mioffice.cn
  app-code: TdXIdiI279t4
  app-secret@kc-sid: oa-asset.g
  app-secret: GCDCxjMlFiSYK2OCBxFM+6aSHxb/24hkdjbE9DQq8xh9VhgSJnxajIj0SnqnJcqxvy71pW4BGBDVbzs9a9VF45BG6DGbCNNiGBScL1CBnkcjDnkr8a2yHIQIo/j2aQA=

# 权限
auth:
  env: prod
  appId: TdXIdiI279t4
  appSecret: GCDjBer/joNT1MrisLWkWgmjzegHjiyP/JyuP3My/U9CMhgSaXgStMfiTMykR0RJe7kjqfIBGBCNbK7LuyFGZYxzzR4BinwyGBR/bs7l2iFoTPfuEaXsGCatjuDblwA=
  authority:
    #应用编码
    appCode: TdXIdiI279t4
    #授权服务URL
    url: https://uc.infra.mioffice.cn/authorize
    #权限服务URL
    adminUrl: https://uc.infra.mioffice.cn/admin
    #权限申请服务URL
    applyUrl: https://uc.infra.mioffice.cn/apply
    #方法跳过
    methodSecuritySkip: false
  token:
    # 多个用逗号分隔
    excludes: /bpm/call-back,/reservation/operation,/reservation/outside/query,/asset-account/relate-asset,/api/v1/uc/dimension/**,/configs/structure/tree,/configs/asset-category/list/all,/configs/x5/**
    remoteHost: https://workbench.mioffice.cn
  auth:
    app:
      remoteHost: https://workbench.mioffice.cn

# IDM
idm:
  env: PROD
  url: http://api.id.mioffice.cn
  app-id: eam
  app-secret@kc-sid: oa-asset.g
  app-secret: GDA48mtC8XvQLW5vCrRxwJwIDBM74SQCnJfmEsmf1P2SSoYfJQaQQmFbEiE5ArBsZKgYEkOxKQ5V5k7Rr9jKz0YM1itwARgQqhrpJSFCR825/mZN2JCw9hgUAfRFNs/9Kmjp/ZHV3ZYd8FD8BLwA

# hrod配置
hrod:
  host: https://api.hrod.mioffice.cn/hapi/HR_C
  appId: 622f92d2-9bec-49fa-8311-9b713f2abd10
  appKey@kc-sid: oa-asset.g # 用来加密password的sid
  appKey: GDA8/aQbDSg3qY0Imm+Pd02qnIa7sv7JESKp1LizAOKIv4YvNghZsyCjVbVYTbzPlmMYEmB/iXlVf0MCiyF+nxrNfVUmARgQ42wxcnIXSg+ZRJ57HUM+oxgU11cghrHO2ryLfXzL7OvVCooVV+EA


# fds配置
fds:
  host: cnbj1-fds.api.xiaomi.net
  accessKey: AKDX3DX6EEUGRRCGMP
  accessSecret@kc-sid: oa-asset.g
  accessSecret: GDC1AjoaJGqM7/krXBTvQMqybCBrxc/gZsHUoGYb4yGcSMRliDIx0vp0AfqnvVpYqp8YEtcKSBOXkUH/rMU6DOBZp1RRARgQQNK0H2WFSoy5gXfZBh9EohgUKw7UuSnHRAEoya34LIo1IsB76ScA

# 空间系统
space:
  url: https://space.mioffice.cn

mi-plan:
  env: PROD
  project-uid: 191ed289cebc4821ad7004dc7ef71aa9

mis:
  host: http://soa01.etcd.b2c.srv:4001
  group: info-application
  app: asset_eam
  ads:
    serviceName: xm_ads

x5:
  server:
    enable: true # 开启 x5-server 组件，默认 false 关闭
    enable-exception-handler: true # 默认打开异常处理
    configs:
      - app-id: x-connector
        app-key@kc-sid: oa-asset.g # 配置sid
        app-key: GCB550veeKryQKoJqRpRZA4vDB5+QlWAiL46ZVYEME/S5BgSbhN2YAnPQ2qKkxI3PtHPOlIBGBBj5+oA/kxPLrV4lPoxv86dGBSkDw4ofPgAdDlmBOo91S/yqVMMKwA= # 配置加密后的key
        allow-uris:
          - /**
      - app-id: eam
        app-key@kc-sid: oa-asset.g # 配置sid
        app-key: GCAvJjFaN2D63HCc5XrZiEWsNbnfYW/H/68JzOBIbG6PqRgSdIlOQggeTXS0vavmAf2ov0wBGBAGMtDnmvRNBrT5NYOWPHs1GBTTXDwRRqo56dfsoavMn/k8NIhtzgA=
        allow-uris:
          - /**
sku-export-template: https://cdn.cnbj1.fds.api.mi-img.com/eam-objects/templates/批量导出物料模板.xlsx

sap:
  host: https://mipop.p.mi.com/
  hana-host: https://miphp.p.mi.com
  account: RFCMIEAM
  password: GBCUFhHF3F6Av1TABMGB7dAyGBISxnoI+4JPUIZRnUygvyWNDwEYEMCTApGpB0vdifr4wRl0MJ0YFL/haajzkdZkHSwC7GLSOZ9fvUUfAA==
  password@kc-sid: oa-asset.g # 用来加密password的sid
  appId: po_mieam
  appKey@kc-sid: oa-asset.g # 用来加密password的sid
  appKey: GDDWvZFZVXx2P47i2WSJyqyz0EWVr+OL/je2Wtb6ozw59bH5J1S35XgvnEiYKCStULcYEiKa3Yk6PE9Xi20TdmPUelnyARgQtTOPhGqQTpWhzITabvnPoBgUHACeiFUvY8keE0E0qK146rA9N6IA

# BPM
bpm:
  url: https://bpm.infra.mioffice.cn/runtime

cas:
  server-url-prefix: https://cas.mioffice.cn/
  host-url: https://mieam.asset.mioffice.cn/_aegis/cas/logout

lark:
  host: https://open.f.mioffice.cn
  app-id: cli_a07a401ab0789062
  app-secret@kc-sid: oa-asset.g # 用来加密password的sid
  app-secret: GDDGCdtXz3qB/aCVTvS1vLBuSqEI2uejToUvdpo9gnMXXK2WwKJ0U5RAgi1+GfDZAWIYEqEvIx/RQkbTmF5YwE9U+R2ZARgQdQQoQC4CThKl/NHTvYBw+hgUa7y957XjwSh8f3MDPTev9zobOEoA

tpm:
  host: https://ac-mid.imp.xiaomi.com/tpm/x5/eam/mdm
  app-id: hyper_tpm
  app-secret@kc-sid: oa-asset.g # 用来加密password的sid
  app-secret: GDCVuuyDRdmOT738hKml+T09V6Bkw0kwSPfqEzED2GYsqoo0j4KYdMbM6ZLMcU9VMOgYEmqI7wOBGUmogxKxstLx8b3aARgQcXyalu1gSRazLyKqhKw4mBgUCaGMqBldPoOhyMuDqPsVtFOK34gA



# 飞书交互按钮
lark-button:
  share-url: https://mieam.asset.mioffice.cn/asset/account/shared-list

neptune-manager:
  apply-method: check_update
  #  正式环境，国内的
  apply-url: http://neptune.be.b2c.srv/api
  apply-url_sg: https://neptune.g.mi.com/api
  #  翻译平台appkey
  app-id: 0430f6b4a0994295be4df2bf10e0e0f4
  #  翻译平台x5key
  app-key: 8c879f76de
  #  是否正式环境
  queryProdVersion: true
  # 是否海外应用。 true：代表部署在海外机房服务 false：代表部署在国内机房服务
  sg: false