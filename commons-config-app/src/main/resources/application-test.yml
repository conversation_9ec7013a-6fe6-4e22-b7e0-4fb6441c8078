server:
  port: 9091

logging:
  level:
    com.mi.oa.asset.commons.config.infra.database.mapper: debug

nacos:
  host: staging-nacos.api.xiaomi.net:80
  namespace: info_mims_asset_test
  username: info_mims_asset_test
  password: GBBWhjXZ97Zw1uD3ScyHakgxGBKARmqXOIBO8ZDAR4WcgTj1t/8YEC9D8VQTvEB4g8wm+n5iKV8YFMl6rnHvsxXfjg8nipxTqONpon++AA==
  password@kc-sid: oa-asset.g
  group: ASSET_GROUP

redis:
  host: wcc.cache01.test.b2c.srv
  port: 22122
  password: GDA/ElRr/hDmpaJJJWlnl2opX8esw0ZuBaER0tw2/ZOqgTfCKRh0iv0qkHLWiFRXUBIYEiO0+eY8TkuEqEMNHXL3B/Gn/xgQyw7sH5N2RXWCsI3JCX+WJRgUIt2Qv6bIQ24R+Gp5JX42slh3hOEA

datasource:
  mysql:
    asset:
      host: ksc.test.mysql01.b2c.srv:3304
      username: info_asset_wn
      password@kc-sid: oa-asset.g
      password: GDBSyMkjmlIAhFRKDJkxvcnccFht0rAoKhYZX87fepzSi7hYuQqyCQtIpMcnQc3ThigYEuam8Q97D0pcvO3RDnptZ8QL_xgQPRkgvWLrQXKJrnnj_pa_qRgURJxzN3oExeaLBLsoZn-NGNSJPLcA

eam:
  url: http://eam-asset.test.mioffice.cn
  app-id: jxstar
  app-secret@kc-sid: oa-asset.g
  app-secret: GBABIqYI98fln3sg0y7Ro/MkGBJjKGS5iBpNuKXyFM1jESDIS/8YECrSmzFtx0gtgeqgwHe4Wp8YFM5L5SmMvhvgZtcmSktGrGzr1+f6AA==
  x5:
    appId: eam_v2_config
    appKey@kc-sid: oa-asset.g # 用来加密password的sid
    appKey: GCDBgSxvJDKMn+MsRL6KQbWkob8zfkC2p95z7Xu5H8lSGRgSBPyFcdBcTO+tqdbQ9blxdtT/GBC2W+MtwIRPEbuNkfokJKXXGBSmYSyqstdXQ26qa6uLSEkK+Siv9AA=
    method: /api/x5/register-role
  mdm:
    url: https://mdm.be.mi.com/getMaster/x5
    app-id: eam
    app-secret@kc-sid: oa-asset.g
    app-secret: GDCnONVCzbZFo2h64oBwPwvis50ID+aEEf22ynoKEeyG8a005m4RzhuhQJcMalHyscIYEjkZDmx0wEJxpFuW2HrH8CtL/xgQzT7F1wrBSXWVdIqD+/D5IBgUTvlQWxWLm58ZDnV7YlvNbH7tJWgA
    query-key:
      company: 476e2b7c9cee4260b8bdbdf545aa0602
      department: 73ed95d30c0e4d219df47f32199d0aac
      supplier: c519638299154387814d320aab8a81cf
      purchaseCatalog: 83524a87e5bb487a9122311e4dff9002
      purchaseItem: 717ee82f2a2a45128a5ce0525adbc1b3
      measurementUnit: 3c9bf68eade248dfb4cedfdc03fe6fc2
    integrate:
      host: http://mdm.be.test.mi.com/integrate/x5
      appId: MDM-EAM
      appKey: Oy1moxiF8tbdb2UeR3iqPDwnXwBKa3r0
  ads:
    url: http://wps.be.xiaomi.com
    appId: xm_1002
    appSecret@kc-sid: oa-asset.g
    appSecret: GDDrEgJUx6FJsEWIjLLEjpxkyHlHgiplr0u6TCWGHXimZRByXv5cNlhan/n7ecsYf7UYEqQnFK1jR0cWiJ16b4gn/t5G/xgQpn5aGn5OR6+dLbCMNMySGxgU9cfHqn2C1J7Q/WG3YdwzXlrTfewA
    method: parse
  ecp:
    enabled: true
    url: https://ecp.pre.mi.com/ecp-server-api
    appId: ecp_openApi_api
    appSecret@kc-sid: oa-asset.g # 用来加密password的sid
    appSecret: GDAeqEx39IAy553pTdAP8aZEzeURB7YRQoEGAmWfYIuit19R5/wvqs575l6A2AAgTYAYEtqC/fyVG0T1idYyuwY6vAY2/xgQO1IIc9YdS2KCO19ipL6kyhgUaBfV6DKNmgqSqAy9MQGWq3HEH6AA
# 工作台
workbench:
  host: http://workbench-ee.test.mioffice.cn
  app-code: lqisPdAVHqCn
  app-secret@kc-sid: oa-asset.g
  app-secret: GCDNhcene2/tfQWHmPvwYFmEH4Xuog6Bv17BqAV395yuiRgSPLf4HZkpTaucDys3bm2LLEX/GBCNVqTlYxlBuLtMZJbtTcNOGBT56bU4+Y/5SSRrcnJa9JKnYiSCDQA=

# 权限
auth:
  env: test
  appId: lqisPdAVHqCn
  appSecret: GCDNhcene2/tfQWHmPvwYFmEH4Xuog6Bv17BqAV395yuiRgSPLf4HZkpTaucDys3bm2LLEX/GBCNVqTlYxlBuLtMZJbtTcNOGBT56bU4+Y/5SSRrcnJa9JKnYiSCDQA=
  authority:
    #应用编码
    appCode: lqisPdAVHqCn
    #授权服务URL
    url: http://uc-infra.test.mioffice.cn/authorize
    #权限服务URL
    adminUrl: http://uc-infra.test.mioffice.cn/admin
    #权限申请服务URL
    applyUrl: http://uc-infra.test.mioffice.cn/apply
    #方法跳过
    methodSecuritySkip: false
  token:
    # 多个用逗号分隔
    excludes: /**
    remoteHost: https://workbench-ee.test.mioffice.cn
  auth:
    app:
      remoteHost: https://workbench-ee.test.mioffice.cn

# IDM
idm:
  env: PROD
  url: http://api.id.mioffice.cn
  app-id: eam
  app-secret@kc-sid: oa-asset.g
  app-secret: GDDyrO4NcNu4kMsywQ8rjUntQhpTnYrbtds4voRoakbPVMrzEu+SGBSu8oe61ev6qtAYEtc2qjPXU0zgiqcESsZPFrxY/xgQGTP3L4/lQwGFQ3+D2GvfaBgUv1oLu7FFj5ZBAZkZ416pNk3IAbwA

# hrod配置
hrod:
  host: https://api.hrod.mioffice.cn/hapi/HR_C
  appId: 622f92d2-9bec-49fa-8311-9b713f2abd10
  appKey@kc-sid: oa-asset.g # 用来加密password的sid
  appKey: GDDEo+v5BHNCy7Vf5jSspbHJUx6nJpZ38MuSHguZw8RtfmC7ETpt+Xvr7UxKhgoMkRcYEgVRezRauEMerWRrMccwUI/n/xgQcVe9cmWUQyCFPCyJsIdb0xgUkbVtNh1P9NjduBdN3+YxiBbBgmEA

# fds配置
fds:
  host: staging-cnbj2-fds.api.xiaomi.net
  accessKey: AKDX3DX6EEUGRRCGMP
  accessSecret@kc-sid: oa-asset.g
  accessSecret: GDBzpHKIWj0nfMxRhgJbslapgCIS1pcMIfgwTR/MINOJ6pCGATJbhiFiGSGPpMV4b98YEg463BO6wEF4h1SI1SIeXZ87/xgQWerapmffQu2kz6hjF6+cWxgUcsKH9wrZqsuIT8ugWwCa0qKpRY0A            # 密钥内容

# 空间系统
space:
  url: https://space-ee.test.mioffice.cn

mi-plan:
  env: TEST
  project-uid: d06a3816715d42888d6569978c3258f7

mis:
  host: http://etcd.test.mi.com
  group: info-application
  app: asset_eam
  ads:
    serviceName: xm_ads

x5:
  server:
    enable: true # 开启 x5-server 组件，默认 false 关闭
    enable-exception-handler: true # 默认打开异常处理
    configs:
      - app-id: x-connector
        app-key@kc-sid: oa-asset.g # 配置sid
        app-key: GCB1o9sQqJQtaKjluZEBWaTAdNCFSIY70Lzfusba10+oYRgS+Hx0V65YTGSyGRdHMznuH+f/GBArXYYoiYlAmZ1rM6sWoH5iGBQdQP19I+TlTj7vaDzpMcYBlD1wdwA= # 配置加密后的key
        allow-uris:
          - /**
      - app-id: eam
        app-key@kc-sid: oa-asset.g # 配置sid
        app-key: GCCqtUHQfNelXVhCcoqHkAfWds1VSyLFtYmIlxulR7VK9BgSHkt0Ctq9Sqe7by3ZxtuBp/n/GBDqF2AKTLJG+qYPpAXGIjE6GBRMqXKG/c4gfitGP43Qvu1vXJ+GzgA= # 配置加密后的key
        allow-uris:
          - /**
sku-export-template: https://eam-objects.cnbj1.mi-fds.com/eam-objects/templates/批量导出物料模板.xlsx


sap:
  host: http://mipod.mioffice.cn:50000
  hana-host: http://mipod.mioffice.cn:50000
  account: RFCMIEAM
  password: GBALh0ub19qRDeNPAxqfXDLSGBJf8mMfe4pK0pLkk6mb0KE0vP8YECNlaZfUlE+BibaO7Jgn0SAYFJ0d/sqX291M9P9zJaxBlC5rs2bvAA==
  password@kc-sid: oa-asset.g
  appId: po_mieam
  appKey: GDDVaT0O2JWqKt0VnIthdmcxF7J5TYuTw78ec545EJAGt0D+T73YP2wg+0jtwsmdgdEYEmrZohcM+0NUv8voraFcLW4S/xgQWTURadI/Rh+6K3mnWYqqNxgUMdGjz/7rWddAHRrKEu0GuBTC7eYA
  appKey@kc-sid: oa-asset.g

# BPM
bpm:
  url: https://bpm-infra.test.mioffice.cn/runtime

cas:
  server-url-prefix: https://castest.mioffice.cn/
  host-url: https://mieam-asset.test.mioffice.cn/_aegis/cas/logout

# 标签打印
label-template:
  funIds: asset_sn,asset_account

lark:
  host: https://open.f.mioffice.cn
  app-id: cli_a06561ec1038d063
  app-secret@kc-sid: oa-asset.g # 用来加密password的sid
  app-secret: GDBn3RgZUWVEGgmxWN2UAnlWTzS8IUkDpTIHyM3iPzFjWXq+0huMXTeQDmEjznHhis0YEk54O/v1W02fj5R3ecHR5zUV/xgQjXn0V/OcT02Nn92OArx4fBgUGHQdccCv49Tsi4cC2fLs2uIdaYAA

tpm:
  host: https://ac-mid.test.mi.com/tpm/x5/eam/mdm
  app-id: hyper_tpm
  app-secret@kc-sid: oa-asset.g # 用来加密password的sid
  app-secret: GDBz7La+tQBnoKndKo//C2luGJy3/q5AQ8HkPJ/pKDjwfyOxm1Rq8ArIAyGGlrEGY8AYEsPzDmq73kGLgL8ZFiJmifWz/xgQTCd2YJ38T166iHao4cmXkBgUJPoTmtdFq9W00Knb6WQT7D4wdVkA


# 飞书交互按钮
lark-button:
  share-url: https://mieam-asset.test.mioffice.cn/asset/account/shared-list

neptune-manager:
  apply-method: check_update
  #  正式环境，国内的
  apply-url: http://neptune.be.b2c.srv/api
  apply-url_sg: https://neptune.g.mi.com/api
  #  翻译平台appkey
  app-id: 0430f6b4a0994295be4df2bf10e0e0f4
  #  翻译平台x5key
  app-key: 8c879f76de
  #  是否正式环境
  queryProdVersion: true
  # 是否海外应用。 true：代表部署在海外机房服务 false：代表部署在国内机房服务
  sg: false