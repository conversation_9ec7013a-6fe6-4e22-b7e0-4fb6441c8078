---
spring:
  profiles: dev
  cloud:
    nacos:
      config:
        server-addr: http://dev-nacos.api.xiaomi.net:80
        namespace: info_mims_dev
        username: info_mims_dev
        password@kc-sid: oa-asset.g
        password: GBBmxlHx7Nk3EOI5HFmCIfrTGBIgMyhYwBxMwqhoXuhIpyX9F/8YEPdMleYB3E6lk8BQlJpFCiMYFJCOuG60GqnoC2QGW6LBHFvXpIAvAA==
        group: ASSET_GROUP
        file-extension: yml

---
spring:
  profiles: test
  cloud:
    nacos:
      config:
        server-addr: http://staging-nacos.api.xiaomi.net:80
        namespace: info_mims_asset_test
        username: info_mims_asset_test
        password@kc-sid: oa-asset.g
        password: GBD6R/+1A46qJiXkwh246x2IGBICObSrH6NHea44FMxcGtkfuP8YED+muKwc40b1mTNJjzCKfHUYFDUk84o71Ew6CB7Yt5y7K6UVh+cmAA==
        group: ASSET_GROUP
        file-extension: yml

---
spring:
  profiles: prod
  cloud:
    nacos:
      config:
        server-addr: http://cnbj1-nacos.api.xiaomi.net:80
        namespace: info_mims_asset_prod
        username: info_mims_asset_prod
        password@kc-sid: oa-asset.g
        password: GBDBK21xcR0IBjQNoU8Rek1FGBKu0JDWylVN0p/nw5wyN8wT5QEYEBIf2bSFU0D1qJQtoBdpT2YYFIjV09yVcqoplY513eiCKY1M4DVyAA==
        group: ASSET_GROUP
        file-extension: yml