# 测试环境、正式环境配置在nacos

codingrules:
  defaultList:
    - code: one_cate_code
      name: 一级分类编码
      connector: 无
      strategy: categroy
      format: 1
    - code: two_cate_code
      name: 二级分类编码
      connector: 无
      strategy: categroy
      format: 2
    - code: three_cate_code
      name: 三级分类编码
      connector: 无
      strategy: categroy
      format: 3
    - code: sku_code
      name: SKU
      connector: 无
      strategy: account_text
      format: ""
    - code: sap_cate_code
      name: SAP分类编码
      connector: 无
      strategy: account_text
      format: ""
    - code: company_code
      name: 公司代码
      connector: 无
      strategy: account_text
      format: ""
    - code: country
      name: 国家代码
      connector: 无
      strategy: account_text
      format: ""
    - code: check_date
      name: 验收日期-年月日（8位）
      connector: 无
      strategy: account_date
      format: yyyyMMdd
    - code: check_date
      name: 验收日期-年月（6位）
      connector: 无
      strategy: account_date
      format: yyyyMM
    - code: check_date
      name: 验收日期-年（4位）
      connector: 无
      strategy: account_date
      format: yyyy
    - code: update_date
      name: 更新日期-年月日（8位）
      connector: 无
      strategy: account_date
      format: yyyyMMdd
    - code: update_date
      name: 更新日期-年月（6位）
      connector: 无
      strategy: account_date
      format: yyyyMM
    - code: update_date
      name: 更新日期-年（4位）
      connector: 无
      strategy: account_date
      format: yyyy
    - code: update_date
      name: 更新日期-年（2位）
      connector: 无
      strategy: account_date
      format: yy
    - code: buy_date
      name: 采购日期-年月日（8位）
      connector: 无
      strategy: account_date
      format: yyyyMMdd
    - code: buy_date
      name: 采购日期-年月（6位）
      connector: 无
      strategy: account_date
      format: yyyyMM
    - code: buy_date
      name: 采购日期-年（4位）
      connector: 无
      strategy: account_date
      format: yyyy
    - code: serial_code
      name: 流水号
      connector: 5
      strategy: serial_code
      format: ""
    - code: custom
      name: 自定义文本
      connector: ""
      strategy: custom_text
      format: ""
  codeRuleList:
    - code: one_cate_code
      name: 一级分类编码
      connector: 无
      strategy: categroy
      format: 1
      businessLine: adm_pub
    - code: two_cate_code
      name: 二级分类编码
      connector: 无
      strategy: categroy
      format: 2
      businessLine: adm_pub
    - code: three_cate_code
      name: 三级分类编码
      connector: 无
      strategy: categroy
      format: 3
      businessLine: adm_pub
    - code: sku_code
      name: SKU
      connector: 无
      strategy: account_text
      format: ""
      businessLine: adm_pub
    - code: sap_cate_code
      name: SAP分类编码
      connector: 无
      strategy: account_text
      format: ""
      businessLine: adm_pub
    - code: company_code
      name: 公司代码
      connector: 无
      strategy: account_text
      format: ""
      businessLine: adm_pub
    - code: country
      name: 国家代码
      connector: 无
      strategy: account_text
      format: ""
      businessLine: adm_pub
    - code: check_date
      name: 验收日期-年月日（8位）
      connector: 无
      strategy: account_date
      format: yyyyMMdd
      businessLine: adm_pub
    - code: check_date
      name: 验收日期-年月（6位）
      connector: 无
      strategy: account_date
      format: yyyyMM
      businessLine: adm_pub
    - code: check_date
      name: 验收日期-年（4位）
      connector: 无
      strategy: account_date
      format: yyyy
      businessLine: adm_pub
    - code: update_date
      name: 更新日期-年月日（8位）
      connector: 无
      strategy: account_date
      format: yyyyMMdd
      businessLine: adm_pub
    - code: update_date
      name: 更新日期-年月（6位）
      connector: 无
      strategy: account_date
      format: yyyyMM
      businessLine: adm_pub
    - code: update_date
      name: 更新日期-年（4位）
      connector: 无
      strategy: account_date
      format: yyyy
      businessLine: adm_pub
    - code: update_date
      name: 更新日期-年（2位）
      connector: 无
      strategy: account_date
      format: yy
      businessLine: adm_pub
    - code: buy_date
      name: 采购日期-年月日（8位）
      connector: 无
      strategy: account_date
      format: yyyyMMdd
      businessLine: adm_pub
    - code: buy_date
      name: 采购日期-年月（6位）
      connector: 无
      strategy: account_date
      format: yyyyMM
      businessLine: adm_pub
    - code: buy_date
      name: 采购日期-年（4位）
      connector: 无
      strategy: account_date
      format: yyyy
      businessLine: adm_pub
    - code: serial_code
      name: 流水号
      connector: 5
      strategy: serial_code
      format: ""
      businessLine: adm_pub
    - code: custom
      name: 自定义文本
      connector: ""
      strategy: custom_text
      format: ""
      businessLine: adm_pub