package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.position.ImportStdPositionReq;
import com.mi.oa.asset.commons.config.api.position.PositionRes;
import com.mi.oa.asset.commons.config.api.position.PositionTreeRes;
import com.mi.oa.asset.commons.config.api.position.SavePositionReq;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import com.mi.oa.asset.commons.config.domain.position.entity.Position;
import com.mi.oa.asset.commons.config.domain.position.entity.PositionSpacePark;
import com.mi.oa.asset.commons.config.domain.position.enums.TpmMdmTypeEnums;
import com.mi.oa.asset.commons.config.domain.position.valobj.TpmMdmItem;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.infra.oaucf.space.rep.SpaceBuildingVO;
import com.mi.oa.infra.oaucf.space.rep.SpaceFloorVO;
import com.mi.oa.infra.oaucf.space.rep.SpaceParkVO;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/10/16 11:22
 */

@Mapper(componentModel = "spring")
public interface PositionConverter extends CommonConverter {

    @Mapping(source = "parkCode", target = "positionCode")
    @Mapping(source = "parkName", target = "positionName")
    @Mapping(target = "subList", expression = "java(stdBuildingToPositionTreeRes(source.getBuildingList(), source.getParkCode()))")
    PositionTreeRes stdParkToPositionTreeRes(SpaceParkVO source);

    List<PositionTreeRes> stdParkToPositionTreeRes(List<SpaceParkVO> source);

    @Mapping(source = "buildingCode", target = "positionCode")
    @Mapping(source = "buildingName", target = "positionName")
    @Mapping(target = "subList", expression = "java(stdFloorToPositionTreeRes(source.getFloorList(), source.getBuildingCode()))")
    PositionTreeRes stdBuildingToPositionTreeRes(SpaceBuildingVO source);

    List<PositionTreeRes> stdBuildingToPositionTreeRes(List<SpaceBuildingVO> source);

    default List<PositionTreeRes> stdBuildingToPositionTreeRes(List<SpaceBuildingVO> source, String parentCode) {
        if(CollectionUtils.isEmpty(source)) return null;

        List<PositionTreeRes> positionTreeResList = stdBuildingToPositionTreeRes(source);
        positionTreeResList.forEach(positionTreeRes -> positionTreeRes.setParentCode(parentCode));

        return positionTreeResList;
    }

    @Mapping(source = "floorCode", target = "positionCode")
    @Mapping(source = "floorName", target = "positionName")
    PositionTreeRes stdFloorToPositionTreeRes(SpaceFloorVO source);

    List<PositionTreeRes> stdFloorToPositionTreeRes(List<SpaceFloorVO> source);

    default List<PositionTreeRes> stdFloorToPositionTreeRes(List<SpaceFloorVO> source, String parentCode) {
        if(CollectionUtils.isEmpty(source)) return null;

        List<PositionTreeRes> positionTreeResList = stdFloorToPositionTreeRes(source);
        positionTreeResList.forEach(positionTreeRes -> positionTreeRes.setParentCode(parentCode));

        return positionTreeResList;
    }

    Position toPosition(SavePositionReq req);

    PositionRes toPositionRes(Position position);

    List<PositionRes> toPositionRes(List<Position> positionList);

    @Mapping(source = "source.positionCode", target = "positionCode")
    @Mapping(source = "source.positionName", target = "positionName")
    @Mapping(source = "source.positionCode", target = "outSysCode")
    Position toPosition(ImportStdPositionReq.Item source, BusinessLine businessLine, DataCreateSource dataSource);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(source = "source.positionName", target = "positionName")
    void updateFromStd(ImportStdPositionReq.Item source, @MappingTarget Position target);

    PositionTreeRes toPositionTreeRes(PositionRes source);

    PositionTreeRes parkToPositionTreeRes(PositionSpacePark source);

    List<PositionTreeRes> parkToPositionTreeResList(List<PositionSpacePark> source);

    List<PositionTreeRes> toPositionTreeRes(List<PositionRes> source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(source = "mdmCode", target = "positionCode")
    @Mapping(source = "mdmNameCn", target = "positionName")
    void toUpdateFromTpm(TpmMdmItem source, @MappingTarget Position target);

    default Position toPosition(TpmMdmItem source, BusinessLine businessLine, DataCreateSource dataSource, TpmMdmTypeEnums type) {
        Position position = Position.builder()
                .positionCode(source.getMdmCode())
                .positionName(source.getMdmNameCn())
                .positionType(type.getCode())
                .dataSource(dataSource)
                .disabled(Boolean.FALSE)
                .createTime(new Date())
                .businessLine(businessLine)
                .parentCode(source.getParentCode())
                .createUser("system")
                .build();
        if(TpmMdmTypeEnums.WORKSHOP.equals(type)){
            position.setParentCode(CommonConstant.M5);
        }
        return position;
    }
}
