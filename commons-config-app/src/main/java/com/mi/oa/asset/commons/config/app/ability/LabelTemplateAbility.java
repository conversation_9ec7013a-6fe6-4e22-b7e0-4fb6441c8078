package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.labelprint.GetLabelTemplateDataReq;
import com.mi.oa.asset.commons.config.api.labelprint.SaveLabelTemplateReq;
import com.mi.oa.asset.commons.config.app.converter.LabelTemplateConverter;
import com.mi.oa.asset.commons.config.domain.labeltemplate.entity.LabelTemplate;
import com.mi.oa.asset.commons.config.domain.labeltemplate.enums.LabelDataSource;
import com.mi.oa.asset.commons.config.domain.labeltemplate.repository.LabelTemplateRepo;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.mi.oa.asset.eam.jxs.enums.QueryConditionEnum;
import com.mi.oa.asset.eam.jxs.req.BaseQueryReq;
import com.mi.oa.asset.eam.jxs.req.QueryConditionReq;
import com.mi.oa.asset.eam.jxs.service.EamJxsService;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/02/14 19:03
 **/
@Slf4j
@Service
public class LabelTemplateAbility {

    @Resource
    private LabelTemplateRepo labelTemplateRepo;

    @Resource
    private LabelTemplateConverter labelTemplateConverter;

    @Autowired
    private EamJxsService eamJxsService;

    @Value("${label-template.funIds}")
    private String funIds;

    public void checkParams(SaveLabelTemplateReq saveLabelTemplateReq) {
        //参数检查模版
    }

    public List<LabelTemplate> getMarketLabelTemplateList() {
        return labelTemplateRepo.findListByType("system", true);
    }

    public List<LabelTemplate> getBusinessTemplateList(){
        return labelTemplateRepo.findListByType("business", false);
    }

    public Integer saveLabelTemplate(SaveLabelTemplateReq saveLabelTemplateReq){
        checkParams(saveLabelTemplateReq);
        LabelTemplate labelTemplate = labelTemplateConverter.toLabelTemplate(saveLabelTemplateReq);
        labelTemplateRepo.save(labelTemplate);
        return labelTemplate.getId();
    }

    public void updateLabelTemplate(SaveLabelTemplateReq saveLabelTemplateReq){
        checkParams(saveLabelTemplateReq);
        LabelTemplate labelTemplate = labelTemplateConverter.toLabelTemplate(saveLabelTemplateReq);
        labelTemplateRepo.update(labelTemplate);
    }

    public void toggleTemplateActivation(Integer templateId, boolean enable) {
        labelTemplateRepo.toggleTemplateActive(templateId, enable);
    }

    public List<?> batchGetTemplateData(GetLabelTemplateDataReq getLabelTemplateDataReq) {
        LabelTemplate labelTemplate = labelTemplateRepo.findById(getLabelTemplateDataReq.getTemplateId());
        //收货入库
        if(LabelDataSource.STOCK_SERIAL == LabelDataSource.getEnum(getLabelTemplateDataReq.getDataSource())){
            //构建通用查询条件,查询入库单明细
            BaseQueryReq queryReq = getAssetSerialBaseQueryReq(labelTemplate.getBusinessLine(), getLabelTemplateDataReq.getIds());
            return eamJxsService.baseQuery(queryReq).getData().getList();
        }
        //资产台账
        else if(LabelDataSource.ASSET_ACCOUNT == LabelDataSource.getEnum(getLabelTemplateDataReq.getDataSource())){
            //构建通用查询条件,查询入库单明细
            BaseQueryReq queryReq = getAssetAccountBaseQueryReq(labelTemplate.getBusinessLine(), getLabelTemplateDataReq.getIds());
            return eamJxsService.baseQuery(queryReq).getData().getList();
        }
        return Collections.emptyList();
    }

    @NotNull
    private BaseQueryReq getAssetSerialBaseQueryReq(String businessLine, List<String> ids) {
        BaseQueryReq queryReq = new BaseQueryReq();
        //功能查询设备sn列表
        queryReq.setFunId("amg_stock_serial");
        queryReq.setBusinessLine(Collections.singletonList(businessLine));
        List<QueryConditionReq> conditionReqs = new ArrayList<>();
        // 设备sn列表
        QueryConditionReq quantity = new QueryConditionReq();
        quantity.setFieldCode("id");
        quantity.setQueryCond(QueryConditionEnum.IN.getCode());
        quantity.setFieldValues(ids);
        conditionReqs.add(quantity);
        queryReq.setQueryConditionReqs(conditionReqs);
        return queryReq;
    }

    /**
     * 组装资产台账查询条件
     * @param businessLine
     * @param ids
     * @return
     */
    @NotNull
    private BaseQueryReq getAssetAccountBaseQueryReq(String businessLine, List<String> ids) {
        BaseQueryReq queryReq = new BaseQueryReq();
        //功能查询设备sn列表
        queryReq.setFunId("asset_account");
        queryReq.setBusinessLine(Collections.singletonList(businessLine));
        List<QueryConditionReq> conditionReqs = new ArrayList<>();
        // 设备sn列表
        QueryConditionReq quantity = new QueryConditionReq();
        quantity.setFieldCode("id");
        quantity.setQueryCond(QueryConditionEnum.IN.getCode());
        quantity.setFieldValues(ids);
        conditionReqs.add(quantity);
        queryReq.setQueryConditionReqs(conditionReqs);
        return queryReq;
    }

    public void deleteLabelTemplate(Integer templateId) {
        labelTemplateRepo.deleteById(templateId);
    }

    public void addUserDefaultTemplate(Integer templateId) {
        String authedUserName = AuthFacade.authedUserName();
        if (StringUtils.isBlank(authedUserName)) {
            throw new ErrorCodeException(ErrorCodes.MISSING_PARAMETER, "未获取到当前登录人信息");
        }
        LabelTemplate labelTemplate = labelTemplateRepo.findById(templateId);
        if(null == labelTemplate) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "模版不存在");
        }
        labelTemplateRepo.addUserDefaultTemplate(authedUserName, labelTemplate);
    }

    public List<LabelTemplate> getUserTemplateList(String businessLine) {
        String authedUserName = AuthFacade.authedUserName();
        if (StringUtils.isBlank(authedUserName)) {
            throw new ErrorCodeException(ErrorCodes.MISSING_PARAMETER, "未获取到当前登录人信息");
        }
        //获取业务线模版列表
        return labelTemplateRepo.getBusinessTemplateList(businessLine, authedUserName);
    }

    public void useLabelTemplate(Integer templateId) {
        String authedUserName = AuthFacade.authedUserName();
        if (StringUtils.isBlank(authedUserName)) {
            throw new ErrorCodeException(ErrorCodes.MISSING_PARAMETER, "未获取到当前登录人信息");
        }
        LabelTemplate labelTemplate = labelTemplateRepo.findById(templateId);
        if(null == labelTemplate) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "模版不存在");
        }
        labelTemplateRepo.addLastUsedTemplate(authedUserName, labelTemplate);
    }

    public List<LabelDataSource> getLabelDataSourceList() {
        List<LabelDataSource> labelDataSourceList = new ArrayList<>();
        for (LabelDataSource labelDataSource : LabelDataSource.values()) {
            if (funIds.contains(labelDataSource.getCode())) {
                labelDataSourceList.add(labelDataSource);
            }
        }
        return labelDataSourceList;
    }

}
