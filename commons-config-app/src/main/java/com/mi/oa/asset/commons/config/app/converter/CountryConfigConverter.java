package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigReq;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigRes;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryBusinessLineDo;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CountryConfigConverter extends CommonConverter {

    CountryConfigRes doToRes(CountryConfigDo countryConfigDo);

    List<CountryConfigRes> listDoToRes(List<CountryConfigDo> countryConfigDoList);

    CountryConfigDo reqToDo(CountryConfigReq countryConfigReq);

    List<CountryConfigDo> listReqToDo(List<CountryConfigReq> countryConfigReqList);

    CountryBusinessLineDo countryConfigDoToCountryBusinessLineDo(CountryConfigDo CountryConfigDo);
}
