package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.position.PositionTreeRes;
import com.mi.oa.asset.commons.config.app.converter.PositionConverter;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import com.mi.oa.asset.commons.config.domain.position.entity.Position;
import com.mi.oa.asset.commons.config.domain.position.entity.PositionSpacePark;
import com.mi.oa.asset.commons.config.domain.position.enums.TpmMdmTypeEnums;
import com.mi.oa.asset.commons.config.domain.position.repository.PositionRepo;
import com.mi.oa.asset.commons.config.domain.position.repository.PositionSpaceParkRepo;
import com.mi.oa.asset.commons.config.domain.position.valobj.TpmMdmItem;
import com.mi.oa.asset.commons.config.domain.position.valobj.TpmPage;
import com.mi.oa.asset.commons.config.infra.rpc.tpm.TpmClient;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.space.rep.SpaceParkVO;
import com.mi.oa.infra.oaucf.space.service.SpaceParkService;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2023/10/16 14:36
 */

@Service
@Slf4j
public class PositionAbility {

    private static final String ROOT_PATH = "0";

    @Resource
    private PositionRepo positionRepo;

    @Resource
    private TpmClient tpmClient;

    @Resource
    private PositionConverter positionConverter;

    @Resource
    private SpaceParkService spaceParkService;

    @Resource
    private PositionSpaceParkRepo positionSpaceParkRepo;

    public void syncSpacePosition() {
        BaseResp<List<SpaceParkVO>> data = spaceParkService.getParkBuildingFloorTree();
        if (BaseResp.CODE_SUCCESS != data.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, data.getMessage());
        }
        List<SpaceParkVO> parkList = data.getData();

        List<PositionSpacePark> positionSpaceParks = buildPositionSpaceParks(parkList);

        List<String> codes = positionSpaceParks.stream().map(PositionSpacePark::getPositionCode).collect(Collectors.toList());

        List<PositionSpacePark> exists = positionSpaceParkRepo.listPositionSpaceParks(codes);
        Map<String, PositionSpacePark> existsMap = new HashMap<>();
        exists.forEach(e -> existsMap.put(e.getPositionCode(), e));

        positionSpaceParks.forEach(i -> {
            PositionSpacePark existsOne = existsMap.get(i.getPositionCode());
            if (null != existsOne) {
                existsOne.setPositionId(i.getPositionId());
            }
        });

        positionSpaceParkRepo.savePositionSpaceParks(positionSpaceParks);
    }


    public List<PositionSpacePark> buildPositionSpaceParks(List<SpaceParkVO> parkList) {
        List<PositionSpacePark> positionSpaceParks = new ArrayList<>();

        parkList.forEach(p -> {
            PositionSpacePark positionSpacePark = new PositionSpacePark();
            positionSpacePark.setPositionCode(p.getParkCode());
            positionSpacePark.setPositionName(p.getParkName());
            positionSpacePark.setParentCode(ROOT_PATH);
            positionSpaceParks.add(positionSpacePark);
            if (CollectionUtils.isNotEmpty(p.getBuildingList())) {
                p.getBuildingList().forEach(b -> {
                    PositionSpacePark building = new PositionSpacePark();
                    building.setPositionCode(b.getBuildingCode());
                    building.setPositionName(b.getBuildingName());
                    building.setParentCode(p.getParkCode());
                    positionSpaceParks.add(building);
                    if (CollectionUtils.isNotEmpty(b.getFloorList())) {
                        b.getFloorList().forEach(f -> {
                            PositionSpacePark floor = new PositionSpacePark();
                            floor.setPositionCode(f.getFloorCode());
                            floor.setPositionName(f.getFloorName());
                            floor.setParentCode(b.getBuildingCode());
                            positionSpaceParks.add(floor);
                        });
                    }
                });
            }
        });
        return positionSpaceParks;
    }


    public List<PositionTreeRes> listPositionTree(List<SpaceParkVO> data) {
        List<PositionSpacePark> spaceParks = this.buildPositionSpaceParks(data);
        List<String> codes = spaceParks.stream().map(PositionSpacePark::getPositionCode).collect(Collectors.toList());
        List<PositionSpacePark> exists = positionSpaceParkRepo.listPositionSpaceParks(codes);
        Map<String, PositionSpacePark> existsMap = new HashMap<>();
        exists.forEach(e -> existsMap.put(e.getPositionCode(), e));
        spaceParks.forEach(i -> {
            PositionSpacePark existsOne = existsMap.get(i.getPositionCode());
            if (null != existsOne) {
                i.setPositionNameEn(existsOne.getPositionNameEn());
                i.setPositionName(existsOne.getPositionNameEn());
            }
        });

        List<PositionSpacePark> rootParks = spaceParks.stream().filter(p -> p.getParentCode().equals(ROOT_PATH)).collect(Collectors.toList());

        List<PositionTreeRes> roots = positionConverter.parkToPositionTreeResList(rootParks);
        roots.forEach(i -> loadPositionTree(i, spaceParks));
        return roots;
    }

    private void loadPositionTree(PositionTreeRes node, List<PositionSpacePark> all) {
        List<PositionTreeRes> subList = positionConverter.parkToPositionTreeResList(
                all.stream().filter(i -> node.getPositionCode().equals(i.getParentCode())).collect(Collectors.toList())
        );

        if (CollectionUtils.isEmpty(subList)) return;

        subList.forEach(sNode -> loadPositionTree(sNode, all));
        node.setSubList(subList);
    }

    public void checkPositionCodeExist(Position position) {
        if (null == position.getBusinessLine()) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线不存在");
        }

        if (null != positionRepo.getPositionByCode(position.getPositionCode(), position.getBusinessLine())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "编码已存在");
        }
    }

    public void loadFromParent(Position position) {
        if (position.getParentCode().equals(position.getPositionCode())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "上级位置不能是自己");
        }

        if (StringUtils.isBlank(position.getParentCode())) {
            position.setPositionPath(ROOT_PATH);
            return;
        }

        Position parent = loadPosition(position.getParentCode(), position.getBusinessLine());
        position.setPositionPath(parent.getPositionPath());
    }

    public Position loadPosition(String positionCode, BusinessLine businessLine) {
        if (StringUtils.isBlank(positionCode)) {
            return null;
        }

        Position position = positionRepo.getPositionByCode(positionCode, businessLine);
        if (null == position) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, String.format("位置【%s】不存在", positionCode));
        }
        return position;
    }

    public Position loadPosition(Integer positionId) {
        Position position = positionRepo.getPosition(positionId);
        if (null == position) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "位置不存在");
        }

        return position;
    }

    public void setStdPositionPath(List<Position> stdPositions) {
        // 按照层级顺序处理：先处理根节点，再处理子节点
        // 创建位置编码到位置的映射
        Map<String, Position> positionMap = stdPositions.stream()
                .collect(Collectors.toMap(Position::getPositionCode, Function.identity()));
        
        // 先处理根节点（parentCode为空或为"0"的位置）
        stdPositions.stream()
                .filter(p -> StringUtils.isBlank(p.getParentCode()) || "0".equals(p.getParentCode()))
                .forEach(p -> p.setPositionPath(ROOT_PATH + "-" + p.getPositionId()));
        
        // 然后按照层级顺序处理其他节点
        boolean hasChanges;
        do {
            hasChanges = false;
            for (Position position : stdPositions) {
                // 跳过已经处理过的根节点
                if (StringUtils.isBlank(position.getParentCode()) || "0".equals(position.getParentCode())) {
                    continue;
                }
                
                // 如果当前节点的 positionPath 还没有设置
                if (StringUtils.isBlank(position.getPositionPath())) {
                    // 查找父节点
                    Position parent = positionMap.get(position.getParentCode());
                    if (parent != null && StringUtils.isNotBlank(parent.getPositionPath())) {
                        // 父节点的 positionPath 已经设置，可以设置当前节点的 positionPath
                        position.setPositionPath(parent.getPositionPath() + "-" + position.getPositionId());
                        hasChanges = true;
                    }
                }
            }
        } while (hasChanges);
        
        // 检查是否还有未设置 positionPath 的节点
        List<Position> unsetPositions = stdPositions.stream()
                .filter(p -> StringUtils.isBlank(p.getPositionPath()))
                .collect(Collectors.toList());
        
        if (!unsetPositions.isEmpty()) {
            log.warn("Found positions with unset positionPath: {}", 
                    unsetPositions.stream().map(Position::getPositionCode).collect(Collectors.toList()));
        }
    }

    public void setTpmPositionPath(List<Position> stdPositions) {
        //大家电一级父节点以M5（M5空调工厂）为主节点
        Position root = stdPositions.stream().filter(p -> p.getPositionCode()
                .equals(CommonConstant.M5)).findFirst().orElse(null);

        if (null == root) return;

        stdPositions.forEach(k -> {
            if (k.getParentCode().equals(CommonConstant.M5)) {
                k.setPositionPath(root.getPositionPath() + "-" + k.getPositionId());
            }
            if (StringUtils.isBlank(k.getPositionPath())) {
                stdPositions.stream()
                        .filter(s -> s.getPositionCode().equals(k.getParentCode()))
                        .findFirst()
                        .ifPresent(p -> k.setPositionPath(p.getPositionPath() + "-" + k.getPositionId()));
            }
        });
        stdPositions.forEach(c -> {
            stdPositions.stream()
                    .filter(s -> s.getPositionCode().equals(c.getParentCode()))
                    .findFirst()
                    .ifPresent(p -> c.setPositionPath(p.getPositionPath() + "-" + c.getPositionId()));
        });
    }


    public void updatePositionPath(Position position, String prePositionPath) {
        // 更新positionPath
        positionRepo.updatePositionPath(position);

        // 更新下级的positionPath
        List<Position> subPositions = positionRepo.getPositionByPaths(Collections.singletonList(prePositionPath));
        subPositions.forEach(i -> {
            String newPath = i.getPositionPath().replace(prePositionPath, position.getPositionPath());
            i.setPositionPath(newPath);
        });
        positionRepo.savePositions(subPositions);
    }

    public void loadFullPositionName(Position position) {
        if (null == position) return;

        loadFullPositionName(Collections.singletonList(position));
    }

    public void loadFullPositionName(List<Position> positions) {
        Set<Integer> parentIds = new HashSet<>();
        positions.stream().map(Position::getPositionPath)
                .map(s -> s.split("-"))
                .map(s -> Stream.of(s).map(Integer::valueOf).collect(Collectors.toSet()))
                .forEach(parentIds::addAll);

        if (CollectionUtils.isEmpty(parentIds)) {
            return;
        }

        List<Position> parents = positionRepo.getPositions(new ArrayList<>(parentIds));
        positions.forEach(i -> {
            List<Integer> ids = Stream.of(i.getPositionPath().split("-")).map(Integer::valueOf).collect(Collectors.toList());
            // 按照ids顺序将parents排序,然后拼接成fullPositionName
            List<String> names = parents.stream()
                    .filter(p -> ids.contains(p.getPositionId()))
                    .sorted(Comparator.comparing(p -> ids.indexOf(p.getPositionId())))
                    .map(Position::getPositionName)
                    .collect(Collectors.toList());
            i.setFullPositionName(String.join("-", names));
            parents.stream().filter(p -> i.getParentCode().equals(p.getPositionCode())).findFirst().ifPresent(p -> i.setParentName(p.getPositionName()));
        });
    }

    public void syncTpmPosition(TpmMdmTypeEnums type) {
        int pageNum = 1;
        int pageSize = 50;
        int total = 0;
        int done = 0;

        log.info("Start sync position from TPM type:{}", type.getCode());
        do {
            TpmPage<List<TpmMdmItem>> res = tpmClient.getPositions(type.getCode(), pageNum++, pageSize);
            if (res == null) {
                log.info("No more data from TPM type:{},pageNum:{},pageSize:{}", type.getCode(), pageNum, pageSize);
                continue;
            }
            total = res.getTotal();
            List<TpmMdmItem> list = res.getList();
            done += list.size();

            List<String> codes = list.stream().map(TpmMdmItem::getMdmCode).collect(Collectors.toList());

            List<Position> exists = positionRepo.getPositionByCodes(codes, BusinessLine.HEA_FACTORY);
            Map<String, Position> existedMap = exists.stream().collect(
                    Collectors.toMap(k -> k.getPositionCode().toUpperCase(), Function.identity())
            );

            List<Position> allList = new ArrayList<>();
            list.forEach(i -> {
                Position existedOne = existedMap.get(i.getMdmCode().toUpperCase());
                if (null == existedOne) {
                    existedOne = positionConverter.toPosition(i, BusinessLine.HEA_FACTORY, DataCreateSource.SYS_TPM, type);
                } else {
                    positionConverter.toUpdateFromTpm(i, existedOne);
                }
                allList.add(existedOne);
            });
            positionRepo.savePositions(allList);

        } while (0 != total && done < total);

        log.info("End sync position from TPM type:{}", type.getCode());
    }

    public void updateTpmPositionPath(BusinessLine businessLine, DataCreateSource source) {

        List<Position> positions = positionRepo.getPositionByBusinessLine(businessLine, source);
        if (CollectionUtils.isEmpty(positions)) return;
        // 设置positionPath关系
        setTpmPositionPath(positions);
        // 更新positionPath
        positionRepo.savePositions(positions);
    }

}
