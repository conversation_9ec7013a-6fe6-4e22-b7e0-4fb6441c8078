package com.mi.oa.asset.commons.config.app.web.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.mi.oa.asset.commons.config.api.assetcategory.*;
import com.mi.oa.asset.commons.config.api.common.DelByIdsReq;
import com.mi.oa.asset.commons.config.app.converter.AssetCategoryConverter;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:28
 */

@HttpApiModule(value = "AssetCategoryController", apiController = SyncAssetCategoryController.class)
@RestController
@RequestMapping("/configs/sync")
public class SyncAssetCategoryController {

    @Resource
    private AssetCategoryProvider provider;

    @HttpApiDoc(apiName = "同步分类", value = "/configs/sync/asset-category", method = MiApiRequestMethod.POST)
    @GetMapping("/asset-category")
    public Result<Void> syncAssetCategory(@RequestParam String businessLine) {
        if(StringUtils.isBlank(businessLine)){
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请传入业务线！");
        }
        provider.syncAssetCategory(businessLine);

        return Result.success();
    }


}
