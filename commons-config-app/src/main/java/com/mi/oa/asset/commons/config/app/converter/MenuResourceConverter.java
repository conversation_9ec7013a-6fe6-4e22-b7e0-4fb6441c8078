package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.menuresource.MenuRes;
import com.mi.oa.asset.commons.config.api.menuresource.ResourceRes;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.ResourceResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.ResourceTreeResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/18 10:59
 **/
@Mapper(componentModel = "spring")
public interface MenuResourceConverter {

    /**
     * 转换
     *
     * @param treeResp
     * @return
     */
    @Mapping(source = "relationResourceFlatDtoList", target = "resourceList")
    @Mapping(target = "desc", expression = "java(converterDescription(treeResp.getDescription()))")
    MenuRes test(ResourceTreeResp treeResp);

    default MenuRes resourceTreeToRes(ResourceTreeResp treeResp) {
        return resourceTreeToRes(treeResp, EAMConstants.CHINESE);
    }


    default MenuRes resourceTreeToRes(ResourceTreeResp treeResp, String eamLanguage) {
        if (treeResp == null) {
            return null;
        }
        if (StringUtils.isEmpty(eamLanguage)) {
            eamLanguage = EAMConstants.CHINESE;
        }
        MenuRes menuRes = new MenuRes();
        menuRes.setResourceList(resourceRespListToResourceResList(treeResp.getRelationResourceFlatDtoList()));
        menuRes.setResourceCode(treeResp.getResourceCode());
        menuRes.setResourceName(treeResp.getResourceName());
        menuRes.setDescription(treeResp.getDescription());
        menuRes.setMenuRoute(treeResp.getMenuRoute());
        // 中英文转换
        String menuTitle = treeResp.getMenuTitle();
        menuRes.setMenuTitle(getMenuTitle(menuTitle, treeResp.getExt(), eamLanguage));
        menuRes.setIconUrl(treeResp.getIconUrl());
        menuRes.setIsShow(treeResp.getIsShow());
        menuRes.setParentCode(treeResp.getParentCode());
        menuRes.setSensitiveLevel(treeResp.getSensitiveLevel());
        menuRes.setDimensionCode(treeResp.getDimensionCode());
        menuRes.setSort(treeResp.getSort());
        menuRes.setChildren(resourceTreeListToResList(treeResp.getChildren(), eamLanguage));

        menuRes.setDesc(converterDescription(treeResp.getDescription()));
        return menuRes;
    }

    default ResourceRes resourceRespToResourceRes(ResourceResp resourceResp) {
        if (resourceResp == null) {
            return null;
        }

        ResourceRes resourceRes = new ResourceRes();

        resourceRes.setResourceCode(resourceResp.getResourceCode());
        resourceRes.setResourceName(resourceResp.getResourceName());
        resourceRes.setDescription(resourceResp.getDescription());

        return resourceRes;
    }

    default List<ResourceRes> resourceRespListToResourceResList(List<ResourceResp> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        List<ResourceRes> resourcelist = new ArrayList<ResourceRes>(list.size());
        for (ResourceResp resourceResp : list) {
            resourcelist.add(resourceRespToResourceRes(resourceResp));
        }

        return resourcelist;
    }


    /**
     * 转换
     *
     * @param treeResp
     * @return
     */
    default List<MenuRes> resourceTreeListToResList(List<ResourceTreeResp> treeResp) {
        return resourceTreeListToResList(treeResp, EAMConstants.CHINESE);
    }

    default List<MenuRes> resourceTreeListToResList(List<ResourceTreeResp> treeResp, String eamLanguage) {
        if (CollectionUtils.isEmpty(treeResp)) {
            return null;
        }

        List<MenuRes> list = new ArrayList<MenuRes>(treeResp.size());
        for (ResourceTreeResp resourceTreeResp : treeResp) {
            list.add(resourceTreeToRes(resourceTreeResp, eamLanguage));
        }

        return list;
    }

    /**
     * 转换
     *
     * @param description
     * @return
     */
    default Object converterDescription(String description) {
        if (StringUtils.isEmpty(description)) {
            return null;
        }
        return JacksonUtils.json2Bean(description, Map.class);
    }

    /**
     * 根据语言获取菜单标题
     *
     * @param ext         扩展字段
     * @param eamLanguage 语言
     * @return 菜单标题
     */
    default String getMenuTitle(String menuTitle, Map<String, Object> ext, String eamLanguage) {
        if (MapUtils.isEmpty(ext)) {
            return menuTitle;
        }
        if (EAMConstants.CHINESE.equals(eamLanguage)) {
            return ext.getOrDefault("zh", "").toString();
        } else {
            return ext.getOrDefault("en", "").toString();
        }
    }

}
