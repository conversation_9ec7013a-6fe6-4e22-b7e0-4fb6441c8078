package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.assetsku.*;
import com.mi.oa.asset.commons.config.api.common.DelByIdsReq;
import com.mi.oa.asset.commons.config.app.ability.AssetSkuAbility;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/9 19:08
 */

@HttpApiModule(value = "AssetSkuController", apiController = AssetSkuController.class)
@RestController
@RequestMapping("/configs/asset-sku")
@Slf4j
public class AssetSkuController {

    @Resource
    private AssetSkuProvider provider;

    @Resource
    private AssetSkuAbility assetSkuAbility;
    @Resource
    private HttpServletRequest request;

    @HttpApiDoc(apiName = "保存SKU", value = "/configs/asset-sku/save", method = MiApiRequestMethod.POST)
    @PostMapping("/save")
    public Result<Integer> save(@Valid @RequestBody SaveAssetSkuReq req) {
        return Result.success(provider.saveAssetSku(req));
    }

    @HttpApiDoc(apiName = "批量删除SKU", value = "/configs/asset-sku/delete", method = MiApiRequestMethod.POST)
    @PostMapping("/delete")
    public Result<Void> delete(@RequestBody DelAssetSkuReq req) {
        provider.deleteAssetSku(req);

        return Result.success();
    }

    @HttpApiDoc(apiName = "物料分页查询", value = "/configs/asset-sku/list/{cateId}", method = MiApiRequestMethod.GET)
    @GetMapping({"/list/{cateId}", "/list"})
    public Result<PageData<AssetSkuRes>> list(
            @PathVariable(required = false) Integer cateId,
            @RequestParam(required = false, defaultValue = "") String businessLine,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Boolean disabled
    ) {

        String language = request.getHeader(EAMConstants.LANGUAGE);
        PageData<AssetSkuRes> pageData;
        if (null != cateId) {
            pageData = provider.getAssetSkuPageData(cateId, Arrays.asList(businessLine.split(",")), keyword, pageNum, pageSize, disabled, true);
        } else {
            pageData = provider.getAssetSkuPageData(StringUtils.isBlank(businessLine) ?
                            Collections.emptyList() : Arrays.asList(businessLine.split(",")),
                    keyword, pageNum,
                    pageSize, disabled,
                    true);
        }
        if (!EAMConstants.CHINESE.equals(language) && org.apache.commons.lang3.StringUtils.isNotEmpty(language)) {
            pageData.getList().forEach(i -> i.setSkuName(i.getSkuNameEn()));
        }
        return Result.success(pageData);
    }

    @HttpApiDoc(apiName = "物料分页查询", value = "/configs/asset-sku/list/no-mi-goods/{cateId}", method = MiApiRequestMethod.GET)
    @GetMapping({"/list/no-mi-goods/{cateId}", "/list/no-mi-goods"})
    public Result<PageData<AssetSkuRes>> listNoMiGoods(
            @PathVariable(required = false) Integer cateId,
            @RequestParam(required = false, defaultValue = "") String businessLine,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Boolean disabled
    ) {
        String language = request.getHeader(EAMConstants.LANGUAGE);
        PageData<AssetSkuRes> pageData;
        if (null != cateId) {
            pageData = provider.getAssetSkuPageData(cateId, Arrays.asList(businessLine.split(",")), keyword, pageNum, pageSize, disabled, false);
        } else {
            pageData = provider.getAssetSkuPageData(Arrays.asList(businessLine.split(",")), keyword, pageNum, pageSize, disabled, false);
        }
        if (!EAMConstants.CHINESE.equals(language) && StringUtils.isNotEmpty(language)) {
            pageData.getList().forEach(i -> i.setSkuName(i.getSkuNameEn()));
        }
        return Result.success(pageData);
    }

    @HttpApiDoc(apiName = "SKU详情", value = "/configs/asset-sku/detail/{skuId}", method = MiApiRequestMethod.GET)
    @GetMapping("/detail/{skuId}")
    public Result<AssetSkuRes> detail(@PathVariable Integer skuId, @RequestParam(required = false) String businessLine) {
        AssetSkuRes assetSku = provider.getAssetSku(skuId);
        List<String> businessLines = new ArrayList<>();
        if(StringUtils.isNotEmpty(businessLine)){
            businessLines.addAll(Arrays.asList(businessLine.split(",")));
        }
        assetSku.getManages().removeIf(assetSkuMgRes -> !businessLines.contains(assetSkuMgRes.getBusinessLine()));
        return Result.success(assetSku);
    }

    @HttpApiDoc(apiName = "SKU详情", value = "/configs/asset-sku/detail-by-code/{skuCode}", method = MiApiRequestMethod.GET)
    @GetMapping("/detail-by-code/{skuCode}")
    public Result<AssetSkuRes> list(@PathVariable String skuCode) {
        return Result.success(provider.getAssetSkuBySkuCode(skuCode));
    }

    @HttpApiDoc(apiName = "删除管理线", value = "/configs/asset-sku/manage/delete", method = MiApiRequestMethod.POST)
    @PostMapping("/manage/delete")
    public Result<Void> deleteManage(@Valid @RequestBody DelSkuMgReq req) {
        provider.deleteAssetSkuManage(req.getSkuId(), req.getManageId());
        return Result.success();
    }

    @HttpApiDoc(apiName = "批量导入SKU", value = "/configs/asset-sku/import", method = MiApiRequestMethod.POST)
    @PostMapping("/import")
    public Result<Void> importSku(@HttpApiDocClassDefine(value = "file", description = "导入文件", required = true) @RequestParam("file")
                                  MultipartFile file, HttpServletResponse response) throws IOException {
        if (file == null) {
            Result<Void> result = Result.failure(ErrorCodes.BAD_PARAMETER, "导入文件不能为空");
            response.getWriter().println(JacksonUtils.bean2Json(result));
            return result;
        }
        provider.importSku(file, response);
        return Result.success();
    }

    @HttpApiDoc(apiName = "批量更新导入SKU", value = "/configs/asset-sku/update/import", method = MiApiRequestMethod.POST)
    @PostMapping("/update/import")
    public Result<Void> updateImportSku(@HttpApiDocClassDefine(value = "file", description = "导入文件", required = true) @RequestParam("file")
                                        MultipartFile file, HttpServletResponse response) throws IOException {
        if (file == null) {
            Result<Void> result = Result.failure(ErrorCodes.BAD_PARAMETER, "导入文件不能为空");
            response.getWriter().println(JacksonUtils.bean2Json(result));
            return result;
        }
        provider.updateImportSku(file, response);
        return Result.success();
    }

    @HttpApiDoc(apiName = "批量导出SKU", value = "/configs/asset-sku/export", method = MiApiRequestMethod.POST, description = "批量导出")
    @PostMapping(value = "/export")
    public Result<Void> exportSku(HttpServletResponse response, @Validated @RequestBody AssetSkuExportReq req) {
        req.setLanguage(request.getHeader(EAMConstants.LANGUAGE));
        provider.exportSku(response, req);
        return Result.success();
    }

    @HttpApiDoc(apiName = "竞品机上线初始化分类和物料数据", value = "/configs/asset-sku/init-data", method = MiApiRequestMethod.GET, description = "竞品机上线初始化分类和物料数据")
    @GetMapping(value = "/init-data")
    public Result<Void> initData() {
        assetSkuAbility.initData();
        return Result.success();
    }

    @HttpApiDoc(apiName = "跨业务线共享物料", value = "/configs/asset-sku/manage/shared-business-line", method = MiApiRequestMethod.POST, description = "跨业务线共享物料")
    @PostMapping(value = "/manage/shared-business-line")
    public Result<Void> sharedBusinessLine(@Validated @RequestBody SharedBusinessLineReq req){
        provider.sharedBusinessLine(req);
        return Result.success();
    }

    @HttpApiDoc(apiName = "创建物料", value = "/configs/asset-sku/push-mdm", method = MiApiRequestMethod.POST , description = "创建物料,推送sku到MDM平台 ")
    @PostMapping("/push-mdm")
    public Result<Void> pushToMdm(@RequestBody List<String> skuCode) throws Exception {
        provider.createPurchaseItem(skuCode);
        return Result.success();
    }

}
