package com.mi.oa.asset.commons.config.app.ability;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.common.enums.ColumnUseWay;
import com.mi.oa.asset.common.enums.FuncManageType;
import com.mi.oa.asset.commons.config.api.queryfield.FieldConfigInfo;
import com.mi.oa.asset.commons.config.api.queryfield.FieldConfigRes;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.commons.config.domain.queryfield.entity.FieldConfig;
import com.mi.oa.asset.eam.mybatis.CamelKeyMap;
import com.mi.oa.asset.eam.mybatis.FuncColumnMapper;
import com.mi.oa.asset.eam.mybatis.FuncColumnPo;
import com.mi.oa.asset.eam.mybatis.SqlConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant.COMMA;

/**
 * <AUTHOR>
 * @date 2024/1/9 19:40
 */

@Slf4j
@Service
@Data
@RefreshScope
@ConfigurationProperties(prefix = "fieldmanage")
public class QueryFieldAbility {

    private List<FieldConfig> fieldConfigs;  // 管理线下的类字段

    private List<FieldConfig> defaultFieldConfigs;  // 默认的列字段

    @Resource
    private FuncColumnMapper funcColumnMapper;

    public FieldConfig getDefaultConfig(String manageLine, String funId) {
        if (!CollectionUtils.isEmpty(fieldConfigs)) {
            // 查询当前管理线的
            List<FieldConfig> collect = fieldConfigs.stream().
                    filter(s -> {
                        List<String> list = Arrays.asList(s.getManageLine().split(COMMA));
                        for (String line : manageLine.split(COMMA)) {
                             if (list.contains(line) && s.getFunId().equals(funId)) return true;
                        }
                        return false;
                    })
                    .collect(Collectors.toList());

            if(!CollectionUtils.isEmpty(collect)){
                return collect.get(0);
            }
        }

        // 获取默认的配置
        if (!CollectionUtils.isEmpty(defaultFieldConfigs)) {
            List<FieldConfig> collect = defaultFieldConfigs.stream()
                    .filter(s -> s.getFunId().equals(funId))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(collect)) {
                return collect.get(0);
            }
        }

        log.error("没有找到管理线【{}】， 功能ID【{}】的默认列字段配置！", manageLine, funId);
        return null;
    }

    /**
     * 根据语言切换中英文
     * @param configRes
     */
    public void switchLanguage(FieldConfigRes configRes, String funcCode, String manageLine, String language) {
        if (Objects.isNull(configRes)) return;
        List<FieldConfigInfo> fieldConfig = configRes.getFieldConfig();
        List<FieldConfigInfo> extra = configRes.getExtra();
        if (CollectionUtils.isEmpty(fieldConfig) && CollectionUtils.isEmpty(extra)) return;
        List<FuncColumnPo> columnPos = funcColumnMapper.selectList(Wrappers.lambdaQuery(FuncColumnPo.class)
                .eq(FuncColumnPo::getUseWay, ColumnUseWay.TABLE.getCode())
                .eq(FuncColumnPo::getFuncCode, funcCode));
        if (CollectionUtils.isEmpty(columnPos)) return;

        Map<String, FuncColumnPo> system = new HashMap<>();
        Map<String, FuncColumnPo> manage = new HashMap<>();
        Map<String, FuncColumnPo> other = new HashMap<>();

        for (FuncColumnPo columnPo : columnPos) {
            Integer manageType = columnPo.getManageType();
            String code = columnPo.getCode();
            //去表名
            if (code.contains(SqlConstants.POINT)) {
                String[] codes = code.split("\\.");
                if (codes.length > 0) code = codes.length > 1 ? codes[1] : codes[0];
            }
            code = CamelKeyMap.underscoreToCamelCase(code);
            if (FuncManageType.SYSTEM.getCode().equals(manageType)) {
                system.put(code, columnPo);
            } else if (FuncManageType.MANAGE.getCode().equals(manageType) && columnPo.getBusinessLine().equals(manageLine)) {
                manage.put(code, columnPo);
            } else {
                other.put(code, columnPo);
            }
        }
        Map<String, FuncColumnPo> map = other;
        if (!manage.isEmpty()) {
            map = manage;
        } else if (!system.isEmpty()) {
            map = system;
        }
        if (map.isEmpty()) return;

        fieldConfig = CollectionUtils.isEmpty(fieldConfig) ? Collections.emptyList() : fieldConfig;
        extra = CollectionUtils.isEmpty(extra) ? Collections.emptyList() : extra;

        boolean isEn = "en-US".equals(language);
        for (FieldConfigInfo field : fieldConfig) {
            FuncColumnPo po = map.get(field.getDataKey());
            if (Objects.isNull(po)) continue;
            field.setDataValue(isEn ? po.getEnCode() : po.getName());
        }

        for (FieldConfigInfo field : extra) {
            FuncColumnPo po = map.get(field.getDataKey());
            if (Objects.isNull(po)) continue;
            field.setDataValue(isEn ? po.getEnCode() : po.getName());
        }
    }
}
