package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.project.ProjectCfgRes;
import com.mi.oa.asset.commons.config.api.project.SaveProjectCfgReq;
import com.mi.oa.asset.commons.config.domain.project.entity.ProjectCfg;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ProjectCfgAppConverter {

    ProjectCfgRes toRes(ProjectCfg source);

    List<ProjectCfgRes> toResList(List<ProjectCfg> source);

    ProjectCfg toDomain(SaveProjectCfgReq req);
}
