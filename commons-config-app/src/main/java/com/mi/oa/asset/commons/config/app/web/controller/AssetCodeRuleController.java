package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.assetcode.AssetCodeRuleProvider;
import com.mi.oa.asset.commons.config.api.assetcode.AssetCodeRuleReq;
import com.mi.oa.asset.commons.config.api.assetcode.AssetCodeRuleRes;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2024-06-25 16:18
 */
@Slf4j
@HttpApiModule(value = "AssetCodeRuleController", apiController = AssetCodeRuleController.class)
@RestController
@RequestMapping("/configs/asset-code-rule")
public class AssetCodeRuleController {

    @Resource
    private AssetCodeRuleProvider assetCodeRuleProvider;

    @HttpApiDoc(apiName = "根据业务线获取资产编码规则模板和已选择项目", value = "/configs/asset-code-rule/query", method = MiApiRequestMethod.GET, description = "根据业务线获取资产编码规则模板和已选择项目")
    @GetMapping(value = "/query")
    public Result<AssetCodeRuleRes> getByBusinessLine(@RequestParam(value = "businessLine") String businessLine) {

        return Result.success(assetCodeRuleProvider.getByBusinessLine(businessLine));
    }

    @HttpApiDoc(apiName = "保存资产编码规则配置", value = "/configs/asset-code-rule/save", method = MiApiRequestMethod.POST, description = "保存资产编码规则配置")
    @PostMapping(value = "/save")
    public Result<Void> save(@RequestBody @Valid AssetCodeRuleReq assetCodeRuleReq) {

        assetCodeRuleProvider.save(assetCodeRuleReq);
        return Result.success();
    }
}
