package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxReq;
import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxRes;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryTaxDo;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CountryTaxConverter extends CommonConverter {

    CountryTaxRes doToRes(CountryTaxDo countryTaxDo);

    List<CountryTaxRes> listDoToRes(List<CountryTaxDo> countryTaxDoList);

    CountryTaxDo reqToDo(CountryTaxReq countryTaxReq);

    List<CountryTaxDo> listReqToDo(List<CountryTaxReq> countryTaxReqList);


}
