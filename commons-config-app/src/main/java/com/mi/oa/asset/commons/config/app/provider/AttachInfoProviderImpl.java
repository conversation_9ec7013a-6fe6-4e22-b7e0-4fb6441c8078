package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.attach.AttachInfoProvider;
import com.mi.oa.asset.commons.config.api.attach.AttachInfoReq;
import com.mi.oa.asset.commons.config.api.attach.AttachInfoRes;
import com.mi.oa.asset.commons.config.api.attach.BatchAttachRes;
import com.mi.oa.asset.commons.config.app.ability.AttachInfoAbility;
import com.mi.oa.asset.commons.config.domain.common.repository.AttachInfoRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @autho zhan
 * @date 2023-10-26 15:23
 */
@Slf4j
@DubboService
public class AttachInfoProviderImpl implements AttachInfoProvider {

    @Resource
    private AttachInfoAbility ability;
    @Resource
    private AttachInfoRepo attachInfoRepo;
    @Override
    public void batchSaveAttach(List<AttachInfoReq> attachInfoReqList) {
        ability.batchSaveAttach(attachInfoReqList);
    }

    @Override
    public void deleteAttach(Integer recordId, String recordType) {
        ability.deleteAttach(recordId, recordType);
    }

    @Override
    public void deleteAttach(Integer recordId, List<String> recordType) {
        attachInfoRepo.deleteAttach(recordId, recordType);
    }

    @Override
    public void batchDeleteAttach(List<Integer> recordIds, String recordType) {
        attachInfoRepo.batchDeleteAttach(recordIds, recordType);
    }

    @Override
    public void deleteAttach(Integer recordId, List<String> attachFileNames, String recordType) {
        ability.deleteAttach(recordId,attachFileNames, recordType);
    }

    @Override
    public void deleteById(List<Integer> idList) {
        ability.deleteById(idList);
    }

    @Override
    public List<AttachInfoRes> getAttachList(Integer recordId, String recordType) {
        return ability.getAttachList(Arrays.asList(recordId), recordType);
    }

    @Override
    public List<AttachInfoRes> listByTypes(Integer recordId, List<String> recordTypes) {
        return ability.listByTypes(recordId, recordTypes);
    }

    @Override
    public List<BatchAttachRes> getBatchAttach(List<Integer> recordIds, String recordType) {
        List<AttachInfoRes> attachInfoRes = ability.getAttachList(recordIds, recordType);
        Map<Integer, List<AttachInfoRes>> attachListMap = attachInfoRes.stream().collect(Collectors.groupingBy(AttachInfoRes::getRecordId));
        List<BatchAttachRes> batchAttachRes = new ArrayList<>();
        for (Map.Entry<Integer, List<AttachInfoRes>> entry : attachListMap.entrySet()) {
            BatchAttachRes attachRes = BatchAttachRes.builder().attachInfoList(entry.getValue()).recordId(entry.getKey()).build();
            batchAttachRes.add(attachRes);
        }
        return batchAttachRes;
    }
}
