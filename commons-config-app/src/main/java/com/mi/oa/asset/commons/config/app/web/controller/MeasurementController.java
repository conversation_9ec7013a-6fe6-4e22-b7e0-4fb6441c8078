package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.measurement.MeasurementProvider;
import com.mi.oa.asset.commons.config.api.measurement.MeasurementUnitQueryReq;
import com.mi.oa.asset.commons.config.api.measurement.MeasurementUnitRes;
import com.mi.oa.asset.commons.config.app.scheduler.SyncMeasurement;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@HttpApiModule(value = "MeasurementController", apiController = MeasurementController.class)
@RestController
@RequestMapping("/configs/measurement")
public class MeasurementController {

    @Resource
    private MeasurementProvider provider;

    @Autowired
    private SyncMeasurement syncMeasurement;

    @Resource
    private HttpServletRequest request;

    @HttpApiDoc(apiName = "计量单位分页查询", value = "/configs/measurement/list", method = MiApiRequestMethod.GET)
    @GetMapping({"/list"})
    public Result<PageData<MeasurementUnitRes>> list(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize
    ) {
        String language = request.getHeader(EAMConstants.LANGUAGE);
        MeasurementUnitQueryReq req = MeasurementUnitQueryReq.builder()
                .keyword(keyword)
                .build();
        PageData<MeasurementUnitRes> pageData = provider.getMeasurementUnitPageData(req, pageNum, pageSize);
        if (!EAMConstants.CHINESE.equals(language) && StringUtils.isNotEmpty(language)) {
            pageData.getList().forEach(i -> i.setMeasureName(i.getMeasureNameEn()));
        }
        return Result.success(pageData);
    }

    @PostMapping({"/sync"})
    public Result<Void> sync() {
        syncMeasurement.execute();
        return Result.success();
    }
}
