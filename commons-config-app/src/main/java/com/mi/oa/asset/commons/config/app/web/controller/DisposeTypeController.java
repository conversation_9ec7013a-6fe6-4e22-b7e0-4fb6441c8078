package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.assetdispose.AssetDisposeTypeProvider;
import com.mi.oa.asset.commons.config.api.assetdispose.SaveAssetDisposeTypeReq;
import com.mi.oa.asset.commons.config.api.common.DelByIdsReq;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/5/14 14:04
 * @description 资产处置类型
 */
@HttpApiModule(value = "DisposeTypeController", apiController = DisposeTypeController.class)
@RestController
@RequestMapping("/configs/dispose-type")
public class DisposeTypeController {
    @Resource
    private AssetDisposeTypeProvider disposeTypeProvider;

    @HttpApiDoc(apiName = "保存（修改）处置类型", value = "/configs/dispose-type/save", method = MiApiRequestMethod.POST)
    @PostMapping("/save")
    public Result<Integer> save(@Valid @RequestBody SaveAssetDisposeTypeReq req, BindingResult result) {
        if(result.hasErrors()) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, result.getAllErrors().get(0).getDefaultMessage());
        }
        Integer disposeId = disposeTypeProvider.saveAssetDisposeType(req);
        return Result.success(disposeId);
    }

    @HttpApiDoc(apiName = "批量删除处置类型", value = "/configs/dispose-type/delete", method = MiApiRequestMethod.POST)
    @PostMapping("/delete")
    public Result<Void> delete(@RequestBody DelByIdsReq req) {
        disposeTypeProvider.deleteAssetDisposeType(req.getIds());
        return Result.success();
    }

}
