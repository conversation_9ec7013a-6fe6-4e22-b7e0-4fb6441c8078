package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.warehouse.SaveWarehouseReq;
import com.mi.oa.asset.commons.config.api.warehouse.WarehouseProvider;
import com.mi.oa.asset.commons.config.api.warehouse.WarehouseRes;
import com.mi.oa.asset.commons.config.app.ability.AddressAbility;
import com.mi.oa.asset.commons.config.app.ability.WarehouseAbility;
import com.mi.oa.asset.commons.config.app.converter.AppWarehouseConverter;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.Warehouse;
import com.mi.oa.asset.commons.config.domain.warehouse.repository.WarehouseRepo;
import com.mi.oa.asset.commons.config.domain.warehouse.valobj.WarehouseData;
import com.mi.oa.asset.eam.utils.ExcelUtils;
import com.mi.oa.asset.excel.enums.ExcelLanguageContextlEnum;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@DubboService
public class WarehouseProviderImpl implements WarehouseProvider {

    @Resource
    private AppWarehouseConverter converter;
    @Resource
    private WarehouseRepo warehouseRepo;
    @Resource
    private AddressAbility addressAbility;
    @Resource
    private WarehouseAbility warehouseAbility;

    @Override
    public Integer saveWarehouse(SaveWarehouseReq req) {
        Warehouse warehouse = converter.toWarehouse(req);
        String code = req.getHouseCode();
        if (StringUtils.isEmpty(code)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "仓库编码不能为空");
        }
        Warehouse old = warehouseRepo.findByCode(code);
        Integer id = req.getId();
        if (id != null) {//更新可能会改编码
            if (old != null && !id.equals(old.getId())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "仓库编码不能重复");
            }
        } else {
            if (old != null) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "仓库编码不能重复");
            }
        }
        warehouseRepo.saveWarehouse(warehouse);
        return warehouse.getId();
    }

    @Override
    public void deleteWarehouse(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) return;
        warehouseRepo.deleteWarehouse(ids);
    }

    @Override
    public WarehouseRes findByCode(String code) {
        List<Warehouse> list = warehouseRepo.getByCode(code);
        if (CollectionUtils.isEmpty(list)) return null;
        return converter.toWarehouseRes(list.get(0));
    }

    @Override
    public List<WarehouseRes> findByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();
        List<Warehouse> list = warehouseRepo.findByCodes(codes);
        return converter.toWarehouseResList(list);
    }

    // 根据业务线和服务类型查询生效的仓库
    @Override
    public List<WarehouseRes> getBatchByServiceType(String businessLine, String serviceType, String key) {
        if (StringUtils.isEmpty(businessLine) || StringUtils.isEmpty(serviceType)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "查询参数必填");
        }
        return converter.toWarehouseResList(warehouseRepo.getBatchByServiceType(businessLine, serviceType, key));
    }


    @SneakyThrows
    @Override
    public void importWarehouse(MultipartFile file) {
        InputStream inputStream = file.getInputStream();
        List<WarehouseData> warehouseData = ExcelUtils.readExcel(inputStream, WarehouseData.class, 2);
        if (CollectionUtils.isEmpty(warehouseData)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "导入参数不能为空");
        }
        List<Warehouse> list = converter.excelToHouses(warehouseData, ExcelLanguageContextlEnum.current());
        warehouseAbility.checkImport(list);

        for (Warehouse warehouse : list) {
            warehouseAbility.fillHouseInfo(warehouse);
        }
        List<String> areaList = warehouseData.stream().map(WarehouseData::getArea).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, String> addressMap = addressAbility.getAreaCodeMapByAddress(areaList);
        if (!CollectionUtils.isEmpty(addressMap)) {
            list.forEach(warehouse -> {
                warehouse.setArea(addressMap.get(warehouse.getArea()));
            });
        }
        warehouseRepo.saveWarehouse(list);
    }

    @Override
    public List<WarehouseRes> getByBusinessLine(String businessLine) {
        if (StringUtils.isEmpty(businessLine)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线不能为空");
        }
        List<Warehouse> list = warehouseRepo.getByBusinessLine(businessLine);
        return converter.toWarehouseResList(list);
    }
}
