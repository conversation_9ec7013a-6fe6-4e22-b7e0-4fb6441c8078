package com.mi.oa.asset.commons.config.app.ability;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.account.api.assetaccount.AssetAccountProvider;
import com.mi.oa.asset.account.api.assetaccount.res.AssetAccountRes;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.MaterialType;
import com.mi.oa.asset.commons.config.api.assetsku.AssetSkuRes;
import com.mi.oa.asset.commons.config.api.assetsku.ManageModel;
import com.mi.oa.asset.commons.config.api.assetsku.SnTypeEnum;
import com.mi.oa.asset.commons.config.api.serialcode.SerialCodeProvider;
import com.mi.oa.asset.commons.config.app.converter.AssetSkuConverter;
import com.mi.oa.asset.commons.config.app.converter.AssetSkuMgConverter;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.AssetCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.repository.AssetCategoryRepo;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSku;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSkuManage;
import com.mi.oa.asset.commons.config.domain.assetsku.enums.SkuSerialCodeConfig;
import com.mi.oa.asset.commons.config.domain.assetsku.enums.StockCostEnum;
import com.mi.oa.asset.commons.config.domain.assetsku.repository.AssetSkuRepo;
import com.mi.oa.asset.commons.config.domain.assetsku.valobj.AssetSkuImportData;
import com.mi.oa.asset.commons.config.domain.assetsku.valobj.AssetSkuUpdateData;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryConfigRepo;
import com.mi.oa.asset.commons.config.domain.mesurement.entity.MeasurementUnit;
import com.mi.oa.asset.commons.config.domain.mesurement.repository.MeasurementRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetSkuManagePo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.AssetSkuPo;
import com.mi.oa.asset.commons.config.infra.repository.impl.AssetSkuManageRepoImpl;
import com.mi.oa.asset.commons.config.infra.repository.impl.AssetSkuRepoImpl;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mi.oa.asset.common.enums.EAMConstants.ENGLISH;

/**
 * <AUTHOR>
 * @Date 2023/10/9 17:44
 */

@Service
@Slf4j
public class AssetSkuAbility {

    private static final String[] SKU_CODE_ERROR =  {"物料编码{0}重复", "Duplicate Material Code {0}"};
    private static final String[] SKU_CODE_UPDATE_ERROR =  {"物料编码{0}不存在", "Material code {0} does not exist"};
    private static final String[] BUSINESS_SKU_CODE_ERROR =  {"相同业务线，物料编码{0}重复", "Same business line, Duplicate Material Code {0}"};
    private static final String[] ASSET_CATE_ERROR =  {"分类{0}不存在", "Category code {0} does not exist"};
    private static final String[] MEASURE_CODE_ERROR =  {"计量单位{0}不存在", "Units of Measure {0} does not exist"};
    private static final String[] COUNTRY_CODE_ERROR =  {"国家编码{0}不存在", "Country code {0} does not exist"};
    private static final String[] COUNTRY_CODE_EMPTY_ERROR =  {"国家编码不能为空", "Country code cannot be empty"};

    @Resource
    private SerialCodeProvider serialCodeProvider;

    @Resource
    private AssetCategoryAbility assetCategoryAbility;

    @Resource
    private AssetSkuRepo assetSkuRepo;

    @Resource
    private MeasurementRepo measurementRepo;

    @DubboReference(check = false)
    private AssetAccountProvider assetAccountProvider;
    @Resource
    private AssetSkuConverter converter;
    @Resource
    private AssetCategoryRepo assetCategoryRepo;

    @Resource
    private AssetSkuRepoImpl assetSkuRepoImpl;

    @Resource
    private AssetSkuManageRepoImpl assetSkuManageRepo;

    @Resource
    private AssetSkuMgConverter assetSkuMgConverter;

    @Resource
    private CountryConfigRepo countryConfigRepo;

    public void checkParams(AssetSku assetSku) {
        if (CollectionUtils.isEmpty(assetSku.getManages()))
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "管理信息不能为空");

        long count = assetSku.getManages().stream().map(AssetSkuManage::getBusinessLine).distinct().count();
        if (count != assetSku.getManages().size()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "管理信息业务线不允许重复");
        }
        if (assetSku.getSkuId() != null) {
            AssetSku sku = assetSkuRepo.getAssetSku(assetSku.getSkuId());
            if (sku == null) {
                throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "无效参数");
            }
            //不相等校验新的物料编码是否唯一
            if (!sku.getSkuCode().equals(assetSku.getSkuCode())) {
                if (assetSkuRepo.isExists(assetSku.getSkuCode())) {
                    throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "SKU已存在，请重新设置！");
                }
            }
        } else {
            if (StringUtils.isNotBlank(assetSku.getSkuCode()) && assetSkuRepo.isExists(assetSku.getSkuCode())) {
                throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "SKU已存在，请重新设置！");
            }
        }
    }

    public void genSkuCode(AssetSku assetSku) {
        if (StringUtils.isNotBlank(assetSku.getSkuCode())) {
            return;
        }
        SkuSerialCodeConfig config = SkuSerialCodeConfig.getPrefix(assetSku.getBusinessLine());
        if (null == config) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "编码前缀未配置");
        }
        String skuCode = serialCodeProvider.genSerialCodeWithIndex(config.getPrefix(), config.getZeroPadSize(), "SKU_CODE:" + config.getZeroPadSize());
        assetSku.setSkuCode(skuCode);
    }

    public void genSkuCode(List<AssetSku> assetSkus) {
        if (CollectionUtils.isEmpty(assetSkus)) return;
        //需要生成的物料按业务线分组
        Map<BusinessLine, List<AssetSku>> businessLineMap = assetSkus.stream().filter(f -> StringUtils.isBlank(f.getSkuCode())).collect(Collectors.groupingBy(AssetSku::getBusinessLine));
        for (Map.Entry<BusinessLine, List<AssetSku>> entry : businessLineMap.entrySet()) {
            SkuSerialCodeConfig config = SkuSerialCodeConfig.getPrefix(entry.getKey());
            List<AssetSku> assetSkuList = entry.getValue();
            List<String> skuCodes = serialCodeProvider.genSerialCodesWithIndex(config.getPrefix(), config.getZeroPadSize(), assetSkuList.size(), "SKU_CODE");
            for (int i = 0; i < assetSkuList.size(); i++) {
                assetSkuList.get(i).setSkuCode(skuCodes.get(i));
            }
        }
    }

    public void loadAssetCategoryManage(AssetSku assetSku) {
        if (assetSku == null || CollectionUtils.isEmpty(assetSku.getManages())) return;
        List<Integer> cateIds = assetSku.getManages().stream().map(AssetSkuManage::getCateId).collect(Collectors.toList());
        List<AssetCategory> assetCategorys = assetCategoryAbility.loadAssetCategorys(cateIds);
        Map<Integer, AssetCategory> categoryMap = assetCategorys.stream().collect(Collectors.toMap(AssetCategory::getCateId, Function.identity()));
        assetSku.getManages().forEach(m -> {
            AssetCategory assetCategory = categoryMap.get(m.getCateId());
            if (assetCategory == null)
                throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "管理信息中分类不存在:" + m.getCateId());
            m.setCateId(assetCategory.getCateId());
            m.setCateCode(assetCategory.getCateCode());
            m.setCateName(assetCategory.getCateName());
        });
        AssetSkuManage skuManage = assetSku.getManages().get(0);
        //取管理信息下第一条业务线和分类信息
        assetSku.setBusinessLine(skuManage.getBusinessLine());
        assetSku.setCateId(skuManage.getCateId());
        assetSku.setCateCode(skuManage.getCateCode());
        assetSku.setCateName(skuManage.getCateName());
        assetSku.setMaterialType(skuManage.getMaterialType());
    }

    public boolean hasSku(List<Integer> cateIds) {
        return assetSkuRepo.getAssetSkuCountsByCategory(cateIds) > 0;
    }

    public void checkCanDelete(AssetSku sku) {
        List<AssetAccountRes> assetAccounts = assetAccountProvider.getBySkuCode(sku.getSkuCode(), sku.getBusinessLine().getCode());

        if (CollectionUtils.isNotEmpty(assetAccounts)) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "请先移除物料数据中关联的资产台账");
        }
    }

    public void initData() {
        log.info("data init start");
        long startTime = System.currentTimeMillis();
        //物料数据清洗
        List<AssetSkuPo> skuPoList = assetSkuRepoImpl.list(Wrappers.lambdaQuery(AssetSkuPo.class)
                .eq(AssetSkuPo::getIsDeleted, 0));
        List<AssetSkuManagePo> skuManagePoList = new ArrayList<>();
        for (AssetSkuPo skuPo : skuPoList) {
            skuManagePoList.add(buildSkuManage(skuPo));
        }
        assetSkuManageRepo.saveBatch(skuManagePoList);

        log.info("data init end:{}ms", System.currentTimeMillis() - startTime);
    }

    private AssetSkuManagePo buildSkuManage(AssetSkuPo skuPo) {
        AssetSkuManagePo po = new AssetSkuManagePo();
        po.setSkuId(skuPo.getId());
        po.setBusinessLine(skuPo.getBusinessLine());
        po.setCateId(skuPo.getCateId());
        po.setCateCode(skuPo.getCateCode());
        po.setCateName(skuPo.getCateName());
        po.setMgModel(ManageModel.ASSET_ACCOUNT.getCode());
        if (skuPo.getIsMultipleManage() == 1) {
            po.setSerialMg(SnTypeEnum.MULTIPLE.getCode());
        }
        if (skuPo.getIsSn() == 1) {
            po.setSerialMg(SnTypeEnum.SN.getCode());
        }
        po.setCosting(StockCostEnum.MOVING_AVG.getCode());
        po.setSecureQuantity(new BigDecimal("0"));
        po.setHighestQuantity(new BigDecimal("0"));
        po.setMiniQuantity(new BigDecimal("0"));
        po.setMaterialType(skuPo.getMaterialType());
        po.setIsDeleted(0);
        po.setCreateUser(skuPo.getCreateUser());
        po.setCreateUserName(skuPo.getCreateUserName());
        po.setCreateTime(skuPo.getCreateTime());
        po.setUpdateUser(skuPo.getUpdateUser());
        po.setUpdateUserName(skuPo.getUpdateUserName());
        po.setUpdateTime(skuPo.getUpdateTime());
        return po;
    }

    public void saveSkuData(List<AssetSkuImportData> assetSkuImportData, Map<String, Object> cacheMap) {
        List<MeasurementUnit> existMeasures = (List<MeasurementUnit>) cacheMap.getOrDefault(CommonConstant.MEASURE, new HashSet<>());
        Map<String, MeasurementUnit> measureNameMap = existMeasures.stream().collect(Collectors.toMap(MeasurementUnit::getMeasureName, Function.identity()));
        Map<String, Map<String, AssetCategory>> cateMap = (Map<String, Map<String, AssetCategory>>) cacheMap.getOrDefault(CommonConstant.ASSET_CATE, new HashSet<>());

        // 计量单位填充
        for (AssetSkuImportData assetSkuImportDatum : assetSkuImportData) {
            String measureName = assetSkuImportDatum.getMeasureName();
            MeasurementUnit measurementUnit = measureNameMap.get(measureName);
            if (measurementUnit != null) {
                assetSkuImportDatum.setMeasureCode(measurementUnit.getMeasureCode());
                assetSkuImportDatum.setMeasureName(measurementUnit.getMeasureName());
            }
            String businessLine = assetSkuImportDatum.getBusinessLine();
            Map<String, AssetCategory> categories = cateMap.getOrDefault(businessLine, new HashMap<>());
            String cateCode = assetSkuImportDatum.getCateCode();
            AssetCategory assetCategory = categories.get(cateCode);
            if (assetCategory != null) {
                assetSkuImportDatum.setCateId(assetCategory.getCateId());
                assetSkuImportDatum.setCateCode(assetCategory.getCateCode());
                assetSkuImportDatum.setCateName(assetCategory.getCateName());
            }
        }

        // 构建SKU信息，管理信息
        List<AssetSku> assetSkus = buildSkuInfo(assetSkuImportData, cateMap);
        assetSkuRepo.batchSaveAssetSku(assetSkus);

        // 填充管理信息skuId
        List<AssetSkuManage> assetSkuManages = setManageIds(assetSkus);

        //生成管理信息
        assetSkuManageRepo.saveAssetSkuManage(assetSkuManages);
    }

    public void updateSkuData(List<AssetSkuUpdateData> assetSkuUpdateData, Map<String, Object> cacheMap) {
        List<MeasurementUnit> existMeasures = (List<MeasurementUnit>) cacheMap.getOrDefault(CommonConstant.MEASURE, new HashSet<>());
        Map<String, MeasurementUnit> measureNameMap = existMeasures.stream().collect(Collectors.toMap(MeasurementUnit::getMeasureName, Function.identity()));
        Map<String, Map<String, AssetCategory>> cateMap = (Map<String, Map<String, AssetCategory>>) cacheMap.getOrDefault(CommonConstant.ASSET_CATE, new HashSet<>());

        // 计量单位填充
        for (AssetSkuUpdateData assetSkuUpdateDatum : assetSkuUpdateData) {
            String measureName = assetSkuUpdateDatum.getMeasureName();
            MeasurementUnit measurementUnit = measureNameMap.get(measureName);
            if (measurementUnit != null) {
                assetSkuUpdateDatum.setMeasureCode(measurementUnit.getMeasureCode());
                assetSkuUpdateDatum.setMeasureName(measurementUnit.getMeasureName());
            }
            String businessLine = assetSkuUpdateDatum.getBusinessLine();
            Map<String, AssetCategory> categories = cateMap.getOrDefault(businessLine, new HashMap<>());
            String cateCode = assetSkuUpdateDatum.getCateCode();
            AssetCategory assetCategory = categories.get(cateCode);
            if (assetCategory != null) {
                assetSkuUpdateDatum.setCateId(assetCategory.getCateId());
                assetSkuUpdateDatum.setCateCode(assetCategory.getCateCode());
                assetSkuUpdateDatum.setCateName(assetCategory.getCateName());
            }
        }

        // 构建SKU信息，管理信息
        List<AssetSku> assetSkus = buildUpdateSkuInfo(assetSkuUpdateData, cateMap);
        assetSkuRepo.updateBatchAssetSku(assetSkus);
        List<AssetSkuManage> assetSkuManages = new ArrayList<>();
        for (AssetSku skus : assetSkus) {
            assetSkuManages.addAll(skus.getManages());
        }

        //生成管理信息
        assetSkuManageRepo.saveAssetSkuManage(assetSkuManages);
    }

    public List<AssetSkuManage> setManageIds(List<AssetSku> assetSkus) {
        List<AssetSkuManage> manageList = new ArrayList<>();

        // assetSkus 转换为SkuCode,skuId 的map
        Map<String, Integer> skuIdMap = assetSkus.stream().collect(Collectors.toMap(AssetSku::getSkuCode, AssetSku::getSkuId));
        for (AssetSku skus : assetSkus) {
            List<AssetSkuManage> manages = skus.getManages();
            for (AssetSkuManage manage : manages) {
                manage.setSkuId(skuIdMap.get(skus.getSkuCode()));
                manageList.add(manage);
            }
        }
        return manageList;
    }

    private List<AssetSku> buildSkuInfo(List<AssetSkuImportData> assetSkuImportData, Map<String, Map<String, AssetCategory>> cateMap) {
        List<AssetSku> assetSkus = new ArrayList<>();
        Map<String, AssetSku> assetSkuCache = new HashMap<>();
        for (AssetSkuImportData assetSkuImportDatum : assetSkuImportData) {
            AssetSku assetSku = converter.importDataToAssetSku(assetSkuImportDatum);
            String skuCode = assetSku.getSkuCode();
            // 指定了SkuCode，就允许配多个业务线
            if (StringUtils.isNotBlank(skuCode)) {
                if (assetSkuCache.containsKey(skuCode)) {
                    assetSku = assetSkuCache.get(skuCode);
                } else {
                    assetSkus.add(assetSku);
                    assetSkuCache.put(skuCode, assetSku);
                }
                // 构建管理信息
                AssetSkuManage assetSkuManage = buildManageInfo(assetSkuImportDatum, cateMap);
                List<AssetSkuManage> manages = assetSku.getManages();
                if (CollectionUtils.isEmpty(manages)) {
                    manages = new ArrayList<>();
                }
                manages.add(assetSkuManage);
                assetSku.setManages(manages);
            } else { // 未填物料编码，自动生成，并绑定一个管理信息
                // 生成skuCode
                this.genSkuCode(assetSku);
                // 构建管理信息
                AssetSkuManage assetSkuManage = buildManageInfo(assetSkuImportDatum, cateMap);
                assetSku.setManages(Arrays.asList(assetSkuManage));
                assetSkus.add(assetSku);
            }
        }
        return assetSkus;
    }

    private List<AssetSku> buildUpdateSkuInfo(List<AssetSkuUpdateData> assetSkuUpdateData, Map<String, Map<String, AssetCategory>> cateMap) {
        // assetSkuUpdateData 获取SkuCode集合，根据code批量查询物料
        List<String> skuCodes = assetSkuUpdateData.stream().map(AssetSkuUpdateData::getSkuCode).collect(Collectors.toList());
        List<AssetSku> existingSkus = assetSkuRepo.getAssetSkuCodes(skuCodes);

        // 每个SkuCode 一个集合，集合中AssetSkuUpdateData
        Map<String, List<AssetSkuUpdateData>> skuCodeMap = assetSkuUpdateData.stream()
                .collect(Collectors.groupingBy(AssetSkuUpdateData::getSkuCode));

        // existingSkus 获取 id集合，再查询管理信息
        List<Integer> skuIds = existingSkus.stream().map(AssetSku::getSkuId).collect(Collectors.toList());
        Map<Integer, List<AssetSkuManage>> manageMap = assetSkuManageRepo.getAssetSkusManages(skuIds);

        // 填充管理信息
        for (AssetSku assetSku : existingSkus) {
            // 导入文件的
            List<AssetSkuUpdateData> assetSkuUpdateDataList = skuCodeMap.get(assetSku.getSkuCode());
            if (CollectionUtils.isEmpty(assetSkuUpdateDataList)) continue;
            converter.updateDataToAssetSku(assetSkuUpdateDataList.get(0), assetSku);

            // 表中的管理信息
            List<AssetSkuManage> assetSkuManages = manageMap.get(assetSku.getSkuId());
            assetSku.setManages(getAssetSkuManage(assetSkuManages, cateMap, assetSkuUpdateDataList, assetSku));
        }

        return existingSkus;
    }

    private List<AssetSkuManage> getAssetSkuManage(List<AssetSkuManage> assetSkuManages, Map<String, Map<String, AssetCategory>> cateMap,
                                                   List<AssetSkuUpdateData> assetSkuUpdateDataList, AssetSku assetSku) {
        // 按业务线 转换assetSkuManages 为Map
        Map<String, AssetSkuManage> oldManageMap = assetSkuManages.stream()
                .collect(Collectors.toMap(m -> m.getBusinessLine().getCode(), Function.identity()));

        List<AssetSkuManage> assetSkuManage = new ArrayList<>();
        for (AssetSkuUpdateData skuUpdateData : assetSkuUpdateDataList) {
            String businessLine = skuUpdateData.getBusinessLine();
            if (StringUtils.isBlank(businessLine)) continue;
            AssetSkuManage oldSkuManage = oldManageMap.get(businessLine);
            // 如果旧的存在就新增，如果存在就更新
            AssetSkuManage newSkuManage;
            if (oldSkuManage == null) {
                newSkuManage = assetSkuMgConverter.updateAssetSkuManage(skuUpdateData);
                newSkuManage.setSkuId(assetSku.getSkuId());
            } else {
                // 转换
                assetSkuMgConverter.updateAssetSkuManage(skuUpdateData, oldSkuManage);
                newSkuManage = oldSkuManage;
            }

            Map<String, AssetCategory> categories = cateMap.getOrDefault(businessLine, new HashMap<>());
            String cateCode = skuUpdateData.getCateCode();
            if (StringUtils.isNotBlank(cateCode)) {
                AssetCategory assetCategory = categories.get(cateCode);
                if (assetCategory != null) {
                    newSkuManage.setCateId(assetCategory.getCateId());
                    newSkuManage.setCateCode(assetCategory.getCateCode());
                    newSkuManage.setCateName(assetCategory.getCateName());
                    assetSku.setCateId(assetCategory.getCateId());
                    assetSku.setCateCode(assetCategory.getCateCode());
                    assetSku.setCateName(assetCategory.getCateName());
                    assetSku.setBusinessLine(BusinessLine.getByCode(businessLine));
                }
            }
            assetSkuManage.add(newSkuManage);

        }
        return assetSkuManage;

    }

    private AssetSkuManage buildManageInfo(AssetSkuImportData assetSkuImportDatum, Map<String, Map<String, AssetCategory>> cateMap) {
        AssetSkuManage manage = new AssetSkuManage();
        String businessLine = assetSkuImportDatum.getBusinessLine();
        Map<String, AssetCategory> categories = cateMap.getOrDefault(businessLine, new HashMap<>());
        String cateCode = assetSkuImportDatum.getCateCode();
        AssetCategory assetCategory = categories.get(cateCode);
        if (assetCategory != null) {
            manage.setCateId(assetCategory.getCateId());
            manage.setCateCode(assetCategory.getCateCode());
            manage.setCateName(assetCategory.getCateName());
        }
        manage.setBusinessLine(BusinessLine.getByCode(assetSkuImportDatum.getBusinessLine()));
        manage.setMgModel(ManageModel.getByCode(assetSkuImportDatum.getMgModel()));
        manage.setSerialMg(SnTypeEnum.getByCode(assetSkuImportDatum.getSerialMg()));
        manage.setCosting(StockCostEnum.MOVING_AVG);
        manage.setSecureQuantity(assetSkuImportDatum.getSecureQuantity());
        manage.setHighestQuantity(assetSkuImportDatum.getHighestQuantity());
        manage.setMiniQuantity(assetSkuImportDatum.getMiniQuantity());
        manage.setMaterialType(MaterialType.getByCode(assetSkuImportDatum.getMaterialType()));
        return manage;
    }

    public void initBaseDataToCache(List<AssetSkuImportData> assetSkuImportData, Map<String, Object> cacheMap) {
        // 初始化资产编号集合
        Map<String, List<String>> invalidSkuCodeMap = new HashMap<>(); // 相同业务线只能有一个物料
        Set<String> measureNames = new HashSet<>();
        Set<String> skuCodes = new HashSet<>();
        Map<String, Set<String>> cateCodeMap = new HashMap<>();

        for (AssetSkuImportData assetSkuData : assetSkuImportData) {
            String skuCode = assetSkuData.getSkuCode();
            String businessLine = assetSkuData.getBusinessLine();
            String measureName = assetSkuData.getMeasureName();
            if (StringUtils.isNotBlank(skuCode)) {
                skuCodes.add(skuCode);
                List<String> invalidSkuCode = invalidSkuCodeMap.getOrDefault(businessLine, new ArrayList<>());
                invalidSkuCode.add(skuCode);
                invalidSkuCodeMap.put(businessLine, invalidSkuCode);
            }
            if (StringUtils.isNotBlank(measureName)) {
                measureNames.add(measureName);
            }
            String cateCode = assetSkuData.getCateCode();
            if (StringUtils.isNotBlank(cateCode)) {
                Set<String> cateCodeSet = cateCodeMap.getOrDefault(businessLine, new HashSet<>());
                cateCodeSet.add(cateCode);
                cateCodeMap.put(businessLine, cateCodeSet);
            }
        }

        // 查询物料是否存在
        List<AssetSku> assetSkuCodes = assetSkuRepo.getAssetSkuCodes(new ArrayList<>(skuCodes));
        Set<String> invalidSkuCodeCache = assetSkuCodes.stream().map(AssetSku::getSkuCode).collect(Collectors.toSet());
        cacheMap.put(CommonConstant.ASSET_SKU_CODE, invalidSkuCodeCache);
        cacheMap.put(CommonConstant.BUSINESS_SKU, invalidSkuCodeMap);

        // 批量查询计量单位
        List<MeasurementUnit> existMeasures = measurementRepo.queryListByMeasureNames(new ArrayList<>(measureNames));
        cacheMap.put(CommonConstant.MEASURE, existMeasures);
        cacheMap.put(CommonConstant.MEASURE_NAME, existMeasures.stream().map(MeasurementUnit::getMeasureName).collect(Collectors.toSet()));

        // 批量查询分类
        Map<String, Map<String, AssetCategory>> assetCateMap = new HashMap<>();
        Map<String, Set<String>> assetCateCodeMap = new HashMap<>();
        cateCodeMap.forEach((businessLine, cateCodeSet) -> {
            List<AssetCategory> existCates = new ArrayList<>();
            if (StringUtils.isNotBlank(businessLine)) {
                existCates = assetCategoryRepo.getByCateCode(businessLine, new ArrayList<>(cateCodeSet));
            }
            assetCateCodeMap.put(businessLine, existCates.stream().map(AssetCategory::getCateCode).collect(Collectors.toSet()));
            assetCateMap.put(businessLine, existCates.stream().collect(Collectors.toMap(AssetCategory::getCateCode, Function.identity())));
        });
        cacheMap.put(CommonConstant.ASSET_CATE, assetCateMap);
        cacheMap.put(CommonConstant.ASSET_CATE_CODE, assetCateCodeMap);
    }

    public void initUpdateDataToCache(List<AssetSkuUpdateData> assetSkuUpdateData, Map<String, Object> cacheMap) {
        // 初始化资产编号集合
        Set<String> measureNames = new HashSet<>();
        Set<String> skuCodes = new HashSet<>();
        Map<String, Set<String>> cateCodeMap = new HashMap<>();

        for (AssetSkuUpdateData assetSkuData : assetSkuUpdateData) {
            String skuCode = assetSkuData.getSkuCode();
            String businessLine = assetSkuData.getBusinessLine();
            String measureName = assetSkuData.getMeasureName();
            if (StringUtils.isNotBlank(skuCode)) {
                skuCodes.add(skuCode);
            }
            if (StringUtils.isNotBlank(measureName)) {
                measureNames.add(measureName);
            }
            String cateCode = assetSkuData.getCateCode();
            if (StringUtils.isNotBlank(cateCode)) {
                Set<String> cateCodeSet = cateCodeMap.getOrDefault(businessLine, new HashSet<>());
                cateCodeSet.add(cateCode);
                cateCodeMap.put(businessLine, cateCodeSet);
            }
        }

        // 查询物料是否存在
        List<AssetSku> assetSkuCodes = assetSkuRepo.getAssetSkuCodes(new ArrayList<>(skuCodes));
        Set<String> validSkuCodeCache = assetSkuCodes.stream().map(AssetSku::getSkuCode).collect(Collectors.toSet());
        cacheMap.put(CommonConstant.ASSET_SKU_CODE, validSkuCodeCache);

        // 批量查询计量单位
        List<MeasurementUnit> existMeasures = measurementRepo.queryListByMeasureNames(new ArrayList<>(measureNames));
        cacheMap.put(CommonConstant.MEASURE, existMeasures);
        cacheMap.put(CommonConstant.MEASURE_NAME, existMeasures.stream().map(MeasurementUnit::getMeasureName).collect(Collectors.toSet()));

        // 批量查询分类
        Map<String, Map<String, AssetCategory>> assetCateMap = new HashMap<>();
        Map<String, Set<String>> assetCateCodeMap = new HashMap<>();
        cateCodeMap.forEach((businessLine, cateCodeSet) -> {
            List<AssetCategory> existCates = new ArrayList<>();
            if (StringUtils.isNotBlank(businessLine)) {
                existCates = assetCategoryRepo.getByCateCode(businessLine, new ArrayList<>(cateCodeSet));
            }
            assetCateCodeMap.put(businessLine, existCates.stream().map(AssetCategory::getCateCode).collect(Collectors.toSet()));
            assetCateMap.put(businessLine, existCates.stream().collect(Collectors.toMap(AssetCategory::getCateCode, Function.identity())));
        });
        cacheMap.put(CommonConstant.ASSET_CATE, assetCateMap);
        cacheMap.put(CommonConstant.ASSET_CATE_CODE, assetCateCodeMap);
    }

    private Integer getMessageIndex(String language) {
        return language.equals(ENGLISH) ? 1 : 0;
    }

    private String validateCountryCode(String countryCode, String language) {
        if (StringUtils.isBlank(countryCode)) {
            return COUNTRY_CODE_EMPTY_ERROR[getMessageIndex(language)];
        }
        List<CountryConfigDo> countryConfigs = countryConfigRepo.getByThreeCode(Collections.singletonList(countryCode));
        if (CollectionUtils.isEmpty(countryConfigs)) {
            return MessageFormat.format(COUNTRY_CODE_ERROR[getMessageIndex(language)], countryCode);
        }
        return null;
    }

    public String importValidate(String filedName, Object value, AssetSkuImportData data, Map<String, Object> cacheMap, String language) {
        Set<String> skuCodeCache = (Set<String>) cacheMap.getOrDefault(CommonConstant.ASSET_SKU_CODE, new HashSet<>());
        Set<String> measureNameCache = (Set<String>) cacheMap.getOrDefault(CommonConstant.MEASURE_NAME, new HashSet<>());
        Map<String, Set<String>> cateCodeMap = (Map<String, Set<String>>) cacheMap.getOrDefault(CommonConstant.ASSET_CATE_CODE, new HashSet<>());
        Map<String, List<String>> invalidSkuCodeMap = (Map<String, List<String>>) cacheMap.getOrDefault(CommonConstant.BUSINESS_SKU, new HashSet<>());
        BusinessLine businessLine = BusinessLine.getByDesc(data.getBusinessLine());
        String businessLineCode = Objects.nonNull(businessLine) ? businessLine.getCode() : "";

        Set<String> cateCodeCache = cateCodeMap.getOrDefault(businessLineCode, new HashSet<>());
        List<String> importSkuList = invalidSkuCodeMap.get(businessLineCode);

        switch (filedName) {
            case CommonConstant.ASSET_SKU_CODE:
                return importValidateSkuCode((String) value, skuCodeCache, importSkuList, language);
            case CommonConstant.ASSET_CATE_CODE:
                return importValidateCateCode((String) value, cateCodeCache, language);
            case CommonConstant.MEASURE_NAME:
                return importValidateMeasureCode((String) value, measureNameCache, language);
            case CommonConstant.COUNTRY:
                return validateCountryCode((String) value, language);
            default:
                break;
        }
        return null;
    }

    public String updateValidate(String filedName, Object value, AssetSkuUpdateData data, Map<String, Object> cacheMap, String language) {
        Set<String> skuCodeCache = (Set<String>) cacheMap.getOrDefault(CommonConstant.ASSET_SKU_CODE, new HashSet<>());
        Set<String> measureNameCache = (Set<String>) cacheMap.getOrDefault(CommonConstant.MEASURE_NAME, new HashSet<>());
        Map<String, Set<String>> cateCodeMap = (Map<String, Set<String>>) cacheMap.getOrDefault(CommonConstant.ASSET_CATE_CODE, new HashSet<>());
        BusinessLine businessLine = BusinessLine.getByDesc(data.getBusinessLine());
        String businessLineCode = Objects.nonNull(businessLine) ? businessLine.getCode() : "";

        Set<String> cateCodeCache = cateCodeMap.getOrDefault(businessLineCode, new HashSet<>());

        switch (filedName) {
            case CommonConstant.ASSET_SKU_CODE:
                return updateValidateSkuCode((String) value, skuCodeCache, language);
            case CommonConstant.ASSET_CATE_CODE:
                return importValidateCateCode((String) value, cateCodeCache, language);
            case CommonConstant.MEASURE_NAME:
                return importValidateMeasureCode((String) value, measureNameCache, language);
            case CommonConstant.COUNTRY:
                return validateCountryCode((String) value, language);
            default:
                break;
        }
        return null;
    }

    private String importValidateSkuCode(String skuCode, Set<String> skuCodeCache, List<String> importSkuList, String language) {
        // 判断 skuCode 是否在 importSkuList中存在大于1个，如果是就返回错误
        if (importSkuList.stream().filter(code -> code.equals(skuCode)).count() > 1) {
            return MessageFormat.format(BUSINESS_SKU_CODE_ERROR[getMessageIndex(language)], skuCode);
        }

        if (skuCodeCache.contains(skuCode)) {
            return MessageFormat.format(SKU_CODE_ERROR[getMessageIndex(language)], skuCode);
        }
        return null;
    }

    private String updateValidateSkuCode(String skuCode, Set<String> skuCodeCache, String language) {
        if (!skuCodeCache.contains(skuCode)) {
            return MessageFormat.format(SKU_CODE_UPDATE_ERROR[getMessageIndex(language)], skuCode);
        }
        return null;
    }

    private String importValidateCateCode(String cateCode, Set<String> cateCodeCache, String language) {
        if (!cateCodeCache.contains(cateCode)) {
            return MessageFormat.format(ASSET_CATE_ERROR[getMessageIndex(language)], cateCode);
        }
        return null;
    }

    private String importValidateMeasureCode(String measureName, Set<String> measureNameCache, String language) {
        if (!measureNameCache.contains(measureName)) {
            return MessageFormat.format(MEASURE_CODE_ERROR[getMessageIndex(language)], measureName);
        }
        return null;
    }

    /**
     * 保存流对象（输入流在第二次使用的时候会失效）
     */
    public byte[] saveInputStream(InputStream ins) {
        byte[] buf = null;
        try {
            if (ins != null) {
                buf = IOUtils.toByteArray(ins);
            }
        } catch (IOException e) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "保存文件流失败！");
        }
        return buf;
    }

    // 按业务线处理物料数据
    public void handleSkuInfo(List<AssetSku> assetSkuList, List<String> businessLineCodes) {
        // 处理数据, 根据当前查询到的物料，查询物料管理信息，找到符合businessLineCodes 的第一个管理信息，设置AssetSku
        List<Integer> skuIds = assetSkuList.stream().map(AssetSku::getSkuId).collect(Collectors.toList());
        Map<Integer, List<AssetSkuManage>> manageMap = assetSkuManageRepo.getAssetSkusManages(skuIds);
        assetSkuList.forEach(sku -> {
            List<AssetSkuManage> manages = manageMap.get(sku.getSkuId());
            if (CollectionUtils.isNotEmpty(manages)) {
                manages.stream()
                        .filter(m -> null != m.getBusinessLine() && businessLineCodes.contains(m.getBusinessLine().getCode()))
                        .findFirst()
                        .ifPresent(manage -> {
                            sku.setCateId(manage.getCateId());
                            sku.setCateCode(manage.getCateCode());
                            sku.setCateName(manage.getCateName());
                            sku.setBusinessLine(manage.getBusinessLine());
                        });
                sku.setBusinessLines(manages.stream().filter(manage -> businessLineCodes.contains(manage.getBusinessLine().getCode())).map(AssetSkuManage::getBusinessLine).collect(Collectors.toList()));
            }
        });
    }

    public PageData<AssetSkuRes> getAssetSkuPageData(List<Integer> relationCateIds, List<String> businessLines, String keyword,
                                                     PageRequest pageRequest, Boolean disabled, Boolean containMiGoods) {
        // todo 增加查询mdm状态的逻辑
        PageData<AssetSku> page = assetSkuRepo.getAssetSkuPageDataV1(relationCateIds, businessLines, keyword, pageRequest, disabled, containMiGoods);

        List<AssetSku> assetSkuList = page.getList();
        if (CollectionUtils.isEmpty(assetSkuList)) {
            return converter.toPageData(page, converter::toAssetSkuRes);
        }
        this.handleSkuInfo(assetSkuList, businessLines);
        //加载分类信息交期（天）信息
        this.loadDeliveryDays(businessLines, relationCateIds, assetSkuList);
        return converter.toPageData(page, converter::toAssetSkuRes);
    }


    public void loadDeliveryDays(List<String> businessLines, List<Integer> relationCateIds, List<AssetSku> assetSkuList) {
        if (CollectionUtils.isEmpty(businessLines) || CollectionUtils.isEmpty(relationCateIds)
                || CollectionUtils.isEmpty(assetSkuList) || businessLines.size() > 1) return;

        List<AssetCategory> categories = assetCategoryRepo.getAssetCategories(relationCateIds);
        if (CollectionUtils.isEmpty(categories)) return;
        Map<Integer, Integer> idAndDeliveryDaysMap = categories.stream().collect(Collectors.toMap(AssetCategory::getCateId, AssetCategory::getDeliveryDays, (v1, v2) -> v1));
        for (AssetSku assetSku : assetSkuList) {
            assetSku.setDeliveryDays(idAndDeliveryDaysMap.get(assetSku.getCateId()));
        }
    }
}
