package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.measurement.MeasurementUnitQueryReq;
import com.mi.oa.asset.commons.config.api.measurement.MeasurementUnitRes;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.MeasurementUnitQuery;
import com.mi.oa.asset.commons.config.domain.mesurement.entity.MeasurementUnit;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.eam.feign.mdm.res.MdmMeasurementUnitRes;
import com.mi.oa.asset.eam.feign.mdm.res.MdmUnitMultiLanguageRes;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


@Mapper(
        componentModel = "spring",
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL
)
public interface MeasurementConverter extends CommonConverter {

    MeasurementUnit toMeasurementUnitPo(MeasurementUnitRes source);

    List<MeasurementUnit> toMeasurementUnitPos(List<MeasurementUnitRes> source);

    MeasurementUnitQuery toMeasurementUnitQuery(MeasurementUnitQueryReq source);

    MeasurementUnitRes toMeasurementUnitRes(MeasurementUnit source);

    List<MeasurementUnitRes> toMeasurementUnitRes(List<MeasurementUnit> source);

    default MeasurementUnit resToMeasurementUnit(MdmMeasurementUnitRes source) {
        if (source == null) {
            return null;
        }
        MeasurementUnit.MeasurementUnitBuilder measurementUnit = MeasurementUnit.builder();
        if (source.getMeasureCode() != null) {
            measurementUnit.measureCode(source.getMeasureCode());
        }
        if (source.getMeasureName() != null) {
            measurementUnit.measureName(source.getMeasureName());
        }
        if (source.getTechCode() != null) {
            measurementUnit.techCode(source.getTechCode());
        }
        if (source.getMuId() != null) {
            measurementUnit.muId(source.getMuId());
        }
        if (source.getUnitTypeCode() != null) {
            measurementUnit.unitTypeCode(source.getUnitTypeCode());
        }
        if (source.getUnitTypeName() != null) {
            measurementUnit.unitTypeName(source.getUnitTypeName());
        }
        if (source.getDataSource() != null) {
            measurementUnit.dataSource(source.getDataSource());
        }
        List<MdmUnitMultiLanguageRes> languages = source.getLanguages();
        if (languages != null) {
            Optional<MdmUnitMultiLanguageRes> first = languages.stream().filter(language -> "EN".equals(language.getLanguage())).findFirst();
            first.ifPresent(mdmUnitMultiLanguageRes -> measurementUnit.measureNameEn(mdmUnitMultiLanguageRes.getTechnicalCode()));
        }
        return measurementUnit.build();
    }

    List<MeasurementUnit> createMeasurement(List<MdmMeasurementUnitRes> toAdd);

    void updateMeasurement(MdmMeasurementUnitRes source, @MappingTarget MeasurementUnit target);

    default List<MeasurementUnit> updateMeasurement(List<MdmMeasurementUnitRes> toUpdate, @MappingTarget List<MeasurementUnit> exists) {
        List<MeasurementUnit> updates = new ArrayList<>();

        Map<String, MdmMeasurementUnitRes> sourceMap = toUpdate.stream().collect(Collectors.toMap(MdmMeasurementUnitRes::getMuId, v -> v));
        exists.forEach(t -> {
            MdmMeasurementUnitRes s = sourceMap.get(t.getMuId());
            if (s == null) return;

            updateMeasurement(s, t);
            List<MdmUnitMultiLanguageRes> languages = s.getLanguages();
            if (languages != null) {
                Optional<MdmUnitMultiLanguageRes> first = languages.stream().filter(language -> "EN".equals(language.getLanguage())).findFirst();
                first.ifPresent(mdmUnitMultiLanguageRes -> t.setMeasureNameEn(mdmUnitMultiLanguageRes.getTechnicalCode()));
            }
            updates.add(t);
        });

        return updates;
    }
}
