package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.position.*;
import com.mi.oa.asset.commons.config.app.ability.PositionAbility;
import com.mi.oa.asset.commons.config.app.converter.PositionConverter;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import com.mi.oa.asset.commons.config.domain.position.entity.Position;
import com.mi.oa.asset.commons.config.domain.position.repository.PositionRepo;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.space.rep.SpaceParkVO;
import com.mi.oa.infra.oaucf.space.service.SpaceParkService;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mi.oa.asset.common.enums.EAMConstants.CHINESE;

/**
 * <AUTHOR>
 * @Date 2023/10/16 11:22
 */

@DubboService
public class PositionProviderImpl implements PositionProvider {

    @Resource
    private SpaceParkService spaceParkService;

    @Resource
    private PositionConverter converter;

    @Resource
    private PositionRepo positionRepo;

    @Resource
    private PositionAbility positionAbility;

    @Resource
    private HttpServletRequest request;

    @Override
    public List<PositionTreeRes> stdPositionTree(String language) {
        BaseResp<List<SpaceParkVO>> data = spaceParkService.getParkBuildingFloorTree();
        if (BaseResp.CODE_SUCCESS != data.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, data.getMessage());
        }
        if (StringUtils.isNotBlank(language) && !CHINESE.equals(language)) {
            return positionAbility.listPositionTree(data.getData());
        } else {
            return converter.stdParkToPositionTreeRes(data.getData());
        }
    }


    @Override
    public void createPosition(SavePositionReq req) {
        Position position = converter.toPosition(req);
        positionAbility.loadFromParent(position);

        // 检查编码是否重复
        positionAbility.checkPositionCodeExist(position);
        // 保存
        positionRepo.savePosition(position);
        // 更新positionPath
        positionRepo.updatePositionPath(position);
    }

    @Override
    public void updatePosition(SavePositionReq req) {
        Position existed = positionAbility.loadPosition(req.getPositionId());
        Position position = converter.toPosition(req);

        // 编码不更新
        position.setPositionCode(null);
        // 保存
        positionRepo.savePosition(position);
        if (!existed.getParentCode().equals(position.getParentCode())) {
            positionAbility.loadFromParent(position);
            // 父节点变更，更新节点及子节点的positionPath
            positionAbility.updatePositionPath(position, existed.getPositionPath());
        }
    }

    @Override
    public void deleteByIds(List<Integer> ids) {
        List<Position> positions = positionRepo.getPositions(ids);
        List<String> paths = positions.stream().map(Position::getPositionPath).collect(Collectors.toList());
        positionRepo.deleteByPaths(paths);
    }

    @Override
    public PageData<PositionRes> getPositionPageData(Integer positionId, String keyword, Integer pageNum, Integer pageSize) {
        String positionPath = null;
        String language = request.getHeader(EAMConstants.LANGUAGE);

        if (null != positionId && positionId > 0) {
            Position position = positionAbility.loadPosition(positionId);
            positionPath = position.getPositionPath();
        }

        PageData<Position> data = positionRepo.getPositionPageData(positionPath, keyword, PageRequest.of(pageSize, pageNum));
        if (!EAMConstants.CHINESE.equals(language) && StringUtils.isNotEmpty(language)) {
            data.getList().forEach(i -> i.setPositionName(i.getPositionNameEn()));
        }
        positionAbility.loadFullPositionName(data.getList());
        return converter.toPageData(data, converter::toPositionRes);
    }

    @Override
    public PageData<PositionRes> getPositionPageData(List<String> businessLineCodes, String keyword, Integer pageNum, Integer pageSize) {
        PageData<Position> data = positionRepo.getPositionPageData(businessLineCodes, keyword, PageRequest.of(pageSize, pageNum));
        String language = request.getHeader(EAMConstants.LANGUAGE);
        if (!EAMConstants.CHINESE.equals(language) && StringUtils.isNotEmpty(language)) {
            data.getList().forEach(i -> i.setPositionName(i.getPositionNameEn()));
        }
        positionAbility.loadFullPositionName(data.getList());

        return converter.toPageData(data, converter::toPositionRes);
    }

    @Override
    public PositionRes getPosition(Integer positionId) {
        return converter.toPositionRes(positionAbility.loadPosition(positionId));
    }

    @Override
    public PositionRes getPosition(String positionCode, String businessLine) {
        Position position = positionRepo.getPositionByCode(positionCode, BusinessLine.getByCode(businessLine));
        positionAbility.loadFullPositionName(position);

        return converter.toPositionRes(position);
    }

    @Override
    public List<PositionRes> getBatchPosition(List<String> positionCode, String businessLine) {
        if (CollectionUtils.isEmpty(positionCode)) return new ArrayList<>();
        List<Position> positions = positionRepo.getPositionByCodes(positionCode, BusinessLine.getByCode(businessLine));
        positionAbility.loadFullPositionName(positions);
        return converter.toPositionRes(positions);
    }

    @Override
    public List<PositionRes> getPositionWithSub(Integer positionId) {
        Position position = positionAbility.loadPosition(positionId);

        return converter.toPositionRes(positionRepo.getPositionByPaths(Collections.singletonList(position.getPositionPath())));
    }

    @Override
    public void importStdPosition(ImportStdPositionReq req) {
        BusinessLine businessLine = BusinessLine.getByCode(req.getBusinessLine());
        if (null == businessLine) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线不存在");
        }

        // 之前已经导入的
        List<Position> positions = positionRepo.getPositionByCodes(
                req.getItems().stream().map(ImportStdPositionReq.Item::getPositionCode).collect(Collectors.toList()),
                businessLine
        );
        Map<String, Position> existedMap = positions.stream().collect(
                Collectors.toMap(Position::getPositionCode, Function.identity())
        );

        List<Position> list = new ArrayList<>();
        req.getItems().forEach(i -> {
            Position existedOne = existedMap.get(i.getPositionCode());

            if (null == existedOne) {
                list.add(converter.toPosition(i, businessLine, DataCreateSource.SYS_SPACE));
            } else {
                converter.updateFromStd(i, existedOne);
                list.add(existedOne);
            }
        });
        String language = request.getHeader(EAMConstants.LANGUAGE);
        if (!EAMConstants.CHINESE.equals(language) && StringUtils.isNotEmpty(language)) {
            list.forEach(i -> i.setPositionNameEn(i.getPositionName()));
        }
        // 保存
        positionRepo.savePositions(list);
        // 更新positionPath
        positionAbility.setStdPositionPath(list);
        positionRepo.savePositions(list);
    }

    @Override
    public List<PositionRes> getAllPosition(List<String> businessLineCodes) {
        List<Position> list = positionRepo.getAllPosition(businessLineCodes);

        return converter.toPositionRes(list);
    }

    @Override
    public List<PositionRes> getPositions(List<Integer> positionIds, boolean includeSub) {
        List<Position> positions = positionRepo.getPositions(positionIds);
        if (includeSub) {
            List<String> paths = positions.stream().map(Position::getPositionPath).collect(Collectors.toList());
            positions.addAll(positionRepo.getPositionByPaths(paths));
        }

        return converter.toPositionRes(positions);
    }
}
