package com.mi.oa.asset.commons.config.app.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.github.javaparser.JavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.stmt.ThrowStmt;
import com.mi.oa.asset.commons.config.app.util.entity.ErrorMessage;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.xiaomi.keycenter.org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 国际化翻译工具类
 */
@Component
public class PullErrorMessageEntryUtil {
    public void pullErrorMessageEntry(String scanPaths) throws FileNotFoundException {
        //扫描文件路径
//        Path startPath = Paths.get("D:\\mi\\code");
        Path startPath = Paths.get(scanPaths);
        List<String> javaFilePaths = getJavaFilePaths(startPath);
        List<List<String>> fileJavaMsg = new ArrayList<>();
        System.out.println("类的数量： " + javaFilePaths.size());
        // 打印类结果验证
        // javaFilePaths.forEach(System.out::println);

        //初始化有错误信息的java文件
        javaFilePaths.forEach(path -> {
            try {
                //获取类里面出现ErrorCodeException
                List<String> throwStmtMsg = getThrowStmtMsg(path);
                if (throwStmtMsg.size() > 0){
                    fileJavaMsg.add(throwStmtMsg);
                }
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            }
        });
        //初始化词条并去重
        Set result = new HashSet();
        for (List<String> strings : fileJavaMsg){
            result.addAll(strings);
        }
        //解析分割中文词条
        Set<String> entrySet = extractAndSplitChineseMessages(result);
        //初始化dataList
        List<ErrorMessage> dataList = new ArrayList<>();
        for (String item : entrySet){
            dataList.add(new ErrorMessage(generateUUIDKey(item), item));
        }
        // 导出Excel
        String fileName = "错误信息词条导出" + DateUtils.dateTimeToString(new Date()) + ".xlsx";
        exportToExcel(dataList, fileName);
    }


    /**
     * 根据词条生成唯一key
     * @param input
     * @return
     */
    public static String generateUUIDKey(String input) {
        return UUID.nameUUIDFromBytes(input.getBytes()).toString();
    }

    /**
     *  获取包路径下的类
     * @param startPath
     * @return
     */
    public static List<String> getJavaFilePaths(Path startPath) {
        try (Stream<Path> stream = Files.walk(startPath)) {
            return stream
                    // 过滤掉目录 排除目录、符号链接等非普通文件
                    .filter(Files::isRegularFile)
                    // 过滤掉非Java文件
                    .filter(path -> path.toString().endsWith(".java"))
                    // Path对象转换为字符串列表
                    .map(Path::toString)
                    .collect(Collectors.toList());
        } catch (IOException e) {
            e.printStackTrace();
            return Collections.emptyList(); // 返回空列表而不是null
        }
    }

    /**
     *   获取类里面出现ErrorCodeException
     * @param filePath
     * @return
     * @throws FileNotFoundException
     */
    public static List<String> getThrowStmtMsg(String filePath) throws FileNotFoundException {
        File file = new File(filePath);
        List<String> list = new ArrayList<>();

        CompilationUnit cu = new JavaParser()
                .parse(file)
                .getResult()
                // 处理可能为空的情况 如果 Optional 为空(解析失败)，抛出 IllegalArgumentException 否则返回 CompilationUnit 对象
                .orElseThrow(IllegalArgumentException::new);
        cu.findAll(ThrowStmt.class).forEach(throwStmt -> {
            if (throwStmt.toString().contains("ErrorCodeException")) {
                System.out.println(throwStmt.toString());
                list.add(throwStmt.toString());
            }
        });
        return list;
    }

    /**
     * 导出错误信息词条到excel
     * @param dataList
     * @param fileName
     */
    public static void exportToExcel(List<ErrorMessage> dataList, String fileName) {
        EasyExcel.write(fileName, ErrorMessage.class)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("错误词条")
                .doWrite(dataList);
    }

    /**
     * 解析分割中文词条
     * @param errorMessages
     * @return
     */
    public static Set<String> extractAndSplitChineseMessages(Set<String> errorMessages) {
        Set<String> chineseSegments = new LinkedHashSet<>();

        // 正则表达式匹配双引号或format括号内的中文字符串
        Pattern messagePattern = Pattern.compile("[\"]([^\"]*[\\u4e00-\\u9fa5]+[^\"]*)[\"]|format\\([\"]([^\"\\{\\}]*[\\u4e00-\\u9fa5]+[^\"\\{\\}]*)[\"]");

        // 正则表达式匹配占位符(%s、{0}等)作为分隔符
        Pattern splitPattern = Pattern.compile("%[a-zA-Z]|\\{\\d+\\}|【[^】]*】");

        for (String item : errorMessages){
            Matcher matcher = messagePattern.matcher(item);
            while (matcher.find()) {
                // 检查哪个捕获组匹配成功
                String matchedText = matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
                if (matchedText != null && !matchedText.trim().isEmpty()) {
                    // 按占位符分割字符串
                    String[] parts = splitPattern.split(matchedText);
                    for (String part : parts) {
                        String trimmedPart = part.trim();
                        if (!trimmedPart.isEmpty()) {
                            trimmedPart = trimmedPart.replaceAll("^[\\p{Punct}\\s]+|[\\p{Punct}\\s]+$", "");
                            trimmedPart = StringUtils.replaceChars(trimmedPart, "【】", "");
                            chineseSegments.add(removeEdgePunctuation(trimmedPart));
                        }
                    }
                }
            }
        }
        return chineseSegments;
    }

    private static String removeEdgePunctuation(String str) {
        // 特别处理以中文逗号开头的情况
        if (str.startsWith("，")) {
            str = str.substring(1);
        }
        // 定义中文和英文标点符号的正则表达式
        String punctuationRegex = "^[\\p{P}\\p{Z}]+|[\\p{P}\\p{Z}]+$";
        return str.replaceAll(punctuationRegex, "").trim();
    }
}
