package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxProvider;
import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxReq;
import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxRes;
import com.mi.oa.asset.commons.config.app.ability.CountryTaxAbility;
import com.mi.oa.asset.commons.config.app.converter.CountryTaxConverter;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryTaxDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryTaxRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Service
@Slf4j
@DubboService
public class CountryTaxProviderImpl implements CountryTaxProvider {

    @Resource
    private CountryTaxRepo countryTaxRepo;

    @Resource
    private CountryTaxConverter converter;

    @Resource
    private CountryTaxAbility ability;

    @Override
    public List<CountryTaxRes> getCountryTaxList() {
        return converter.listDoToRes(countryTaxRepo.searchAll());
    }

    @Override
    public CountryTaxRes getById(Integer countryTaxId) {
        CountryTaxDo countryTaxDo = countryTaxRepo.getById(countryTaxId);
        if (countryTaxDo == null) return null;
        return converter.doToRes(countryTaxDo);
    }

    @Override
    public Integer saveOrUpdate(CountryTaxReq req) {
        CountryTaxDo entity = converter.reqToDo(req);
        ability.check(entity);
        if (entity.getId() != null) {
            countryTaxRepo.updateById(entity);
            return entity.getId();
        } else {
            return countryTaxRepo.save(entity);
        }
    }

    @Override
    public void removeByIds(List<Integer> ids) {
        countryTaxRepo.deleteByIds(ids);
    }

    @Override
    public List<CountryTaxRes> byCountryId(Integer countryId) {
        List<CountryTaxDo> countryTaxDoList = countryTaxRepo.getByCountryId(countryId);
        if (countryTaxDoList.isEmpty()) return null;
        return converter.listDoToRes(countryTaxDoList);
    }
}
