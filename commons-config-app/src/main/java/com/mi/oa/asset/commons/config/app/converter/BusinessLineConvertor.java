package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.businessline.*;
import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.domain.common.entity.ManageLineDo;
import com.mi.oa.asset.commons.config.domain.common.valobj.AllotConfig;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/31 19:34
 */

@Mapper(componentModel = "spring")
public interface BusinessLineConvertor extends CommonConverter {

    BusinessLineRes doToRes(BusinessLineDo businessLineDo);

    List<BusinessLineRes> doToResBatch(List<BusinessLineDo> businessLineDo);

    BusinessLineDo reqToDo(BusinessLineReq businessLineReq);

    AllotConfigRes allotConfigToRes(AllotConfig allotConfig);

    List<AllotConfigRes> allotConfigToResList(List<AllotConfig> allotConfigList);

    AllotConfig allotConfigReqToDo(AllotConfigReq allotConfig);

    List<AllotConfig> allotConfigReqToDoList(List<AllotConfigReq> allotConfigList);

    ManageLineRes doToManageRes(ManageLineDo manageLineDo);

    List<ManageLineRes> doToManageResBatch(List<ManageLineDo> businessLineDo);
}
