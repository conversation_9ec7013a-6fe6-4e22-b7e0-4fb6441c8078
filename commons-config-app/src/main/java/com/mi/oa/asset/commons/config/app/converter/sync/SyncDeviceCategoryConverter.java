package com.mi.oa.asset.commons.config.app.converter.sync;

import com.mi.oa.asset.commons.config.domain.assetcategory.entity.AssetCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.old.DeviceCategory;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:27
 */

@Mapper(componentModel = "spring")
public interface SyncDeviceCategoryConverter {

    //@Mapping(source = "showId", target = "cateId")
    @Mapping(source = "showCode", target = "cateCode")
    @Mapping(source = "showName", target = "cateName")
    @Mapping(source = "showNameEn", target = "cateNameEn")
    @Mapping(source = "showLevel", target = "level")
    //@Mapping(source = "showLevel", target = "catePath")
    @Mapping(source = "assetSort", target = "sort")
    @Mapping(source = "accCode", target = "sapCateCode")
    @Mapping(source = "accName", target = "sapCateName")
    @Mapping(source = "isValid", target = "disabled")
    @Mapping(target = "dataSource", expression = "java(deviceCategoryMapping(deviceCategory))")
    @Mapping(target = "useYear", expression = "java(deviceCategory.getUseYear()== null ||deviceCategory.getUseYear().equals(\"\")? 0 : Integer.parseInt(deviceCategory.getUseYear()))")
    @Mapping(target = "useMonth", expression = "java(deviceCategory.getUseMonth() == null||deviceCategory.getUseMonth().equals(\"\") ? 0 : Integer.parseInt(deviceCategory.getUseMonth()))")
    //@Mapping(source = "showLevel", target = "isSerialCodeManage")
    //@Mapping(source = "showLevel", target = "isMultipleManage")
   // @Mapping(source = "showLevel", target = "materialType")
    AssetCategory toAssetCategory(DeviceCategory deviceCategory);

    default DataCreateSource deviceCategoryMapping(DeviceCategory deviceCategory) {
        //if(deviceCategory.getDataSource())
        return DataCreateSource.MANUAL;
    }


}
