package com.mi.oa.asset.commons.config.app.provider;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.nacos.shaded.com.google.common.reflect.TypeToken;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.alibaba.nacos.shaded.com.google.gson.GsonBuilder;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.MaterialType;
import com.mi.oa.asset.common.enums.YesNo;
import com.mi.oa.asset.commons.config.api.assetsku.*;
import com.mi.oa.asset.commons.config.app.ability.AssetCategoryAbility;
import com.mi.oa.asset.commons.config.app.ability.AssetSkuAbility;
import com.mi.oa.asset.commons.config.app.converter.AssetSkuConverter;
import com.mi.oa.asset.commons.config.app.converter.AssetSkuMgConverter;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.AssetCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.repository.AssetCategoryRepo;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSku;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSkuManage;
import com.mi.oa.asset.commons.config.domain.assetsku.enums.MdmPurchaseItemBusinessTypeEnum;
import com.mi.oa.asset.commons.config.domain.assetsku.repository.AssetSkuManageRepo;
import com.mi.oa.asset.commons.config.domain.assetsku.repository.AssetSkuRepo;
import com.mi.oa.asset.commons.config.domain.assetsku.valobj.AssetSkuImportData;
import com.mi.oa.asset.commons.config.domain.assetsku.valobj.AssetSkuUpdateData;
import com.mi.oa.asset.commons.config.domain.assetsku.valobj.CreatePurchaseItemDTO;
import com.mi.oa.asset.commons.config.domain.common.repository.CommonDataRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.TemplateUrl;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryConfigRepo;
import com.mi.oa.asset.eam.feign.x5.X5Response;
import com.mi.oa.asset.eam.feign.x5.X5ResponseHeader;
import com.mi.oa.asset.eam.utils.HttpClientUtil;
import com.mi.oa.asset.excel.enums.ExcelLanguageContextlEnum;
import com.mi.oa.asset.excel.utils.ExcelUtils;
import com.mi.oa.asset.excel.validate.ValidateHandler;
import com.mi.oa.asset.excel.validate.Validation;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.mi.oa.asset.common.enums.EAMConstants.ENGLISH;
import static com.mi.oa.asset.excel.common.ExcelConstants.ATTACHMENT_FILENAME;
import static com.xiaomi.mit.common.http.api.HeaderNames.CONTENT_DISPOSITION;

/**
 * <AUTHOR>
 * @Date 2023/10/9 17:42
 */

@DubboService
@Slf4j
@RefreshScope
public class AssetSkuProviderImpl implements AssetSkuProvider {

    private static final String IMPORT_TEMP_FUN_ID = "asset_sku";
    private static final String UPDATE_TEMP_FUN_ID = "asset_sku_update";

    @Resource
    private AssetSkuRepo assetSkuRepo;

    @Resource
    private AssetSkuAbility assetSkuAbility;

    @Resource
    private AssetCategoryAbility assetCategoryAbility;

    @Resource
    private AssetSkuConverter converter;

    @Resource
    private AssetSkuManageRepo assetSkuManageRepo;

    @Resource
    private AssetSkuMgConverter mgConverter;

    @Resource
    private CommonDataRepo commonDataRepo;

    @Resource
    private AssetCategoryRepo assetCategoryRepo;

    @Resource
    private CountryConfigRepo countryConfigRepo;

    @Value("${sku-export-template}")
    private String skuExportTemplate;

    // todo 缺少正式环境的
    @Value("${eam.mdm.integrate.host}")
    private String integrateHost;

    // todo 缺少正式环境的
    @Value("${eam.mdm.integrate.appId}")
    private String integrateAppId;

    // todo 缺少正式环境的
    @Value("${eam.mdm.integrate.appKey}")
    private String integrateAppKey;

    private static final String DEFAULT_CREATE_SYSTEM = "EAM2";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveAssetSku(SaveAssetSkuReq req) {
        AssetSku assetSku = converter.toAssetSku(req);
        //校验参数
        assetSkuAbility.checkParams(assetSku);

        assetSkuAbility.loadAssetCategoryManage(assetSku);
        if (null == assetSku.getSkuId()) {
            // 生成skuCode
            assetSkuAbility.genSkuCode(assetSku);
        }
        //保存物料
        assetSkuRepo.saveAssetSku(assetSku);
        //保存物料管理信息
        assetSkuManageRepo.saveAssetSkuManage(mgConverter.toAssetSkuMgList(assetSku.getManages(), assetSku.getSkuId()));
        return assetSku.getSkuId();
    }

    @Override
    public void deleteAssetSku(List<Integer> ids) {
        DelAssetSkuReq req = new DelAssetSkuReq(ids, StringUtils.EMPTY);
        deleteAssetSku(req);
    }

    @Override
    public void deleteAssetSku(DelAssetSkuReq req) {
        //记录要被删除的skuId
        List<Integer> skuIds = new ArrayList<>();
        List<Integer> delManageIds = new ArrayList<>();
        //删除必须要选择业务线
        if(StringUtils.isNotBlank(req.getBusinessLine()) && CollectionUtils.isNotEmpty(req.getIds())){
            List<String> businessLines = Arrays.asList(req.getBusinessLine().split(","));
            Map<Integer, List<AssetSkuManage>> skuManageMap = assetSkuManageRepo.getAssetSkusManages(req.getIds());
            for (Integer skuId : req.getIds()) {
                List<AssetSkuManage> manages = skuManageMap.get(skuId);
                if (CollectionUtils.isNotEmpty(manages)) {
                    //所有的ids
                    List<Integer> mIds = manages.stream().map(AssetSkuManage::getId).collect(Collectors.toList());
                    //本次要删除的
                    List<Integer> delIds =
                            manages.stream().filter(m -> businessLines.contains(m.getBusinessLine().getCode())).map(AssetSkuManage::getId).collect(Collectors.toList());
                    delManageIds.addAll(delIds);
                    //移除掉本次删除的
                    mIds.removeAll(delIds);
                    //如果移除掉后为空，则删除主物料数据
                    if(mIds.isEmpty()){
                        skuIds.add(skuId);
                    }
                }else{
                    //如果对应的管理信息为空，则删除主物料数据
                    skuIds.add(skuId);
                }
            }
        }
        //批量删除
        assetSkuManageRepo.deleteAssetSkuManage(delManageIds);
        if(CollectionUtils.isNotEmpty(skuIds)){
            List<AssetSku> skus = assetSkuRepo.getAssetSkus(skuIds);
            skus.forEach(assetSkuAbility::checkCanDelete);
            assetSkuRepo.deleteAssetSku(skuIds);
        }
    }

    @Override
    public PageData<AssetSkuRes> getAssetSkuPageData(List<String> businessLineCodes, String keyword, Integer pageNum, Integer pageSize, Boolean disabled, Boolean containMiGoods) {
//        PageData<AssetSku> page = assetSkuRepo.getAssetSkuPageData(assetCategoryAbility.getRelationCateIds(businessLineCodes), keyword, PageRequest.of(pageSize, pageNum), disabled, containMiGoods);

//        List<Integer> relationCateIds = assetCategoryAbility.getRelationCateIds(businessLineCodes);
        return assetSkuAbility.getAssetSkuPageData(Collections.emptyList(), businessLineCodes, keyword, PageRequest.of(pageSize, pageNum), disabled, containMiGoods);
    }

    @Override
    public PageData<AssetSkuRes> getAssetSkuPageData(Integer cateId, List<String> businessLineCodes, String keyword, Integer pageNum, Integer pageSize, Boolean disabled, Boolean containMiGoods) {
//        PageData<AssetSku> page = assetSkuRepo.getAssetSkuPageData(assetCategoryAbility.getRelationCateIds(cateId), keyword, PageRequest.of(pageSize, pageNum), disabled, containMiGoods);

        List<Integer> relationCateIds = assetCategoryAbility.getRelationCateIds(cateId);
        return assetSkuAbility.getAssetSkuPageData(relationCateIds, businessLineCodes, keyword, PageRequest.of(pageSize, pageNum), disabled, containMiGoods);
    }

    @Override
    public AssetSkuRes getAssetSku(Integer skuId) {
        AssetSkuRes assetSkuRes = converter.toAssetSkuRes(assetSkuRepo.getAssetSku(skuId));
        if (assetSkuRes != null) {
            assetSkuRes.setManages(mgConverter.toAssetSkuMgResList(assetSkuManageRepo.getByAssetSkuId(assetSkuRes.getSkuId())));
        }
        return assetSkuRes;
    }

    @Override
    public AssetSkuRes getAssetSkuBySkuCode(String skuCode) {
        AssetSkuRes assetSkuRes = converter.toAssetSkuRes(assetSkuRepo.getAssetSkuBySkuCode(skuCode));
        if (assetSkuRes != null) {
            assetSkuRes.setManages(mgConverter.toAssetSkuMgResList(assetSkuManageRepo.getByAssetSkuId(assetSkuRes.getSkuId())));
        }
        return assetSkuRes;
    }

    @Override
    public AssetSkuRes getAssetSkuBySkuCode(String businessLine, String skuCode) {
        AssetSku assetSku = assetSkuRepo.getAssetSkuBySkuCode(skuCode);
        if (assetSku == null) {
            return null;
        }
        AssetSkuRes assetSkuRes = converter.toAssetSkuRes(assetSku);
        List<AssetSkuManage> manages = assetSkuManageRepo.getByAssetSkuId(assetSku.getSkuId());
        List<AssetSkuManage> filteredManages = manages.stream()
                .filter(m -> businessLine.equals(m.getBusinessLine().getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filteredManages)) {
            AssetSkuManage assetSkuManage = filteredManages.get(0);
            assetSkuRes.setCateId(assetSkuManage.getCateId());
            assetSkuRes.setCateCode(assetSkuManage.getCateCode());
            assetSkuRes.setCateName(assetSkuManage.getCateName());
            assetSkuRes.setManages(mgConverter.toAssetSkuMgResList(filteredManages));
        } else if (!assetSkuRes.getBusinessLine().equals(businessLine)) {
            assetSkuRes.setCateId(null);
            assetSkuRes.setCateCode("");
            assetSkuRes.setCateName("");
        }
        return assetSkuRes;
    }

    @Override
    public List<AssetSkuRes> listBySkuCodes(List<String> skuCodes) {
        if (CollectionUtils.isEmpty(skuCodes)) return new ArrayList<>();
        List<AssetSkuRes> list = converter.toAssetSkuRes(assetSkuRepo.getAssetSkuCodes(skuCodes));
        if (CollectionUtils.isEmpty(list)) return list;
        Map<Integer, List<AssetSkuManage>> manageMap = assetSkuManageRepo.getAssetSkusManages(list.stream().map(AssetSkuRes::getSkuId).collect(Collectors.toList()));
        list.forEach(i -> {
            List<AssetSkuManage> manageList = manageMap.get(i.getSkuId());
            if (CollectionUtils.isNotEmpty(manageList)) {
                i.setManages(mgConverter.toAssetSkuMgResList(manageList));
            }
        });
        return list;
    }

    @Override
    public List<AssetSkuRes> listBySkuCodes(String businessLine, List<String> skuCodes) {
        // 批量查询SKU
        List<AssetSku> skus = assetSkuRepo.getAssetSkuCodes(skuCodes);
        if (CollectionUtils.isEmpty(skus)) return Collections.emptyList();
        List<AssetSkuRes> result = converter.toAssetSkuRes(skus);
        Map<Integer, List<AssetSkuManage>> manageMap = assetSkuManageRepo.getAssetSkusManages(skus.stream().map(AssetSku::getSkuId).collect(Collectors.toList()));
        result.forEach(res -> {
            List<AssetSkuManage> manages = manageMap.get(res.getSkuId());
            if (CollectionUtils.isNotEmpty(manages)) {
                List<AssetSkuManage> filteredManages = manages.stream()
                        .filter(m -> businessLine.equals(m.getBusinessLine().getCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filteredManages)) {
                    AssetSkuManage assetSkuManage = filteredManages.get(0);
                    res.setCateId(assetSkuManage.getCateId());
                    res.setCateCode(assetSkuManage.getCateCode());
                    res.setCateName(assetSkuManage.getCateName());
                    res.setManages(mgConverter.toAssetSkuMgResList(filteredManages));
                } else if (!res.getBusinessLine().equals(businessLine)) {
                    res.setCateId(null);
                    res.setCateCode("");
                    res.setCateName("");
                }
            }
        });
        return result;
    }

    @Override
    public void deleteAssetSkuManage(Integer skuId, Integer manageId) {

        AssetSku assetSku = assetSkuRepo.getAssetSku(skuId);
        if (assetSku == null)
            throw new ErrorCodeException(ErrorCodes.NOT_FOUND, "数据不合法");

        List<AssetSkuManage> manages = assetSkuManageRepo.getByAssetSkuId(skuId);
        if (CollectionUtils.isEmpty(manages) || manages.size() == 1)
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "无法删除");
        //判断当前管理信息ID业务线跟物料信息业务线一致就不允许删除
        manages.forEach(m -> {
            if (m.getId().equals(manageId) && m.getBusinessLine().equals(assetSku.getBusinessLine())) {
                throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "无法删除，至少有一条管理信息");
            }
        });
        assetSkuManageRepo.deleteAssetSkuManage(Collections.singletonList(manageId));
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importSku(MultipartFile file, HttpServletResponse response) {
        // 校验文件格式，必须为excel文件
        ExcelUtils.isExcelExtension(Objects.requireNonNull(file.getOriginalFilename()));
        String  language = ExcelLanguageContextlEnum.current();
        // 获取数据
        InputStream inputStream = file.getInputStream();
        byte[] bytes = assetSkuAbility.saveInputStream(inputStream);
        List<AssetSkuImportData> assetSkuImportData = ExcelUtils.readExcel(new ByteArrayInputStream(bytes), AssetSkuImportData.class, 2);
        for (AssetSkuImportData assetSkuImportDatum : assetSkuImportData) {
            BusinessLine businessLine = BusinessLine.getByDesc(assetSkuImportDatum.getBusinessLine());
            assetSkuImportDatum.setBusinessLine(Objects.nonNull(businessLine) ? businessLine.getCode() : "");  // 此时也是保存编码
        }

        // 保存各类基础数据的缓存
        Map<String, Object> cacheMap = new HashMap<>();
        assetSkuAbility.initBaseDataToCache(assetSkuImportData, cacheMap);

        TemplateUrl templateUrl = commonDataRepo.getTemplateUrl(IMPORT_TEMP_FUN_ID);
        URL url = new URL(templateUrl.getTemplateUrl());
        URLConnection conn = url.openConnection();
        InputStream tplIs = conn.getInputStream();

        ExcelUtils.readExcel(new ByteArrayInputStream(bytes), AssetSkuImportData.class, 2,
                new ValidateHandler<AssetSkuImportData>(response.getOutputStream(), tplIs) {
                    @Override
                    protected String validate(String filedName, Object value, AssetSkuImportData data) {
                        // 校验数据
                        return assetSkuAbility.importValidate(filedName, value, data, cacheMap, language);
                    }

                    @Override
                    protected void onEffectiveData(List<AssetSkuImportData> effectiveData) {
                        if (CollectionUtils.isEmpty(effectiveData)) return;
                        assetSkuAbility.saveSkuData(effectiveData, cacheMap);
                    }

                    @SneakyThrows
                    @Override
                    protected void onFinish(List<Validation.ValidateResult> validateResults) {
                        response.reset();
                        response.setCharacterEncoding("utf-8");
                        if (!validateResults.isEmpty()) {
                            String fileName = "物料导入-错误数据.xlsx";
                            if (ENGLISH.equals(language)) {
                                fileName = "Bulk Import Template for Material - Wrong Data.xlsx";
                            }
                            String errorFile = URLEncoder.encode(fileName, "UTF-8").replace("+", "%20");
                            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + errorFile);
                            super.onFinish(validateResults);
                        }
                    }
                });
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateImportSku(MultipartFile file, HttpServletResponse response) {
        // 校验文件格式，必须为excel文件
        ExcelUtils.isExcelExtension(Objects.requireNonNull(file.getOriginalFilename()));
        String  language = ExcelLanguageContextlEnum.current();

        // 获取数据
        InputStream inputStream = file.getInputStream();
        byte[] bytes = assetSkuAbility.saveInputStream(inputStream);
        List<AssetSkuUpdateData> assetSkuUpdateData = ExcelUtils.readExcel(new ByteArrayInputStream(bytes), AssetSkuUpdateData.class, 2);
        for (AssetSkuUpdateData assetSkuUpdateDatum : assetSkuUpdateData) {
            BusinessLine businessLine = BusinessLine.getByDesc(assetSkuUpdateDatum.getBusinessLine());
            assetSkuUpdateDatum.setBusinessLine(Objects.nonNull(businessLine) ? businessLine.getCode() : "");  // 此时也是保存编码
        }

        // 保存各类基础数据的缓存
        Map<String, Object> cacheMap = new HashMap<>();
        assetSkuAbility.initUpdateDataToCache(assetSkuUpdateData, cacheMap);

        TemplateUrl templateUrl = commonDataRepo.getTemplateUrl(UPDATE_TEMP_FUN_ID);
        URL url = new URL(templateUrl.getTemplateUrl());
        URLConnection conn = url.openConnection();
        InputStream tplIs = conn.getInputStream();

        ExcelUtils.readExcel(new ByteArrayInputStream(bytes), AssetSkuUpdateData.class, 2,
                new ValidateHandler<AssetSkuUpdateData>(response.getOutputStream(), tplIs) {
                    @Override
                    protected String validate(String filedName, Object value, AssetSkuUpdateData data) {
                        // 校验数据
                        return assetSkuAbility.updateValidate(filedName, value, data, cacheMap, language);
                    }

                    @Override
                    protected void onEffectiveData(List<AssetSkuUpdateData> effectiveData) {
                        if (CollectionUtils.isEmpty(effectiveData)) return;
                        assetSkuAbility.updateSkuData(effectiveData, cacheMap);
                    }

                    @SneakyThrows
                    @Override
                    protected void onFinish(List<Validation.ValidateResult> validateResults) {
                        response.reset();
                        response.setCharacterEncoding("utf-8");
                        if (!validateResults.isEmpty()) {
                            String fileName = "物料更新-错误数据.xlsx";
                            if (ENGLISH.equals(language)) {
                                fileName = "Bulk Update Template for Material - Wrong Data.xlsx";
                            }
                            String errorFile = URLEncoder.encode(fileName, "UTF-8").replace("+", "%20");
                            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + errorFile);
                            super.onFinish(validateResults);
                        }
                    }
                });
    }

    @Override
    @SneakyThrows
    public void exportSku(HttpServletResponse response, AssetSkuExportReq req) {
        if (req == null || CollectionUtils.isEmpty(req.getBusinessLine()))
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "必填参数不能为空");

        List<Integer> relationCateIds;
        if (req.getCateId() != null) {
            relationCateIds = assetCategoryAbility.getRelationCateIds(req.getCateId());
        } else {
            relationCateIds = assetCategoryAbility.getRelationCateIds(req.getBusinessLine());
        }
        List<AssetSku> data = assetSkuRepo.listAssetSkuByParams(relationCateIds, req.getBusinessLine(), req.getKeyword());
        if (CollectionUtils.isEmpty(data))
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "数据不合法");

        List<Integer> skuIds = data.stream().map(AssetSku::getSkuId).collect(Collectors.toList());
        Map<Integer, List<AssetSkuManage>> manageMap = assetSkuManageRepo.getAssetSkusManagesByBusinessLines(skuIds, req.getBusinessLine());
        if (MapUtils.isEmpty(manageMap))
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "数据不合法");

        List<AssetSkuImportData> exportDataList = converter.toAssetSkuImportData(data, manageMap);
        URL url = new URL(skuExportTemplate);
        URLConnection conn = url.openConnection();
        InputStream is = conn.getInputStream();
        OutputStream outputStream = response.getOutputStream();
        String fileName = (!ENGLISH.equals(req.getLanguage()) ? "物料数据" : "Material data") + DateUtils.dateTimeToString(new Date()) + ".xlsx";
        String newFileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader(CONTENT_DISPOSITION, ATTACHMENT_FILENAME + newFileName);
        EasyExcelFactory.write(outputStream).withTemplate(is).sheet().doFill(exportDataList);
    }


    @Override
    public void createPurchaseItem(List<String> skuCode) throws Exception {
        // 校验参数
        if (CollectionUtils.isEmpty(skuCode)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "skuCode不能为空");
        }

        // 根据skuCode查询SKU数据
        List<AssetSku> assetSkus = assetSkuRepo.getAssetSkuCodes(skuCode);
        if (CollectionUtils.isEmpty(assetSkus)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "未找到对应的SKU数据：" + skuCode);
        }

        // 获取分类编码
        List<String> cateCodes = assetSkus.stream()
                .map(AssetSku::getCateCode)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(cateCodes)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "数据异常,未找到对应的分类编码" + cateCodes);
        }

        // 判断分类编码是否存在
        List<String> notExistsCateCodes = cateCodes.stream()
                .filter(cateCode -> !assetCategoryRepo.isExists(cateCode))
                .collect(Collectors.toList());
        // 如果有不存在的分类编码，抛出异常
        if (CollectionUtils.isNotEmpty(notExistsCateCodes)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "数据异常,分类编码不存在 " + notExistsCateCodes);
        }

        // 根据分类编码获取分类信息
        List<AssetCategory> byCateCodes = assetCategoryRepo.getByCateCodes(cateCodes);
        // 构建 cateCode -> purchaseCatalogCode 的映射  有重复key时保留第一个
        Map<String, String> cateCodeToPurchaseCatalogCode = byCateCodes.stream().collect(Collectors.toMap(AssetCategory::getCateCode, AssetCategory::getPurchaseCatalogCode, (v1, v2) -> v1));
        // 构建分类编码到skuCode的映射
        Map<String, List<String>> cateCodeToSkuCodes = new HashMap<>();
        for (AssetSku sku : assetSkus) {
            cateCodeToSkuCodes.computeIfAbsent(sku.getCateCode(), k -> new ArrayList<>()).add(sku.getSkuCode());
        }

        // 检查所有分类是否都有采购目录编码
        List<String> missingPurchaseCodes = cateCodes.stream()
                .filter(code -> StringUtils.isBlank(cateCodeToPurchaseCatalogCode.get(code)))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(missingPurchaseCodes)) {
            StringBuilder errorMsg = new StringBuilder("以下分类缺少采购目录编码：");
            for (String cateCode : missingPurchaseCodes) {
                List<String> skuCodes = cateCodeToSkuCodes.getOrDefault(cateCode, Collections.emptyList());
                errorMsg.append("\n分类编码: ").append(cateCode).append("，对应SkuCode: ").append(skuCodes);
            }
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, errorMsg.toString());
        }

        // 获取国家编码3位
        List<String> countryCodes = assetSkus.stream()
                .map(AssetSku::getCountry)
                .distinct()
                .collect(Collectors.toList());
        // 查询国家配置
        List<CountryConfigDo> countryConfigDos = countryConfigRepo.getByThreeCode(countryCodes);
        // 构建三位编码到二位编码的映射
        Map<String, String> threeToTwoCodeMap = countryConfigDos.stream().collect(Collectors.toMap(CountryConfigDo::getCountryCodeAlphaThree, CountryConfigDo::getCountryCodeAlphaTwo, (v1, v2) -> v1));
        // 检查是否所有国家都有对应的二位编码映射
        List<String> missingCountryCodes = countryCodes.stream()
                .filter(code -> !threeToTwoCodeMap.containsKey(code))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(missingCountryCodes)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "以下国家编码缺少二位码映射：" + missingCountryCodes);
        }

        List<CreatePurchaseItemDTO> dtoList = new ArrayList<>();
        for (AssetSku assetSku : assetSkus) {
            if (StringUtils.isBlank(assetSku.getSkuName())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "SKU名称不能为空：" + assetSku.getSkuCode());
            }
            if (assetSku.getMiGoodsId() != null && !assetSku.getMiGoodsId().isEmpty()) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "小米商品,无法推送SKU，skuCode: " + assetSku.getSkuCode());
            }
            if (StringUtils.isBlank(assetSku.getCountry())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "国家编码不能为空：" + assetSku.getSkuCode());
            }
            if (assetSku.getPrice() != null && assetSku.getPrice().compareTo(BigDecimal.ZERO) < 0) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "价格不能为负数：" + assetSku.getSkuCode());
            }
            CreatePurchaseItemDTO dto = new CreatePurchaseItemDTO();
            dto.setPrice(Optional.ofNullable(assetSku.getPrice())
                    .map(BigDecimal::doubleValue)
                    .orElse(0D));
            dto.setSkuId(assetSku.getSkuCode());
            dto.setCateCode(cateCodeToPurchaseCatalogCode.get(assetSku.getCateCode()));
            dto.setSkuName(assetSku.getSkuName());
            dto.setCountry(threeToTwoCodeMap.get(assetSku.getCountry()));
            dto.setIsSn(assetSku.getIsSn() == null || !assetSku.getIsSn().equals(YesNo.NO.getCode()));
            boolean isMiGoods = StringUtils.isNotBlank(assetSku.getMiGoodsId());
            dto.setIsMiGoods(isMiGoods);
            if (isMiGoods) {
                dto.setGoodsId(assetSku.getMiGoodsId());
            }
            dto.setIsConsumable(assetSku.getMaterialType() == MaterialType.CONSUMABLE);
            dto.setIsActive(true);
            dto.setPurchaseItemBizType(MdmPurchaseItemBusinessTypeEnum.XZ.getCode());
            // 只有mdmCreateStatus为1时才设置miSkuCode
            if (YesNo.YES.getCode().equals(assetSku.getMdmCreateStatus())) {
                dto.setMiSkuCode(assetSku.getMiSkuCode());
            }
            dtoList.add(dto);
        }

        List<Map<String, Object>> dataList = new ArrayList<>();
        Gson gson = new GsonBuilder().create();
        for (CreatePurchaseItemDTO dto : dtoList) {
            Map<String, Object> data = gson.fromJson(gson.toJson(dto), new TypeToken<Map<String, Object>>() {}.getType());
            data.put("SourceSystem", DEFAULT_CREATE_SYSTEM);
            data.put("SourceSystemSeq", dto.getSkuId());
            dataList.add(data);
        }
        Map<String, Object> params = new HashMap<>();
        //params.put("createId", createId);
        params.put("List", dataList);

        try {
            X5Response res = HttpClientUtil.doX5Post(integrateHost, integrateAppId, integrateAppKey, params, null);
            if (!X5ResponseHeader.SUCCESS_CODE.equals(res.getHeader().getCode())) {
                throw new Exception(res.getHeader().getMessage());
            }
            if (res.getBody() != null) {
                Object bodyObj = res.getBody();
                String bodyMsg = null;
                if (bodyObj instanceof Map) {
                    Object msgObj = ((Map<?, ?>) bodyObj).get("message");
                    if (msgObj != null && StringUtils.isNotBlank(msgObj.toString())) {
                        bodyMsg = msgObj.toString();
                    }
                }
                if (StringUtils.isNotBlank(bodyMsg)) {
                    throw new Exception(bodyMsg);
                }
            }
        } catch (Exception e) {
            log.error("推送MDM采购项失败", e);
            throw new Exception("推送MDM采购项失败：" + e.getMessage());
        }
    }





    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sharedBusinessLine(SharedBusinessLineReq req) {
        if (req == null)
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "参数不能为空");
        if(CollectionUtils.isEmpty(req.getSkuIds())){
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "skuIds不能为空");
        }
        if(CollectionUtils.isEmpty(req.getBusinessLines())){
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "businessLines不能为空");
        }
        Map<Integer, List<AssetSkuManage>> assetSkusManageMap = assetSkuManageRepo.getAssetSkusManages(req.getSkuIds());
        for (Integer skuId : req.getSkuIds()) {
            List<AssetSkuManage> assetSkuManages = assetSkusManageMap.get(skuId);
            List<AssetSkuManage> newAssetSkuManages = new ArrayList<>();
            //需要分享到的业务线
            for (String businessLine : req.getBusinessLines()) {
                //是否已经存在
                boolean exits =
                        CollectionUtils.isNotEmpty(assetSkuManages) && assetSkuManages.stream().anyMatch(assetSkuManage -> assetSkuManage.getBusinessLine().getCode().equals(businessLine));
                if (!exits) {
                    AssetSkuManage newAssetSkuManage = new AssetSkuManage();
                    newAssetSkuManage.setBusinessLine(BusinessLine.getByCode(businessLine));
                    newAssetSkuManages.add(newAssetSkuManage);
                }
            }
            assetSkuManageRepo.saveAssetSkuManage(mgConverter.toAssetSkuMgList(newAssetSkuManages, skuId));
        }
    }

}
