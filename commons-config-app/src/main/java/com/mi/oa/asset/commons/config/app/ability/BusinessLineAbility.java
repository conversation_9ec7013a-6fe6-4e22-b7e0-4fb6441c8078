package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.businessline.AllotConfigReq;
import com.mi.oa.asset.commons.config.api.businessline.BusinessLineReq;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigDataRage;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigField;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigLimitData;
import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.domain.common.repository.AllotConfigRepo;
import com.mi.oa.asset.commons.config.domain.common.repository.BusinessLineRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.AllotConfig;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/16 21:21
 */

@Service
public class BusinessLineAbility {

    @Resource
    private BusinessLineRepo businessLineRepo;

    @Resource
    private AllotConfigRepo allotConfigRepo;

    public void businessLineSaveCheck(BusinessLineReq businessLineReq) {
        Integer businessLineId = businessLineReq.getBusinessLineId();
        String businessLine = businessLineReq.getBusinessLine();
        List<AllotConfigReq> allotConfigs = businessLineReq.getAllotConfigs();
        // 校验业务线是否存在
        if (Objects.isNull(businessLineId)) {
            BusinessLineDo businessLineDo = businessLineRepo.searchByBusinessLine(businessLine);
            if (Objects.nonNull(businessLineDo)) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "【" + businessLine + "】，该业务线编码已存在，请修改");
            }
        }

        checkRoleDept(allotConfigs);
        for (AllotConfigReq allotConfig : allotConfigs) {
            String fieldCode = allotConfig.getFieldCode();
            AllotConfigField allotConfigField = AllotConfigField.getByCode(fieldCode);
            if (Objects.isNull(allotConfigField)) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, fieldCode + " 字段不支持配置");
            }

            List<String> limitBizData = allotConfig.getLimitBizData();
            String dataRage = allotConfig.getDataRage();
            checkConfigField(limitBizData, dataRage, allotConfigField);
        }
    }

    private void checkConfigField(List<String> limitBizData, String dataRage, AllotConfigField allotConfigField) {
        AllotConfigDataRage allotConfigDataRage = AllotConfigDataRage.getByCode(dataRage);
        if (Objects.isNull(allotConfigDataRage)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, dataRage + " 数据范围不存在");
        }
        if (AllotConfigDataRage.ALL != allotConfigDataRage && CollectionUtils.isEmpty(limitBizData)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, allotConfigField.getDesc() + " 字段的限定内容不能为空");
        }

        // 可选的数据范围校验
        List<AllotConfigDataRage> dataRageList = allotConfigField.getDataRageList();
        List<String> dataRageCodeList = dataRageList.stream().map(AllotConfigDataRage::getCode).collect(Collectors.toList());
        if (!dataRageCodeList.contains(dataRage)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, allotConfigField.getDesc() + " 当前字段不允许配置" + allotConfigDataRage.getDesc() + "数据范围");
        }

        // 数据范围为全部，直接跳过校验
        if (AllotConfigDataRage.ALL.getCode().equals(dataRage)) return;

        // 数据限制的具体范围校验
        List<AllotConfigLimitData> dataLimitList = allotConfigField.getDataLimitList();
        if (Objects.isNull(dataLimitList)) return;
        if (CollectionUtils.isNotEmpty(dataLimitList) && CollectionUtils.isEmpty(limitBizData)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, allotConfigField.getDesc() + " 当前字段必须配置数据范围");
        }

        List<String> dataLimitCodeList = dataLimitList.stream().map(AllotConfigLimitData::getCode).collect(Collectors.toList());
        for (String limitBizDatum : limitBizData) {
            AllotConfigLimitData limitData = AllotConfigLimitData.getByCode(limitBizDatum);
            String desc = Objects.isNull(limitData) ? limitBizDatum : limitData.getDesc();
            if (!dataLimitCodeList.contains(limitBizDatum)) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, allotConfigField.getDesc() + " 当前字段不允许配置【" + desc + "】数据范围");
            }
        }
    }

    private void checkRoleDept(List<AllotConfigReq> allotConfigs) {
        // 只有调入人选择限定业务角色，使用部门或管理部门才能选限定业务角色部门
        boolean isBizRole = false;
        boolean isRoleDept = false;
        for (AllotConfigReq allotConfig : allotConfigs) {
            String fieldCode = allotConfig.getFieldCode();
            String dataRage = allotConfig.getDataRage();
            List<String> limitBizData = allotConfig.getLimitBizData();
            if (AllotConfigField.IN_USER_NAME.getCode().equals(fieldCode)
                    && AllotConfigDataRage.BIZ_ROLE.getCode().equals(dataRage)) {
                isBizRole = true;
            }
            if (CollectionUtils.isNotEmpty(limitBizData) && limitBizData.contains(AllotConfigLimitData.ROLE_DEPT.getCode())) {
                isRoleDept = true;
            }
        }
        if (!isBizRole && isRoleDept) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "只有调入人选择限定业务角色，使用部门或管理部门才能选限定业务角色部门");
        }
    }

    public boolean isMustUseDept(Integer businessLineId) {
        List<AllotConfig> allotConfigs = allotConfigRepo.allotConfigList(businessLineId);
        return allotConfigs.stream()
                .anyMatch(allotConfig -> AllotConfigField.IN_MANAGE_DEPT_CODE == allotConfig.getFieldCode() &&
                        allotConfig.getLimitBizData().contains(AllotConfigLimitData.USE_MANAGE_DEPT.getCode()));
    }
}
