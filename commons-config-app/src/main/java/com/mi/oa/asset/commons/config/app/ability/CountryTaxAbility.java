package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.domain.international.entity.CountryTaxDo;
import com.mi.oa.asset.commons.config.domain.international.enums.DefaultDataEnums;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryTaxRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/7
 */
@Service
@Slf4j
public class CountryTaxAbility {

    @Resource
    private CountryTaxRepo countryTaxRepo;

    /**
     * 校验是否重复增加数据，以及默认税率
     * @param entity
     */
    public void check(CountryTaxDo entity) {
        List<CountryTaxDo> byCountryId = countryTaxRepo.getByCountryId(entity.getCountryConfigId());
        boolean notEmpty = CollectionUtils.isNotEmpty(byCountryId);
        if (notEmpty){
            Integer id = entity.getId();
            boolean isDuplicate = (id != null)
                    ? checkUpdate(byCountryId, id, entity)
                    : checkCreate(byCountryId, entity);

            if (isDuplicate) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER,
                        "当前国家的税码和税率组合已存在");
            }

            boolean isDefault  = (id != null)
                    ? checkDeaultUpdate(byCountryId, id, entity)
                    : checkDeaultCreate(byCountryId, entity);

            if (isDefault) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER,
                        "该税率和国家已有默认的税率，请修改默认税率。");
            }
        }
    }
    // 带ID校验方法
    private boolean checkUpdate(List<CountryTaxDo> list, Integer id, CountryTaxDo entity) {
        return list.stream()
                .filter(Objects::nonNull)
                .anyMatch(item -> !Objects.equals(item.getId(), id)
                        && isTaxCombinationMatch(item, entity));
    }

    // 无ID校验方法
    private boolean checkCreate(List<CountryTaxDo> list, CountryTaxDo entity) {
        return list.stream()
                .anyMatch(item -> isTaxCombinationMatch(item, entity));
    }

    // 共用条件判断
    private boolean isTaxCombinationMatch(CountryTaxDo item, CountryTaxDo entity) {
        return item.getTaxRate().compareTo(entity.getTaxRate()) == 0
                && Objects.equals(item.getTaxCode(), entity.getTaxCode());
    }

    private boolean checkDeaultUpdate(List<CountryTaxDo> list, Integer id, CountryTaxDo entity) {
        return list.stream()
                .filter(Objects::nonNull)
                .anyMatch(item -> !Objects.equals(item.getId(), id)
                        && isDefaultMatch(item,  entity));
    }

    private boolean checkDeaultCreate(List<CountryTaxDo> list, CountryTaxDo entity) {
        return list.stream()
                .anyMatch(item -> isDefaultMatch(item,  entity));
    }
    private boolean isDefaultMatch(CountryTaxDo item, CountryTaxDo entity) {
        return Objects.equals(DefaultDataEnums.DEFAULTTAXRATE.getKey(),item.getDefaultTaxRate())
                && entity.getDefaultTaxRate() != null && Objects.equals(DefaultDataEnums.DEFAULTTAXRATE.getKey(), entity.getDefaultTaxRate());
    }

}
