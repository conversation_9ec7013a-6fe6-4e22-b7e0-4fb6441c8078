package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineProvider;
import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineReq;
import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineRes;
import com.mi.oa.asset.commons.config.app.converter.CountryBusinessLineConverter;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryBusinessLineDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryBusinessLineRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Service
@Slf4j
@DubboService
public class CountryBusinessLineProviderImpl implements CountryBusinessLineProvider {

    @Resource
    private CountryBusinessLineRepo countryBusinessLineRepo;

    @Resource
    private CountryBusinessLineConverter converter;

    @Override
    public List<CountryBusinessLineRes> getCountryRegionList() {
        return converter.listDoToRes(countryBusinessLineRepo.searchAll());
    }

    @Override
    public CountryBusinessLineRes getById(Integer id) {
        CountryBusinessLineDo countryConfigDo = countryBusinessLineRepo.getById(id);
        if (countryConfigDo == null) return null;
        return converter.doToRes(countryConfigDo);
    }

    @Override
    public List<CountryBusinessLineRes> getCountryId(Integer countryId) {
        List<CountryBusinessLineDo> byCountryId = countryBusinessLineRepo.getByCountryId(countryId);
        if (byCountryId == null) return null;
        return converter.listDoToRes(byCountryId);
    }

    @Override
    public List<CountryBusinessLineRes> getByBusinessLine(String businessLine) {
        List<CountryBusinessLineDo> byBusinessLine = countryBusinessLineRepo.getByBusinessLine(businessLine);
        if (byBusinessLine == null) return null;
        return converter.listDoToRes(byBusinessLine);
    }

    @Override
    public Integer saveOrUpdate(CountryBusinessLineReq req) {
        CountryBusinessLineDo entity = converter.reqToDo(req);
        if (entity.getId() != null) {
            countryBusinessLineRepo.updateById(entity);
            return entity.getId();
        } else {
            return countryBusinessLineRepo.save(entity);
        }
    }

    @Override
    public void removeByIds(List<Integer> ids) {
        countryBusinessLineRepo.deleteByIds(ids);
    }

}
