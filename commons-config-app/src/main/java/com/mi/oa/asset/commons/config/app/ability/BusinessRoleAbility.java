package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleInfoRes;
import com.mi.oa.asset.commons.config.app.converter.BusinessRoleConverter;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRole;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRoleUser;
import com.mi.oa.asset.commons.config.domain.businessrole.repository.BusinessRoleRepo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/01/15 19:55
 */

@Service
public class BusinessRoleAbility {

    @Resource
    private BusinessRoleRepo businessRoleRepo;

    @Resource
    private BusinessRoleConverter converter;

    public List<BusinessRoleInfoRes> getBusinessRoleInfo(AssetOrgUnit orgUnit, String language) {
        List<BusinessRoleInfoRes> list = new ArrayList<>();
        List<BusinessRoleUser> businessRoleUserList = businessRoleRepo.getRoleByOrgCodeAndBusiness(orgUnit.getOrgCode(), orgUnit.getBusinessLine());
        if (CollectionUtils.isEmpty(businessRoleUserList)) {
            return list;
        }
        Map<String, List<BusinessRoleUser>> roleMap = businessRoleUserList.stream()
                .collect(Collectors.groupingBy(BusinessRoleUser::getRoleCode));
        List<BusinessRole> roles = businessRoleRepo.findByCodes(businessRoleUserList.stream().map(BusinessRoleUser::getRoleCode).distinct().collect(Collectors.toList()));
        Map<String, BusinessRole> businessRoleMap = roles.stream().collect(Collectors.toMap(BusinessRole::getRoleCode, Function.identity()));
        for (Map.Entry<String, List<BusinessRoleUser>> roleKey : roleMap.entrySet()) {
            String roleCode = roleKey.getKey();
            BusinessRoleInfoRes roleInfoRes = new BusinessRoleInfoRes();
            BusinessRole role = businessRoleMap.getOrDefault(roleCode, new BusinessRole());
            roleInfoRes.setRoleCode(roleCode);
            if (!EAMConstants.ENGLISH.equals(language)) {
                roleInfoRes.setRoleName(role.getRoleName());
                roleInfoRes.setRoleDesc(role.getRoleDesc());
            } else {
                roleInfoRes.setRoleName(role.getRoleNameEn());
                roleInfoRes.setRoleDesc(role.getRoleDescEn());
            }
            roleInfoRes.setUsers(converter.toUserList(roleKey.getValue()));
            list.add(roleInfoRes);
        }
        return list;
    }
}
