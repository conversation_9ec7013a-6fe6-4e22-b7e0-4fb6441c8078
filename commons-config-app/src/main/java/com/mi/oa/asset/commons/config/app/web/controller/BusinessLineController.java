/**
 * <AUTHOR>
 * @date 2024-01-15
 */
package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.businessline.*;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

import static com.mi.oa.asset.common.enums.EAMConstants.LANGUAGE;

@RestController
@HttpApiModule(value = "BusinessLineController", apiController = BusinessLineController.class)
@RequestMapping("/configs")
public class BusinessLineController {
    @Autowired
    private HttpServletRequest request;

    @Resource
    private BusinessLineProvider businessLineProvider;

    @HttpApiDoc(apiName = "业务线明细查询接口", value = "/configs/business-line/detail", method = MiApiRequestMethod.GET)
    @GetMapping("/business-line/detail")
    public Result<BusinessLineRes> getBusinessLineDetail(@RequestParam(value = "businessLineId", required = false) Integer businessLineId,
                                                         @RequestParam(value = "businessLine", required = false) String businessLine) {
        if (Objects.isNull(businessLineId) && StringUtils.isBlank(businessLine)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "查询参数不能为空");
        }
        if (Objects.nonNull(businessLineId)) {
            return Result.success(businessLineProvider.getBusinessLineDetail(businessLineId));
        } else {
            return Result.success(businessLineProvider.getBusinessLineDetail(businessLine));
        }
    }

    @HttpApiDoc(apiName = "业务线明细保存接口（含子功能配置）", value = "/configs/business-line/submit", method = MiApiRequestMethod.POST)
    @PostMapping("/business-line/submit")
    public Result<Void> businessLineSave(@RequestBody @Valid BusinessLineReq businessLineReq) {
        businessLineProvider.businessLineSave(businessLineReq);
        return Result.success();
    }

    @HttpApiDoc(apiName = "业务线调拨配置查询接口", value = "/configs/business-line/allot-config/list", method = MiApiRequestMethod.GET)
    @GetMapping("/business-line/allot-config/list")
    public Result<List<AllotConfigRes>> allotConfigList(@RequestParam(value = "businessLineId") Integer businessLineId) {
        return Result.success(businessLineProvider.allotConfigList(businessLineId));
    }

    @HttpApiDoc(apiName = "业务线列表", value = "/configs/business-line/list", method = MiApiRequestMethod.GET)
    @GetMapping("/business-line/business-line/list")
    public Result<List<BusinessLineRes>> getBusinessLineList() {
        return Result.success(businessLineProvider.getBusinessLineList(request.getHeader(LANGUAGE)));
    }

    @HttpApiDoc(apiName = "管理线列表", value = "/configs//business-line/manage-line/list", method = MiApiRequestMethod.GET)
    @GetMapping("/business-line/manage-line/list")
    public Result<List<ManageLineRes>> getManageLineList() {
        return Result.success(businessLineProvider.getManageLineList(request.getHeader(LANGUAGE)));
    }

    @HttpApiDoc(apiName = "管理线明细查询接口", value = "/configs/manage-line/detail", method = MiApiRequestMethod.GET)
    @GetMapping("/manage-line/detail")
    public Result<List<ManageLineRes>> getManageLine(@RequestParam(value = "manageLines", required = false) List<String> manageLines) {
        return Result.success(businessLineProvider.getManageLines(manageLines));
    }

}
