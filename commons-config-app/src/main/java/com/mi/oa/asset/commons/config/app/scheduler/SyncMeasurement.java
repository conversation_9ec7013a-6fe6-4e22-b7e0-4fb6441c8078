package com.mi.oa.asset.commons.config.app.scheduler;

import com.mi.oa.asset.commons.config.api.function.FunctionProvider;
import com.mi.oa.asset.commons.config.api.function.GlobalValRes;
import com.mi.oa.asset.commons.config.app.converter.MeasurementConverter;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.commons.config.domain.mesurement.entity.MeasurementUnit;
import com.mi.oa.asset.commons.config.domain.mesurement.repository.MeasurementRepo;
import com.mi.oa.asset.eam.feign.mdm.MdmClient;
import com.mi.oa.asset.eam.feign.mdm.req.MdmBaseReq;
import com.mi.oa.asset.eam.feign.mdm.res.MdmBaseRes;
import com.mi.oa.asset.eam.feign.mdm.res.MdmMeasurementUnitRes;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Service
@PlanTask(name = "syncMeasurement", description = "定时同步计量单位", quartzCron = "0 9 5,13 * * ?")
public class SyncMeasurement implements PlanExecutor {

    @Resource
    private MdmClient mdmClient;

    @Resource
    private MeasurementRepo measurementRepo;

    @Resource
    private MeasurementConverter converter;

    @Resource
    private FunctionProvider functionProvider;

    @Override
    public void execute() {
        int page = 1;
        int pageSize = 200;
        int total = 0;
        int done = 0;

        log.info("Start sync measurement from MDM");
        MdmBaseReq req = MdmBaseReq.builder().pageNum(page).pageSize(pageSize).build();
        do {
            req.setPageNum(page++);
            MdmBaseRes<MdmMeasurementUnitRes> res = mdmClient.getMeasurementUnit(req);
            total = res.getTotal();
            List<MdmMeasurementUnitRes> list = res.getList();
            done += list.size();

            list.stream().forEach(i -> i.muId());

            // 无效的
            List<MdmMeasurementUnitRes> inactive = list.stream().filter(i -> !i.enabled()).collect(Collectors.toList());
            log.info("Inactivate measurement counts: {}", inactive.size());
            measurementRepo.inactivateMeasurement(inactive.stream().map(MdmMeasurementUnitRes::getMuId).collect(Collectors.toList()));

            // 有效的
            List<MdmMeasurementUnitRes> active = list.stream().filter(MdmMeasurementUnitRes::enabled).collect(Collectors.toList());
            List<GlobalValRes> amgMeasureUnitFilter = functionProvider.getGlobalValByCode(Collections.singletonList(CommonConstant.MEASURE_UNIT_FILTER));
            // 过滤measureCode重复的数据
            if (!CollectionUtils.isEmpty(amgMeasureUnitFilter)) {
                List<String> filterCodes = amgMeasureUnitFilter.stream().map(GlobalValRes::getValue).map(i -> i.split(CommonConstant.COMMA)).flatMap(Arrays::stream).collect(Collectors.toList());
                active = active.stream().filter(i -> !filterCodes.contains(i.getTechCode() + CommonConstant.HORIZONTAL + i.getMeasureCode())).collect(Collectors.toList());
            }
            if(CollectionUtils.isEmpty(active)) {
                continue;
            }

            List<String> muIds = active.stream().map(MdmMeasurementUnitRes::getMuId).collect(Collectors.toList());
            List<MeasurementUnit> exists = measurementRepo.getMeasurementUnits(muIds);

            List<String> existMuIds = exists.stream().map(MeasurementUnit::getMuId).collect(Collectors.toList());

            // 新增不存在的
            List<MdmMeasurementUnitRes> creates = active.stream().filter(i -> !existMuIds.contains(i.getMuId())).collect(Collectors.toList());
            log.info("create measurement counts: {}", creates.size());
            if (CollectionUtils.isNotEmpty(creates)) {
                measurementRepo.createMeasurements(converter.createMeasurement(creates));
            }

            // 更新存在的
            List<MdmMeasurementUnitRes> toUpdate = active.stream().filter(i -> existMuIds.contains(i.getMuId())).collect(Collectors.toList());
            List<MeasurementUnit> updates = converter.updateMeasurement(toUpdate, exists);
            log.info("update measurement counts: {}", updates.size());
            if (CollectionUtils.isNotEmpty(toUpdate)) {
                measurementRepo.updateMeasurements(updates);
            }
        } while (0 != total && done < total);

        log.info("End sync measurement from MDM");
    }
}
