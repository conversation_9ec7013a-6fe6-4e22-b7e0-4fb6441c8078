package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineReq;
import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineRes;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryBusinessLineDo;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CountryBusinessLineConverter extends CommonConverter {

    CountryBusinessLineRes doToRes(CountryBusinessLineDo regionCountryDo);

    List<CountryBusinessLineRes> listDoToRes(List<CountryBusinessLineDo> regionCountryDoList);

    CountryBusinessLineDo reqToDo(CountryBusinessLineReq regionCountryReq);

    List<CountryBusinessLineDo> listReqToDo(List<CountryBusinessLineReq> regionCountryReqList);

    CountryBusinessLineDo clone(CountryBusinessLineDo source);


}
