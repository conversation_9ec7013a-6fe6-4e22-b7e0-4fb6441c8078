package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.businessline.*;
import com.mi.oa.asset.commons.config.app.ability.BusinessLineAbility;
import com.mi.oa.asset.commons.config.app.converter.BusinessLineConvertor;
import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.domain.common.entity.ManageLineDo;
import com.mi.oa.asset.commons.config.domain.common.repository.AllotConfigRepo;
import com.mi.oa.asset.commons.config.domain.common.repository.BusinessLineRepo;
import com.mi.oa.asset.commons.config.domain.common.repository.ManageLineRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.AllotConfig;
import com.mi.oa.asset.commons.config.infra.common.CacheKey;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/10 11:49
 */

@DubboService
public class BusinessLineProviderImpl implements BusinessLineProvider {

    @Resource
    private BusinessLineRepo businessLineRepo;

    @Resource
    private AllotConfigRepo allotConfigRepo;

    @Resource
    private BusinessLineConvertor businessLineConvertor;

    @Resource
    private BusinessLineAbility businessLineAbility;

    @Resource
    private ManageLineRepo manageLineRepo;

    @Override
    public BusinessLineRes getBusinessLineDetail(Integer businessLineId) {
        BusinessLineDo businessLineDo = businessLineRepo.searchById(businessLineId);
        BusinessLineRes businessLineRes = businessLineConvertor.doToRes(businessLineDo);

        // 查询管理部门是否需要传使用部门
        businessLineRes.setIsMustUseDept(businessLineAbility.isMustUseDept(businessLineDo.getBusinessLineId()));
        return businessLineRes;
    }

    @Override
    public BusinessLineRes getBusinessLineDetail(String businessLine) {
        BusinessLineDo businessLineDo = businessLineRepo.searchByBusinessLine(businessLine);
        BusinessLineRes businessLineRes = businessLineConvertor.doToRes(businessLineDo);

        // 查询管理部门是否需要传使用部门
        businessLineRes.setIsMustUseDept(businessLineAbility.isMustUseDept(businessLineDo.getBusinessLineId()));
        return businessLineRes;
    }

    @Override
    public List<BusinessLineRes> batchGetBusinessLine(List<String> businessLines) {
        // 调用batchSearchByBusinessLine查询业务线，并转换为List<BusinessLineRes>
        List<BusinessLineDo> businessLineDos = businessLineRepo.batchSearchByBusinessLine(businessLines);
        return businessLineConvertor.doToResBatch(businessLineDos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void businessLineSave(BusinessLineReq businessLineReq) {
        // 参数校验
        businessLineAbility.businessLineSaveCheck(businessLineReq);

        BusinessLineDo businessLineDo = businessLineConvertor.reqToDo(businessLineReq);
        List<AllotConfigReq> allotConfigs = businessLineReq.getAllotConfigs();

        // 是否新增
        boolean isCreate = Objects.isNull(businessLineReq.getBusinessLineId());

        // 保存主单
        Integer businessLineId = businessLineRepo.save(businessLineDo);

        // 保存调拨配置
        allotConfigRepo.save(businessLineConvertor.allotConfigReqToDoList(allotConfigs), businessLineId, isCreate);

        // 更新部门展示类型缓存
        String useDeptKey = CacheKey.USE_DEPT_NAME_SHOW_WAY.genKey(businessLineReq.getBusinessLine());
        RedisUtils.set(useDeptKey, businessLineReq.getUseDeptNameShowWay());

        String manageDeptKey = CacheKey.MANAGE_DEPT_NAME_SHOW_WAY.genKey(businessLineReq.getBusinessLine());
        RedisUtils.set(manageDeptKey, businessLineReq.getManageDeptNameShowWay());
    }

    @Override
    public List<AllotConfigRes> allotConfigList(Integer businessLineId) {
        List<AllotConfig> allotConfigs = allotConfigRepo.allotConfigList(businessLineId);
        return businessLineConvertor.allotConfigToResList(allotConfigs);
    }

    @Override
    public List<AllotConfigRes> allotConfigList(String businessLine) {
        BusinessLineDo businessLineDo = businessLineRepo.searchByBusinessLine(businessLine);
        return allotConfigList(businessLineDo.getBusinessLineId());
    }

    @Override
    public List<BusinessLineRes> getBusinessLineList(String language) {
        List<BusinessLineRes> list = businessLineConvertor.doToResBatch(businessLineRepo.searchAll());
        if (!EAMConstants.CHINESE.equals(language) && StringUtils.isNotEmpty(language)) {
            list.forEach(i -> i.setBusinessLineName(i.getBusinessLineNameEn()));
        }
        return list;
    }

    @Override
    public List<ManageLineRes> getManageLineList(String language) {
        List<ManageLineRes> list = businessLineConvertor.doToManageResBatch(manageLineRepo.getManageLine());
        if (!EAMConstants.CHINESE.equals(language) && StringUtils.isNotEmpty(language)) {
            list.forEach(i -> i.setManageLineName(i.getManageLineNameEn()));
        }
        return list;
    }

    @Override
    public ManageLineRes getManageLine(String manageLine) {
        if (StringUtils.isBlank(manageLine)) return null;
        List<ManageLineRes> manageLines = getManageLines(Arrays.asList(manageLine));
        return manageLines.get(0);
    }

    @Override
    public List<ManageLineRes> getManageLines(List<String> manageLines) {
        List<ManageLineDo> list = manageLineRepo.getManageLine(manageLines);
        List<String> manageLineCodes = list.stream().map(ManageLineDo::getManageLine).collect(Collectors.toList());
        List<BusinessLineDo> businessLine = businessLineRepo.getBusinessLine(manageLineCodes);
        Map<String, List<BusinessLineDo>> businessLineMap = businessLine.stream().collect(Collectors.groupingBy(BusinessLineDo::getManageLineCode));

        List<ManageLineRes> lineRes = new ArrayList<>(list.size());
        for (ManageLineDo manageLineDo : list) {
            String manageLine = manageLineDo.getManageLine();
            List<BusinessLineDo> businessLineDos = businessLineMap.get(manageLine);
            ManageLineRes line = new ManageLineRes();
            line.setManageLine(manageLineDo.getManageLine());
            line.setManageLineName(manageLineDo.getManageLineName());
            line.setBusinessLines(businessLineConvertor.doToResBatch(businessLineDos));
            lineRes.add(line);
        }
        return lineRes;
    }
}
