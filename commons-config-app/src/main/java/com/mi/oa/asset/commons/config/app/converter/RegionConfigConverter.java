package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.regionconfig.EmployeeRegionCountryRes;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigReq;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigRes;
import com.mi.oa.asset.commons.config.domain.international.entity.RegionConfigDo;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RegionConfigConverter extends CommonConverter {

    RegionConfigRes doToRes(RegionConfigDo regionConfigDo);

    List<RegionConfigRes> listDoToRes(List<RegionConfigDo> regionConfigDoList);

    RegionConfigDo reqToDo(RegionConfigReq regionConfigReq);

    List<RegionConfigDo> listReqToDo(List<RegionConfigReq> regionConfigReqList);

    EmployeeRegionCountryRes doToEmployeeRegionCountryRes(RegionConfigDo regionConfigDo);

    List<EmployeeRegionCountryRes> listDoToEmployeeRegionCountryRes(List<RegionConfigDo> regionConfigDo);

}
