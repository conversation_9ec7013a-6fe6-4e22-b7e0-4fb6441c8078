package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.systemvar.AssetCodeRuleConfig;
import com.mi.oa.asset.commons.config.api.systemvar.SystemVarDetail;
import com.mi.oa.asset.commons.config.api.systemvar.SystemVarProvider;
import com.mi.oa.asset.commons.config.app.converter.SystemVarConverter;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.commons.config.domain.systemvar.entity.SystemVar;
import com.mi.oa.asset.commons.config.domain.systemvar.repository.SystemVarRepo;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/3/19 14:57
 */

@DubboService
public class SystemVarProviderImpl implements SystemVarProvider {

    @Resource
    private SystemVarConverter converter;

    @Resource
    private SystemVarRepo systemVarRepo;

    @Override
    public void saveSystemVar(SystemVarDetail req) {
        // TODO: 检查是否存在
        systemVarRepo.saveSystemVar(converter.toSystemVar(req));
    }

    @Override
    public SystemVarDetail getSystemVar(String varCode, BusinessLine businessLine) {
        SystemVar systemVar = systemVarRepo.getSystemVar(varCode, businessLine);

        return converter.toSystemVarDetail(systemVar);
    }

    @Override
    public String getSystemVarValue(String varCode, BusinessLine businessLine) {
        SystemVar systemVar = systemVarRepo.getSystemVar(varCode, businessLine);

        return null == systemVar ? null : systemVar.getVarValue();
    }

    @Override
    public Map<String, String> getSystemVarValue(List<String> varCodes, BusinessLine businessLine) {
        List<SystemVar> list = systemVarRepo.getSystemVar(varCodes, businessLine);

        Map<String, String> values = new HashMap<>();
        list.forEach(systemVar -> values.put(systemVar.getVarCode(), systemVar.getVarValue()));

        return values;
    }

    @Override
    public AssetCodeRuleConfig getAssetCodeRuleConfig(BusinessLine businessLine) {
        String varCode = MessageFormat.format(CommonConstant.ASSET_CODE_RULE_VAR_CODE, businessLine.getCode());
        String varValue = getSystemVarValue(varCode, businessLine);
        if(StringUtils.isBlank(varValue)) {
            return null;
        }
        AssetCodeRuleConfig rule = JacksonUtils.json2Bean(varValue, AssetCodeRuleConfig.class);
        rule.getSelectedItemList().forEach(field -> {
            if (CommonConstant.NONE.equals(field.getConnector())) field.setConnector("");
        });

        return rule;
    }
}
