package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.warehouse.RemoveWarehouseAreaReq;
import com.mi.oa.asset.commons.config.api.warehouse.WarehouseAreaProvider;
import com.mi.oa.asset.commons.config.api.warehouse.WarehouseAreaReq;
import com.mi.oa.asset.commons.config.api.warehouse.WarehouseAreaRes;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.WarehouseArea;
import com.mi.oa.asset.commons.config.domain.warehouse.repository.WarehouseAreaRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@DubboService
public class WarehouseAreaProviderImpl implements WarehouseAreaProvider {

    @Resource
    private WarehouseAreaRepo warehouseAreaRepo;

    @Override
    public Integer saveWarehouse(WarehouseAreaReq req) {
        Objects.requireNonNull(req.getWarehouseCode(), "仓库编码不能为空");
        Objects.requireNonNull(req.getWarehouseName(), "仓库名称不能为空");
        Objects.requireNonNull(req.getBusinessLine(), "业务线不能为空");
        Objects.requireNonNull(req.getPriority(), "优先级不能为空");
        if (req.getPriority() <= 0) {
            throw new IllegalArgumentException("优先级必须是正整数");
        }
        // 自动切割 area_id，顺序：国家、省、市、区、街道，缺失的为null
        if (req.getAreaId() != null && !req.getAreaId().isEmpty()) {
            String[] arr = req.getAreaId().split("-");
            req.setCountryId(arr.length > 0 ? arr[0] : null);
            req.setProvinceId(arr.length > 1 ? arr[1] : null);
            req.setCityId(arr.length > 2 ? arr[2] : null);
            req.setAreaId(arr.length > 3 ? arr[3] : null);
            req.setStreetId(arr.length > 4 ? arr[4] : null);
        }
        WarehouseArea warehouseArea = new WarehouseArea();
        warehouseArea.setWarehouseCode(req.getWarehouseCode());
        warehouseArea.setWarehouseName(req.getWarehouseName());
        warehouseArea.setBusinessLine(req.getBusinessLine());
        warehouseArea.setPriority(req.getPriority());
        warehouseArea.setCountryId(req.getCountryId());
        warehouseArea.setProvinceId(req.getProvinceId());
        warehouseArea.setCityId(req.getCityId());
        warehouseArea.setAreaId(req.getAreaId());
        warehouseArea.setStreetId(req.getStreetId());
        warehouseArea.setAreaName(req.getAreaName());
        return warehouseAreaRepo.saveWarehouse(warehouseArea);
    }

    @Override
    public void removeWarehouse(RemoveWarehouseAreaReq req) {
        // 自动切割 area_id，顺序：国家、省、市、区、街道，缺失的为null
        if (req.getAreaId() != null && !req.getAreaId().isEmpty()) {
            String[] arr = req.getAreaId().split("-");
            req.setCountryId(arr.length > 0 ? arr[0] : null);
            req.setProvinceId(arr.length > 1 ? arr[1] : null);
            req.setCityId(arr.length > 2 ? arr[2] : null);
            req.setAreaId(arr.length > 3 ? arr[3] : null);
            req.setStreetId(arr.length > 4 ? arr[4] : null);
        }
        warehouseAreaRepo.removeWarehouse(req.getWarehouseCode(), req.getCountryId(), req.getProvinceId(), req.getCityId(), req.getAreaId(), req.getStreetId());
    }

    @Override
    public List<WarehouseAreaRes> listByBizAndRegion(List<String> businessLines, String region, String areaName) {
        if (businessLines == null || businessLines.isEmpty()) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线不能为空");
        }
        String countryId = null, provinceId = null, cityId = null, areaId = null, streetId = null;
        if (region != null && !region.isEmpty()) {
            String[] arr = region.split("-");
            countryId = arr.length > 0 ? arr[0] : null;
            provinceId = arr.length > 1 ? arr[1] : null;
            cityId = arr.length > 2 ? arr[2] : null;
            areaId = arr.length > 3 ? arr[3] : null;
            streetId = arr.length > 4 ? arr[4] : null;
        }
        List<WarehouseArea> warehouseAreas = warehouseAreaRepo.listByBizAndRegion(businessLines, countryId, provinceId, cityId, areaId, streetId, areaName);
        List<WarehouseAreaRes> resList = new ArrayList<>();
        for (WarehouseArea area : warehouseAreas) {
            if (area == null) continue;
            WarehouseAreaRes res = new WarehouseAreaRes();
            res.setId(area.getId());
            res.setWarehouseCode(area.getWarehouseCode());
            res.setWarehouseName(area.getWarehouseName());
            res.setBusinessLine(area.getBusinessLine());
            res.setPriority(area.getPriority());
            // 合成 area_id
            StringBuilder areaIdBuilder = new StringBuilder();
            if (area.getCountryId() != null) areaIdBuilder.append(area.getCountryId());
            if (area.getProvinceId() != null) areaIdBuilder.append("-").append(area.getProvinceId());
            if (area.getCityId() != null) areaIdBuilder.append("-").append(area.getCityId());
            if (area.getAreaId() != null) areaIdBuilder.append("-").append(area.getAreaId());
            if (area.getStreetId() != null) areaIdBuilder.append("-").append(area.getStreetId());
            res.setAreaId(areaIdBuilder.length() > 0 ? areaIdBuilder.toString() : null);
            res.setAreaName(area.getAreaName());
            resList.add(res);
        }
        resList.sort(Comparator.comparingInt(WarehouseAreaRes::getPriority));
        return resList;
    }

    /**
     * 根据关键词（仓编码或仓名称）模糊查询仓库区域，支持业务线过滤
     */
    @Override
    public List<WarehouseAreaRes> searchWarehouseArea(String keyword, List<String> businessLines) {
        if (businessLines == null || businessLines.isEmpty()) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线不能为空");
        }
        List<WarehouseArea> warehouseAreas = warehouseAreaRepo.searchWarehouseArea(keyword, businessLines);
        List<WarehouseAreaRes> warehouseAreaResList = new ArrayList<>();
        for (WarehouseArea warehouseArea : warehouseAreas) {
            WarehouseAreaRes warehouseAreaRes = new WarehouseAreaRes();
            warehouseAreaRes.setWarehouseCode(warehouseArea.getWarehouseCode());
            warehouseAreaRes.setWarehouseName(warehouseArea.getWarehouseName());
            warehouseAreaRes.setBusinessLine(warehouseArea.getBusinessLine());
            warehouseAreaRes.setPriority(warehouseArea.getPriority());
            warehouseAreaRes.setCountryId(warehouseArea.getCountryId());
            warehouseAreaRes.setProvinceId(warehouseArea.getProvinceId());
            warehouseAreaRes.setCityId(warehouseArea.getCityId());
            warehouseAreaRes.setAreaId(warehouseArea.getAreaId());
            warehouseAreaRes.setStreetId(warehouseArea.getStreetId());
            warehouseAreaResList.add(warehouseAreaRes);
        }
        warehouseAreaResList.sort(Comparator.comparingInt(WarehouseAreaRes::getPriority));
        return warehouseAreaResList;
    }
}
