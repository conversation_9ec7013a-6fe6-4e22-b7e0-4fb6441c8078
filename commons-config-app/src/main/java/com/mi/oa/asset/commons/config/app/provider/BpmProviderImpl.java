package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.enums.BpmLanguage;
import com.mi.oa.asset.commons.config.api.bpm.BpmApprovalTaskRes;
import com.mi.oa.asset.commons.config.api.bpm.BpmProvider;
import com.mi.oa.asset.commons.config.app.ability.BpmApprovalAbility;
import com.mi.oa.asset.commons.config.app.converter.BpmApproveConverter;
import com.mi.oa.asset.commons.config.domain.bpm.entity.BpmApprovalTask;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 审批
 * @Date 2024/9/23 10:56
 **/

@Slf4j
@Service
@DubboService
public class BpmProviderImpl implements BpmProvider {

    @Resource
    private BpmApprovalAbility bpmApprovalAbility;

    @Resource
    private BpmApproveConverter approveConverter;


    @Override
    public List<BpmApprovalTaskRes> getApprovalTaskList(String businessKey) {
        return getApprovalTaskList(businessKey, false);
    }

    @Override
    public List<BpmApprovalTaskRes> getApprovalTaskList(String businessKey, boolean needPredict) {
        return getApprovalTaskList(businessKey, needPredict, BpmLanguage.ZH.getCode());
    }

    @Override
    public List<BpmApprovalTaskRes> getApprovalTaskList(String businessKey, boolean needPredict, String language) {
        if (StringUtils.isBlank(businessKey)) {
            return Collections.emptyList();
        }
        List<BpmApprovalTask> bpmApprovalTasks = bpmApprovalAbility.listApprovalHistory(businessKey, needPredict,  language);
        return approveConverter.toTaskResList(bpmApprovalTasks);
    }
}
