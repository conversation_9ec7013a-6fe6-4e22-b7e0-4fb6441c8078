package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.assetcategory.*;
import com.mi.oa.asset.commons.config.app.ability.AssetCategoryAbility;
import com.mi.oa.asset.commons.config.app.converter.AssetCategoryConverter;
import com.mi.oa.asset.commons.config.app.converter.sync.SyncDeviceCategoryConverter;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.AssetCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.old.DeviceCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.old.MaterialCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.repository.AssetCategoryRepo;
import com.mi.oa.asset.commons.config.domain.assetcategory.repository.old.DeviceCategoryRepo;
import com.mi.oa.asset.commons.config.domain.assetcategory.repository.old.MaterialCategoryRepo;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:25
 */

@DubboService
@Slf4j
public class AssetCategoryProviderImpl implements AssetCategoryProvider {

    @Resource
    private AssetCategoryRepo assetCategoryRepo;

    @Resource
    private DeviceCategoryRepo deviceCategoryRepo;

    @Resource
    private AssetCategoryAbility assetCategoryAbility;

    @Resource
    private AssetCategoryConverter converter;

    @Resource
    private SyncDeviceCategoryConverter syncCategoryConverter;
    @Resource
    private MaterialCategoryRepo categoryRepo;

    @Resource
    private HttpServletRequest request;

    @Override
    public Integer saveAssetCategory(SaveAssetCategoryReq req) {
        AssetCategory assetCategory = converter.reqToAssetCategory(req);
        //分类业务线默认取管理信息第一条记录
        if (null == assetCategory.getCateId()) {
            // 生成编码
            assetCategoryAbility.genCateCode(assetCategory);
        } else {
            // 编码不可修改
            assetCategory.setCateCode(null);
        }

        // 加载上级相关信息
        assetCategoryAbility.loadInfoFromParent(assetCategory);
        // 保存分类
        assetCategoryRepo.saveAssetCategory(assetCategory);
        //更新catePath
        assetCategoryRepo.updateCatePath(assetCategory);

        return assetCategory.getCateId();
    }

    @Override
    public List<AssetCategoryRes> getAllAssetCategory(List<String> businessLineCodes) {
        List<AssetCategory> list = assetCategoryRepo.getAllAssetCategory(businessLineCodes, false);

        return converter.assetCategoriesToRes(list);
    }

    @Override
    public AssetCategoryRes getAssetCategory(Integer cateId) {
        AssetCategory assetCategory = assetCategoryRepo.getAssetCategory(cateId);
        assetCategoryAbility.loadFullCateName(assetCategory);

        return converter.assetCategoryToRes(assetCategory);
    }

    @Transactional
    @Override
    public void deleteAssetCategory(List<Integer> cateIds) {
        List<AssetCategory> categories = assetCategoryRepo.getAssetCategories(cateIds);
        if (CollectionUtils.isEmpty(categories)) return;
        categories.forEach(assetCategoryAbility::checkCanDelete);

        // 根据catePath匹配删除下级
        List<String> catePaths = categories.stream().map(AssetCategory::getCatePath).collect(Collectors.toList());
        assetCategoryRepo.deleteByPath(catePaths);
    }

    @Override
    public PageData<AssetCategoryRes> getAssetCategoryPageData(List<String> businessLineCodes, String keyword, Integer pageNum, Integer pageSize) {
        PageData<AssetCategory> data = assetCategoryRepo.assetCategoryPageData(businessLineCodes, keyword, PageRequest.of(pageSize, pageNum));
        String language = request.getHeader(EAMConstants.LANGUAGE);
        if (!EAMConstants.CHINESE.equals(language) && StringUtils.isNotEmpty(language)) {
            data.getList().forEach(i -> i.setCateName(i.getCateNameEn()));
        }
        assetCategoryAbility.loadFullCateName(data.getList());

        return converter.toPageData(data, converter::assetCategoriesToRes);
    }

    @Override
    public PageData<AssetCategoryRes> getAssetCategoryPageData(Integer cateId, String keyword, Integer pageNum, Integer pageSize) {
        String catePath = null;
        if (null != cateId && cateId > 0) {
            AssetCategory assetCategory = assetCategoryAbility.loadAssetCategory(cateId);
            catePath = assetCategory.getCatePath();
        }

        PageData<AssetCategory> data = assetCategoryRepo.assetCategoryPageData(catePath, keyword, PageRequest.of(pageSize, pageNum));
        String language = request.getHeader(EAMConstants.LANGUAGE);
        if (!EAMConstants.CHINESE.equals(language) && StringUtils.isNotEmpty(language)) {
            data.getList().forEach(i -> i.setCateName(i.getCateNameEn()));
        }
        assetCategoryAbility.loadFullCateName(data.getList());

        return converter.toPageData(data, converter::assetCategoriesToRes);
    }

    @Override
    public List<AssetCategoryX5Res> allAssetCategory(List<String> businessLineCodes) {
        if(CollectionUtils.isEmpty(businessLineCodes)) return Collections.emptyList();
        List<AssetCategory> list = assetCategoryRepo.getAllAssetCategory(businessLineCodes, true);
        //加载分类全名
        assetCategoryAbility.loadFullCateName(list);

        return converter.assetCategoriesToX5Res(list);
    }

    @Override
    public void importStdCategory(ImportStdCategoryReq req) {
        BusinessLine businessLine = BusinessLine.getByCode(req.getBusinessLine());
        if (null == businessLine) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线不存在");
        }

        // 之前已经导入
        List<AssetCategory> existedCategories = assetCategoryRepo.getByCatalogCodes(
                req.getItems().stream().map(ImportStdCategoryReq.Item::getPurchaseCatalogCode).collect(Collectors.toList()),
                businessLine
        );
        Map<String, AssetCategory> existedMap = existedCategories.stream().collect(
                Collectors.toMap(AssetCategory::getPurchaseCatalogCode, Function.identity())
        );

        List<AssetCategory> list = new ArrayList<>();
        req.getItems().forEach(i -> {
            AssetCategory existedOne = existedMap.get(i.getPurchaseCatalogCode());
            if (null == existedOne) {
                list.add(converter.toAssetCategory(i, businessLine, DataCreateSource.SYS_MDM));
            } else {
                converter.updateFromStd(i, existedOne);
                list.add(existedOne);
            }
        });
        //导入分类名称英文
        String language = request.getHeader(EAMConstants.LANGUAGE);
        if (!EAMConstants.CHINESE.equals(language) && StringUtils.isNotEmpty(language)) {
            list.forEach(i -> i.setCateNameEn(i.getCateName()));
        }
        // 保存
        assetCategoryRepo.saveAssetCategories(list);
        // 更新catePath
        assetCategoryAbility.setStdCatePath(list);
        assetCategoryRepo.saveAssetCategories(list);
    }

    @Override
    public AssetCategoryRes getAssetCategoryByCode(String businessLine, String cateCode) {
        AssetCategory assetCategory = assetCategoryRepo.getByCateCode(businessLine, cateCode);
        assetCategoryAbility.loadFullCateName(assetCategory);

        return converter.assetCategoryToRes(assetCategory);
    }

    @Override
    public List<AssetCategoryRes> getAssetCategoryByCode(String businessLine, List<String> cateCodes) {
        if (CollectionUtils.isEmpty(cateCodes)) return new ArrayList<>();
        List<AssetCategory> list = assetCategoryRepo.getByCateCode(businessLine, cateCodes);
        assetCategoryAbility.loadFullCateName(list);

        return converter.assetCategoriesToRes(list);
    }

    @Override
    public List<AssetCategoryRes> getAssetCategoryByIds(List<Integer> cateIds, boolean includeSub) {
        List<AssetCategory> list = assetCategoryRepo.getAssetCategories(cateIds);
        if (includeSub) {
            List<String> catePaths = list.stream().map(AssetCategory::getCatePath).collect(Collectors.toList());
            list.addAll(assetCategoryRepo.getByCatePaths(catePaths));
        }

        return converter.assetCategoriesToRes(list);
    }


    @Override
    public void syncAssetCategory(String businessLine) {
        for (int i = 0; i < 5; i++) {
            List<DeviceCategory> deviceCategories = deviceCategoryRepo.getAllAssetCategory(businessLine, i);
            if (CollectionUtils.isEmpty(deviceCategories)) {
                continue;
            }
            log.info("syncAssetCategory begin,businessLine:{},totalNum:{}", businessLine, deviceCategories.size());
            List<AssetCategory> assetCategories = new ArrayList<>();
            for (DeviceCategory deviceCategory : deviceCategories) {
                DeviceCategory parentCategory = null;
                if (StringUtils.isNotBlank(deviceCategory.getParentId())) {
                    parentCategory = deviceCategoryRepo.getAssetCategory(deviceCategory.getParentId());
                }
                AssetCategory assetCategory = syncCategoryConverter.toAssetCategory(deviceCategory);
                MaterialCategory category = categoryRepo.getMaterialCategory(String.valueOf(deviceCategory.getShowId()));
                assetCategory.setBusinessLine(BusinessLine.CAR);
                AssetCategory parentAssetCategory = null;
                if (parentCategory != null) {
                    assetCategory.setParentCateCode(parentCategory.getShowCode());
                    parentAssetCategory = assetCategoryRepo.getByCateCode(BusinessLine.CAR.getCode(), parentCategory.getShowCode());
                }
                if (category != null) {
                    assetCategory.setPurchaseCatalogCode(category.getTypeCode());
                    assetCategory.setPurchaseCatalogName(category.getTypeName());
                }
                if (parentAssetCategory != null) {
                    if (StringUtils.isNotBlank(assetCategory.getParentCateCode())) {
                        assetCategory.setPurchaseCatalogCodePath(StringUtils.isNotBlank(parentAssetCategory.getPurchaseCatalogCodePath()) ?
                                parentAssetCategory.getPurchaseCatalogCodePath() + "-" + assetCategory.getPurchaseCatalogCode() : assetCategory.getPurchaseCatalogCode());
                        assetCategory.setPurchaseCatalogNamePath(StringUtils.isNotBlank(parentAssetCategory.getPurchaseCatalogNamePath()) ?
                                parentAssetCategory.getPurchaseCatalogNamePath() + "-" + assetCategory.getPurchaseCatalogName() : assetCategory.getPurchaseCatalogName());
                    }
                    assetCategory.setCatePath(parentAssetCategory.getCatePath());
                } else {
                    assetCategory.setPurchaseCatalogCodePath(assetCategory.getPurchaseCatalogCode());
                    assetCategory.setPurchaseCatalogNamePath(assetCategory.getPurchaseCatalogName());
                }
                assetCategory.setIsMultipleManage(false);
                assetCategory.setIsSerialCodeManage(true);
                assetCategories.add(assetCategory);
            }
            if (i == 1) {
                assetCategoryRepo.saveAssetCategories(assetCategories);
                assetCategoryAbility.setStdCatePath(assetCategories);
                assetCategoryRepo.saveAssetCategories(assetCategories);
            } else {
                assetCategoryRepo.saveAssetCategories(assetCategories);
                assetCategoryRepo.updateCatePathBatch(assetCategories);
            }
            log.info("syncAssetCategory end,businessLine:{},totalNum:{},saveNum:{}", businessLine, deviceCategories.size(), assetCategories.size());
        }
    }
}
