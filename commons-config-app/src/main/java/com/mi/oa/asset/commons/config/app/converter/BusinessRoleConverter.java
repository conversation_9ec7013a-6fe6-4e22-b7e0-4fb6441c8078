package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleInfoReq;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleRes;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleUserRes;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRole;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRoleUser;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/01/15 19:35
 */

@Mapper(componentModel = "spring")
public interface BusinessRoleConverter {

    @Mapping(source = "user.userName", target = "userCode")
    @Mapping(source = "user.displayName", target = "userName")
    BusinessRoleUser toBusinessRoleUser(BusinessRole role, User user, BusinessLine businessLine, String orgCode);

    default List<BusinessRoleUser> toBusinessRoleUser(List<BusinessRoleInfoReq> source, List<BusinessRole> roles, BusinessLine businessLine, String orgCode) {
        Map<String, BusinessRole> roleMap = roles.stream().collect(Collectors.toMap(BusinessRole::getRoleCode, Function.identity()));
        List<BusinessRoleUser> list = new ArrayList<>();
        source.forEach(roleInfo -> {
            BusinessRole role = roleMap.get(roleInfo.getRoleCode());
            if (null == role) return;

            roleInfo.getUsers().forEach(user -> list.add(toBusinessRoleUser(role, user, businessLine, orgCode)));
        });

        return list;
    }

    @Mapping(source = "userCode", target = "userName")
    @Mapping(source = "userName", target = "displayName")
    @Mapping(source = "deptName", target = "deptName")
    @Mapping(source = "avatar", target = "avatar")
    User toUser(BusinessRoleUser source);

    List<User> toUserList(List<BusinessRoleUser> source);

    BusinessRoleUserRes toBusinessRoleUserRes(BusinessRoleUser businessRoleUser);

    List<BusinessRoleUserRes> toBusinessRoleUserRes(List<BusinessRoleUser> businessRoleUsers);

    BusinessRoleRes toBusinessRoleRes(BusinessRole businessRole);

    default BusinessRoleRes toBusinessRoleRes(BusinessRole businessRole, String language) {
        if (businessRole == null) return null;
        BusinessRoleRes businessRoleRes = toBusinessRoleRes(businessRole);
        if (!EAMConstants.CHINESE.equals(language)) {
            businessRoleRes.setRoleName(businessRole.getRoleNameEn());
            businessRoleRes.setRoleDesc(businessRole.getRoleDescEn());
        }
        return businessRoleRes;
    }

    List<BusinessRoleRes> toBusinessRoleRes(List<BusinessRole> businessRoles);

    default List<BusinessRoleRes> toBusinessRoleRes(List<BusinessRole> businessRoles, String language) {
        if (businessRoles == null) {
            return null;
        }
        List<BusinessRoleRes> list = new ArrayList<BusinessRoleRes>(businessRoles.size());
        for (BusinessRole businessRole : businessRoles) {
            list.add(toBusinessRoleRes(businessRole, language));
        }
        return list;
    }
}
