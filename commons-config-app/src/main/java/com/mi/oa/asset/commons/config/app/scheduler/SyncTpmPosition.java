package com.mi.oa.asset.commons.config.app.scheduler;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.app.ability.PositionAbility;

import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import com.mi.oa.asset.commons.config.domain.position.enums.TpmMdmTypeEnums;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 每天早上6点同步大家电TPM位置信息
 */
@Slf4j
@Service
@PlanTask(name = "syncTpmPosition", description = "定时同步大家电TPM位置信息", quartzCron = "0 0 6 * * ?")
public class SyncTpmPosition implements PlanExecutor {
    @Resource
    private PositionAbility positionAbility;

    @Override
    public void execute() {
        log.info("Start sync position from TPM");
        for (TpmMdmTypeEnums tpmMdmTypeEnums : TpmMdmTypeEnums.values()) {
            positionAbility.syncTpmPosition(tpmMdmTypeEnums);
        }
        // 更新positionPath
        positionAbility.updateTpmPositionPath(BusinessLine.HEA_FACTORY, DataCreateSource.SYS_TPM);
        log.info("End sync position from TPM");

        //同步空间位置信息
        positionAbility.syncSpacePosition();
    }
}
