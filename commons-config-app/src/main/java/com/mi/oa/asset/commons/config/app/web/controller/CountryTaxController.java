package com.mi.oa.asset.commons.config.app.web.controller;


import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxProvider;
import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxReq;
import com.mi.oa.asset.commons.config.api.countrytax.CountryTaxRes;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 税率配置表的 Controller
 */
@RestController
@HttpApiModule(value = "CountryTaxController", apiController = CountryTaxController.class)
@RequestMapping("/configs/countryTax")
@Slf4j
public class CountryTaxController {

    @Resource
    private CountryTaxProvider countryTaxProvider;

    @HttpApiDoc(apiName = "查询列表", value = "/configs/countryTax/list", method = MiApiRequestMethod.GET)
    @GetMapping("/list")
    public Result<List<CountryTaxRes>> list() {
        return Result.success(countryTaxProvider.getCountryTaxList());
    }

    @HttpApiDoc(apiName = "根据id查详情", value = "/configs/countryTax/detail", method = MiApiRequestMethod.GET)
    @GetMapping("/detail")
    public Result<CountryTaxRes> info(@RequestParam("countryTaxId") Integer countryTaxId) {
        return Result.success(countryTaxProvider.getById(countryTaxId));
    }

    @HttpApiDoc(apiName = "根据国家ID查详情", value = "/configs/countryTax/byCountryId", method = MiApiRequestMethod.GET)
    @GetMapping("/byCountryId")
    public Result<List<CountryTaxRes>> byCountryId(@RequestParam("countryId") Integer countryId) {
        return Result.success(countryTaxProvider.byCountryId(countryId));
    }

    @HttpApiDoc(apiName = "更新", value = "/configs/countryTax/edit", method = MiApiRequestMethod.POST)
    @PostMapping("/edit")
    public Result<Integer> edit(@RequestBody CountryTaxReq req) {
        log.info("countryTax edit : {}", JacksonUtils.bean2Json(req));
        return Result.success(countryTaxProvider.saveOrUpdate(req));
    }

    @HttpApiDoc(apiName = "删除", value = "/configs/countryTax/delete", method = MiApiRequestMethod.POST)
    @PostMapping("/delete")
    public Result<Void> delete(@RequestBody CountryTaxReq req) {
        log.info("countryTax delete : {}", JacksonUtils.bean2Json(req));
        countryTaxProvider.removeByIds(Collections.singletonList(req.getId()));
        return Result.success();
    }
}
