package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.assetshare.AssetShareClientEnum;
import com.mi.oa.asset.commons.config.api.assetshare.AssetShareProvider;
import com.mi.oa.asset.commons.config.api.assetshare.ShareConditionReq;
import com.mi.oa.asset.commons.config.app.ability.AssetShareAbility;
import com.mi.oa.asset.commons.config.domain.assetshare.valobj.ShareQueryReq;
import com.mi.oa.asset.eam.jxs.req.BaseQueryReq;
import com.mi.oa.asset.eam.jxs.req.QueryConditionReq;
import com.mi.oa.asset.eam.jxs.res.ListDataRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025-01-20 17:53
 */
@DubboService
@Slf4j
public class AssetShareProviderImpl implements AssetShareProvider {

    @Resource
    private AssetShareAbility assetShareAbility;

    @Override
    public String queryShareList(String businessLine, String userCode, String deptCode, String shareScene) {
        return queryShareList(businessLine, userCode, deptCode, shareScene, null);
    }

    @Override
    public String queryShareList(String businessLine, String userCode, String deptCode, String shareScene, String shareClient) {
        ShareQueryReq req = new ShareQueryReq();
        req.setBusinessLine(Arrays.asList(businessLine));
        req.setUserCode(userCode);
        req.setDeptCode(deptCode);
        req.setShareScene(shareScene);
        req.setShareClient(StringUtils.isBlank(shareClient) ? AssetShareClientEnum.EAM_MANAGE.getCode() : shareClient);
        return assetShareAbility.queryShareList(req);
    }

    @Override
    public List<Map<String, String>> customQueryShareAsset(List<ShareConditionReq> conditionReqs, List<String> businessLine, String shareScene) {
        return customQueryShareAsset(conditionReqs, businessLine, shareScene, null);
    }

    @Override
    public List<Map<String, String>> customQueryShareAsset(List<ShareConditionReq> conditionReqs, List<String> businessLine, String shareScene, String shareClient) {
        BaseQueryReq req = new BaseQueryReq();
        req.setBusinessLine(businessLine);
        req.setPageSize(10000);
        if (Objects.nonNull(conditionReqs)) {
            List<QueryConditionReq> queryConditionReqs = new ArrayList<>();
            for (ShareConditionReq conditionReq : conditionReqs) {
                queryConditionReqs.add(QueryConditionReq.builder()
                        .fieldCode(conditionReq.getFieldCode())
                        .fieldValues(conditionReq.getFieldValues())
                        .filedType(conditionReq.getFiledType())
                        .queryCond(conditionReq.getQueryCond())
                        .connRelate(conditionReq.getConnRelate()).build());
            }
            req.setQueryConditionReqs(queryConditionReqs);
        }
        // 默认查询EAM管理端数据
        if (StringUtils.isBlank(shareClient)) {
            req.setEamSource(AssetShareClientEnum.EAM_MANAGE.getCode());
        }
        ListDataRes<Map<String, String>> dataRes = assetShareAbility.customQueryShareAsset(req, shareScene);
        return dataRes.getList();
    }
}
