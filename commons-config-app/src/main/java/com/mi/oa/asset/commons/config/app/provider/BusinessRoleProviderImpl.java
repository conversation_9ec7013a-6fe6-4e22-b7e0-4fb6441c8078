package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.common.enums.YesNo;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleInfoReq;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleProvider;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleRes;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleUserRes;
import com.mi.oa.asset.commons.config.app.ability.AssetOrgAbility;
import com.mi.oa.asset.commons.config.app.converter.BusinessRoleConverter;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.assetorganization.repository.AssetOrgRepo;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgStructure;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRole;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRoleUser;
import com.mi.oa.asset.commons.config.domain.businessrole.repository.BusinessRoleRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/27 15:38
 */

@Slf4j
@DubboService
public class BusinessRoleProviderImpl implements BusinessRoleProvider {

    @Resource
    private AssetOrgRepo assetOrgRepo;

    @Resource
    private BusinessRoleRepo businessRoleRepo;

    @Resource
    private BusinessRoleConverter businessRoleConverter;

    @Resource
    private AssetOrgAbility assetOrgAbility;

    @Override
    public void saveBusinessRole(String orgCode, BusinessLine businessLine, List<BusinessRoleInfoReq> roleInfo) {
        if(CollectionUtils.isEmpty(roleInfo)) return;

        List<BusinessRole> businessRoles = businessRoleRepo.findByCodes(roleInfo.stream().map(BusinessRoleInfoReq::getRoleCode).collect(Collectors.toList()));
        List<BusinessRoleUser> currentRoleUsers = businessRoleRepo.getRoleByOrgCodeAndBusiness(orgCode, businessLine);
        List<BusinessRoleUser> targetRoleUsers = businessRoleConverter.toBusinessRoleUser(roleInfo, businessRoles, businessLine, orgCode);

        // 剔除存在的
        List<BusinessRoleUser> sameRoleUsers = new ArrayList<>(targetRoleUsers);
        sameRoleUsers.retainAll(currentRoleUsers);
        targetRoleUsers.removeAll(sameRoleUsers);
        currentRoleUsers.removeAll(sameRoleUsers);

        // 需要删除的
        businessRoleRepo.deleteByIds(currentRoleUsers.stream().map(BusinessRoleUser::getId).collect(Collectors.toList()));

        // 需要新增的
        businessRoleRepo.createRoleUser(targetRoleUsers);
    }

    @Override
    public void removeAllRoles(String orgCode, BusinessLine businessLine) {
        businessRoleRepo.deleteRoles(orgCode, businessLine);
    }

    @Override
    public Set<String> getBusinessRole(String orgCode, String businessLine, String roleCode) {
        AssetOrgUnit assetOrgUnit = assetOrgAbility.loadOrgUnit(orgCode, BusinessLine.getByCode(businessLine));
        List<BusinessRoleUser> roleUserList = businessRoleRepo.getRolesByOrgCode(roleCode, orgCode, assetOrgUnit.getBusinessLine());
        return roleUserList.stream().map(BusinessRoleUser::getUserCode).collect(Collectors.toSet());
    }

    @Override
    public Set<String> getBusinessRoleWithUp(String orgCode, String businessLine, String roleCode) {
        List<BusinessRoleUserRes> roleUserResList = getBusinessRoleInfoWithUp(orgCode, businessLine, roleCode);
        return roleUserResList.stream().map(BusinessRoleUserRes::getUserCode).collect(Collectors.toSet());
    }

    @Override
    public List<BusinessRoleUserRes> getBusinessRoleInfoWithUp(String orgCode, String businessLine, String roleCode) {
        AssetOrgStructure orgStructure = assetOrgRepo.getOrgStructureByCode(orgCode, Boolean.TRUE);
        if (null == orgStructure) return new ArrayList<>();

        List<BusinessRoleUser> result = findRoleUsersInOrgHierarchy(orgStructure, BusinessLine.getByCode(businessLine), roleCode);
        return businessRoleConverter.toBusinessRoleUserRes(result);
    }

    private List<BusinessRoleUser> findRoleUsersInOrgHierarchy(AssetOrgStructure currentOrg, BusinessLine businessLine, String roleCode) {
        if (YesNo.NO.getCode().equals(currentOrg.getDisabled())) {
            List<BusinessRoleUser> roleUserList = businessRoleRepo.getRolesByOrgCode(roleCode, currentOrg.getOrgCode(), businessLine);
            if (!CollectionUtils.isEmpty(roleUserList)) return roleUserList;
        }
        if (currentOrg.getLevel() == 1) return new ArrayList<>();

        AssetOrgStructure parentOrg = assetOrgRepo.getOrgStructureByCode(currentOrg.getParentCode(), Boolean.TRUE);
        if (null == parentOrg) return new ArrayList<>();

        return findRoleUsersInOrgHierarchy(parentOrg, businessLine, roleCode);
    }

    @Override
    public Set<String> getBatchBusinessRoleWithUp(List<String> orgCodes, String businessLine, String roleCode) {
        Set<String> res = new HashSet<>();
        orgCodes.forEach(orgCode -> res.addAll(getBusinessRoleWithUp(orgCode, businessLine, roleCode)));
        return res;
    }

    @Override
    public List<BusinessRoleRes> getBusinessRoleList() {
        return businessRoleConverter.toBusinessRoleRes(businessRoleRepo.getRoleList());
    }

    @Override
    public List<BusinessRoleRes> getBusinessRoleList(String language) {
        return businessRoleConverter.toBusinessRoleRes(businessRoleRepo.getRoleList(), language);
    }

    @Override
    public void removeAllRolesByOrgCodes(List<String> orgCode, BusinessLine businessLine) {
        businessRoleRepo.deleteRolesByOrgCodes(orgCode, businessLine);
    }
}
