package com.mi.oa.asset.commons.config.app.web.controller;


import com.mi.oa.asset.commons.config.api.datarange.DataRangeProvider;
import com.mi.oa.asset.commons.config.api.datarange.DataRangeReq;
import com.mi.oa.asset.commons.config.api.datarange.DeptRangeRes;
import com.mi.oa.asset.commons.config.api.use.UseReasonRes;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据范围查询
 *
 * <AUTHOR>
 * @date 2024-05-2 13:17:29
 */
@RestController
@HttpApiModule(value = "DataRangeController", apiController = DataRangeController.class)
@RequestMapping("/configs/data-range")
@Slf4j
@Validated
public class DataRangeController {
    @Resource
    private DataRangeProvider dataRangeProvider;


    @PostMapping("/cost-center")
    @HttpApiDoc(apiName = "成本中心数据范围", value = "/configs/data-range/cost-center", method = MiApiRequestMethod.GET)
    public Result<String> getCostCenter(@RequestBody DataRangeReq req) {
        return Result.success(dataRangeProvider.getCostCenter(req));
    }


    @PostMapping("/use-dept")
    @HttpApiDoc(apiName = "使用部门数据范围", value = "/configs/data-range/use-dept", method = MiApiRequestMethod.GET)
    public Result<List<DeptRangeRes>> getUseDept(@RequestBody DataRangeReq req) {
        return Result.success(dataRangeProvider.getDeptList(req, true));
    }


    @PostMapping("/manage-dept")
    @HttpApiDoc(apiName = "管理部门数据范围", value = "/configs/data-range/manage-dept", method = MiApiRequestMethod.GET)
    public Result<List<DeptRangeRes>> getManageDept(@RequestBody DataRangeReq req) {
        return Result.success(dataRangeProvider.getDeptList(req, false));
    }

    @PostMapping("/company")
    @HttpApiDoc(apiName = "公司主体数据范围", value = "/configs/data-range/company", method = MiApiRequestMethod.GET)
    public Result<List<DeptRangeRes>> getCompany(@RequestBody DataRangeReq req) {
        return Result.success(dataRangeProvider.getCompany(req));
    }

}
