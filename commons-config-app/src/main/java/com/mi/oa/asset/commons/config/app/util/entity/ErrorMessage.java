package com.mi.oa.asset.commons.config.app.util.entity;/**
 * @description TODO
 * <AUTHOR>
 * @date 2025/5/20 17:44
 */

import com.alibaba.excel.annotation.ExcelProperty;

/**
 *@description
 *<AUTHOR>
 *@date 2025/5/20 17:44
 */
public class ErrorMessage {

    @ExcelProperty("词条key")
    private String identifier;

    @ExcelProperty("翻译结果")
    private String entry;

    public ErrorMessage() {

    }

    public ErrorMessage(String identifier, String entry) {
        this.identifier = identifier;
        this.entry = entry;
    }

    public String getEntry() {
        return entry;
    }

    public void setEntry(String entry) {
        this.entry = entry;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }
}
