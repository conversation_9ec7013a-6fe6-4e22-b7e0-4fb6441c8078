package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.assetquit.AssetQuitProvider;
import com.mi.oa.asset.commons.config.api.assetquit.AssetQuitRes;
import com.mi.oa.asset.commons.config.app.converter.AssetQuitConverter;
import com.mi.oa.asset.commons.config.domain.assetquit.entity.AssetQuit;
import com.mi.oa.asset.commons.config.domain.assetquit.repository.AssetQuitRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 离职员工信息表 dubbo 接口实现
 * <AUTHOR>
 * @date 2024-04-12 02:05:55
 */
@DubboService
@Slf4j
public class AssetQuitProviderImpl implements AssetQuitProvider {
    @Resource
    private AssetQuitRepo assetQuitRepo;

    @Resource
    private AssetQuitConverter assetQuitConverter;
    @Override
    public boolean isQuitUser(String userCode) {
        if(StringUtils.isBlank(userCode)){
            log.warn("判断账号是否离职时，账号为空！");
            return false;
        }
        return assetQuitRepo.exists(userCode);
    }

    public List<AssetQuitRes> queryResignEmpByTime(String dataTime){
        List<AssetQuit> assetQuits = assetQuitRepo.queryResignEmpByTime(dataTime);
        return assetQuitConverter.toAssetQuitResList(assetQuits);
    }
}

