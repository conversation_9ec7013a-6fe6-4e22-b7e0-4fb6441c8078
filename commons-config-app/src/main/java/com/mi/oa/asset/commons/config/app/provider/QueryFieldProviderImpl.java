package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.queryfield.*;
import com.mi.oa.asset.commons.config.app.ability.QueryConfigAbility;
import com.mi.oa.asset.commons.config.app.ability.QueryFieldAbility;
import com.mi.oa.asset.commons.config.app.converter.QueryFieldConverter;
import com.mi.oa.asset.commons.config.domain.queryfield.entity.FieldConfig;
import com.mi.oa.asset.commons.config.domain.queryfield.entity.QueryConfig;
import com.mi.oa.asset.commons.config.domain.queryfield.repository.FieldConfigRepo;
import com.mi.oa.asset.commons.config.domain.queryfield.repository.QueryConfigRepo;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:54
 */

@Service
@Slf4j
@DubboService
public class QueryFieldProviderImpl implements QueryFieldProvider {

    @Resource
    QueryConfigRepo queryConfigRepo;

    @Resource
    FieldConfigRepo fieldConfigRepo;

    @Resource
    QueryFieldConverter queryFieldConverter;

    @Resource
    QueryFieldAbility queryFieldAbility;

    @Resource
    private QueryConfigAbility queryConfigAbility;

    @Override
    public List<QueryConfigRes> queryConfigList(String manageLine, String funId) {
        // 获取当前登录人信息
        String authedUserName = AuthFacade.authedUserName();
        if (StringUtils.isBlank(authedUserName)) {
            log.error("用户信息获取失败！");
            throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Failed to obtain user information!");
        }
        List<QueryConfig> queryConfigs = queryConfigRepo.queryConfigList(manageLine, funId, authedUserName);
        String defaultConfigName = queryConfigAbility.getDefaultConfigName(funId);
        List<QueryConfig> collect = queryConfigs.stream().filter(o -> !defaultConfigName.equals(o.getQueryName())).collect(Collectors.toList());
        return queryFieldConverter.queryConfigToResList(collect);
    }

    @Override
    public QueryConfigRes queryDefaultConfig(String manageLine, String funId) {
        // 获取当前登录人信息
        String authedUserName = AuthFacade.authedUserName();
        if (StringUtils.isBlank(authedUserName)) {
            log.error("用户信息获取失败！");
            throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Failed to obtain user information!");
        }
        String defaultName = queryConfigAbility.getDefaultConfigName(funId);
        QueryConfig queryConfig = queryConfigRepo.queryConfigByName(manageLine, funId, authedUserName, defaultName);
        return queryFieldConverter.queryConfigToRes(queryConfig);
    }

    @Override
    public void saveQueryConfig(QueryConfigReq queryConfigReq, Boolean isDefaultConfig) {
        String defaultName = queryConfigAbility.getDefaultConfigName(queryConfigReq.getFunId());
        if (!isDefaultConfig) {
            String queryName = queryConfigReq.getQueryName();
            if (defaultName.equals(queryName)) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "The name does not comply with the specification and conflicts with the default name. Please change it.");
            }
        } else {
            queryConfigReq.setQueryName(defaultName);
        }

        // 获取当前登录人信息
        String authedUserName = AuthFacade.authedUserName();
        if (StringUtils.isBlank(authedUserName)) {
            log.error("用户信息获取失败！");
            throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Failed to obtain user information!");
        }
        QueryConfig queryConfig = queryFieldConverter.queryConfigReqToDo(queryConfigReq);
        queryConfigRepo.saveQueryConfig(queryConfig);


        if (!isDefaultConfig) {
            // 前端要求仅对新增的数据返回主键
            if (null == queryConfigReq.getQueryId()) {
                queryConfigReq.setQueryId(queryConfig.getQueryId());
            } else {
                queryConfigReq.setQueryId(null);
            }
        } else {
            queryConfigReq.setQueryId(queryConfig.getQueryId());
        }
    }

    @Override
    public void deleteQueryConfig(Integer queryId) {
        queryConfigRepo.deleteQueryConfig(queryId);
    }

    @Override
    public FieldConfigRes searchFieldConfig(String manageLine, String funId) {
        // 获取当前登录人信息
        String authedUserName = AuthFacade.authedUserName();
        if (StringUtils.isBlank(authedUserName)) {
            log.error("用户信息获取失败！");
            throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Failed to obtain user information!");
        }
        FieldConfig fieldConfig = fieldConfigRepo.searchFieldConfig(manageLine, funId, authedUserName);

        // 如果没有配置，取配置文件中默认配置
        if (null == fieldConfig) {
            fieldConfig = queryFieldAbility.getDefaultConfig(manageLine, funId);
        }

        return queryFieldConverter.fieldConfigToRes(fieldConfig);
    }

    @Override
    public FieldConfigRes searchItemFieldConfig(String manageLine, String businessLine, String funId, String type) {
        // 获取当前登录人信息
        String authedUserName = AuthFacade.authedUserName();
        if (StringUtils.isBlank(authedUserName)) {
            log.error("用户信息获取失败！");
            throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Failed to obtain user information!");
        }
        FieldConfig fieldConfig = fieldConfigRepo.searchFieldConfig(manageLine, funId, authedUserName);
        if (null != fieldConfig) {
            FieldConfigRes res = queryFieldConverter.fieldConfigToRes(fieldConfig);
            queryConfigAbility.loadAssetAccountFields(res, businessLine, type);
            return res;
        } else {
            // 如果没有配置，取配置文件中默认配置
            fieldConfig = queryFieldAbility.getDefaultConfig(manageLine, funId);
            FieldConfigRes res = queryFieldConverter.fieldConfigToRes(fieldConfig);
            queryConfigAbility.loadAssetAccountFields(res, businessLine, type);
            return res;
        }
    }


    @Override
    public void saveFieldConfig(FieldConfigReq fieldConfigReq) {
        FieldConfig fieldConfig = queryFieldConverter.fieldConfigReqToDo(fieldConfigReq);
        fieldConfigRepo.saveFieldConfig(fieldConfig);
        fieldConfigReq.setFieldId(fieldConfig.getFieldId());
    }
}
