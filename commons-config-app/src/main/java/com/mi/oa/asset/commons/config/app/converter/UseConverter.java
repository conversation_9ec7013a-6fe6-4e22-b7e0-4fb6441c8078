package com.mi.oa.asset.commons.config.app.converter;



import com.mi.oa.asset.commons.config.api.use.UseReasonRes;
import com.mi.oa.asset.commons.config.api.use.UseReq;
import com.mi.oa.asset.commons.config.api.use.UseRes;
import com.mi.oa.asset.commons.config.domain.use.entity.Use;
import com.mi.oa.asset.commons.config.domain.use.entity.UseReason;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 办公用途 appConverter 转换器
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@Mapper(componentModel = "spring")
public interface UseConverter extends CommonConverter {
    List<UseRes> toUseResList(List<Use> source);

    Use toUse(UseReq source);

    List<UseReasonRes> toUseReasonResList(List<UseReason> source);
}