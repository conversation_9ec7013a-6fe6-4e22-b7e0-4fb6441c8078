package com.mi.oa.asset.commons.config.app.web.controller;


import com.mi.oa.asset.commons.config.api.regionconfig.EmployeeRegionCountryRes;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigProvider;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigReq;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigRes;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 区域配置表的 Controller
 */
@RestController
@HttpApiModule(value = "RegionConfigController", apiController = RegionConfigController.class)
@RequestMapping("/configs/regionConfig")
@Slf4j
public class RegionConfigController {

    @Resource
    private RegionConfigProvider regionConfigProvider;


    @HttpApiDoc(apiName = "查询列表", value = "/configs/regionConfig/list", method = MiApiRequestMethod.GET)
    @GetMapping("/list")
    public Result<List<RegionConfigRes>> list() {
        return Result.success(regionConfigProvider.getRegionConfigList());
    }



    @HttpApiDoc(apiName = "根据id获取详情", value = "/configs/regionConfig/detail", method = MiApiRequestMethod.GET)
    @GetMapping("/detail")
    public Result<EmployeeRegionCountryRes> info(@RequestParam("regionConfigId") Integer regionConfigId) {
        return Result.success(regionConfigProvider.getRegionAndCountrys(regionConfigId));
    }

    @HttpApiDoc(apiName = "员工端切换地区国家列表查询", value = "/configs/regionConfig/listEmployeeRegionCountry", method = MiApiRequestMethod.GET)
    @GetMapping("/listEmployeeRegionCountry")
    public Result<List<EmployeeRegionCountryRes>> listEmployeeRegionCountry() {
        return Result.success(regionConfigProvider.getRegionAndCountrysList());
    }

    @HttpApiDoc(apiName = "更新", value = "/configs/regionConfig/edit", method = MiApiRequestMethod.POST)
    @PostMapping("/edit")
    public Result<Void> edit(@RequestBody RegionConfigReq req) {
        log.info("regionConfig edit : {}", JacksonUtils.bean2Json(req));
        regionConfigProvider.saveOrUpdate(req);
        return Result.success();
    }

    @HttpApiDoc(apiName = "删除", value = "/configs/regionConfig/delete", method = MiApiRequestMethod.POST)
    @PostMapping("/delete")
    public Result<Void> delete(@RequestBody RegionConfigReq req) {
        log.info("regionConfig delete : {}", JacksonUtils.bean2Json(req));
        regionConfigProvider.removeByIds(Collections.singletonList(req.getId()));
        return Result.success();
    }
}
