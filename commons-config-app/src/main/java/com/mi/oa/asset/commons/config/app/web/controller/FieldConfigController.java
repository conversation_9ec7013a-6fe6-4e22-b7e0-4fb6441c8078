package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.queryfield.FieldConfigReq;
import com.mi.oa.asset.commons.config.api.queryfield.FieldConfigRes;
import com.mi.oa.asset.commons.config.api.queryfield.QueryFieldProvider;
import com.mi.oa.asset.commons.config.app.ability.QueryFieldAbility;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

import static com.mi.oa.asset.common.enums.EAMConstants.LANGUAGE;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:21
 */

@HttpApiModule(value = "FieldConfigController", apiController = FieldConfigController.class)
@RestController
@RequestMapping("/configs/field-config")
public class FieldConfigController {
    @Autowired
    private HttpServletRequest request;

    @Resource
    QueryFieldProvider queryFieldProvider;

    @Resource
    private QueryFieldAbility queryFieldAbility;

    @HttpApiDoc(apiName = "查询列字段配置", value = "/configs/field-config/search", method = MiApiRequestMethod.GET)
    @GetMapping({"/search"})
    public Result<FieldConfigRes> searchFieldConfig(@RequestParam(value = "manageLine", required = false) String manageLine, @RequestParam("funId") String funId) {
        FieldConfigRes fieldConfigRes = queryFieldProvider.searchFieldConfig(manageLine, funId);
        queryFieldAbility.switchLanguage(fieldConfigRes, funId, manageLine, request.getHeader(LANGUAGE));
        return Result.success(fieldConfigRes);
    }

    @HttpApiDoc(apiName = "保存列字段配置", value = "/configs/field-config/save", method = MiApiRequestMethod.POST)
    @PostMapping({"/save"})
    public Result<FieldConfigReq> saveFieldConfig(@Valid @RequestBody FieldConfigReq fieldConfigReq, BindingResult result) {
        List<FieldError> fieldErrors = result.getFieldErrors();
        if (result.hasErrors() && CollectionUtils.isNotEmpty(fieldErrors)) {
            return Result.error(ErrorCodes.INTERNAL_SERVER_ERROR, fieldErrors.get(0).getDefaultMessage());
        }
        queryFieldProvider.saveFieldConfig(fieldConfigReq);
        return Result.success(fieldConfigReq);
    }

    @HttpApiDoc(apiName = "查询通用明细列字段配置", value = "/configs/field-config/item/search", method = MiApiRequestMethod.GET)
    @GetMapping({"/item/search"})
    public Result<FieldConfigRes> searchItemFieldConfig(@RequestParam(value = "manageLine", required = false) String manageLine,
                                                        @RequestParam(value = "businessLine", required = false) String businessLine,
                                                        @RequestParam("funId") String funId,
                                                        @RequestParam(value = "type", required = false) String type) {
        FieldConfigRes fieldConfigRes = queryFieldProvider.searchItemFieldConfig(manageLine, businessLine, funId, type);
        queryFieldAbility.switchLanguage(fieldConfigRes, funId, manageLine, request.getHeader(LANGUAGE));
        return Result.success(fieldConfigRes);
    }
}
