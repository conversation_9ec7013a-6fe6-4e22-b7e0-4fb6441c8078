package com.mi.oa.asset.commons.config.app.converter;


import com.mi.oa.asset.common.model.AdsCityDTO;
import com.mi.oa.asset.commons.config.api.address.AddressCityRes;
import com.mi.oa.asset.commons.config.api.address.AssetReceiveAddressReq;
import com.mi.oa.asset.commons.config.api.address.AssetReceiveAddressRes;
import com.mi.oa.asset.commons.config.domain.address.entity.AssetReceiveAddress;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * appConverter 转换器
 *
 * <AUTHOR>
 * @date 2024-04-08 09:01:07
 */
@Mapper(componentModel = "spring")
public interface AssetReceiveAddressConverter extends CommonConverter {

    AssetReceiveAddressRes toAssetReceiveAddressRes(AssetReceiveAddress source);

    List<AssetReceiveAddressRes> toAssetReceiveAddressResList(List<AssetReceiveAddress> source);

    AssetReceiveAddress toAssetReceiveAddress(AssetReceiveAddressReq source);

    AddressCityRes toAddressCityRes(AdsCityDTO source);

    List<AddressCityRes> toAddressCityResList(List<AdsCityDTO> source);
}