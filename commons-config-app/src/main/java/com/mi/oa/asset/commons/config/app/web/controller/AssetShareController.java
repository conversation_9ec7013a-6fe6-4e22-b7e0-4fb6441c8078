package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.assetshare.*;
import com.mi.oa.asset.commons.config.app.ability.AssetShareAbility;
import com.mi.oa.asset.commons.config.app.scheduler.ShareNotify;
import com.mi.oa.asset.commons.config.domain.assetshare.valobj.ShareQueryReq;
import com.mi.oa.asset.eam.jxs.req.BaseQueryReq;
import com.mi.oa.asset.eam.jxs.res.ListDataRes;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 资产共享控制器
 *
 * <AUTHOR>
 * @date 2025-01-06 16:22
 */
@Slf4j
@HttpApiModule(value = "AssetShareController", apiController = AssetShareController.class)
@RestController
@RequestMapping("/configs/asset-share")
public class AssetShareController {

    @Resource
    private AssetShareAbility assetShareAbility;

    @Resource
    private ShareNotify shareNotify;

    @HttpApiDoc(apiName = "通过ID查询共享记录", value = "/configs/asset-share/get", method = MiApiRequestMethod.GET)
    @GetMapping(value = "/get")
    public Result<ShareRecordRes> getById(@RequestParam("id") Integer id) {
        ShareRecordRes res = assetShareAbility.getById(id);
        return Result.success(res);
    }

    @HttpApiDoc(apiName = "自定义清单分页查询", value = "/configs/asset-share/item/page", method = MiApiRequestMethod.GET)
    @GetMapping(value = "/item/page")
    public Result<PageData<CustomShareListRes>> itemPage(@RequestParam Integer shareId,
                                                         @RequestParam(defaultValue = "1") Integer pageNum,
                                                         @RequestParam(defaultValue = "20") Integer pageSize) {

        PageData<CustomShareListRes> pageData = assetShareAbility.pageCustomShareList(shareId, pageNum, pageSize);
        return Result.success(pageData);
    }

    @HttpApiDoc(apiName = "查询共享单下所有自定义共享资产ID", value = "/configs/asset-share/item/all-asset-ids", method = MiApiRequestMethod.GET)
    @GetMapping(value = "/item/all-asset-ids")
    public Result<List<Integer>> getAssetIds(@RequestParam Integer shareId) {
        List<Integer> assetIds = assetShareAbility.getAssetIds(shareId);
        return Result.success(assetIds);
    }

    @HttpApiDoc(apiName = "共享记录保存", value = "/configs/asset-share/save", method = MiApiRequestMethod.POST)
    @PostMapping(value = "/save")
    public Result<Integer> saveShareRecord(@RequestBody @Valid SaveShareRecordReq req) {
        log.info("saveShareRecord request params:{}", req);
        Integer shareId = assetShareAbility.saveShareRecord(req);
        log.info("saveShareRecord request result:{}", shareId);
        return Result.success(shareId);
    }

    @HttpApiDoc(apiName = "共享记录删除", value = "/configs/asset-share/remove/{id}", method = MiApiRequestMethod.GET)
    @GetMapping("/remove/{id}")
    public Result<Void> removeShareRecord(@PathVariable("id") Integer id) {
        assetShareAbility.removeShareRecord(id);
        return Result.success();
    }

    @HttpApiDoc(apiName = "移除自定义共享清单", value = "/configs/asset-share/item/remove", method = MiApiRequestMethod.GET)
    @PostMapping("/item/remove")
    public Result<Void> removeCustomShareList(@RequestBody List<Integer> ids) {
        assetShareAbility.removeCustomShareList(ids);
        return Result.success();
    }

    @HttpApiDoc(apiName = "导入自定义资产清单", value = "/configs/asset-share/item/import", method = MiApiRequestMethod.POST)
    @PostMapping("/item/import")
    public Result<Void> importCustomShareList(@Validated CustomShareListImportReq req) {
        assetShareAbility.importCustomShareList(req);
        return Result.success();
    }

    @HttpApiDoc(apiName = "共享清单自定义查询", value = "/configs/asset-share/list", method = MiApiRequestMethod.POST)
    @PostMapping(value = "/list")
    public Result<ListDataRes<Map<String, String>>> customQueryShareAsset(@RequestBody BaseQueryReq req) {
        ListDataRes<Map<String, String>> res = assetShareAbility.customQueryShareAsset(req, Strings.EMPTY);
        return Result.success(res);
    }

    @HttpApiDoc(apiName = "共享清单批量导出前先获取查询where条件", value = "/configs/asset-share/getSql", method = MiApiRequestMethod.GET)
    @GetMapping(value = "/getSql")
    public Result<String> getShareWhereSql(@RequestParam("businessLine") String businessLine,
                                           @RequestParam(value = "shareClient", defaultValue = "admin") String shareClient) {
        String shareWhereSql = assetShareAbility.getShareWhereSql(businessLine, shareClient);
        return Result.success(shareWhereSql);
    }

    @HttpApiDoc(apiName = "共享清单调拨点击通过后端查询资产信息", value = "/configs/asset-share/allot-info", method = MiApiRequestMethod.POST)
    @PostMapping("/allot-info")
    public Result<List<ShareAssetAllotRes>> getAssetAllotInfo(@RequestBody @Valid ShareAllotReq req) {
        log.info("getAssetAllotInfo ShareAllotReq：{}", JacksonUtils.bean2Json(req));
        List<ShareAssetAllotRes> res = assetShareAbility.getAssetAllotInfo(req.getAllot(), req.getShareClient());
        return Result.success(res);
    }

    @GetMapping("/query-union")
    public Result<String> queryShareList(@RequestParam(value = "businessLine", required = false) List<String> businessLine,
                                         @RequestParam("userCode") String userCode,
                                         @RequestParam("deptCode") String deptCode,
                                         @RequestParam(value = "shareScene", required = false) String shareScene,
                                         @RequestParam(value = "shareClient", required = false, defaultValue = "admin") String shareClient) {
        ShareQueryReq shareQueryReq = new ShareQueryReq();
        shareQueryReq.setBusinessLine(businessLine);
        shareQueryReq.setUserCode(userCode);
        shareQueryReq.setDeptCode(deptCode);
        shareQueryReq.setShareScene(shareScene);
        shareQueryReq.setShareClient(shareClient);
        String res = assetShareAbility.queryShareList(shareQueryReq);
        return Result.success(res);
    }

    @GetMapping("/send-message")
    public Result<Void> sendMessage() {
        shareNotify.execute();
        return Result.success();
    }
}
