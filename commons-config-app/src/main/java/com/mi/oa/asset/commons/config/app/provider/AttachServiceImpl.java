package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.attach.AttachInfoProvider;
import com.mi.oa.asset.commons.config.api.attach.AttachRes;
import com.mi.oa.asset.commons.config.api.attach.AttachService;
import com.mi.oa.asset.commons.config.domain.common.repository.AttachInfoRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.AttachInfo;
import com.mi.oa.infra.oaucf.fds.config.FDSHelper;
import com.mi.oa.infra.oaucf.fds.errorcode.FdsErrorCodeEnum;
import com.mi.oa.infra.oaucf.fds.service.FDSService;
import com.xiaomi.infra.galaxy.fds.acl.CannedAcl;
import com.xiaomi.infra.galaxy.fds.model.HttpMethod;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 附件service
 *
 * <AUTHOR>
 * @date 2023/9/19 16:20
 */
@Slf4j
@DubboService
public class AttachServiceImpl implements AttachService {

    @Resource
    private FDSService fdsService;

    @Resource
    private FDSHelper fdsHelper;

    @Resource
    private AttachInfoRepo attachInfoRepo;

    @Override
    public AttachRes uploadAttach(String fileName) {
        List<AttachRes> attachRes = this.uploadAttachBatch(Arrays.asList(fileName));
        if (CollectionUtils.isEmpty(attachRes)) {
            return null;
        }
        return attachRes.get(0);
    }

    @Override
    public List<AttachRes> uploadAttachBatch(List<String> fileNames) {
        if (CollectionUtils.isEmpty(fileNames)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "文件名为空，无法上传！");
        }
        List<AttachRes> attachFileDTOS = new ArrayList<>();
        String httpPrefix = fdsHelper.getEnableHttps() ? "https://" : "http://";
        String fdsEndpoint = fdsHelper.getFdsEndpoint();
        String bucketName = fdsHelper.getBucketName();
        for (String fileName : fileNames) {
            if (StringUtils.isEmpty(fileName)) {
                log.error("文件名为空，无法上传！");
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "文件名为空，无法上传！");
            }
            List<String> subResources = new ArrayList<>();
            subResources.add("cannedAcl=" + CannedAcl.PUBLIC_READ.name());
            String newFileName = null;
            String uuid = UUID.randomUUID().toString();
            String fileExtension = StringUtils.substringAfter(fileName, ".");
            if (StringUtils.isNotBlank(fileExtension)) {
                newFileName = uuid + "." + fileExtension;
            }
            try {
                String preSignedUri = fdsService.generatePresignedUri(newFileName, subResources, true, HttpMethod.POST);
                log.info("preSignedUri = {},", preSignedUri);
                if (preSignedUri == null) {
                    log.error(FdsErrorCodeEnum.FDS_NOT_GET_PRESIGNED_URI.getErrDesc());
                    throw new ErrorCodeException(ErrorCodes.NOT_FOUND, "fds未生成预签名文件！");
                }
                AttachRes attachFileDTO = new AttachRes();
                attachFileDTO.setUri(preSignedUri);
                attachFileDTO.setFileName(newFileName);
                attachFileDTO.setOriginFileName(fileName);
                attachFileDTO.setDownloadUrl(httpPrefix + fdsEndpoint + "/" + bucketName + "/" + newFileName);
                attachFileDTOS.add(attachFileDTO);
            } catch (Exception e) {
                log.error("----- generatePreSignedUriError", e);
                throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, "生成预签名文件失败！");
            }
        }
        return attachFileDTOS;
    }

    @Override
    public boolean deleteFDSFileByFileName(String fileName) {
        return fdsService.deleteFile(fileName);
    }

    @Override
    public ResponseEntity<byte[]> downloadFile(String fileName, String originName) {
        if (StringUtils.isBlank(fileName)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "文件名不能为空");
        }
        try (InputStream is = fdsService.getFile(fileName)) {
            if (is == null) {
                throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, "文件不存在，下载失败");
            }
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String newFileName = originName;
            if(StringUtils.isBlank(newFileName)){
                newFileName = fileName;
            }
            newFileName = URLEncoder.encode(newFileName, "UTF-8");
            // 需要转换成原始文件名称
            headers.setContentDispositionFormData("attachment", newFileName);

            byte[] fb = IOUtils.toByteArray(is);
            return new ResponseEntity<>(fb, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("下载文件失败", e);
            return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
        }
    }

    @Override
    public ResponseEntity<byte[]> downloadTemplateFile(String fileFlag) {
        if (StringUtils.isBlank(fileFlag)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "文件标识不能为空");
        }
        AttachInfo templateAttach = attachInfoRepo.getTemplateAttach(fileFlag);

        try (InputStream is = fdsService.getFile(templateAttach.getAttachName())) {
            if (is == null) {
                throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, "文件不存在，下载失败");
            }
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String newFileName = templateAttach.getOriginName();
            if(StringUtils.isBlank(newFileName)){
                newFileName = templateAttach.getAttachName();
            }
            newFileName = URLEncoder.encode(newFileName, "UTF-8");
            // 需要转换成原始文件名称
            headers.setContentDispositionFormData("attachment", newFileName);

            byte[] fb = IOUtils.toByteArray(is);
            return new ResponseEntity<>(fb, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("下载文件失败", e);
            return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
        }
    }
}
