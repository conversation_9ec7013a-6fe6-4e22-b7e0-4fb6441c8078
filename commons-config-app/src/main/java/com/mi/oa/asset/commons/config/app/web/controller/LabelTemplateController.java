package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.labelprint.GetLabelTemplateDataReq;
import com.mi.oa.asset.commons.config.api.labelprint.LabelTemplateProvider;
import com.mi.oa.asset.commons.config.api.labelprint.LabelTemplateRes;
import com.mi.oa.asset.commons.config.api.labelprint.SaveLabelTemplateReq;
import com.mi.oa.asset.commons.config.app.ability.LabelTemplateAbility;
import com.mi.oa.asset.commons.config.domain.labeltemplate.entity.LabelTemplate;
import com.mi.oa.asset.commons.config.domain.labeltemplate.enums.LabelDataSource;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/02/14 14:40
 */
@HttpApiModule(value = "LableTemplateController", apiController = LabelTemplateController.class)
@RestController
@RequestMapping("/configs/label-template")
public class LabelTemplateController {

    @Resource
    private LabelTemplateAbility labelTemplateAbility;

    @Resource
    private LabelTemplateProvider labelTemplateProvider;

    @HttpApiDoc(apiName = "模版详情", value = "/detail/{templateId}", method = MiApiRequestMethod.GET)
    @GetMapping("/detail/{templateId}")
    public Result<LabelTemplateRes> detail(@PathVariable Integer templateId) {
        return Result.success(labelTemplateProvider.getLabelTemplateById(templateId));
    }

    @HttpApiDoc(apiName = "模版市场列表", value = "/market/list", method = MiApiRequestMethod.GET)
    @GetMapping("/market/list")
    public Result<List<LabelTemplate>> marketList() {
        return Result.success(labelTemplateAbility.getMarketLabelTemplateList());
    }

    @HttpApiDoc(apiName = "业务模版列表", value = "/business/list", method = MiApiRequestMethod.GET)
    @GetMapping("/business/list")
    public Result<List<LabelTemplate>> businessList(){
        return Result.success(labelTemplateAbility.getBusinessTemplateList());
    }

    @HttpApiDoc(apiName = "获取用户默认模版", value = "/user/list", method = MiApiRequestMethod.GET)
    @GetMapping("/user/list")
    public Result<List<LabelTemplate>> getUserTemplateList(@RequestParam(value = "businessLine") String businessLine){
        return Result.success(labelTemplateAbility.getUserTemplateList(businessLine));
    }

    @HttpApiDoc(apiName = "保存模版", value = "/save", method = MiApiRequestMethod.POST)
    @PostMapping("/save")
    public Result<Integer> saveOrUpdateLabelTemplate(@Validated @RequestBody SaveLabelTemplateReq saveLabelTemplateReq){
        if(saveLabelTemplateReq.getId() == null){
            return Result.success(labelTemplateAbility.saveLabelTemplate(saveLabelTemplateReq));
        }else{
            labelTemplateAbility.updateLabelTemplate(saveLabelTemplateReq);
        }
        return Result.success();
    }

    @HttpApiDoc(apiName = "启用/禁用模版", value = "/activation/toggle", method = MiApiRequestMethod.PUT)
    @PutMapping("/activation/toggle")
    public Result<Void> toggleTemplateActivation(@RequestParam Integer templateId, @RequestParam boolean enable) {
        labelTemplateAbility.toggleTemplateActivation(templateId, enable);
        return Result.success();
    }

    @HttpApiDoc(apiName = "设置模版为用户默认模版", value = "/user/default/update", method = MiApiRequestMethod.PUT)
    @PutMapping("/user/default/update")
    public Result<Void> updateUserDefaultTemplate(@RequestParam Integer templateId) {
        labelTemplateAbility.addUserDefaultTemplate(templateId);
        return Result.success();
    }

    @HttpApiDoc(apiName = "删除模版", value = "/delete", method = MiApiRequestMethod.DELETE)
    @DeleteMapping("/delete")
    public Result<Void> deleteLabelTemplate(Integer templateId){
        labelTemplateAbility.deleteLabelTemplate(templateId);
        return Result.success();
    }


    @HttpApiDoc(apiName = "批量获取标签数据", value = "/batch/get", method = MiApiRequestMethod.POST)
    @PostMapping("/batch/get")
    public Result<List<?>> batchGet(@Validated @RequestBody GetLabelTemplateDataReq getLabelTemplateDataReq){
        return Result.success(labelTemplateAbility.batchGetTemplateData(getLabelTemplateDataReq));
    }

    @HttpApiDoc(apiName = "使用模版", value = "/user/use", method = MiApiRequestMethod.POST)
    @PutMapping("/user/use")
    public Result<Void> useLabelTemplate(@RequestParam Integer templateId) {
        labelTemplateAbility.useLabelTemplate(templateId);
        return Result.success();
    }

    @HttpApiDoc(apiName = "获取数据来源列表", value = "/datasource", method = MiApiRequestMethod.GET)
    @GetMapping("/datasource")
    public Result<List<LabelDataSource>> getLabelDataSource(){
        return Result.success(labelTemplateAbility.getLabelDataSourceList());
    }

}
