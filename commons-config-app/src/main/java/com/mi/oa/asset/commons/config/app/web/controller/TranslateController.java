package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.translate.NeputunReq;
import com.mi.oa.asset.commons.config.api.translate.TranslateProvider;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 * 翻译 Controller
 */
@RestController
@HttpApiModule(value = "TranslateController", apiController = TranslateController.class)
@RequestMapping("/configs/translate")
@Slf4j
public class TranslateController {

    @Resource
    private TranslateProvider translateProvider;



    @GetMapping("/translateFromKey")
    @HttpApiDoc(apiName = "根据键标识符获取翻译数据", value = "/configs/translate/translateFromKey", method = MiApiRequestMethod.GET)
    public Result<Object> translateFromKey(@RequestParam NeputunReq neputunReq) {
        return Result.success(translateProvider.getTranslateFromKey(neputunReq.getKey(), neputunReq.getLang()));
    }

    @PostMapping("/translateData")
    @HttpApiDoc(apiName = "获取翻译Json数据", value = "/configs/translate/translateData", method = MiApiRequestMethod.POST)
    public Result<Object> getTranslateData(@RequestBody NeputunReq neputunReq){
        String translateJsonData = translateProvider.getTranslateJsonData(neputunReq);
        return Result.success(translateJsonData);
    }

}
