package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.common.BaseConverter;
import com.mi.oa.asset.commons.config.api.systemvar.SystemVarDetail;
import com.mi.oa.asset.commons.config.domain.systemvar.entity.SystemVar;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @Date 2024/3/19 15:14
 */

@Mapper(componentModel = "spring")
public interface SystemVarConverter extends BaseConverter {

    SystemVar toSystemVar(SystemVarDetail source);

    SystemVarDetail toSystemVarDetail(SystemVar source);
}
