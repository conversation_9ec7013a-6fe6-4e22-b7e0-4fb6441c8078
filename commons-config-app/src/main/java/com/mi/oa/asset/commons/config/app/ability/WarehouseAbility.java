package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.user.UserBaseInfoRes;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.Warehouse;
import com.mi.oa.asset.commons.config.domain.warehouse.repository.WarehouseRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class WarehouseAbility {

    @Resource
    private AssetOrgAbility assetOrgAbility;
    @Resource
    private UserInfoService userInfoService;

    @Resource
    private WarehouseRepo warehouseRepo;

    public void checkImport(List<Warehouse> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "数据为空");
        }
        list.forEach(this::checkWarehouse);
        long count = list.stream().map(Warehouse::getHouseCode).filter(StringUtils::isNotEmpty).distinct().count();
        if (count != list.size()) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "导入数据有重复仓库编码，请检查");
        }
        List<Warehouse> warehouses = warehouseRepo.findByCodes(list.stream().map(Warehouse::getHouseCode).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(warehouses)) {
            List<String> duplicate = warehouses.stream().map(Warehouse::getHouseCode).collect(Collectors.toList());
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "仓库编码不能重复：【" + String.join(",", duplicate) + "】");
        }
    }

    public void checkWarehouse(Warehouse warehouse ) {
        if (StringUtils.isEmpty(warehouse.getHouseCode())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "不能为空");
        }
        String houseName = warehouse.getHouseName();
        if (StringUtils.isEmpty(houseName)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "仓库名称不能为空");
        }
        String dept = warehouse.getDepartCode();
        if (StringUtils.isEmpty(dept)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "所属不能为空");
        }
        String admins = warehouse.getAdminCodes();
        if (StringUtils.isEmpty(admins)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "管理员不能为空");
        }
        String houseStatus = warehouse.getHouseStatus();
        if (StringUtils.isEmpty(houseStatus)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "状态不能为空");
        }
        if (Objects.isNull(warehouse.getBusinessLine())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线不能为空");
        }
        if (Objects.isNull(warehouse.getHouseType())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "仓库类型不能为空");
        }
        String logisticsType = warehouse.getLogisticsType();
        if (StringUtils.isEmpty(logisticsType)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "物流属性不能为空");
        }
        String services = warehouse.getServices();
        if (StringUtils.isEmpty(services)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "支持服务不能为空");
        }
        Boolean shelves = warehouse.getShelves();
        if (Objects.isNull(shelves)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "是否货架管理不能为空");
        }
    }

    public void fillHouseInfo(Warehouse warehouse) {
        AssetOrgUnit assetOrgUnit = assetOrgAbility.loadOrgUnit(warehouse.getDepartCode(), warehouse.getBusinessLine());
        if (assetOrgUnit == null)
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "所属部门不存在");
        warehouse.setDepartName(assetOrgUnit.getOrgNamePath());
        List<String> userNames = Arrays.asList(warehouse.getAdminCodes().split(","));
        //管理员信息
        List<UserBaseInfoRes> userBaseInfos = userInfoService.getUserBaseInfosByUserNames(userNames);
        Map<String, UserBaseInfoRes> userMap = userBaseInfos.stream().collect(Collectors.toMap(UserBaseInfoRes::getUserName, Function.identity()));
        if (CollectionUtils.isEmpty(userBaseInfos)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "管理员不存在");
        }
        List<String> adminNames = userNames.stream().map(userName -> {
            UserBaseInfoRes user = userMap.get(userName);
            if (user == null) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "管理员不存在");
            }
            return user.getDisplayName();
        }).collect(Collectors.toList());
        warehouse.setAdminNames(String.join(",", adminNames));
    }
}
