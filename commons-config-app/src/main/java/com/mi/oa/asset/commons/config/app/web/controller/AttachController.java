package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.attach.AttachRes;
import com.mi.oa.asset.commons.config.api.attach.AttachService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.ListVO;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 附件
 *
 * <AUTHOR>
 * @date 2023/9/19 16:38
 */
@Slf4j
@RequestMapping("/configs")
@HttpApiModule(value = "AttachController", apiController = AttachController.class)
@RestController
public class AttachController {

    @Resource
    private AttachService attachService;

    /**
     * 批量上传附件
     *
     * @param fileNames
     * @return
     */
    @GetMapping("/pre-signed-url-batch")
    @HttpApiDoc(value = "/configs/pre-signed-url-batch", method = MiApiRequestMethod.GET, description = "批量上传附件", apiName = "批量上传附件")
    public Result<ListVO<AttachRes>> getAllMenus(@RequestParam("fileNames") List<String> fileNames) {
        return Result.success(ListVO.build(attachService.uploadAttachBatch(fileNames)));
    }

    /**
     * 根据文件名删除文件
     *
     * @param fileName
     * @return
     */
    @GetMapping("/attach-delete")
    @HttpApiDoc(value = "/configs/attach-delete", method = MiApiRequestMethod.DELETE, description = "根据文件名删除文件", apiName = "根据文件名删除文件")
    public Result<Object> deleteFDSFileByFileName(@RequestParam("fileName") String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return Result.error(ErrorCodes.BAD_PARAMETER, "文件名为空，无法删除");
        }
        attachService.deleteFDSFileByFileName(fileName);
        return Result.success();
    }

    /**
     * 下载文件
     *
     * @param fileName
     * @return
     */
    @GetMapping("/file/download")
    @HttpApiDoc(value = "/configs/file/download", method = MiApiRequestMethod.GET, description = "下载文件", apiName = "下载文件")
    public ResponseEntity<byte[]> downloadFile(@RequestParam("fileName") String fileName, @RequestParam(value = "originName",required = false) String originName) {
        try {
            return attachService.downloadFile(fileName, originName);
        } catch (Exception e) {
            log.error("下载文件失败", e);
            return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
        }
    }

    /**
     * 下载模板
     */
    @GetMapping("/template/download")
    @HttpApiDoc(value = "/configs/template/download", method = MiApiRequestMethod.GET, description = "下载模板文件", apiName = "下载模板文件")
    public ResponseEntity<byte[]> downloadTemplateFile(@RequestParam("fileFlag") String fileFlag) {
        try {
            return attachService.downloadTemplateFile(fileFlag);
        } catch (Exception e) {
            log.error("下载文件模板失败", e);
            return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
        }
    }
}
