package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.account.api.dispose.res.ExtraFieldInfoRes;
import com.mi.oa.asset.common.model.CodeNameProperty;
import com.mi.oa.asset.common.model.ExtraFieldInfo;
import com.mi.oa.asset.commons.config.api.common.CompanyRes;
import com.mi.oa.asset.commons.config.api.common.ProviderRes;
import com.mi.oa.asset.commons.config.api.common.PurchaseCatalogRes;
import com.mi.oa.asset.commons.config.domain.common.valobj.Company;
import com.mi.oa.asset.commons.config.domain.common.valobj.Provider;
import com.mi.oa.asset.commons.config.domain.common.valobj.PurchaseCatalog;
import com.mi.oa.asset.commons.config.domain.common.valobj.SapAssetCategory;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/11 16:55
 */

@Mapper(componentModel = "spring")
public interface CommonDataConverter {

    PurchaseCatalogRes toPurchaseCatalogRes(PurchaseCatalog source);

    List<PurchaseCatalogRes> toPurchaseCatalogsRes(List<PurchaseCatalog> source);

    CompanyRes toCompanyRes(Company source);

    List<CompanyRes> toCompaniesRes(List<Company> source);

    @Mapping(source = "sapCateCode", target = "code")
    @Mapping(source = "sapCateName", target = "name")
    CodeNameProperty toSapCategoryRes(SapAssetCategory source);

    List<CodeNameProperty> toSapCategoryRes(List<SapAssetCategory> source);

    ProviderRes toProviderRes(Provider source);

    List<ProviderRes> toProvidersRes(List<Provider> source);

    ExtraFieldInfo toExtraFieldInfo(ExtraFieldInfoRes extraFieldInfoRes);

    List<ExtraFieldInfo> toExtraFieldInfoList(List<ExtraFieldInfoRes> extraFieldInfoResList);
}
