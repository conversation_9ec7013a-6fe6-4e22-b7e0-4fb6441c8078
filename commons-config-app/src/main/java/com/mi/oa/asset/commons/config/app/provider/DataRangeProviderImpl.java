package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgProvider;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgUnitQueryReq;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgUnitRes;
import com.mi.oa.asset.commons.config.api.datarange.DataRangeProvider;
import com.mi.oa.asset.commons.config.api.datarange.DeptRangeRes;
import com.mi.oa.asset.commons.config.api.datarange.enums.DataRange;
import com.mi.oa.asset.commons.config.api.datarange.DataRangeReq;
import com.mi.oa.asset.commons.config.app.ability.DataRangeAbility;
import com.mi.oa.asset.commons.config.app.converter.DataRangeConverter;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.Warehouse;
import com.mi.oa.asset.eam.feign.client.HrodClient;
import com.mi.oa.asset.eam.feign.req.HrodEmployeeConditionReq;
import com.mi.oa.asset.eam.feign.res.HrodEmployeeRes;
import com.mi.oa.infra.oaucf.idm.api.IdmUserService;
import com.mi.oa.infra.oaucf.idm.api.rep.UserInfoDto;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant.COMMA;


@DubboService
@Slf4j
public class DataRangeProviderImpl implements DataRangeProvider {
    @Resource
    private DataRangeAbility dataRangeAbility;

    @Resource
    private IdmUserService idmUserService;

    @Resource
    private DataRangeConverter dataRangeConverter;

    @Resource
    private AssetOrgProvider provider;

    @Resource
    private HrodClient hrodClient;

    @Override
    public String getCostCenter(DataRangeReq req) {
        List<String> dataRangeList = Arrays.asList(req.getDataRang().split(COMMA));
        if (dataRangeList.contains(DataRange.USE_DEPT.getCode())) {
            String companyCode = req.getCompanyCode();
            if (StringUtils.isEmpty(companyCode)) {
                log.info("getCostCenter companyCode is empty");
                return StringUtils.EMPTY;
            }
            AssetOrgUnit orgUnit = dataRangeAbility.getOrgUnitByCode(req);
            if (Objects.nonNull(orgUnit)) return "C" +req.getCompanyCode() + orgUnit.getCostCenter();
        }
        String userName = req.getUserName();
        if ((dataRangeList.contains(DataRange.EMPLOYEE.getCode()))
                && StringUtils.isNotEmpty(userName)) {
            UserInfoDto userInfo = dataRangeAbility.getUserInfo(userName);
            if (Objects.nonNull(userInfo) && StringUtils.isNotEmpty(userInfo.getCostCt())) {
                return userInfo.getCostCt();
            }
        }
        return StringUtils.EMPTY;
    }

    @Override
    public List<DeptRangeRes> getDeptList(DataRangeReq req, boolean useDept) {
        Set<DeptRangeRes> result = new HashSet<>();
        List<String> dataRangeList = Arrays.asList(req.getDataRang().split(COMMA));
        //业务角色关联部门
        if (dataRangeList.contains(DataRange.BUSINESS_ROLE.getCode()) || dataRangeList.contains(DataRange.ALL.getCode())) {
            List<AssetOrgUnit> orgUnitList = dataRangeAbility.getAssetOrgUnit(req, useDept);
            List<DeptRangeRes> rangeConverterResList = dataRangeConverter.toResList(orgUnitList);
            if (!CollectionUtils.isEmpty(rangeConverterResList)) {
                result.addAll(rangeConverterResList);
            }
        }
        String userName = req.getUserName();
        //员工所属部门
        if ((dataRangeList.contains(DataRange.EMPLOYEE.getCode()) || dataRangeList.contains(DataRange.ALL.getCode()))
                && StringUtils.isNotEmpty(userName)) {
            UserInfoDto userInfo = dataRangeAbility.getUserInfo(userName);
            if (Objects.nonNull(userInfo) && StringUtils.isNotEmpty(userInfo.getDeptId())) {
                req.setUseDeptCode(userInfo.getDeptId());
                AssetOrgUnit orgUnit = dataRangeAbility.getOrgUnitByCode(req);
                if (Objects.nonNull(orgUnit)) {
                    result.add(dataRangeConverter.toRes(orgUnit));
                }
            }
        }
        //仓库所属部门
        String wareHouse = req.getWarehouseCode();
        if ((dataRangeList.contains(DataRange.WAREHOUSE.getCode()) || dataRangeList.contains(DataRange.ALL.getCode()))
                && StringUtils.isNotEmpty(wareHouse)) {
            Warehouse warehouse = dataRangeAbility.getHouse(wareHouse);
            if (Objects.nonNull(warehouse) && StringUtils.isNotEmpty(warehouse.getDepartCode())) {
                req.setUseDeptCode(warehouse.getDepartCode());
                AssetOrgUnit orgUnit = dataRangeAbility.getOrgUnitByCode(req);
                if (Objects.nonNull(orgUnit)) {
                    result.add(dataRangeConverter.toRes(orgUnit));
                }
            }
        }

        String key = req.getKey();
        //过滤key的模糊搜索
        if (!CollectionUtils.isEmpty(result) && StringUtils.isNotEmpty(key)) {
            result = result.stream().filter(i -> i.getDeptName().contains(key)).collect(Collectors.toSet());
        }
        if (dataRangeList.contains(DataRange.ALL.getCode())) {
            AssetOrgUnitQueryReq unitQueryReq = AssetOrgUnitQueryReq.builder()
                    .businessLines( Arrays.asList(req.getBusinessLine()))
                    .isAssetUseOrg(useDept)
                    .isAssetManageOrg(!useDept)
                    .keyword(req.getKey())
                    .build();

            PageData<AssetOrgUnitRes> pageData = provider.getAssetOrgUnitPageData(unitQueryReq, 1, 50);
            List<AssetOrgUnitRes> list = pageData.getList();
            if (!CollectionUtils.isEmpty(list)) {
                result.addAll(dataRangeConverter.unitToResList(list));
            }
        }
        return new ArrayList<>(result);
    }

    @Override
    public List<DeptRangeRes> getCompany(DataRangeReq req) {
        Set<DeptRangeRes> result = new HashSet<>();
        List<String> dataRangeList = Arrays.asList(req.getDataRang().split(COMMA));
        if (dataRangeList.contains(DataRange.USE_DEPT.getCode()) || dataRangeList.contains(DataRange.ALL.getCode())) {
            AssetOrgUnit orgUnit = dataRangeAbility.getOrgUnitByCode(req);
            if (Objects.nonNull(orgUnit) && StringUtils.isNotEmpty(orgUnit.getCompanyCode())) {
                result.add(dataRangeConverter.toRes(orgUnit));
            }
        }
        String userName = req.getUserName();
        if ((dataRangeList.contains(DataRange.EMPLOYEE.getCode()) || dataRangeList.contains(DataRange.ALL.getCode()))
                && StringUtils.isNotEmpty(userName)) {
        //    UserInfoDto userInfo = dataRangeAbility.getUserInfo(userName);
            HrodEmployeeConditionReq build = HrodEmployeeConditionReq.builder().userName(userName).build();
            HrodEmployeeRes hrodEmployeeRes = hrodClient.getEmployeeByUserName(build);
            if (Objects.nonNull(hrodEmployeeRes) && StringUtils.isNotEmpty(hrodEmployeeRes.getMiCompanyCode())) {
                DeptRangeRes res = new DeptRangeRes();
                res.setCompanyCode(hrodEmployeeRes.getMiCompanyCode());
                res.setCompanyName(hrodEmployeeRes.getCompanyName());
                result.add(res);
            }
        }
        return new ArrayList<>(result);
    }
}
