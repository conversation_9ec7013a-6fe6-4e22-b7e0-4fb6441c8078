package com.mi.oa.asset.commons.config.app.converter;


import com.mi.oa.asset.commons.config.api.assetuseway.AssetUsewayReasonRes;
import com.mi.oa.asset.commons.config.api.assetuseway.AssetUsewayReq;
import com.mi.oa.asset.commons.config.api.assetuseway.AssetUsewayRes;
import com.mi.oa.asset.commons.config.domain.assetuseway.entity.AssetUseway;
import com.mi.oa.asset.commons.config.domain.assetuseway.entity.AssetUsewayReason;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 办公用途 appConverter 转换器
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@Mapper(componentModel = "spring")
public interface AssetUsewayConverter extends CommonConverter {
    List<AssetUsewayRes> toAssetUsewayResList(List<AssetUseway> source);
    AssetUseway toAssetUseway(AssetUsewayReq source);
    List<AssetUsewayReasonRes> toAssetUsewayReasonResList(List<AssetUsewayReason> source);
}