package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.assetcategory.*;
import com.mi.oa.asset.commons.config.api.common.DelByIdsReq;
import com.mi.oa.asset.commons.config.app.converter.AssetCategoryConverter;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:28
 */

@HttpApiModule(value = "AssetCategoryController", apiController = AssetCategoryController.class)
@RestController
@RequestMapping("/configs/asset-category")
public class AssetCategoryController {

    @Resource
    private AssetCategoryProvider provider;

    @Resource
    private AssetCategoryConverter converter;

    @Resource
    private HttpServletRequest request;

    @HttpApiDoc(apiName = "保存分类", value = "/configs/asset-category/save", method = MiApiRequestMethod.POST)
    @PostMapping("/save")
    public Result<Integer> save(@Valid @RequestBody SaveAssetCategoryReq req, BindingResult result) {
        if(result.hasErrors()) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, result.getAllErrors().get(0).getDefaultMessage());
        }
        return Result.success(provider.saveAssetCategory(req));
    }

    @HttpApiDoc(apiName = "分类树", value = "/configs/asset-category/tree", method = MiApiRequestMethod.POST)
    @GetMapping("/tree")
    public Result<List<AssetCategoryTreeRes>> tree(@RequestParam String businessLine) {
        List<AssetCategoryRes> all = provider.getAllAssetCategory(Arrays.asList(businessLine.split(",")));
        if(CollectionUtils.isEmpty(all)) return Result.success(new ArrayList<>());

        String language = request.getHeader(EAMConstants.LANGUAGE);
        if (!EAMConstants.CHINESE.equals(language) && StringUtils.isNotEmpty(language)) {
            all.forEach(i -> i.setCateName(i.getCateNameEn()));
        }
        List<AssetCategoryTreeRes> roots = converter.toAssetCategoryTreeRes(
                all.stream().filter(i -> 1 == i.getLevel()).collect(Collectors.toList())
        );

        roots.forEach(node -> loadAssetCategorySublist(node, all));

        return Result.success(roots);
    }

    @HttpApiDoc(apiName = "分类详情", value = "/configs/asset-category/detail/{cateId}", method = MiApiRequestMethod.POST)
    @GetMapping("/detail/{cateId}")
    public Result<AssetCategoryRes> detail(@PathVariable Integer cateId) {
        return Result.success(provider.getAssetCategory(cateId));
    }

    @HttpApiDoc(apiName = "删除分类", value = "/configs/asset-category/delete", method = MiApiRequestMethod.POST)
    @PostMapping("/delete")
    public Result<Void> delete(@RequestBody DelByIdsReq req) {
        provider.deleteAssetCategory(req.getIds());

        return Result.success();
    }

    @HttpApiDoc(apiName = "分类分页查询", value = "/configs/asset-category/list/{cateId}", method = MiApiRequestMethod.GET)
    @GetMapping({"/list/{cateId}", "/list"})
    public Result<PageData<AssetCategoryRes>> list(
            @PathVariable(required = false) Integer cateId,
            @RequestParam(required = false, defaultValue = "") String businessLine,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize
    ) {
        if(null != cateId) {
            return Result.success(provider.getAssetCategoryPageData(cateId, keyword, pageNum, pageSize));
        } else {
            return Result.success(provider.getAssetCategoryPageData(Arrays.asList(businessLine.split(",")), keyword, pageNum, pageSize));
        }
    }


    @HttpApiDoc(apiName = "导入标准分类", value = "/configs/asset-category/import-std", method = MiApiRequestMethod.POST)
    @PostMapping("/import-std")
    public Result<Void> importStd(@RequestBody ImportStdCategoryReq req) {
        provider.importStdCategory(req);

        return Result.success();
    }

    private void loadAssetCategorySublist(AssetCategoryTreeRes node, List<AssetCategoryRes> all) {
        List<AssetCategoryTreeRes> subList = converter.toAssetCategoryTreeRes(
                all.stream().filter(i -> node.getCateCode().equals(i.getParentCateCode())
                                && node.getBusinessLine().equals(i.getBusinessLine())).collect(Collectors.toList())
        );

        if(CollectionUtils.isEmpty(subList)) return;

        subList.forEach(sNode -> loadAssetCategorySublist(sNode, all));
        node.setSubList(subList);
    }
}
