package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.bpm.BpmApprovalTaskRes;
import com.mi.oa.asset.commons.config.api.bpm.BpmApproverRes;
import com.mi.oa.asset.commons.config.domain.bpm.entity.BpmApprovalTask;
import com.mi.oa.asset.commons.config.domain.bpm.entity.BpmApprover;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface BpmApproveConverter {

    BpmApproverRes toApproverRes(BpmApprover approver);

    BpmApprovalTaskRes toTaskRes(BpmApprovalTask task);

    List<BpmApprovalTaskRes> toTaskResList(List<BpmApprovalTask> taskList);
}
