package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.IEnum;
import com.mi.oa.asset.common.model.CodeNameProperty;
import com.mi.oa.asset.commons.config.api.assetorganization.*;
import com.mi.oa.asset.commons.config.api.common.DelByIdsReq;
import com.mi.oa.asset.commons.config.app.converter.AssetOrgConverter;
import com.mi.oa.asset.commons.config.app.scheduler.SyncDepartment;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgType;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/15 16:48
 */

@Slf4j
@HttpApiModule(value = "AssetOrgController", apiController = AssetOrgController.class)
@RestController
@RequestMapping("/configs/asset-org")
public class AssetOrgController {

    @Resource
    private AssetOrgProvider provider;

    @Resource
    private AssetOrgConverter converter;

    @Resource
    private SyncDepartment syncDepartment;

    @HttpApiDoc(apiName = "组织单元类型", value = "/configs/asset-org/unit/type", method = MiApiRequestMethod.GET)
    @GetMapping("/unit/type")
    public Result<List<CodeNameProperty>> orgTypes(@RequestHeader(value = "eam-language", required = false, defaultValue = "zh-CN") String language) {
        return Result.success(AssetOrgType.getAllType(language));
    }

    @HttpApiDoc(apiName = "新建非虚拟组织单元", value = "/configs/asset-org/unit/create-no-virtual", method = MiApiRequestMethod.POST)
    @PostMapping("/unit/create-no-virtual")
    public Result<Void> createUnit(@RequestBody SaveAssetOrgUnitReq req) {
        provider.createNoVirtualUnit(req);

        return Result.success();
    }

    @HttpApiDoc(apiName = "保存组织单元", value = "/configs/asset-org/unit/save", method = MiApiRequestMethod.POST)
    @PostMapping("/unit/save")
    public Result<Void> saveUnit(@RequestBody SaveAssetOrgUnitReq req) {
        if(null == req.getOrgId()) {
            provider.createAssetOrgUnit(req);
        } else {
            provider.updateAssetOrgUnit(req);
        }

        return Result.success();
    }

    @HttpApiDoc(apiName = "批量删除组织单元", value = "/configs/asset-org/unit/delete", method = MiApiRequestMethod.POST)
    @PostMapping("/unit/delete")
    public Result<Void> deleteStructure(@RequestBody DelByIdsReq req) {
        provider.deleteOrgUnitByIds(req.getIds());

        return Result.success();
    }

    @HttpApiDoc(apiName = "组织单元详情", value = "/configs/asset-org/unit/detail/{orgId}", method = MiApiRequestMethod.GET)
    @GetMapping("/unit/detail/{orgId}")
    public Result<AssetOrgUnitRes> unitDetail(@PathVariable Integer orgId,
                                              @RequestHeader(value = "eam-language", required = false, defaultValue = "zh-CN") String language) {
        return Result.success(provider.getOrgUnitById(orgId, language));
    }

    @HttpApiDoc(apiName = "组织单元分页查询", value = "/configs/asset-org/unit/list/{orgCode}", method = MiApiRequestMethod.GET)
    @GetMapping({"/unit/list/{orgCode}", "/unit/list"})
    public Result<PageData<AssetOrgUnitRes>> list(
            @PathVariable(required = false) String orgCode,
            @RequestParam(required = false, defaultValue = "") String businessLine,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false, defaultValue = "") String orgType,
            @RequestParam(required = false) Integer isUseOrg,
            @RequestParam(required = false) Integer isManageOrg,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @RequestHeader(value = "eam-language", required = false, defaultValue = "zh-CN") String language
    ) {
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .orgCode(orgCode)
                .businessLines( Arrays.asList(StringUtils.split(businessLine, ",")))
                .orgTypes(Arrays.asList(StringUtils.split(orgType, ",")))
                .isAssetUseOrg(null != isUseOrg ? 1 == isUseOrg : null)
                .isAssetManageOrg(null != isManageOrg ? 1 == isManageOrg : null)
                .keyword(keyword)
                .build();

        return Result.success(provider.getAssetOrgUnitPageData(req, pageNum, pageSize, language));
    }

    @HttpApiDoc(apiName = "调拨部门接口查询", value = "/configs/asset-org/unit/allot-org-list", method = MiApiRequestMethod.GET)
    @GetMapping("/unit/allot-org-list")
    public Result<PageData<AssetOrgUnitRes>> allotOrgList(
            @RequestParam(required = false) String businessLine,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String inUseCode,
            @RequestParam(required = false) String inUseDeptCode,
            @RequestParam(required = false) Integer isUseOrg,
            @RequestParam(required = false) Integer isManageOrg,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
            @RequestHeader(value = "eam-language", required = false, defaultValue = "zh-CN") String language
    ) {
        if (StringUtils.isBlank(businessLine)) {
            return Result.failure(ErrorCodes.BAD_PARAMETER, "业务线不能为空");
        }
        if (StringUtils.isBlank(inUseCode)) {
            return Result.failure(ErrorCodes.BAD_PARAMETER, "调入人账号不能为空");
        }
        AssetOrgUnitQueryReq req = AssetOrgUnitQueryReq.builder()
                .businessLine(businessLine)
                .isAssetUseOrg(null != isUseOrg ? 1 == isUseOrg : null)
                .isAssetManageOrg(null != isManageOrg ? 1 == isManageOrg : null)
                .keyword(keyword)
                .inUseCode(inUseCode)
                .inUseDeptCode(inUseDeptCode)
                .build();
        return Result.success(provider.getAllotOrgList(req, pageNum, pageSize, language));
    }

    @HttpApiDoc(apiName = "组织结构详情", value = "/configs/asset-org/structure/detail", method = MiApiRequestMethod.GET)
    @GetMapping("/structure/detail")
    public Result<AssetOrgUnitRes> structureDetail(@RequestParam String orgCode, @RequestParam String businessLine) {
        return Result.success(provider.getOrgUnitByCode(orgCode, businessLine));
    }

    @HttpApiDoc(apiName = "删除组织结构", value = "/configs/asset-org/structure/delete", method = MiApiRequestMethod.POST)
    @PostMapping("/structure/delete/{orgCode}")
    public Result<Void> deleteStructure(@PathVariable String orgCode) {
        provider.deleteOrgStructure(orgCode);

        return Result.success();
    }

    @HttpApiDoc(apiName = "组织结构详情", value = "/configs/asset-org/structure/detail/{orgCode}", method = MiApiRequestMethod.GET)
    @GetMapping("/structure/detail/{orgCode}")
    public Result<AssetOrgStructureRes> structureDetail(@PathVariable String orgCode) {
        return Result.success(provider.getOrgStructureByCode(orgCode));
    }

    @GetMapping("/structure/sub")
    public Result<List<AssetOrgStructureRes>> getOrgStructureWithSub(@RequestParam("orgCode") String orgCode, @RequestParam("businessLine") String businessLine) {
        return Result.success(provider.getOrgStructureWithSub(orgCode, BusinessLine.getByCode(businessLine)));
    }

    @HttpApiDoc(apiName = "组织结构树", value = "/configs/asset-org/structure/tree", method = MiApiRequestMethod.GET)
    @GetMapping("/structure/tree")
    public Result<List<AssetOrgStructureTreeRes>> assetOrgStructureTree(@RequestParam(required = false, defaultValue = "") String businessLine,
                                                                        @RequestParam(required = false, defaultValue = "") String orgCode,
                                                                        @RequestHeader(value = "eam-language", required = false, defaultValue = "zh-CN") String language) {
        List<AssetOrgStructureRes> all = provider.getOrgStructure(Arrays.asList(StringUtils.split(businessLine, ",")), orgCode);
        log.info("assetOrgStructureTree language: {}", language);
        List<AssetOrgStructureTreeRes> roots = converter.toAssetOrgStructureTreeRes(
                all.stream().filter(i -> 1 == i.getLevel()).collect(Collectors.toList()), language
        );
        log.info("assetOrgStructureTree first roots: {}", roots);
        List<List<AssetOrgStructureRes>> list = new ArrayList<>();
        for(int l = 2; l < 10; ++l) {
            int level = l;
            list.add(all.stream().filter(i -> level == i.getLevel()).collect(Collectors.toList()));
        }
        roots.forEach(node -> loadAssetOrgStructureSublist(node, list, language));

        return Result.success(roots);
    }

    @HttpApiDoc(apiName = "管理部门列表", value = "/configs/asset-org/unit/is-manage ", method = MiApiRequestMethod.GET)
    @GetMapping("/unit/is-manage")
    public Result<List<AssetOrgUnitRes>> manageOrgUnits(@RequestParam String businessLine,
                                                        @RequestHeader(value = "eam-language", required = false, defaultValue = "zh-CN") String language) {
        return Result.success(provider.getManageOrgUnit(businessLine, language));
    }

    @HttpApiDoc(apiName = "公司部门树", value = "/configs/asset-org/department/tree", method = MiApiRequestMethod.GET)
    @GetMapping("/department/tree")
    public Result<List<AssetOrgStructureTreeRes>> departmentTree(@RequestHeader(value = "eam-language", required = false, defaultValue = "zh-CN") String language) {
        List<AssetOrgStructureRes> all = provider.getDepartments();
        List<AssetOrgStructureTreeRes> roots = converter.toAssetOrgStructureTreeRes(
                all.stream().filter(i -> 1 == i.getLevel()).collect(Collectors.toList()), language
        );
        List<List<AssetOrgStructureRes>> lvChunks = new ArrayList<>();
        for(int l = 2; l < 10; ++l) {
            int level = l;
            lvChunks.add(all.stream().filter(i -> level == i.getLevel()).collect(Collectors.toList()));
        }
        roots.forEach(node -> loadAssetOrgStructureSublist(node, lvChunks, language));

        return Result.success(roots);
    }

    @HttpApiDoc(apiName = "组织结构分页查询", value = "/configs/asset-org/structure/list", method = MiApiRequestMethod.GET)
    @GetMapping({"/structure/list"})
    public Result<PageData<AssetOrgStructureRes>> list(
            @RequestParam(required = false) String businessLine,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize
    ) {
        return Result.success(provider.getAssetOrgStructurePageData(businessLine, keyword, pageNum, pageSize));
    }

    private void loadAssetOrgStructureSublist(AssetOrgStructureTreeRes node, List<List<AssetOrgStructureRes>> lvChunks, String language) {
        List<AssetOrgStructureRes> list = lvChunks.get(node.getLevel() - 1);
        List<AssetOrgStructureTreeRes> subList = converter.toAssetOrgStructureTreeRes(
                list.stream().filter(i -> node.getCode().equals(i.getParentCode())).collect(Collectors.toList()), language
        );

        if(CollectionUtils.isEmpty(subList)) return;

        subList.parallelStream().forEach(sNode -> loadAssetOrgStructureSublist(sNode, lvChunks, language));
        node.setSubList(subList);
    }

    @GetMapping({"/structure/cache"})
    public Result<Void> cacheOrg() {
        syncDepartment.cacheAssetOrgStructureByLevel();
        return Result.success();
    }

    @HttpApiDoc(apiName = "批量导入组织", value = "/configs/asset-org/unit/import", method = MiApiRequestMethod.POST)
    @PostMapping("/unit/import")
    public Result<Void> importOrgUnit(@Valid @RequestBody AssetOrgUnitImportReq req) {
        provider.importOrgUnit(req);
        return Result.success();
    }

    @HttpApiDoc(apiName = "属性组织结构", value = "/configs/asset-org/structure/refresh", method = MiApiRequestMethod.GET)
    @GetMapping("/structure/refresh")
    public Result<Void> refreshOrgStructure(@RequestParam(value = "orgCode", required = false) String orgCode,
                                                                  @RequestParam(value = "businessLine") String businessLine) {
        provider.refreshOrgStructure(orgCode, businessLine);
        return Result.success();
    }

    /**
     * 更新组织单元部门名称路径
     * @return
     */
    @GetMapping("/unit/refresh")
    public Result<Void> refreshOrgUnitPath(@RequestParam("businessLine") List<String> businessLine) {
        provider.refreshOrgUnit(businessLine);
        return Result.success();
    }
}
