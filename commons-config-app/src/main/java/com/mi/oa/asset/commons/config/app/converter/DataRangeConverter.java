package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgUnitRes;
import com.mi.oa.asset.commons.config.api.datarange.DeptRangeRes;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DataRangeConverter {
    @Mappings({
            @Mapping(target = "deptCode", source = "orgCode"),
            @Mapping(target = "deptName", source = "orgName"),
            @Mapping(target = "deptCodePath", source = "orgCodePath"),
            @Mapping(target = "deptNamePath", source = "orgNamePath"),
    })
    DeptRangeRes toRes(AssetOrgUnit unit);

    List<DeptRangeRes> toResList(List<AssetOrgUnit> unit);

    @Mappings({
            @Mapping(target = "deptCode", source = "orgCode"),
            @Mapping(target = "deptName", source = "orgName"),
            @Mapping(target = "deptCodePath", source = "orgCodePath"),
            @Mapping(target = "deptNamePath", source = "orgNamePath"),
    })
    DeptRangeRes unitToRes(AssetOrgUnitRes unit);

    List<DeptRangeRes> unitToResList(List<AssetOrgUnitRes> unit);

}
