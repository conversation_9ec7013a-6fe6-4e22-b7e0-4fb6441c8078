package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.model.AdsCityDTO;
import com.mi.oa.asset.common.model.AdsDTO;
import com.mi.oa.asset.commons.config.api.address.AddressCityRes;
import com.mi.oa.asset.commons.config.api.address.AssetReceiveAddressProvider;
import com.mi.oa.asset.commons.config.api.address.AssetReceiveAddressReq;
import com.mi.oa.asset.commons.config.api.address.AssetReceiveAddressRes;
import com.mi.oa.asset.commons.config.api.myfunctions.Country;
import com.mi.oa.asset.commons.config.app.ability.AddressAbility;
import com.mi.oa.asset.commons.config.app.converter.AssetReceiveAddressConverter;
import com.mi.oa.asset.commons.config.domain.address.entity.AssetReceiveAddress;
import com.mi.oa.asset.commons.config.domain.address.enums.SubsetLevelEnum;
import com.mi.oa.asset.commons.config.domain.address.repository.AssetReceiveAddressRepo;
import com.mi.oa.asset.commons.config.infra.rpc.service.AddressServiceImpl;
import com.mi.oa.asset.eam.feign.client.AddressClient;
import com.mi.oa.asset.eam.feign.dto.AddressParseDTO;
import com.mi.oa.asset.eam.feign.res.CommonResp;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * dubbo 接口实现
 *
 * <AUTHOR>
 * @date 2024-04-08 11:14:07
 */
@DubboService
@Slf4j
public class AssetReceiveAddressProviderImpl implements AssetReceiveAddressProvider {

    @Resource
    private AssetReceiveAddressConverter converter;
    @Resource
    private AssetReceiveAddressRepo assetReceiveAddressRepo;
    @Resource
    private AddressServiceImpl addressServiceImpl;
    @Resource
    private AddressClient addressClient;
    @Resource
    private AddressAbility addressAbility;
    public static final String SUCCESS_CODE = "200";

    @Override
    public List<AdsDTO> qryAdsList(String type, Integer parentId) {
        if (org.springframework.util.StringUtils.isEmpty(type)) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "参数不合法");
        }
        List<AdsDTO> list = new ArrayList<>();
        if (SubsetLevelEnum.PROVINCE.enumEquals(type)) {
            list = addressServiceImpl.getProvince(parentId == null ? 1 : parentId);
            if (CollectionUtils.isNotEmpty(list)) {
                // 去掉澳门
                list.removeIf(item -> item.getId() == 34);
                // 印度
                if (parentId != null && parentId == 28587) {
                    // 去掉非印度数据
                    list.removeIf(item -> item.getId() != 7607);
                }
            }
        } else if (SubsetLevelEnum.CITY.enumEquals(type) && null != parentId) {
            list = addressServiceImpl.getCity(parentId);
        } else if (SubsetLevelEnum.DISTRICT.enumEquals(type) && null != parentId) {
            list = addressServiceImpl.getDistrict(parentId);
        } else if (SubsetLevelEnum.STREET.enumEquals(type) && null != parentId) {
            list = addressServiceImpl.getStreet(parentId);
        }
        return list;
    }

    @Override
    public Object parseAddress(String address) {
        if (StringUtils.isEmpty(address)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "地址不能为空！");
        }
        AddressParseDTO params = AddressParseDTO.builder().address(Collections.singletonList(AddressParseDTO.Address.builder().title(address).build())).build();
        CommonResp<Object> result;
        try {
            result = addressClient.parse(params);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.failure(ErrorCodes.BAD_REQUEST, "调用三方解析出错！");
        }
        if (!SUCCESS_CODE.equals(result.getHeader().getCode())) {
            log.error("adsParse err,msg:{}", result.getHeader().getDesc());
            return Result.failure(ErrorCodes.BAD_REQUEST, result.getHeader().getDesc());
        }
        return result.getBody();
    }

    @Override
    public List<AssetReceiveAddressRes> list(AssetReceiveAddressReq request) {
        List<AssetReceiveAddress> list = assetReceiveAddressRepo.list(converter.toAssetReceiveAddress(request));
        return converter.toAssetReceiveAddressResList(list);
    }

    @Override
    public AssetReceiveAddressRes getById(Long id) {
        AssetReceiveAddress entity = assetReceiveAddressRepo.getById(id);
        return converter.toAssetReceiveAddressRes(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdateReceiveAddress(AssetReceiveAddressReq request) {
        addressAbility.checkParams(request);
        AssetReceiveAddress entity = converter.toAssetReceiveAddress(request);
        entity.setUpdateTime(ZonedDateTime.now().toEpochSecond());
        if (Boolean.TRUE.equals(entity.getIsDefault())) {
            assetReceiveAddressRepo.cancelDefault(request.getUserId());
        }
        if (entity.getId() != null) {
            assetReceiveAddressRepo.updateById(entity);
            return entity.getId();
        } else {
            return assetReceiveAddressRepo.save(entity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByIds(List<Long> idList) {
        assetReceiveAddressRepo.deleteByIds(idList);
    }

    @Override
    public AssetReceiveAddressRes getDefault(String userName) {
        if (StringUtils.isBlank(userName)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "用户不能为空！");
        }
        return converter.toAssetReceiveAddressRes(assetReceiveAddressRepo.getDefault(userName));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setDefault(Long id) {
        return assetReceiveAddressRepo.setDefault(id);
    }

    @Override
    public List<AddressCityRes> getCityByName(String cityName) {
        List<AdsCityDTO> cityDTOS = addressServiceImpl.queryCityByFuzzyName(cityName);
        return converter.toAddressCityResList(cityDTOS);
    }

    @Override
    public List<AdsDTO> getCountry() {
        return addressServiceImpl.getCountry();
    }

    @Override
    public List<Country> getCountryV1() {
        return assetReceiveAddressRepo.listCountryV1();
    }

}

