package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.function.GlobalValReq;
import com.mi.oa.asset.commons.config.api.function.GlobalValRes;
import com.mi.oa.asset.commons.config.domain.function.entity.GlobalVal;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.GlobalValPo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface GlobalValConverter extends CommonConverter {


    GlobalVal reqToDo(GlobalValReq source);

    List<GlobalVal> reqToDoList(List<GlobalValReq> source);

    GlobalValRes toRes(GlobalVal source);

    List<GlobalValRes> toResList(List<GlobalVal> source);

    GlobalValRes poToRes(GlobalValPo source);

    List<GlobalValRes> poToResList(List<GlobalValPo> source);
}
