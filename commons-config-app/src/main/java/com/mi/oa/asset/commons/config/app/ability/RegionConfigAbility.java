package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigRes;
import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyRes;
import com.mi.oa.asset.commons.config.api.regionconfig.EmployeeRegionCountryRes;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigReq;
import com.mi.oa.asset.commons.config.app.converter.CountryConfigConverter;
import com.mi.oa.asset.commons.config.app.converter.CountryCurrencyConverter;
import com.mi.oa.asset.commons.config.app.converter.RegionConfigConverter;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryCurrencyDo;
import com.mi.oa.asset.commons.config.domain.international.entity.RegionConfigDo;
import com.mi.oa.asset.commons.config.domain.international.enums.DefaultDataEnums;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryConfigRepo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryCurrencyRepo;
import com.mi.oa.asset.commons.config.domain.international.repository.RegionConfigRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RegionConfigAbility {

    @Resource
    private RegionConfigRepo regionConfigRepo;

    @Resource
    private CountryConfigRepo countryConfigRepo;

    @Resource
    private CountryConfigConverter countryConfigConverter;

    @Resource
    private RegionConfigConverter converter;

    @Resource
    private CountryCurrencyRepo  currencyRepo;

    @Resource
    private CountryCurrencyConverter currencyConverter;
    /**
     * 获取所有区域和归属的所有国家
     * @param employeeRegionCountryResList 区域列表
     * @return
     */
    public List<EmployeeRegionCountryRes> getRegionAndCountrysList(List<EmployeeRegionCountryRes> employeeRegionCountryResList)  {
        if (CollectionUtils.isEmpty(employeeRegionCountryResList)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "地区信息为空，请配置地区信息。");
        }
        // 一次性查出所有地区的国家信息
        List<Integer> idList = employeeRegionCountryResList.stream()
                .map(EmployeeRegionCountryRes::getId)
                .collect(Collectors.toList());
        List<CountryConfigDo> byRegionIds = countryConfigRepo.getByRegionIds(idList);
        List<CountryConfigRes> countryRegionRes = countryConfigConverter.listDoToRes(byRegionIds);

        //一次性查出来地区下所有国家的默认货币信息
        List<Integer> countryIdList = countryRegionRes.stream()
                .map(CountryConfigRes::getId)
                .collect(Collectors.toList());
        List<CountryCurrencyDo>  defaultCurrency = currencyRepo.listCountryDefaultCurrency(countryIdList, DefaultDataEnums.DEFAULTCURRENCY.getKey());
        List<CountryCurrencyRes> countryCurrencyResList = currencyConverter.listDoToRes(defaultCurrency);

        // 构建所有countryConfigId到货币对象的映射
        Map<Integer, CountryCurrencyRes> currencyMap = countryCurrencyResList.stream()
                .collect(Collectors.toMap(
                        CountryCurrencyRes::getCountryConfigId,
                        Function.identity(),
                        (existing, replacement) -> existing // 处理重复键
                ));

        employeeRegionCountryResList.forEach(employeeRegionCountryRes -> {
            List<CountryConfigRes> filteredCountryConfigs = countryRegionRes.stream()
                    .filter(countryConfigRes -> countryConfigRes.getRegionId().equals(employeeRegionCountryRes.getId()))
                    .collect(Collectors.toList());
            employeeRegionCountryRes.setCountryConfigRes(filteredCountryConfigs);
            //遍历并设置默认货币
            filteredCountryConfigs.forEach(country -> {
                CountryCurrencyRes matchedCurrency = currencyMap.get(country.getId());
                if (matchedCurrency != null) {
                    country.setDefaultCurrency(matchedCurrency);
                }
            });
        });


        return employeeRegionCountryResList;
    }

    /**
     * 获取单个区域和归属的国家
     * @param employeeRegionCountryRes
     * @return
     */
    public EmployeeRegionCountryRes getRegionAndCountrys(EmployeeRegionCountryRes employeeRegionCountryRes) {
        if (Objects.isNull(employeeRegionCountryRes)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "选择的地区为空，请配置地区信息。");
        }
        List<CountryConfigDo> byRegionId = countryConfigRepo.getByRegionId(employeeRegionCountryRes.getId());
        List<CountryConfigRes> countryRegionRes = countryConfigConverter.listDoToRes(byRegionId);
        countryRegionRes.forEach(country -> {
            CountryCurrencyDo  defaultCurrency = currencyRepo.getCountryDefaultCurrency(country.getId(), DefaultDataEnums.DEFAULTCURRENCY.getKey());
            country.setDefaultCurrency(currencyConverter.doToRes(defaultCurrency));
        });
        employeeRegionCountryRes.setCountryConfigRes(countryRegionRes);
        return employeeRegionCountryRes;
    }

    /**
     * 保存修改地区 和绑定地区下的国家
     * @param req
     */
    public void saveOrUpdate(RegionConfigReq req) {
        List<Integer> countryIdList = req.getCountryConfigIdList();
        if (CollectionUtils.isEmpty(countryIdList)){
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请选择地区下的国家。");
        }

        //请求不走正常流程，用其他地区下的国家id
        checkRegionCountry(countryIdList,Objects.isNull(req.getId())?0:req.getId());

        RegionConfigDo entity = converter.reqToDo(req);
        Integer regionConfigId = entity.getId();
        if (Objects.isNull(regionConfigId)) {
            regionConfigId = regionConfigRepo.save(entity);
            // 增加修改之前会在 configs/countryConfig/listForRegionConfig 过滤掉已经绑定的国家，所以直接绑定即可。
            saveReginCountry(countryIdList,regionConfigId);
        } else {
            regionConfigRepo.updateById(entity);
            List<CountryConfigDo> byRegionId = countryConfigRepo.getByRegionId(regionConfigId);
            if (CollectionUtils.isEmpty(byRegionId)) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "该区域下没有国家绑定，请删除后重新创建。");
            }

            List<Integer> alreadyBoundIdList = byRegionId.stream()
                    .map(CountryConfigDo::getId)
                    .collect(Collectors.toList());

            // 已绑定的 和 入参要求 一样，说明没做修改 国家信息。
            if (CollectionUtils.isEqualCollection(alreadyBoundIdList, countryIdList)){
                return;
            }

            // 过滤出 已绑定的 没在入参要求的地区里，要新增绑定的国家
            List<Integer> addDifference = countryIdList.stream()
                    .filter(id -> byRegionId.stream().noneMatch(item -> item.getId().equals(id)))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(addDifference)){
                saveReginCountry(countryIdList,regionConfigId);
            }

            // 过滤出 入参要求的地区 没在 已绑定的地区里，说明已绑定的要解除。
            List<CountryConfigDo> collect = byRegionId.stream()
                    .filter(item -> !countryIdList.contains(item.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)){
                updateReginIdByIds(collect,0);

            }
        }
    }

    /**
     * 检查传入的国家id是否在其他地区下
     * @param countryIdList
     */
    public void checkRegionCountry(List<Integer> countryIdList, Integer regionConfigId) {
        for (Integer countryConfigId : countryIdList){
            CountryConfigDo byCountryId = countryConfigRepo.getById(countryConfigId);
            if (Objects.isNull(byCountryId)){
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "选择的国家不存在，请重新选择。");
            }
            Integer regionId = byCountryId.getRegionId();
            if (!regionId.equals(0) && !regionId.equals(regionConfigId)){
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, byCountryId.getCountryName() + "在其他的地区归属下，请重新选择。");
            }
        }
    }

    /**
     * 新增归属的国家
     * @param countryIdList
     * @param regionConfigId
     */
    public void saveReginCountry(List<Integer> countryIdList,Integer regionConfigId) {
        List<CountryConfigDo> configs = countryConfigRepo.getByIds(countryIdList);
        if (CollectionUtils.isEmpty(configs)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "选择的国家不存在，请重新选择。");
        }
        updateReginIdByIds(configs,regionConfigId);
    }

    /**
     * 删除地区和归属的国家
     * @param idList
     */
    public void deleteByIds(List<Integer> idList) {
        regionConfigRepo.deleteByIds(idList);
        List<CountryConfigDo> byRegionIds = countryConfigRepo.getByRegionIds(idList);
        updateReginIdByIds(byRegionIds,0);
    }

    /**
     * 批量更新 regionId
     * @param countryConfigDoList
     * @param regionConfigId
     */
    public void updateReginIdByIds(List<CountryConfigDo> countryConfigDoList,  Integer regionConfigId) {
        countryConfigDoList.forEach(countryConfigDo -> countryConfigDo.setRegionId(regionConfigId));
        countryConfigRepo.updateBatchById(countryConfigDoList);
    }

}
