package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.common.CommonListReq;
import com.mi.oa.asset.commons.config.api.user.UserInfoRes;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 员工信息
 *
 * <AUTHOR>
 * @date 2023/10/13 10:55
 */
@Slf4j
@RequestMapping("/configs")
@HttpApiModule(value = "UserController", apiController = UserController.class)
@RestController
public class UserController {

    @Resource
    private UserInfoService userInfoService;

    @GetMapping("/user-info")
    @HttpApiDoc(value = "/configs/user-info", method = MiApiRequestMethod.GET, description = "查询人员信息", apiName = "查询人员信息")
    public Object getUserInfo() {
        String authedUserName = AuthFacade.authedUserName();
        if (StringUtils.isBlank(authedUserName)) {
            throw new ErrorCodeException(ErrorCodes.MISSING_PARAMETER, "未获取到当前登录人信息");
        }
        return Result.success(userInfoService.getUserInfoByUserName(authedUserName));
    }

    @GetMapping("/like-user")
    @HttpApiDoc(value = "/configs/like-user", method = MiApiRequestMethod.GET, description = "模糊查询人员信息", apiName = "模糊查询人员信息")
    public Result<List<UserInfoRes>> getLikeUserInfo(@RequestParam(value = "userName", required = false) String userName,
                                                     @RequestParam(value = "limit", required = false, defaultValue = "50") String limit) {
        if (StringUtils.isBlank(userName)) {
            return Result.success(new ArrayList<>());
        }

        List<UserInfoRes> infoRes = userInfoService.fuzzySearchUserInfo(userName, limit);
        return Result.success(infoRes);
    }

    @PostMapping("/users-info")
    @HttpApiDoc(value = "/configs/like-user", method = MiApiRequestMethod.POST, description = "根据username批量查询人员信息", apiName = "根据username批量查询人员信息")
    public Result<List<UserInfoRes>> getUsersInfo(@RequestBody CommonListReq commonListReq) {
        if (CollectionUtils.isEmpty(commonListReq.getNames())) {
            return Result.success(new ArrayList<>());
        }
        List<UserInfoRes> infoRes = userInfoService.getUsersByUserNames(commonListReq.getNames());
        return Result.success(infoRes);
    }

    @GetMapping("/allot/like-user")
    @HttpApiDoc(value = "/configs/allot/like-user", method = MiApiRequestMethod.GET, description = "资产调拨调入人查询", apiName = "资产调拨调入人查询")
    public Result<List<UserInfoRes>> getAllotLikeUserInfo(@RequestParam(value = "businessLine") String businessLine,
                                                     @RequestParam(value = "userName", required = false) String userName,
                                                     @RequestParam(value = "limit", required = false, defaultValue = "50") String limit) {
        if (StringUtils.isBlank(userName)) {
            return Result.success(new ArrayList<>());
        }

        List<UserInfoRes> infoRes = userInfoService.getAllotLikeUserInfo(businessLine, userName, limit);
        return Result.success(infoRes);
    }

}
