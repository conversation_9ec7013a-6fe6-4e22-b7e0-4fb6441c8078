package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.project.ProjectCfgProvider;
import com.mi.oa.asset.commons.config.api.project.ProjectCfgRes;
import com.mi.oa.asset.commons.config.api.project.SaveProjectCfgReq;
import com.mi.oa.asset.commons.config.app.converter.ProjectCfgAppConverter;
import com.mi.oa.asset.commons.config.domain.project.entity.ProjectCfg;
import com.mi.oa.asset.commons.config.domain.project.repository.ProjectCfgRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@DubboService
@Service
@Slf4j
public class ProjectCfgProviderImpl implements ProjectCfgProvider {
    @Resource
    private ProjectCfgRepo projectCfgRepo;
    @Resource
    private ProjectCfgAppConverter projectCfgAppConverter;

    @Override
    public ProjectCfgRes getProjectCfg(String businessLine, String projectCode) {
        return projectCfgAppConverter.toRes(projectCfgRepo.getProjectCfg(businessLine, projectCode));
    }

    @Override
    public ProjectCfgRes getProjectCfg(Integer projectId) {
        return projectCfgAppConverter.toRes(projectCfgRepo.getProjectCfg(projectId));
    }

    @Override
    public List<ProjectCfgRes> listProjectCfg(String businessLine) {
        return projectCfgAppConverter.toResList(projectCfgRepo.listProjectCfg(businessLine));
    }

    @Override
    public Integer saveProjectCfg(SaveProjectCfgReq req) {
        ProjectCfg projectCfg = projectCfgAppConverter.toDomain(req);
        projectCfgRepo.saveProjectCfg(projectCfg);
        return projectCfg.getId();
    }

}
