package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.assetcategory.AssetCategoryProvider;
import com.mi.oa.asset.commons.config.api.assetcategory.AssetCategoryX5Res;
import com.mi.oa.asset.commons.config.api.assetcategory.BusinessLineX5Req;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgProvider;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgQueryReq;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgStructureTreeRes;
import com.mi.oa.asset.commons.config.api.position.PositionProvider;
import com.mi.oa.asset.commons.config.api.position.PositionRes;
import com.mi.oa.asset.commons.config.api.position.PositionTreeRes;
import com.mi.oa.asset.commons.config.app.converter.AssetOrgConverter;
import com.mi.oa.asset.commons.config.app.converter.PositionConverter;
import com.mi.oa.asset.commons.config.app.scheduler.SyncDepartment;
import com.mi.oa.asset.commons.config.infra.common.CacheKey;
import com.mi.oa.asset.commons.config.infra.rpc.mdm.*;
import com.mi.oa.asset.eam.feign.x5.X5RequestBody;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.redis.annotation.OACacheSet;
import com.xiaomi.core.auth.x5.server.annotation.X5Mapping;
import com.xiaomi.mit.api.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.mi.oa.asset.common.enums.EAMConstants.CHINESE;

/**
 * <AUTHOR>
 * @Date 2024/7/1 16:48
 */

@Slf4j
@RestController
@X5Mapping("/configs")
public class AssetOpenController {

    @Resource
    private AssetOrgProvider provider;

    @Resource
    private AssetOrgConverter converter;

    @Resource
    private SyncDepartment syncDepartment;

    @Resource
    private AssetOrgController assetOrgController;

    @Resource
    private AssetCategoryProvider assetCategoryProvider;

    @Resource
    private MdmService mdmService;

    @Resource
    private SapService sapService;

    @Resource
    private PositionProvider positionProvider;

    @Resource
    private PositionConverter positionConverter;
    /**
     * 查询部门下的子级部门
     * @param req
     * @return
     */
    @X5Mapping(value = "/structure/tree")
    @OACacheSet(cacheEnum = CacheKey.class, cacheEnumField = "KEY_DEPARTMENT", param = "req.orgCode", refreshCacheTime = 2 * 60 * 3600)
    public Result<AssetOrgStructureTreeRes> getSubOrgStructureTree(AssetOrgQueryReq req) {
        Result<List<AssetOrgStructureTreeRes>> result = assetOrgController.assetOrgStructureTree("car", StringUtils.EMPTY, EAMConstants.CHINESE);

        return Result.success(findOrgTreeByCode(req.getOrgCode(), result.getData()));
    }

    @X5Mapping("/asset-category/list/all")
//    @OACacheSet(cacheEnum = CacheKey.class, cacheEnumField = "KEY_ASSET_CATEGORY_ALL", param = "req.businessLine", refreshCacheTime = 2 * 60 * 3600)
    public Result<List<AssetCategoryX5Res>> allList(@X5RequestBody BusinessLineX5Req req) {
        if (req == null || StringUtils.isEmpty(req.getBusinessLine())) return Result.success();
        return Result.success(assetCategoryProvider.allAssetCategory(Arrays.asList(req.getBusinessLine().split(","))));
    }

    @X5Mapping("/x5/mdm/distributePurchaseItem")
    // todo 路径白名单
    public Result<Void> distributePurchaseItem(@X5RequestBody MdmDistributeVO data) {
        log.info("/x5/mdm/distributePurchaseItem 入参: {}", JacksonUtils.bean2Json(data));
        DistributeFeedbackDTO feedbackInfo = DistributeFeedbackDTO.builder()
                .modelId(data.getModelId())
                .modelName(data.getModelName())
                .build();

        List<MdmDistributePurchaseItemVO> list = data.getData();

        if (!CollectionUtils.isEmpty(list)) {
            for (Object obj : list) {
                if (!(obj instanceof MdmDistributePurchaseItemVO)) {
                    throw new IllegalArgumentException("预期是MdmDistributePurchaseItemVO, 但是得到的是: " + obj.getClass());
                }
            }
            List<DistributeFeedbackDTO.Item> feedbackItems = mdmService.savePurchaseItemFromDistribute(list);
            feedbackInfo.setData(feedbackItems);
        }

        sapService.distributeFeedback(feedbackInfo);

        return Result.success();
    }

    /**
     * 查询位置信息树
     * @param businessLine
     * @return
     */
    @X5Mapping(value = "/x5/position/tree")
    public Result<List<PositionTreeRes>> getPositions(@X5RequestBody BusinessLineX5Req businessLine) {
        List<PositionTreeRes> treePosition = getAllPosition(businessLine.getBusinessLine());
        return Result.success(treePosition);
    }

    private AssetOrgStructureTreeRes findOrgTreeByCode(String departCode, List<AssetOrgStructureTreeRes> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        for (AssetOrgStructureTreeRes treeRes : list) {
            if (departCode.equals(treeRes.getCode())) {
                return treeRes;
            }else {
                AssetOrgStructureTreeRes sub = findSubOrgTreeByCode(departCode, treeRes);
                if (sub != null) {
                    return sub;
                }
            }
        }
        return null;
    }

    /**
     * 递归查找子级
     * @param departCode
     * @param tree
     * @return
     */
    private AssetOrgStructureTreeRes findSubOrgTreeByCode(String departCode, AssetOrgStructureTreeRes tree) {
        if (departCode.equals(tree.getCode())) {
            return tree;
        }
        List<AssetOrgStructureTreeRes> list = tree.getSubList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        for (AssetOrgStructureTreeRes subTree : list) {
            AssetOrgStructureTreeRes sub = findSubOrgTreeByCode(departCode, subTree);
            if (sub != null) {
                return sub;
            }
        }
        return null;
    }

    /**
     * 通过业务线查询所有位置信息
     * @param businessLine
     * @return
     */
    private List<PositionTreeRes> getAllPosition(String businessLine) {
        if (StringUtils.isEmpty(businessLine)){
            return new ArrayList<>();
        }
        // 查询数据库对应业务线的所有位置信息
        List<PositionRes> allPosition = positionProvider.getAllPosition(Arrays.asList(StringUtils.split(businessLine, ",")));
        if(allPosition == null || CollectionUtils.isEmpty(allPosition)){
            return new ArrayList<>();
        }
        // 找到树的根节点
        List<PositionTreeRes> roots = positionConverter.toPositionTreeRes(
                allPosition.stream().filter(i -> StringUtils.isBlank(i.getParentCode())).collect(Collectors.toList())
        );
        // 构建树形结构
        roots.forEach(node -> positionTree(node, allPosition));
        return roots;
    }

    /**
     * 递归构建位置树信息
     * @param node
     * @param all
     */
    private void positionTree(PositionTreeRes node, List<PositionRes> all) {
        // 空值检查
        if (node == null || all == null || all.isEmpty()) {
            return;
        }

        String parentCode = node.getPositionCode();
        String businessLine = node.getBusinessLine();

        if (parentCode == null || businessLine == null) {
            return;
        }

        List<PositionTreeRes> subList = positionConverter.toPositionTreeRes(
                all.stream().filter(i -> parentCode.equals(i.getParentCode())
                        && businessLine.equals(i.getBusinessLine())).collect(Collectors.toList())
        );

        node.setSubList(subList);
        subList.forEach(n -> positionTree(n, all));
    }
}
