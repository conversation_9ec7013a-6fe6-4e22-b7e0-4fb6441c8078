package com.mi.oa.asset.commons.config.app.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;


@Slf4j
public class TranslateUtil {

    /**
     * 存储实际翻译数据
     */
    public static ConcurrentMap<String, ConcurrentMap<String, String>> data = null;

    /**
     * 解析 zip数据
     * @param bucketUrl
     * @param hash
     * @return
     * @throws IOException
     */
    public static Map<String, Map<String, String>> loadData(String bucketUrl, String hash)
            throws IOException {
        Map<String, Map<String, String>> res = new HashMap<>();
        URL url = new URL(bucketUrl);
        try (ReadableByteChannel readableByteChannel = Channels.newChannel(url.openStream());
             ZipInputStream zipInputStream = new ZipInputStream(Channels.newInputStream(readableByteChannel))) {
            if (!hash.equals(calculateFileMD5(url.openStream()))) {
                return new HashMap<>();
            }
            for (ZipEntry entry = zipInputStream.getNextEntry(); entry != null; entry = zipInputStream.getNextEntry()) {
                if (!entry.getName().endsWith(".properties")) {
                    continue;
                }
                String language = entry.getName().replace(".properties", "");

                Properties properties = new Properties();
                properties.load(new InputStreamReader(zipInputStream, StandardCharsets.UTF_8));
                //转为map类型
                Enumeration<?> enumeration = properties.propertyNames();
                Map<String, String> currentMap = new HashMap<>();
                while (enumeration.hasMoreElements()) {
                    String key = String.valueOf(enumeration.nextElement());
                    currentMap.put(key, properties.getProperty(key));
                }
                res.put(language, currentMap);
            }
        }
        return res;
    }

    /**
     * 重新装载线上词条 只支持新增和修改
     * @param map
     */
    public static void reload(Map<String, Map<String, String>> map) {
        if (data == null) {
            data = new ConcurrentHashMap<>();
        }
        //把map中的全部数据赋值给你data
        map.forEach((lang, translations) -> {
            data.putIfAbsent(lang, new ConcurrentHashMap<>(translations.size()));
            data.get(lang).putAll(translations);
        });
    }

    public static String calculateFileMD5(InputStream inputStream) {
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] byteArray = new byte[1024];
            int bytesCount = 0;
            while ((bytesCount = inputStream.read(byteArray)) != -1) {
                digest.update(byteArray, 0, bytesCount);
            }
            byte[] bytes = digest.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException | IOException e) {
            throw new RuntimeException(e);
        }
    }
}
