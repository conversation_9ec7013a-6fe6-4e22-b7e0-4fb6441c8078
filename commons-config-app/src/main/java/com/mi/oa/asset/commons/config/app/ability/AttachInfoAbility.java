package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.attach.AttachInfoReq;
import com.mi.oa.asset.commons.config.api.attach.AttachInfoRes;
import com.mi.oa.asset.commons.config.app.converter.AttachInfoConverter;
import com.mi.oa.asset.commons.config.domain.common.repository.AttachInfoRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.AttachInfo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @autho zhan
 * @date 2023-10-26 15:03
 */
@Service
public class AttachInfoAbility {

    @Resource
    private AttachInfoRepo attachInfoRepo;

    @Resource
    private AttachInfoConverter converter;

    public void batchSaveAttach(List<AttachInfoReq> attachInfoReqList) {
        if (CollectionUtils.isEmpty(attachInfoReqList)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请填写附件信息");
        }
        for (AttachInfoReq req : attachInfoReqList) {
            if (StringUtils.isBlank(req.getOriginName())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "存在附件原始名称为空的记录，请检查");
            }
            if (StringUtils.isBlank(req.getAttachName())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "存在附件名称为空的记录，请检查");
            }
            if (StringUtils.isBlank(req.getAttachLink())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "存在附件链接为空的记录，请检查");
            }
        }
        attachInfoRepo.batchSaveAttach(converter.reqListToDo(attachInfoReqList));
    }

    public void deleteAttach(Integer recordId, String recordType) {
        if (StringUtils.isBlank(recordType)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务单据类型不允许为空");
        }
        attachInfoRepo.deleteAttach(recordId, recordType);
    }

    public void deleteAttach(Integer recordId,List<String> attachFileNames, String recordType) {
        attachInfoRepo.deleteAttach(recordId, attachFileNames,recordType);
    }

    public void deleteById(List<Integer> idList){
        attachInfoRepo.deleteById(idList);
    }

    public List<AttachInfoRes> getAttachList(List<Integer> recordIds, String recordType) {
        if (CollectionUtils.isEmpty(recordIds)) return new ArrayList<>();
        List<AttachInfo> attachList = attachInfoRepo.getAttachList(recordIds, recordType);
        return converter.doListToRes(attachList);
    }

    public List<AttachInfoRes> listByTypes(Integer recordId, List<String> recordTypes) {
        if (CollectionUtils.isEmpty(recordTypes) || Objects.isNull(recordId)) return Collections.emptyList();
        List<AttachInfo> attachList = attachInfoRepo.listByTypes(recordId, recordTypes);
        return converter.doListToRes(attachList);
    }
}
