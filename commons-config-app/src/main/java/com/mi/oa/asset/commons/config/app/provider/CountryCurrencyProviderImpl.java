package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyProvider;
import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyReq;
import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyRes;
import com.mi.oa.asset.commons.config.app.ability.CountryCurrencyAbility;
import com.mi.oa.asset.commons.config.app.converter.CountryCurrencyConverter;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryCurrencyDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryCurrencyRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Service
@Slf4j
@DubboService
public class CountryCurrencyProviderImpl implements CountryCurrencyProvider {

    @Resource
    private CountryCurrencyRepo countryCurrencyRepo;

    @Resource
    private CountryCurrencyConverter converter;

    @Resource
    private CountryCurrencyAbility currencyAbility;

    @Override
    public List<CountryCurrencyRes> getCountryCurrencyList() {
        return converter.listDoToRes(countryCurrencyRepo.searchAll());
    }

    @Override
    public CountryCurrencyRes getById(Integer countryCurrencyigId) {
        CountryCurrencyDo countryCurrencyigDo = countryCurrencyRepo.getById(countryCurrencyigId);
        if (countryCurrencyigDo == null) return null;
        return converter.doToRes(countryCurrencyigDo);
    }

    @Override
    public Integer saveOrUpdate(CountryCurrencyReq req) {
        CountryCurrencyDo entity = converter.reqToDo(req);
        currencyAbility.check(entity);
        if (entity.getId() != null) {
            countryCurrencyRepo.updateById(entity);
            return entity.getId();
        } else {
            return countryCurrencyRepo.save(entity);
        }
    }

    @Override
    public void removeByIds(List<Integer> ids) {
        countryCurrencyRepo.deleteByIds(ids);
    }

    @Override
    public List<CountryCurrencyRes> getByCountryId(Integer countryId) {
        List<CountryCurrencyDo> countryCurrencyigDoList = countryCurrencyRepo.getByCountryId(countryId);
        if (countryCurrencyigDoList.isEmpty()) return null;
        return converter.listDoToRes(countryCurrencyigDoList);
    }

    @Override
    public List<CountryCurrencyRes> getByCountryCode(String countryCode) {
        List<CountryCurrencyDo> countryCurrencyigDoList = countryCurrencyRepo.getByCountryCode(countryCode);
        if (countryCurrencyigDoList.isEmpty()) return null;
        return converter.listDoToRes(countryCurrencyigDoList);
    }
}
