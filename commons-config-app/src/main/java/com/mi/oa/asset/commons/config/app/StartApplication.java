package com.mi.oa.asset.commons.config.app;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

import com.mi.oa.infra.oaucf.EnableFDS;
import com.mi.oa.infra.oaucf.EnableOACache;
import com.xiaomi.mone.http.docs.EnableHttpApiDocs;
import org.springframework.context.annotation.FilterType;

/**
 * <AUTHOR>
 * 此处排除spring security配置，避免因接入工作台导致gateway服务访问此服务需要登录
 */
@EnableDiscoveryClient
@EnableOACache
@EnableHttpApiDocs
@EnableConfigurationProperties
@ComponentScan(
        basePackages = {"com.mi.oa"},
        excludeFilters = {
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = {"com.mi.oa.asset.eam.bpm.*"})
        }
)
@SpringBootApplication(scanBasePackages = {"com.mi.oa.asset.commons.config"}, exclude = {
        SecurityAutoConfiguration.class, ManagementWebSecurityAutoConfiguration.class
})
@MapperScan(basePackages = {"com.mi.oa.asset.commons.config.infra.database.mapper"})
@EnableFDS
public class StartApplication {

    public static void main(String[] args) {
        SpringApplication.run(StartApplication.class, args);
    }
}

