package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.menuresource.MenuFunctionRes;
import com.mi.oa.asset.commons.config.api.menuresource.MenuResource;
import com.mi.oa.asset.commons.config.api.menuresource.MenuResourceRes;
import com.mi.oa.asset.commons.config.app.ability.WhitelistAbility;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.mi.oa.infra.console.sdk.config.WorkbenchProperties;
import com.mi.oa.infra.console.sdk.resp.MenuVO;
import com.mi.oa.infra.console.sdk.service.MenuService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.dto.ListVO;
import com.mi.oa.infra.oaucf.newauth.autoconfig.authority.AuthorityProperties;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.AccountAuthorityResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.RoleNewResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.remote.AuthorityClientService;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.remote.RoleService;
import com.mi.oa.infra.uc.common.enmu.AccountTypeEnum;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * 菜单权限
 *
 * <AUTHOR>
 * @date 2023/9/7 15:36
 */
@Slf4j
@RequestMapping("/configs")
@HttpApiModule(value = "MenusController", apiController = MenusController.class)
@RestController
public class MenusController {

    @Resource
    private WorkbenchProperties workbenchProperties;

    @Resource
    private AuthorityProperties authorityProperties;

    @Resource
    private AuthorityClientService authorityClientService;

    @Resource
    private MenuService menuService;

    @Autowired
    private HttpServletRequest request;

    @Resource
    private RoleService roleService;

    @Resource
    private MenuResource menuResource;

    @Resource
    private WhitelistAbility whitelistAbility;

    /**
     * 查询所有菜单
     *
     * @return
     */

    @GetMapping("/menus-all")
    @HttpApiDoc(value = "/configs/menus-all", method = MiApiRequestMethod.GET, description = "查询所有菜单", apiName = "查询所有菜单")
    public Result<ListVO<MenuVO>> getAllMenus() {
        BaseResp<ListVO<MenuVO>> menus = menuService.getMenus(workbenchProperties.getAppId(), "");
        return Result.success(menus.getData());
    }

    /**
     * 查询员工可见的菜单数据
     *
     * @param userName
     * @return
     */

    @GetMapping("/menus-by-user")
    @HttpApiDoc(value = "/configs/menus-by-user", method = MiApiRequestMethod.GET, description = "查询指定用户菜单", apiName = "查询指定用户菜单")
    public Object getMenusByUserName(@RequestParam("userName") String userName) {
        BaseResp<ListVO<MenuVO>> menus = menuService.getMenus(workbenchProperties.getAppId(), userName);
        return Result.success(menus.getData());
    }

    /**
     * 查询用户有权限的菜单功能
     *
     * @param userName
     * @param appCode
     * @param accountType
     * @return
     */
    @GetMapping("/menus-resource")
    @HttpApiDoc(value = "/configs/menus-resource", method = MiApiRequestMethod.GET, description = "查询指定用户菜单及功能权限", apiName = "查询指定用户菜单及功能权限")
    public Object getMenusResource(@RequestParam(value = "userName", required = false) String userName,
                                   @RequestParam(value = "appCode", required = false) String appCode,
                                   @RequestParam(value = "accountType", defaultValue = "0") Integer accountType,
                                   @RequestParam(value = "isShow", defaultValue = "0") Integer isShow,
                                   @RequestHeader(value = "eam-language", required = false, defaultValue = "zh-CN") String eamLanguage) {
        if (StringUtils.isBlank(userName)) {
            String account = AuthFacade.authedUserName();
            if (StringUtils.isBlank(account)) {
                throw new ErrorCodeException(ErrorCodes.MISSING_PARAMETER, "未获取到当前登录人信息");
            }
            userName = account;
        }
        MenuResourceRes menuResourceRes = menuResource.getMenuResourceByUserName(userName, appCode, accountType, isShow, eamLanguage);
        if (menuResourceRes == null) {
            return Result.error(ErrorCodes.NOT_FOUND, "查询用户菜单功能异常", "");
        }
        return Result.success(menuResourceRes);
    }

    @GetMapping("/v2/menus-resource")
    @HttpApiDoc(value = "/configs/v2/menus-resource", method = MiApiRequestMethod.GET, description = "查询指定用户菜单及功能权限version2.0", apiName = "查询指定用户菜单及功能权限")
    public Object getMenusResourceV2(@RequestParam(value = "userName", required = false) String userName,
                                     @RequestParam(value = "appCode", required = false) String appCode,
                                     @RequestParam(value = "accountType", defaultValue = "0") Integer accountType,
                                     @RequestParam(value = "isShow", defaultValue = "0") Integer isShow,
                                     @RequestHeader(value = "eam-source", required = false, defaultValue = "admin") String eamSource,
                                     @RequestHeader(value = "eam-language", required = false, defaultValue = "zh-CN") String eamLanguage) {
        if (StringUtils.isBlank(userName)) {
            String account = AuthFacade.authedUserName();
            if (StringUtils.isBlank(account)) {
                throw new ErrorCodeException(ErrorCodes.MISSING_PARAMETER, "未获取到当前登录人信息");
            }
            userName = account;
        }
        List<MenuFunctionRes> menuFunctionRes = menuResource.getMenuFunctionResource(userName, appCode, accountType, isShow, eamSource, eamLanguage);
        if (CollectionUtils.isEmpty(menuFunctionRes)) {
            return Result.error(ErrorCodes.NOT_FOUND, "查询用户菜单功能异常", "");
        }
        return Result.success(menuFunctionRes);
    }

    @GetMapping("/auth/workbench")
    @HttpApiDoc(value = "/configs/auth/workbench", method = MiApiRequestMethod.GET, description = "查询指定用户是否拥有员工工作台权限", apiName = "查询指定用户是否拥有员工工作台权限")
    public Result<Void> getWorkbenchAuth(@RequestParam(value = "userName", required = false) String userName) {
        if (StringUtils.isBlank(userName)) {
            String account = AuthFacade.authedUserName();
            if (StringUtils.isBlank(account)) {
                throw new ErrorCodeException(ErrorCodes.MISSING_PARAMETER, "未获取到当前登录人信息");
            }
            userName = account;
        }
        whitelistAbility.getWorkbenchAuth(userName);
        return Result.success();
    }

    /**
     * 查询指定账号角色，菜单、功能等信息
     *
     * @param userName
     * @param accountType
     * @param appCode
     * @return
     */
    @GetMapping("/authority-account")
    @HttpApiDoc(value = "/configs/authority-account", method = MiApiRequestMethod.GET, description = "查询指定用户角色，菜单、功能等信息", apiName = "查询指定用户角色，菜单、功能等信息")
    public Object getAccountAuth(@RequestParam("userName") String userName,
                                 @RequestParam(value = "accountType", defaultValue = "0") Integer accountType,
                                 @RequestParam(value = "appCode", required = false) String appCode) {
        if (StringUtils.isBlank(appCode)) {
            appCode = authorityProperties.getAppCode();
        }
        AccountTypeEnum item = AccountTypeEnum.getItem(accountType);
        if (item == null) {
            return BaseResp.error("账户类型异常", null);
        }
        BaseResp<AccountAuthorityResp> accountAuthorityRespBaseResp = authorityClientService.queryAccountAuth(userName, item.name(), appCode);
        return Result.success(accountAuthorityRespBaseResp.getData());
    }

    /**
     * 查询指定角色信息（包括对应菜单编码、权限编码等）
     *
     * @param roleCode
     * @param appCode
     * @return
     */
    @GetMapping("/authority-role")
    @HttpApiDoc(value = "/configs/authority-role", method = MiApiRequestMethod.GET, description = "查询指定角色信息（包括对应菜单编码、权限编码等）", apiName = "查询指定角色信息")
    public Object getAccountRole(@RequestParam("roleCode") String roleCode,
                                 @RequestParam(value = "appCode", required = false) String appCode) {
        if (StringUtils.isBlank(appCode)) {
            appCode = authorityProperties.getAppCode();
        }
        BaseResp<RoleNewResp> roleNewRespBaseResp = roleService.queryRoleLoadResource(appCode, roleCode);
        return Result.success(roleNewRespBaseResp.getData());
    }

}
