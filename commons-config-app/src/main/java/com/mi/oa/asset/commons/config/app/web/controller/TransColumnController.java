package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.function.TranslateColumnProvider;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@RestController
@RequestMapping("/configs/translate")
public class TransColumnController {
    @Resource
    private TranslateColumnProvider translateColumnProvider;

    @HttpApiDoc(apiName = "导入列英文翻译", value = "/stock/translate/column", method = MiApiRequestMethod.POST)
    @PostMapping("/column")
    public Result<Void> column(@RequestParam("file") MultipartFile file) {
        translateColumnProvider.translateColumn(file);
        return Result.success();
    }

    @HttpApiDoc(apiName = "导入字典英文翻译", value = "/stock/translate/dict", method = MiApiRequestMethod.POST)
    @PostMapping("/dict")
    public Result<Void> transDict(@RequestParam("file") MultipartFile file) {
        translateColumnProvider.translateDict(file);
        return Result.success();
    }
}
