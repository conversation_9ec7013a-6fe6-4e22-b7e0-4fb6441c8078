package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.common.*;
import com.mi.oa.asset.commons.config.api.serialcode.RollingType;
import com.mi.oa.asset.commons.config.api.serialcode.SerialCodeProvider;
import com.mi.oa.asset.commons.config.app.converter.CommonDataConverter;
import com.mi.oa.asset.commons.config.app.converter.AsyncTaskConvertor;
import com.mi.oa.asset.commons.config.domain.common.entity.AsyncTask;
import com.mi.oa.asset.commons.config.domain.common.repository.CommonDataRepo;
import com.mi.oa.asset.commons.config.domain.common.repository.AsyncTaskRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.Company;
import com.mi.oa.asset.commons.config.domain.common.valobj.Provider;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:25
 */

@DubboService
public class CommonProviderImpl implements CommonProvider {

    @Resource
    private CommonDataRepo commonDataRepo;

    @Resource
    private CommonDataConverter commonDataConverter;

    @Resource
    private AsyncTaskRepo asyncTaskRepo;

    @Resource
    private AsyncTaskConvertor asyncTaskConvertor;

    @Resource
    private SerialCodeProvider serialCodeProvider;

    @Override
    public ProviderRes getProvider(String keyword) {
        Provider provider = commonDataRepo.getProvider(keyword);
        return commonDataConverter.toProviderRes(provider);
    }

    @Override
    public List<ProviderRes> getProvider(List<String> providerCodes) {
        if (CollectionUtils.isEmpty(providerCodes)) return new ArrayList<>();
        List<Provider> providers = commonDataRepo.getProviders(providerCodes);
        return commonDataConverter.toProvidersRes(providers);
    }

    @Override
    public CompanyRes getCompany(String keyword, String countryCode) {
        Company company = commonDataRepo.getCompany(keyword, countryCode);
        return commonDataConverter.toCompanyRes(company);
    }

    @Override
    public CompanyRes getCompany(String companyCode) {
        return commonDataConverter.toCompanyRes(commonDataRepo.getCompany(companyCode));
    }

    @Override
    public List<CompanyRes> getBatchCompany(List<String> companyCodes) {
        if (CollectionUtils.isEmpty(companyCodes)) return new ArrayList<>();
        return commonDataConverter.toCompaniesRes(commonDataRepo.getBatchCompany(companyCodes));
    }

    @Override
    public Integer createAsyncTask(AsyncTaskReq asyncTaskReq) {
        AsyncTask asyncTask = asyncTaskConvertor.reqToDo(asyncTaskReq);
        // 创建单号
        asyncTask.setRecordNo(serialCodeProvider.genSerialCode("T", RollingType.YM, 5));
        return asyncTaskRepo.saveTask(asyncTask);
    }

    @Override
    public void updateAsyncTask(AsyncTaskReq asyncTaskReq) {
        AsyncTask dataImportTask = asyncTaskConvertor.reqToDo(asyncTaskReq);
        asyncTaskRepo.saveTask(dataImportTask);
    }

    @Override
    public AsyncTaskRes getAsyncTaskById(Integer taskId) {
        if (Objects.isNull(taskId)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "参数不合法！");
        }

        return asyncTaskConvertor.doToRes(asyncTaskRepo.getTaskById(taskId));
    }
}
