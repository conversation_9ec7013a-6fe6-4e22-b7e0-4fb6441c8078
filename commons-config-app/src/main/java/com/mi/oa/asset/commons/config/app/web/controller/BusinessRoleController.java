/**
 * <AUTHOR>
 * @date 2024-01-15
 */
package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleProvider;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleRes;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@HttpApiModule(value = "BusinessRoleController", apiController = BusinessRoleController.class)
@RequestMapping("/configs/business-role")
public class BusinessRoleController {

    @Resource
    private BusinessRoleProvider businessRoleProvider;

    @HttpApiDoc(apiName = "查询业务角色列表", value = "/configs/business-role/list", method = MiApiRequestMethod.GET)
    @GetMapping("/list")
    public Result<List<BusinessRoleRes>> getBusinessRoleList(@RequestHeader(value = "eam-language", required = false, defaultValue = "zh-CN") String language) {
        return Result.success(businessRoleProvider.getBusinessRoleList(language));
    }

}
