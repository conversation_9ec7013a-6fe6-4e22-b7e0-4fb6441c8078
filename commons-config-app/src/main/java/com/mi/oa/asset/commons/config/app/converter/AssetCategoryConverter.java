package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.assetcategory.*;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.AssetCategory;
import com.mi.oa.asset.commons.config.domain.common.enums.DataCreateSource;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:27
 */

@Mapper(componentModel = "spring")
public interface AssetCategoryConverter extends CommonConverter {

    AssetCategory reqToAssetCategory(SaveAssetCategoryReq source);

    AssetCategoryRes assetCategoryToRes(AssetCategory source);

    @Mapping(source = "source.purchaseCatalogCode", target = "cateCode")
    List<AssetCategoryRes> assetCategoriesToRes(List<AssetCategory> source);

    AssetCategoryX5Res assetCategoryToX5Res(AssetCategory source);

    List<AssetCategoryX5Res> assetCategoriesToX5Res(List<AssetCategory> source);

    AssetCategoryTreeRes toAssetCategoryTreeRes(AssetCategoryRes source);

    List<AssetCategoryTreeRes> toAssetCategoryTreeRes(List<AssetCategoryRes> source);

    @Mapping(source = "source.purchaseCatalogCode", target = "cateCode")
    @Mapping(source = "source.purchaseCatalogName", target = "cateName")
    @Mapping(source = "source.parentCode", target = "parentCateCode")
    AssetCategory toAssetCategory(ImportStdCategoryReq.Item source, BusinessLine businessLine, DataCreateSource dataSource);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(source = "source.purchaseCatalogName", target = "cateName")
    void updateFromStd(ImportStdCategoryReq.Item source, @MappingTarget AssetCategory target);
}
