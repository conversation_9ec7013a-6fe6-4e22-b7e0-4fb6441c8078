package com.mi.oa.asset.commons.config.app.provider;

import com.mi.info.infra.moon.dto.ClientSingleResponse;
import com.mi.oa.asset.commons.config.api.function.*;
import com.mi.oa.asset.commons.config.api.common.ResultCode;
import com.mi.oa.asset.commons.config.app.ability.FunctionAbility;
import com.mi.oa.infra.oaucf.newauth.autoconfig.authority.AuthorityProperties;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.RoleResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.remote.RoleService;
import com.mi.oa.infra.uc.common.enmu.RoleStatusEnum;
import com.mi.oa.infra.uc.common.util.PageModel;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Service
@Slf4j
@DubboService
public class FunctionProviderImpl implements FunctionProvider {

    @Resource
    private FunctionAbility functionAbility;

    @Resource
    private RoleService roleService;

    @Resource
    private AuthorityProperties authorityProperties;


    @Override
    public PageData<FunctionRes> listFunc(FuncQueryReq funcQueryReq) {
        return functionAbility.listFunc(funcQueryReq);
    }

    @Override
    public PageData<FuncTableRes> listTable(FuncQueryReq funcQueryReq) {
        return functionAbility.listTable(funcQueryReq);
    }

    @Override
    public FunctionRes findFunctionById(Integer id) {
        return functionAbility.findFunctionById(id);
    }

    @Override
    public Integer saveFunction(FunctionReq req) {
        return functionAbility.saveFunction(req);
    }

    @Override
    public void deleteFunction(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) return;
        functionAbility.deleteFunctionById(ids);
    }

    @Override
    public PageData<FuncFieldRes> listFuncField(FieldQueryReq query) {
        return functionAbility.listFuncField(query);
    }

    @Override
    public List<FormFieldRes> getFieldMap(FormFieldReq req) {
        return functionAbility.getFieldMap(req);
    }

    @Override
    public PageData<FuncFieldRes> listTableField(FieldQueryReq fieldReq) {
        return functionAbility.listTableField(fieldReq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importField(FieldImportReq importReq) {
        functionAbility.importField(importReq);
    }

    @Override
    public void saveField(List<FuncFieldReq> saveReq) {
        functionAbility.saveField(saveReq);
    }

    @Override
    public void deleteField(List<Integer> ids) {
        functionAbility.deleteField(ids);
    }

    @Override
    public void saveDictAuth(FuncDictAuthReq saveReq) {
        functionAbility.saveDictAuth(saveReq);
    }

    @Override
    public List<FuncDictRelRes> listDictAuth(FuncDictAuthReq authReq) {
        return functionAbility.listDictAuth(authReq);
    }

    @Override
    public void deleteDictAuth(List<Integer> ids) {
        functionAbility.deleteDictAuth(ids);
    }

    @Override
    public PageData<FunctionRoleRes> getPageRole(FieldQueryReq req) {
        String appCode = authorityProperties.getAppCode();
        ClientSingleResponse<PageModel<RoleResp>> res = roleService.searchRoleListPage(appCode, null, req.getPageSize(), req.getPageNum());
        if (!ResultCode.OK.equals(res.getCode()) || Objects.isNull(res.getData())) return new PageData<>();
        List<RoleResp> data = res.getData().getItems();
        List<FunctionRoleRes> list = new ArrayList<>();
        for (RoleResp datum : data) {
            if (RoleStatusEnum.DISABLE.equals(datum.getRoleStatusEnum())) continue;
            FunctionRoleRes roleRes = new FunctionRoleRes();
            roleRes.setCode(datum.getRoleCode());
            roleRes.setRoleName(datum.getRoleName());
            list.add(roleRes);
        }
        return PageData.of(list, req.getPageSize(), req.getPageNum(), (int) res.getData().getTotalSize());
    }

    @Override
    public FuncDataPermissionRes getDataAuth(String funCode, String businessLine) {
        return functionAbility.getDataAuth(funCode, businessLine);
    }

    @Override
    public void saveDataAuth(FuncDataPermissionReq req) {
        functionAbility.saveDataAuth(req);
    }

    @Override
    public List<FuncDictRes> getAuthDictInfo(FuncDictAuthReq req) {
        return functionAbility.getAuthDictInfo(req);
    }

    @Override
    public FuncTableRes findFuncTableById(Integer id) {
        return functionAbility.findFuncTableById(id);
    }

    @Override
    public Integer saveFuncTable(FuncTableReq req) {
        return functionAbility.saveFuncTable(req);
    }

    @Override
    public void deleteFuncTable(List<Integer> ids) {
        functionAbility.deleteFuncTable(ids);
    }

    @Override
    public PageData<DictRes> listDict(DictQueryReq dictQueryReq) {
        return functionAbility.listDict(dictQueryReq);
    }

    @Override
    public Map<String, List<DictRes>> getDictList(List<String> codeList) {
        return functionAbility.getDictList(codeList);
    }

    @Override
    public DictRes getDict(Integer id) {
        return functionAbility.getDictById(id);
    }

    @Override
    public Integer saveDict(DictReq dictReq) {
        return functionAbility.saveDict(dictReq);
    }

    @Override
    public void deleteDict(List<Integer> ids) {
        functionAbility.deleteDict(ids);
    }

    @Override
    public PageData<FunctionDictRes> listDictRel(DictQueryReq queryReq) {
        return functionAbility.listDictRel(queryReq);
    }

    @Override
    public void importFuncDict(DictImportReq importReq) {
        functionAbility.importFuncDict(importReq);
    }

    @Override
    public void saveFuncDict(FuncDictReq dictReq) {
        functionAbility.saveFuncDict(dictReq);
    }

    @Override
    public void deleteDictRel(List<Integer> ids) {
        functionAbility.deleteDictRel(ids);
    }

    @Override
    public List<DictRes> listDictRelByBusinessLine(DictBusinessLineQueryReq queryReq) {
        if (queryReq == null) {
            throw new NullPointerException("queryReq cannot be null");
        }
        return functionAbility.listDictRelByBusinessLine(queryReq);
    }

    @Override
    public Map<String,List<DictRes>> listDictRelByBusinessLineBatch(DictBusinessLineBatchReq queryReq) {
        if (queryReq == null) {
            throw new NullPointerException("queryReq cannot be null");
        }
        return functionAbility.listDictRelByBusinessLineBatch(queryReq);
    }

    @Override
    public PageData<GlobalValRes> listGlobalVal(GlobalValQueryReq queryReq) {
        if (queryReq == null) {
            throw new NullPointerException("queryReq cannot be null");
        }
        return functionAbility.listGlobalVal(queryReq);
    }

    @Override
    public Integer saveGlobalVal(GlobalValReq req) {
        return functionAbility.saveGlobalVal(req);
    }

    @Override
    public void deleteGlobalVal(List<Integer> ids) {
        functionAbility.deleteGlobalVal(ids);
    }

    @Override
    public void importGlobalVal(GlobalImportReq req) {
        functionAbility.importGlobalVal(req);
    }

    @Override
    public List<GlobalValRes> getGlobalVal(List<String> code, String businessLine) {
        if (CollectionUtils.isEmpty(code)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "code list cannot be empty");
        }
        return functionAbility.getGlobalVal(code, businessLine);
    }

    @Override
    public List<GlobalValRes> getGlobalValByCode(List<String> code) {
        return functionAbility.getGlobalValByCode(code);
    }

    @Override
    public PageData<DownTaskRes> listDownTask(DownTaskQueryReq queryReq) {
        if (queryReq == null) {
            throw new NullPointerException("queryReq cannot be null");
        }
        return functionAbility.listDownTask(queryReq);
    }

}
