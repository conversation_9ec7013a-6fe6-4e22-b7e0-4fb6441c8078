package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.assetdispose.SaveAssetDisposeTypeReq;
import com.mi.oa.asset.commons.config.domain.common.entity.AssetDisposeType;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2024/5/14 16:27
 * @description 资产处置类型
 */
@Mapper(componentModel = "spring")
public interface AssetDisposeTypeAppConverter extends CommonConverter {
    AssetDisposeType reqToDisposeType(SaveAssetDisposeTypeReq source);

}
