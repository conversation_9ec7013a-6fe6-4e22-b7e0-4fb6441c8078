package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.assetdispose.AssetDisposeTypeProvider;
import com.mi.oa.asset.commons.config.api.assetdispose.SaveAssetDisposeTypeReq;
import com.mi.oa.asset.commons.config.app.converter.AssetDisposeTypeAppConverter;
import com.mi.oa.asset.commons.config.domain.common.entity.AssetDisposeType;
import com.mi.oa.asset.commons.config.domain.common.repository.AssetDisposeTypeRepo;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/14 15:04
 * @description 资产处置类型
 */
@DubboService
public class AssetDisposeTypeProviderImpl implements AssetDisposeTypeProvider {
    @Resource
    private AssetDisposeTypeRepo disposeTypeRepo;

    @Resource
    private AssetDisposeTypeAppConverter disposeTypeConverter;

    @Override
    public Integer saveAssetDisposeType(SaveAssetDisposeTypeReq req) {
        validate(req);
        AssetDisposeType disposeType = disposeTypeConverter.reqToDisposeType(req);
        fillUserInfo(disposeType);
        return disposeTypeRepo.saveOrUpdateDisposeType(disposeType);
    }

    @Override
    public void deleteAssetDisposeType(List<Integer> typeIds) {
        disposeTypeRepo.batchDeleteDisposeType(typeIds);
    }

    private void fillUserInfo(AssetDisposeType disposeType) {
        User currentUser = AuthFacade.authedUserInfo();
        if (disposeType.getId() == null) {
            disposeType.setCreateUser(currentUser.getUserName());
            disposeType.setCreateUserName(currentUser.getDisplayName());
        }
        disposeType.setUpdateUser(currentUser.getUserName());
        disposeType.setUpdateUserName(currentUser.getDisplayName());
        disposeType.setUpdateTime(new Date());
    }

    private void validate(SaveAssetDisposeTypeReq req) {
        Integer disposeTypeId = disposeTypeRepo.getDisposeTypeNum(req.getRecordNo());
        if (disposeTypeId == null) {
            return;
        }
        if (req.getId() == null && disposeTypeId != null) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "处置类型编码已存在，请重新输入！");
        }
        AssetDisposeType disposeType = disposeTypeRepo.getDisposeTypeNum(req.getId());
        if (!disposeType.getRecordNo().equals(req.getRecordNo())) {
            if (!disposeTypeId.equals(disposeType.getId())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "处置类型编码已存在，请重新输入！");
            }
        }
    }


}
