package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.eam.auth.data.DataPermissionService;
import com.mi.oa.asset.eam.dto.BaseRes;
import com.mi.oa.asset.eam.jxs.req.BaseQueryReq;
import com.xiaomi.mit.api.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/2 18:53
 */

@RestController
@RequestMapping("/")
@Slf4j
public class IndexController {

    @Value("${cas.server-url-prefix}")
    private String casHost;

    @Value("${cas.host-url}")
    private String frontHost;

    @GetMapping("/index")
    public Result<String> index() {
        return Result.success("index of config server");
    }

    @GetMapping(value = "/cas/log-out")
    public Result<String> clearCookie(HttpServletResponse response) {
        Cookie cookie = new Cookie("_aegis_cas", null);
        cookie.setMaxAge(0);
        cookie.setPath("/");
        // 已受到保护，以免被盗窃（HttpOnly=true）
        cookie.setHttpOnly(true);
        cookie.setSecure(true);
        response.addCookie(cookie);
        // 米盾退出登录接口
        return Result.success(frontHost);
    }

    @Resource
    private DataPermissionService dataPermissionService;
    @PostMapping("/test/query")
    public BaseRes query(@Valid @RequestBody BaseQueryReq req) throws Exception {
        return dataPermissionService.queryByOuter(req);
    }
}
