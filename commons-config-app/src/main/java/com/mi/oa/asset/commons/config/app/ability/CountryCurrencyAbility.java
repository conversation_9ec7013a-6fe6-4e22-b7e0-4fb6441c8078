package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.domain.international.entity.CountryCurrencyDo;
import com.mi.oa.asset.commons.config.domain.international.enums.DefaultDataEnums;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryCurrencyRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/7
 */
@Service
@Slf4j
public class CountryCurrencyAbility {

    @Resource
    private CountryCurrencyRepo currencyRepo;

    /**
     * 校验是否重复增加数据，以及默认币种
     * @param entity
     */
    public void check(CountryCurrencyDo entity) {
        List<CountryCurrencyDo> byCountryId = currencyRepo.getByCountryId(entity.getCountryConfigId());
        boolean notEmpty = CollectionUtils.isNotEmpty(byCountryId);
        if (notEmpty){
            Integer id = entity.getId();
            boolean isDuplicate = (id != null)
                    ? checkUpdate(byCountryId, id, entity)
                    : checkCreate(byCountryId, entity);

            if (isDuplicate) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER,
                        "当前国家的货币代码已存在");
            }

            boolean isDefault  = (id != null)
                    ? checkDeaultUpdate(byCountryId, id, entity)
                    : checkDeaultCreate(byCountryId, entity);

            if (isDefault) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER,
                        "该币种和国家已有默认的币种，请修改默认币种。");
            }
        }
    }
    // 带ID校验方法
    private boolean checkUpdate(List<CountryCurrencyDo> list, Integer id, CountryCurrencyDo entity) {
        return list.stream()
                .filter(Objects::nonNull)
                .anyMatch(item -> !Objects.equals(item.getId(), id)
                        && isCurrencyMatch(item, entity));
    }

    // 无ID校验方法
    private boolean checkCreate(List<CountryCurrencyDo> list, CountryCurrencyDo entity) {
        return list.stream()
                .anyMatch(item -> isCurrencyMatch(item, entity));
    }

    // 共用条件判断
    private boolean isCurrencyMatch(CountryCurrencyDo item, CountryCurrencyDo entity) {
        return Objects.equals(item.getCurrencyCode(), entity.getCurrencyCode());
    }

    private boolean checkDeaultUpdate(List<CountryCurrencyDo> list, Integer id, CountryCurrencyDo entity) {
        return list.stream()
                .filter(Objects::nonNull)
                .anyMatch(item -> !Objects.equals(item.getId(), id)
                        && isDefaultMatch(item, entity));
    }

    private boolean checkDeaultCreate(List<CountryCurrencyDo> list, CountryCurrencyDo entity) {
        return list.stream()
                .anyMatch(item ->  isDefaultMatch(item, entity));
    }

    private boolean isDefaultMatch(CountryCurrencyDo item, CountryCurrencyDo entity) {
        return Objects.equals(DefaultDataEnums.DEFAULTCURRENCY.getKey(),item.getDefaultCurrency())
                && entity.getDefaultCurrency() != null && Objects.equals(DefaultDataEnums.DEFAULTCURRENCY.getKey(), entity.getDefaultCurrency());
    }
}
