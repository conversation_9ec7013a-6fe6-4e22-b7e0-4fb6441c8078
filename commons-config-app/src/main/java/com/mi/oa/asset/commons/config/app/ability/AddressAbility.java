package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.address.AssetReceiveAddressReq;
import com.mi.oa.asset.commons.config.domain.address.utils.CommonUtil;
import com.mi.oa.asset.commons.config.domain.address.valobj.AddressParse;
import com.mi.oa.asset.eam.feign.client.AddressClient;
import com.mi.oa.asset.eam.feign.dto.AddressParseDTO;
import com.mi.oa.asset.eam.feign.res.CommonResp;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.routines.EmailValidator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2024/4/10 16:10
 */
@Service
@Slf4j
public class AddressAbility {
    public static final String SUCCESS_CODE = "200";
    @Resource
    private AddressClient addressClient;

    public void checkParams(AssetReceiveAddressReq request) {

        if(StringUtils.isBlank(request.getReceiveName())){
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请填写收货人姓名");
        }
        if (StringUtils.isBlank(request.getReceiveMobile())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请填写收货人手机号码");
        } else {
            String regexp = "^1\\d{10}$";
            Pattern pattern = Pattern.compile(regexp);
            Matcher matcher = pattern.matcher(request.getReceiveMobile().trim());
            if (!matcher.matches()) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请输入正确的手机号码");
            }
        }
        if (StringUtils.isNotBlank(request.getReceiveEmail())) {
            // 校验邮箱信息
            EmailValidator instance = EmailValidator.getInstance();
            boolean valid = instance.isValid(request.getReceiveEmail());
            if (!valid) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请输入正确的邮箱地址");
            }
        }
        if(StringUtils.isBlank(request.getProvinceCode())){
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请选择所在省份");
        }
        if(StringUtils.isBlank(request.getCityCode())){
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请选择所在城市");
        }
        if(StringUtils.isBlank(request.getAreaCode())){
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请选择所在区县");
        }
        if(StringUtils.isBlank(request.getDetailAddress())){
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请输入收货人详细地址");
        }
        if (CommonUtil.isSpecialChar(request.getDetailAddress())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "详细地址不能有表情等特殊字符");
        }
    }

    public Map<String, String> getAreaCodeMapByAddress(List<String> addresss) {
        Map<String, String> retMap = new HashMap<>();
        if (CollectionUtils.isEmpty(addresss)) return retMap;
        AddressParseDTO params = new AddressParseDTO();
        List<AddressParseDTO.Address> addresses = new ArrayList<>();
        addresss.forEach(i ->
                addresses.add(new AddressParseDTO.Address(i))
        );
        params.setAddress(addresses);
        try {
            CommonResp<Object> result = addressClient.parse(params);
            if (SUCCESS_CODE.equals(result.getHeader().getCode())) {
                List<AddressParse> addressParse = JacksonUtils.json2List(JacksonUtils.bean2Json(result.getBody()), AddressParse.class);
                if (CollectionUtils.isNotEmpty(addressParse)) {
                    addressParse.forEach(i -> {
                        StringBuilder addressBuilder = new StringBuilder();
                        if (i != null) {
                            if (i.getProvince() != null) {
                                addressBuilder.append(i.getProvince().getCode()).append(",");
                            }
                            if (i.getCity() != null) {
                                addressBuilder.append(i.getCity().getCode()).append(",");
                            }
                            if (i.getDistrict() != null) {
                                addressBuilder.append(i.getDistrict().getCode()).append(",");
                            }
                            if (StringUtils.isNotBlank(addressBuilder.toString())) {
                                retMap.put(i.getAddress(), StringUtils.substring(addressBuilder.toString(), 0, -1));
                            }
                        }
                    });
                }
            }
        } catch (Exception e) {
            log.error("地址解析失败", e);
        }
        return retMap;
    }
}
