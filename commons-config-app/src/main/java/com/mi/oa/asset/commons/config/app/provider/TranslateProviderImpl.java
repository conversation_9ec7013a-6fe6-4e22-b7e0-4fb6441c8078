package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.translate.NeputunReq;
import com.mi.oa.asset.commons.config.api.translate.TranslateProvider;
import com.mi.oa.asset.commons.config.app.ability.TranslateAbility;
import com.mi.oa.asset.commons.config.domain.translate.enums.NeptuneEnums;
import com.mi.xms.sdk.T;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 翻译服务实现
 */
@Service
@Slf4j
@DubboService
public class TranslateProviderImpl implements TranslateProvider {

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private TranslateAbility translateAbility;

    /**
     * 默认false，非海外
     */
    private static final boolean IS_SG = false;

    /**
     * 依赖插件，扫描本地classes下的Neptune文件翻译
     * @param key
     * @param lang
     * @return
     */
    @Override
    public String getTranslateFromKey(String key, String lang) {
        return  T.tr(key,lang);
    }

    @SuppressWarnings("unchecked")
    @Override
    public String getTranslateJsonData(NeputunReq neputunReq) {
       String jsonData = (String) redisTemplate.opsForValue().get(NeptuneEnums.CACHE_TRANSLATE_NEPTUN_JSON_DATA_KEY.getKey());
        if (jsonData == null || jsonData.isEmpty()) {
            String data = translateAbility.getData(neputunReq.isSg());
            redisTemplate.opsForValue().set(NeptuneEnums.CACHE_TRANSLATE_NEPTUN_JSON_DATA_KEY.getKey(), data);
            redisTemplate.expire(NeptuneEnums.CACHE_TRANSLATE_NEPTUN_JSON_DATA_KEY.getKey(), 12, TimeUnit.HOURS);
        }
        return jsonData;
    }

}
