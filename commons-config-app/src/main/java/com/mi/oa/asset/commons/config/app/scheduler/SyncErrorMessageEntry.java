package com.mi.oa.asset.commons.config.app.scheduler;

import com.mi.oa.asset.commons.config.app.ability.TranslateAbility;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 同步翻译平台错误信息词条
 * <AUTHOR>
 * @Date 2025/5/21
 */
@Slf4j
@Service
@PlanTask(name = "SyncErrorMessageEntry", quartzCron = "0 0 1 * * ?", description = "定时同步错误信息词条数据，每天 01:00 点同步一次")
public class SyncErrorMessageEntry implements PlanExecutor {

    @Resource
    private TranslateAbility translateAbility;
    /**
     * 默认false，非海外
     */
    private static final boolean IS_SG = false;


    /**
     * 同步翻译平台错误信息词条
     */
    @Override
    public void execute() {
        log.info("Sync SyncErrorMessageEntry data, start at: {}", DateTime.now());
        translateAbility.pullDataToRedis(IS_SG);
        log.info("Sync SyncErrorMessageEntry data, end at: {}", DateTime.now());
    }
}
