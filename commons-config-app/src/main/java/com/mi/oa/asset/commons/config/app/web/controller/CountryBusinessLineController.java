package com.mi.oa.asset.commons.config.app.web.controller;


import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineProvider;
import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineReq;
import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineRes;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 国家地区和业务线 关联表的 Controller
 */
@RestController
@HttpApiModule(value = "CountryBusinessLineController", apiController = CountryBusinessLineController.class)
@RequestMapping("/configs/countryBusinessLine")
@Slf4j
public class CountryBusinessLineController {

    @Resource
    private CountryBusinessLineProvider countryBusinessLineProvider;

    @HttpApiDoc(apiName = "查询列表", value = "/configs/countryBusinessLine/list", method = MiApiRequestMethod.GET)
    @GetMapping("/list")
    public Result<List<CountryBusinessLineRes>> list() {
        return Result.success(countryBusinessLineProvider.getCountryRegionList());
    }

    @HttpApiDoc(apiName = "根据id查询详情", value = "/configs/countryBusinessLine/detail", method = MiApiRequestMethod.GET)
    @GetMapping("/detail")
    public Result<CountryBusinessLineRes> info(@RequestParam("countryBusinessLineId") Integer countryBusinessLineId) {
        return Result.success(countryBusinessLineProvider.getById(countryBusinessLineId));
    }

    @HttpApiDoc(apiName = "根据业务线查询详情", value = "/configs/countryBusinessLine/byBusinessLine", method = MiApiRequestMethod.GET)
    @GetMapping("/byBusinessLine")
    public Result<List<CountryBusinessLineRes>> byBusinessLine(@RequestParam("businessLine") String businessLine) {
        return Result.success(countryBusinessLineProvider.getByBusinessLine(businessLine));
    }

    @HttpApiDoc(apiName = "更新", value = "/configs/countryBusinessLine/edit", method = MiApiRequestMethod.POST)
    @PostMapping("/edit")
    public Result<Integer> edit(@RequestBody CountryBusinessLineReq req) {
        log.info("countryBusinessLine edit : {}", JacksonUtils.bean2Json(req));
        return Result.success(countryBusinessLineProvider.saveOrUpdate(req));
    }

    @HttpApiDoc(apiName = "删除", value = "/configs/countryBusinessLine/delete", method = MiApiRequestMethod.POST)
    @PostMapping("/delete")
    public Result<Void> delete(@RequestBody CountryBusinessLineReq req) {
        log.info("countryBusinessLine delete : {}", JacksonUtils.bean2Json(req));
        countryBusinessLineProvider.removeByIds(Collections.singletonList(req.getId()));
        return Result.success();
    }
}
