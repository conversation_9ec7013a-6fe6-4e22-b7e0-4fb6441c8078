package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.assetorganization.*;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleInfoRes;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgStructure;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgUnitQuery;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.eam.feign.mdm.res.MdmDepartmentRes;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/15 16:53
 */

@Mapper(
        componentModel = "spring",
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL
)
public interface AssetOrgConverter extends CommonConverter {

    AssetOrgUnit reqToAssetOrgUnit(SaveAssetOrgUnitReq source);

    AssetOrgStructure orgUnitToStructure(AssetOrgUnit source);

    void updateStructure(AssetOrgUnit source, @MappingTarget AssetOrgStructure target);

    AssetOrgStructureRes toAssetOrgStructureRes(AssetOrgStructure source);

    List<AssetOrgStructureRes> toAssetOrgStructureRes(List<AssetOrgStructure> source);

    @Mapping(source = "orgCode", target = "code")
    @Mapping(source = "orgName", target = "name")
    @Mapping(source = "defaultCostCenter", target = "costCenter")
    @Mapping(target = "isVirtual", expression = "java(source.getIsVirtual() ? true : null)")
    AssetOrgStructureTreeRes toAssetOrgStructureTreeRes(AssetOrgStructureRes source);

    default AssetOrgStructureTreeRes toAssetOrgStructureTreeRes(AssetOrgStructureRes source, String language) {
        if (source == null) {
            return null;
        }
        AssetOrgStructureTreeRes structureTreeRes = toAssetOrgStructureTreeRes(source);
        if (structureTreeRes == null) {
            return null;
        }
        if (!EAMConstants.CHINESE.equals(language)) {
            structureTreeRes.setName(StringUtils.isBlank(source.getOrgNameEn()) ? structureTreeRes.getName() : source.getOrgNameEn());
        }
        return structureTreeRes;
    }

    List<AssetOrgStructureTreeRes> toAssetOrgStructureTreeRes(List<AssetOrgStructureRes> source);

    default List<AssetOrgStructureTreeRes> toAssetOrgStructureTreeRes(List<AssetOrgStructureRes> source, String language) {
        if (source == null) {
            return null;
        }
        List<AssetOrgStructureTreeRes> list = new ArrayList<AssetOrgStructureTreeRes>(source.size());
        for (AssetOrgStructureRes assetOrgStructureRes : source) {
            list.add(toAssetOrgStructureTreeRes(assetOrgStructureRes, language));
        }
        return list;
    }

    AssetOrgUnitRes toAssetOrgUnitRes(AssetOrgUnit source);

    default AssetOrgUnitRes toAssetOrgUnitRes(AssetOrgUnit source, String language) {
        return toAssetOrgUnitRes(source, language, false);
    }

    default AssetOrgUnitRes toAssetOrgUnitRes(AssetOrgUnit source, String language, boolean showEnName) {
        if (source == null) return null;
        AssetOrgUnitRes assetOrgUnitRes = toAssetOrgUnitRes(source);
        if (!EAMConstants.CHINESE.equals(language)) {
            assetOrgUnitRes.setOrgNamePath(StringUtils.isNotBlank(source.getOrgNamePathEn()) ? source.getOrgNamePathEn() : source.getOrgNamePath());
        }
        if (showEnName) {
            assetOrgUnitRes.setOrgName(StringUtils.isNotBlank(source.getOrgNameEn()) ? source.getOrgNameEn() : source.getOrgName());
        }
        return assetOrgUnitRes;
    }

    AssetOrgUnitRes toAssetOrgUnitRes(AssetOrgUnit source, List<BusinessRoleInfoRes> roleInfoList);

    List<AssetOrgUnitRes> toAssetOrgUnitRes(List<AssetOrgUnit> source);

    default List<AssetOrgUnitRes> toAssetOrgUnitRes(List<AssetOrgUnit> source, String language) {
        if (source == null) {
            return null;
        }
        List<AssetOrgUnitRes> list = new ArrayList<AssetOrgUnitRes>(source.size());
        for (AssetOrgUnit assetOrgUnit : source) {
            list.add(toAssetOrgUnitRes(assetOrgUnit, language));
        }

        return list;
    }

    default List<AssetOrgUnitRes> toAssetOrgUnitRes(List<AssetOrgUnit> source, String language, boolean showEnName) {
        if (source == null) {
            return null;
        }
        List<AssetOrgUnitRes> list = new ArrayList<AssetOrgUnitRes>(source.size());
        for (AssetOrgUnit assetOrgUnit : source) {
            list.add(toAssetOrgUnitRes(assetOrgUnit, language, showEnName));
        }

        return list;
    }


    AssetOrgUnitQuery toAssetOrgUnitQuery(AssetOrgUnitQueryReq source);

    @Mapping(source = "deptCode", target = "orgCode")
    @Mapping(source = "deptName", target = "orgName")
    @Mapping(source = "deptNameEn", target = "orgNameEn")
    @Mapping(source = "parentDeptCode", target = "parentCode")
    @Mapping(source = "deptCostCenter", target = "defaultCostCenter")
    @Mapping(target = "isVirtual", constant = "false")
    @Mapping(target = "dataSource", constant = "sys_mdm")
    AssetOrgStructure createAssetOrgStructure(MdmDepartmentRes source);


    List<AssetOrgStructure> createAssetOrgStructure(List<MdmDepartmentRes> source);

    @Mapping(source = "deptCode", target = "orgCode")
    @Mapping(source = "deptName", target = "orgName")
    @Mapping(source = "deptNameEn", target = "orgNameEn")
    @Mapping(source = "parentDeptCode", target = "parentCode")
    @Mapping(source = "deptCostCenter", target = "defaultCostCenter")
    @Mapping(target = "dataSource", constant = "sys_mdm")
    void updateAssetOrgStructure(MdmDepartmentRes source, @MappingTarget AssetOrgStructure target);

    default List<AssetOrgStructure> updateAssetOrgStructure(List<MdmDepartmentRes> source, @MappingTarget List<AssetOrgStructure> target) {
        List<AssetOrgStructure> updates = new ArrayList<>();

        Map<String, MdmDepartmentRes> sourceMap = source.stream().collect(Collectors.toMap(MdmDepartmentRes::getDeptCode, v -> v));
        target.forEach(t -> {
            MdmDepartmentRes s = sourceMap.get(t.getOrgCode());
            if (s == null) return;

            updateAssetOrgStructure(s, t);
            updates.add(t);
        });

        return updates;
    }

    @Mapping(target = "isAssetManageOrg", constant = "false")
    @Mapping(target = "isAssetUseOrg", constant = "true")
    @Mapping(target = "companyCode", constant = "")
    @Mapping(target = "companyName", constant = "")
    @Mapping(target = "costCenter", source = "defaultCostCenter")
    @Mapping(target = "isVirtual", constant = "false")
    AssetOrgUnit orgStructureToUnit(AssetOrgStructure orgStructure);

}
