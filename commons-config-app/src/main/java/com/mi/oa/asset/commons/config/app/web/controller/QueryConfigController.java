package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.queryfield.QueryConfigReq;
import com.mi.oa.asset.commons.config.api.queryfield.QueryConfigRes;
import com.mi.oa.asset.commons.config.api.queryfield.QueryFieldProvider;
import com.mi.oa.asset.commons.config.app.ability.QueryConfigAbility;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:21
 */

@HttpApiModule(value = "QueryFieldController", apiController = QueryConfigController.class)
@RestController
@RequestMapping("/configs/query-config")
public class QueryConfigController {

    @Resource
    QueryFieldProvider queryFieldProvider;

    @Resource
    private QueryConfigAbility queryConfigAbility;

    @HttpApiDoc(apiName = "查询方案清单", value = "/configs/query-config/list", method = MiApiRequestMethod.GET)
    @GetMapping({"/list"})
    public Result<List<QueryConfigRes>> queryConfigList(@RequestParam(value = "manageLine", required = false) String manageLine,
                                                        @RequestParam("funId") String funId) {
        List<QueryConfigRes> queryConfigRes = queryFieldProvider.queryConfigList(manageLine, funId);
        return Result.success(queryConfigRes);
    }

    @HttpApiDoc(apiName = "保存查询方案", value = "/configs/query-config/save", method = MiApiRequestMethod.POST)
    @PostMapping({"/save"})
    public Result<QueryConfigReq> queryConfigSave(@RequestBody QueryConfigReq queryConfigReq) {
        List<ConstraintViolation<QueryConfigReq>> errors = queryConfigAbility.validateSaveReq(queryConfigReq, false);
        if (CollectionUtils.isNotEmpty(errors)) {
            return Result.error(ErrorCodes.INTERNAL_SERVER_ERROR.getCode(), errors.get(0).getMessage());
        }
        queryFieldProvider.saveQueryConfig(queryConfigReq, false);
        return Result.success(queryConfigReq);
    }

    @HttpApiDoc(apiName = "查询默认方案", value = "/configs/query-config/default-config", method = MiApiRequestMethod.GET)
    @GetMapping({"/default-config"})
    public Result<QueryConfigRes> queryDefaultConfig(@RequestParam(value = "manageLine", required = false) String manageLine,
                                                     @RequestParam("funId") String funId) {
        return Result.success(queryFieldProvider.queryDefaultConfig(manageLine, funId));
    }

    @HttpApiDoc(apiName = "保存默认查询方案", value = "/configs/query-config/default-config-save", method = MiApiRequestMethod.POST)
    @PostMapping({"/default-config-save"})
    public Result<QueryConfigReq> defaultQueryConfigSave(@RequestBody QueryConfigReq queryConfigReq) {
        List<ConstraintViolation<QueryConfigReq>> errors = queryConfigAbility.validateSaveReq(queryConfigReq, true);
        if (CollectionUtils.isNotEmpty(errors)) {
            return Result.error(ErrorCodes.INTERNAL_SERVER_ERROR.getCode(), errors.get(0).getMessage());
        }
        queryFieldProvider.saveQueryConfig(queryConfigReq, true);
        return Result.success(queryConfigReq);
    }

    @HttpApiDoc(apiName = "删除查询方案", value = "/configs/query-config/delete/{queryId}", method = MiApiRequestMethod.POST)
    @PostMapping({"/delete/{queryId}"})
    public Result<Void> queryConfigDelete(@PathVariable("queryId") Integer queryId) {
        queryFieldProvider.deleteQueryConfig(queryId);
        return Result.success();
    }
}
