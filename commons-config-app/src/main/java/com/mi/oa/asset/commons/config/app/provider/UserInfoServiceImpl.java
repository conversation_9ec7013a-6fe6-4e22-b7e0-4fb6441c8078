package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigDataRage;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigField;
import com.mi.oa.asset.commons.config.api.user.*;
import com.mi.oa.asset.commons.config.app.converter.UserInfoConverter;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRoleUser;
import com.mi.oa.asset.commons.config.domain.businessrole.repository.BusinessRoleRepo;
import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.domain.common.repository.AllotConfigRepo;
import com.mi.oa.asset.commons.config.domain.common.repository.BusinessLineRepo;
import com.mi.oa.asset.commons.config.domain.common.repository.CommonDataRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.AllotConfig;
import com.mi.oa.asset.commons.config.domain.common.valobj.Company;
import com.mi.oa.asset.eam.feign.res.HrodEmployeeRes;
import com.mi.oa.asset.eam.feign.service.HrodService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.idm.api.IdmAccountService;
import com.mi.oa.infra.oaucf.idm.api.IdmUserService;
import com.mi.oa.infra.oaucf.idm.api.rep.*;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户service
 *
 * <AUTHOR>
 * @date 2023/9/11 11:17
 */
@Service
@DubboService
public class UserInfoServiceImpl implements UserInfoService {

    @Resource
    private UserInfoConverter userInfoConverter;

    @Resource
    private IdmUserService idmUserService;

    @Resource
    private IdmAccountService idmAccountService;

    @Resource
    private BusinessLineRepo businessLineRepo;

    @Resource
    private AllotConfigRepo allotConfigRepo;

    @Resource
    private BusinessRoleRepo businessRoleRepo;

    @Resource
    private HrodService hrodService;

    @Resource
    private CommonDataRepo commonDataRepo;

    @Override
    public UserBaseInfoRes getUserInfoByUid(String uid) {
        Resp<UserInfoDto> userInfoDtoResp = idmUserService.findUseInfoByUid(uid);
        if (BaseResp.CODE_SUCCESS != userInfoDtoResp.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, userInfoDtoResp.getMsg());
        }
        UserInfoDto userInfoDto = userInfoDtoResp.getData();
        return userInfoConverter.getUserBaseInfo(userInfoDto);
    }

    @Override
    public UserBaseInfoRes getUserInfoByUserName(String userName) {
        Resp<String> uidResp = idmAccountService.findUidByUserName(userName);
        if (BaseResp.CODE_SUCCESS != uidResp.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST);
        }
        String uid = uidResp.getData();
        Resp<UserInfoDto> useInfoByUid = idmUserService.findUseInfoByUid(uid);
        if (BaseResp.CODE_SUCCESS != useInfoByUid.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, useInfoByUid.getMsg());
        }
        UserInfoDto userInfoDto = useInfoByUid.getData();
        return userInfoConverter.getUserBaseInfo(userInfoDto);
    }

    @Override
    public List<User> getUsersByUserName(List<String> userNames) {
        userNames = userNames.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(userNames)) return new ArrayList<>();

        Resp<List<UserDo>> uidRes =  idmUserService.findUidsByUserNames(StringUtils.join(userNames, ","));
        if (BaseResp.CODE_SUCCESS != uidRes.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, uidRes.getMsg());
        }

        List<String> uids = uidRes.getData().stream().map(UserDo::getUid).collect(Collectors.toList());
        Resp<List<UserBaseInfoDto>> userInfoRes = idmUserService.findUserInfoListByUidList(StringUtils.join(uids, ","));
        if (BaseResp.CODE_SUCCESS != userInfoRes.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, userInfoRes.getMsg());
        }

        return userInfoConverter.toUser(userInfoRes.getData());
    }

    @Override
    public User getUserByUserName(String userName) {
        Resp<String> uidRes = idmAccountService.findUidByUserName(userName);
        if (BaseResp.CODE_SUCCESS != uidRes.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST);
        }
        Resp<UserInfoDto> useInfoRes = idmUserService.findUseInfoByUid(uidRes.getData());
        if (BaseResp.CODE_SUCCESS != useInfoRes.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, useInfoRes.getMsg());
        }

        return userInfoConverter.toUser(useInfoRes.getData());
    }

    @Override
    public List<UserBaseInfoRes> getUserByLikeUserName(String userName, String limit) {
        Resp<List<EsAccountDto>> employees = idmAccountService.fuzzySearchAccountByName(userName, limit);
        if (BaseResp.CODE_SUCCESS != employees.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, employees.getMsg());
        }
        List<EsAccountDto> userInfoDto = employees.getData();
        return userInfoConverter.getUserBaseInfos(userInfoDto);
    }

    @Override
    public List<UserInfoRes> fuzzySearchUserInfo(String userName, String limit) {
        Resp<List<EsAccountDto>> employees = idmAccountService.fuzzySearchAccountByName(userName, limit);
        if (BaseResp.CODE_SUCCESS != employees.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, employees.getMsg());
        }

        List<EsAccountDto> userInfoDto = employees.getData();
        List<String> uidList = userInfoDto.stream().map(EsAccountDto::getUid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(uidList)) {
            return Collections.emptyList();
        }
        Map<String, EsAccountDto> userInfoMap = new HashMap<>();
        userInfoDto.forEach(esAccountDto -> userInfoMap.put(esAccountDto.getUid(), esAccountDto));

        List<UserInfoRes> userInfoRes = getUserInfoByUid(uidList, userInfoMap);
        //加载成本中心的公司信息
        return fillCostCenterCompanyInfo(userInfoRes);
    }

    private List<UserInfoRes> fillCostCenterCompanyInfo(List<UserInfoRes> userInfoRes) {
        List<String> costCts = userInfoRes.stream().map(UserInfoRes::getCostCenter)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, String> companyCostCtMap = new HashMap<>();
        costCts.forEach(s -> {
            if (s.length() > 5) {
                companyCostCtMap.put(s, s.substring(1, 5));
            }
        });
        if (!CollectionUtils.isEmpty(companyCostCtMap.values())) {
            List<Company> companies = commonDataRepo.getBatchCompany(new ArrayList<>(companyCostCtMap.values()));
            if (!CollectionUtils.isEmpty(companies)) {
                Map<String, String> companyMap = companies.stream().collect(Collectors.toMap(Company::getCompanyCode, Company::getCompanyName));
                userInfoRes.forEach(user -> {
                    String costCt = user.getCostCenter();
                    String company = MapUtils.getString(companyCostCtMap, costCt, StringUtils.EMPTY);
                    user.setCompany(company);
                    user.setCompanyDescr(MapUtils.getString(companyMap, company, StringUtils.EMPTY));
                });
            }
        }
        return userInfoRes;
    }

    @Override
    public List<UserInfoRes> getUsersByUserNames(List<String> userNames) {
        if (CollectionUtils.isEmpty(userNames)) return Collections.emptyList();
        Resp<List<UserDo>> uidRes = idmUserService.findUidsByUserNames(StringUtils.join(userNames, ","));
        if (BaseResp.CODE_SUCCESS != uidRes.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, uidRes.getMsg());
        }
        List<String> uidList = uidRes.getData().stream().map(UserDo::getUid).collect(Collectors.toList());
        return getUserInfoByUid(uidList);
    }

    @Override
    public List<UserBaseInfoRes> getUserBaseInfosByUserNames(List<String> userNames) {
        if (CollectionUtils.isEmpty(userNames)) return Collections.emptyList();
        Resp<List<UserDo>> uidRes = idmUserService.findUidsByUserNames(StringUtils.join(userNames, ","));
        if (BaseResp.CODE_SUCCESS != uidRes.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, uidRes.getMsg());
        }
        List<String> uidList = uidRes.getData().stream().map(UserDo::getUid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(uidList)) return Collections.emptyList();
        Resp<List<UserBaseInfoDto>> userInfoListRes = idmUserService.findUserInfoListByUidList(String.join(",", uidList));
        if (BaseResp.CODE_SUCCESS != userInfoListRes.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, userInfoListRes.getMsg());
        }

        List<UserBaseInfoDto> data = userInfoListRes.getData();
        return userInfoConverter.dtoToUserBaseInfos(data);
    }

    @Override
    public List<UserInfoRes> getAllotLikeUserInfo(String businessLine, String userName, String limit) {
        BusinessLineDo businessLineDo = businessLineRepo.searchByBusinessLine(businessLine);

        // 查询调拨配置
        List<AllotConfig> allotConfigs = allotConfigRepo.allotConfigList(businessLineDo.getBusinessLineId());
        List<AllotConfig> inUserConfigs = allotConfigs.stream().filter(o -> o.getFieldCode() == AllotConfigField.IN_USER_NAME).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inUserConfigs)) {
            throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, "该业务线调入人属性未配置");
        }
        AllotConfig allotConfig = inUserConfigs.get(0);
        AllotConfigDataRage dataRage = allotConfig.getDataRage();
        if (dataRage == AllotConfigDataRage.ALL) {
            return fuzzySearchUserInfo(userName, limit);
        } else {
            List<String> limitBizData = allotConfig.getLimitBizData();
            // 查询业务角色对应的用户
            List<BusinessRoleUser> rolesByRoleCodes = businessRoleRepo.getRolesByRoleCodes(limitBizData, userName,
                    Integer.valueOf(limit), businessLine);
            List<String> userNames = rolesByRoleCodes.stream().map(BusinessRoleUser::getUserCode).collect(Collectors.toList());
            return getUsersByUserNames(userNames);
        }
    }

    @Override
    public EmployeeInfoRes getEmpInfoByUserName(String userName) {
        HrodEmployeeRes employeeRes = hrodService.getEmpByUserName(userName);
        if (Objects.isNull(employeeRes)) return null;

        return userInfoConverter.employeeResToEmployeeInfoRes(employeeRes);
    }

    @Override
    public UserMobileRes getMobileByUserName(String userName) {
        Resp<String> uidRes = idmAccountService.findUidByUserName(userName);
        if (BaseResp.CODE_SUCCESS != uidRes.getCode() || StringUtils.isEmpty(uidRes.getData())) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "未查询到员工信息");
        }
        Resp<MobileComboDto> resp = idmAccountService.findFullMobileByUid(uidRes.getData());
        if (BaseResp.CODE_SUCCESS != resp.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, resp.getMsg());
        }
        MobileComboDto data = resp.getData();
        return UserMobileRes.builder().zoneCode(data.getZoneCode()).mobile(data.getMobile()).build();
    }

    private List<UserInfoRes> getUserInfoByUid(List<String> uidList, Map<String, EsAccountDto> userInfoMap) {
        List<UserInfoRes> userInfoRes = getUserInfoByUid(uidList);
        userInfoRes.forEach(user -> {
            EsAccountDto esAccountDto = userInfoMap.get(user.getUid());
            user.setCostCenter(esAccountDto == null ? "" : esAccountDto.getCostCt());
        });
        return userInfoRes;
    }

    private List<UserInfoRes> getUserInfoByUid(List<String> uidList) {
        if (CollectionUtils.isEmpty(uidList)) {
            return new ArrayList<>();
        }
        Resp<List<UserBaseInfoDto>> userInfoListByUidList = idmUserService.findUserInfoListByUidList(String.join(",", uidList));

        List<UserBaseInfoDto> userInfoListDTOS = userInfoListByUidList.getData();
        Iterator<UserBaseInfoDto> iterator = userInfoListDTOS.iterator();
        while (iterator.hasNext()) {
            UserBaseInfoDto dto = iterator.next();
            if ("I".equals(dto.getHrStatus())) {
                iterator.remove();
                continue;
            }
            if (StringUtils.isNotEmpty(dto.getHeadUrl())) {
                dto.setHeadUrl(dto.getHeadUrl() + "?thumb=1&w=96&h=96");
            }
        }
        return userInfoConverter.userInfoDtoToResList(userInfoListDTOS);
    }

}
