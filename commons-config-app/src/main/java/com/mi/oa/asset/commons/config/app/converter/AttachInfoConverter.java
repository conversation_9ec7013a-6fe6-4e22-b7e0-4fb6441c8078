package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.attach.AttachInfoReq;
import com.mi.oa.asset.commons.config.api.attach.AttachInfoRes;
import com.mi.oa.asset.commons.config.domain.common.valobj.AttachInfo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @autho zhan
 * @date 2023-10-26 15:23
 */
@Mapper(componentModel = "spring")
public interface AttachInfoConverter {

    List<AttachInfo> reqListToDo(List<AttachInfoReq> attachInfoReqList);

    List<AttachInfoRes> doListToRes(List<AttachInfo> attachInfoList);
}
