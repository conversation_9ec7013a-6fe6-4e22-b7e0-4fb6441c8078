package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.account.api.assetaccount.AssetAccountProvider;
import com.mi.oa.asset.account.api.assetaccount.res.AssetAccountRes;
import com.mi.oa.asset.account.api.assetaccount.res.ExtraFieldsRes;
import com.mi.oa.asset.common.enums.AssetUseStatus;
import com.mi.oa.asset.common.enums.CheckStatus;
import com.mi.oa.asset.common.model.Attach;
import com.mi.oa.asset.common.model.SaveAttach;
import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgProvider;
import com.mi.oa.asset.commons.config.api.assetshare.*;
import com.mi.oa.asset.commons.config.api.common.ComplexRange;
import com.mi.oa.asset.commons.config.api.serialcode.SerialCodeProvider;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.app.converter.AssetShareConverter;
import com.mi.oa.asset.commons.config.app.converter.CommonDataConverter;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.CustomShareList;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareChangeLog;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRange;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRecord;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.CustomShareListRepo;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.ShareChangeLogRepo;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.ShareRangeRepo;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.ShareRecordRepo;
import com.mi.oa.asset.commons.config.domain.assetshare.valobj.CustomShareListExport;
import com.mi.oa.asset.commons.config.domain.assetshare.valobj.CustomShareListImport;
import com.mi.oa.asset.commons.config.domain.assetshare.valobj.ShareLogJson;
import com.mi.oa.asset.commons.config.domain.assetshare.valobj.ShareQueryReq;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.commons.config.domain.common.repository.AttachInfoRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.AttachInfo;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.mi.oa.asset.eam.auth.data.DataPermissionService;
import com.mi.oa.asset.eam.dto.BaseRes;
import com.mi.oa.asset.eam.jxs.enums.QueryConditionEnum;
import com.mi.oa.asset.eam.jxs.req.BaseQueryReq;
import com.mi.oa.asset.eam.jxs.req.QueryConditionReq;
import com.mi.oa.asset.eam.jxs.res.ListDataRes;
import com.mi.oa.asset.eam.jxs.service.EamJxsService;
import com.mi.oa.asset.eam.jxs.utils.CommonUtils;
import com.mi.oa.asset.eam.utils.ExcelUtils;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.mi.oa.infra.uc.common.util.GsonUtil;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 共享资产业务处理
 *
 * <AUTHOR>
 * @date 2025-01-07 11:24
 */
@Service
@Slf4j
public class AssetShareAbility {

    @Resource
    private ShareRecordRepo shareRecordRepo;

    @Resource
    private ShareRangeRepo shareRangeRepo;

    @Resource
    private CustomShareListRepo shareListRepo;

    @Resource
    private ShareChangeLogRepo shareChangeLogRepo;

    @Resource
    private AttachInfoRepo attachInfoRepo;

    @Resource
    private AssetShareConverter converter;

    @Resource
    private CommonDataConverter commonDataConverter;

    @Resource
    private ShareConditionMergeAbility mergeAbility;

    @Resource
    private EamJxsService eamJxsService;

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private AssetOrgProvider assetOrgProvider;

    @DubboReference(check = false)
    private AssetAccountProvider accountProvider;

    @DubboReference(check = false)
    private SerialCodeProvider serialCodeProvider;

    @Resource
    private DataPermissionService dataPermissionService;

    public ShareRecordRes getById(Integer id) {
        ShareRecordRes res = new ShareRecordRes();
        // 查询共享主记录
        ShareRecord shareRecord = shareRecordRepo.getById(id);
        if (Objects.isNull(shareRecord)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "参数错误，查询记录不存在");
        }
        Integer shareId = shareRecord.getId();
        res.setId(shareId);
        res.setBusinessLine(shareRecord.getBusinessLine());
        ShareRecordBaseRes recordBaseRes = converter.recordToRes(shareRecord);
        // 处理人员范围json串
        if (StringUtils.isNotBlank(shareRecord.getShareUserCode())) {
            String[] userCode = shareRecord.getShareUserCode().split(CommonConstant.SEMICOLON);
            String[] userName = shareRecord.getShareUserName().split(CommonConstant.SEMICOLON);
            List<Map<String, String>> userList = new ArrayList<>();
            for (int i = 0; i < userCode.length; i++) {
                Map<String, String> userMap = new HashMap<>();
                userMap.put("name", userCode[i]);
                userMap.put("userName", userName[i]);
                userList.add(userMap);
            }
            recordBaseRes.setShareUser(GsonUtil.toJsonString(userList));
        }
        res.setBaseInfo(recordBaseRes);
        // 查询附件
        List<AttachInfo> attachList = attachInfoRepo.getAttachList(Arrays.asList(shareId), CommonConstant.ASSET_SHARE_FUN);
        if (CollectionUtils.isNotEmpty(attachList)) {
            List<Attach> attaches = new ArrayList<>();
            attachList.forEach(item -> {
                Attach attach = new Attach();
                attach.setFileName(item.getAttachName());
                attach.setOriginFileName(item.getOriginName());
                attach.setDownloadUrl(item.getAttachLink());
                attaches.add(attach);
            });
            res.setAttachInfo(attaches);
        }
        // 查询更多筛选范围
        List<ShareRange> shareRanges = shareRangeRepo.getByShareId(shareId);
        if (CollectionUtils.isNotEmpty(shareRanges)) {
            List<ComplexRange> complexRanges = converter.rangToResList(shareRanges);
            res.setComplexRanges(complexRanges);
        }
        return res;
    }

    /**
     * 分页查询共享自定义清单（资产必须展示信息来源于台账接口）
     *
     * @param shareId
     * @param pageNum
     * @param pageSize
     * @return
     */
    public PageData<CustomShareListRes> pageCustomShareList(Integer shareId, Integer pageNum, Integer pageSize) {
        log.info("pageQuery customShareList param:{}", shareId);
        if (Objects.isNull(shareId)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "共享ID为空，请检查");
        }
        PageData<CustomShareList> pageData = shareListRepo.pageQuery(shareId, pageNum, pageSize);
        List<CustomShareList> shareLists = pageData.getList();
        if (CollectionUtils.isNotEmpty(shareLists)) {
            // 处理台账额外字段
            List<Integer> assetIds = shareLists.stream().map(CustomShareList::getAssetId).collect(Collectors.toList());
            List<AssetAccountRes> accountResList = accountProvider.queryListByIds(assetIds);
            Map<Integer, AssetAccountRes> assetAccountMap = accountResList.stream().collect(Collectors.toMap(AssetAccountRes::getId, Function.identity()));
            List<ExtraFieldsRes> allExtraFields = accountProvider.getAllExtraFields(null, CommonConstant.SHARE_LIST_FUN, assetIds);
            Map<Integer, ExtraFieldsRes> extraMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(allExtraFields)) {
                // 添加必要字段信息
                extraMap = allExtraFields.stream().collect(Collectors.toMap(ExtraFieldsRes::getAssetId, Function.identity()));
            }
            Map<Integer, ExtraFieldsRes> finalExtraMap = extraMap;
            shareLists.forEach(item -> {
                AssetAccountRes assetAccountRes = assetAccountMap.get(item.getAssetId());
                item.setAssetCode(assetAccountRes.getAssetCode());
                item.setAssetName(assetAccountRes.getAssetName());
                item.setUserCode(assetAccountRes.getUserCode());
                item.setUserName(assetAccountRes.getUserName());
                item.setAssetQuantity(assetAccountRes.getQuantity());
                item.setOriginValue(assetAccountRes.getOriginValue());
                item.setNetValue(assetAccountRes.getNetValue());

                ExtraFieldsRes extraFieldsRes = finalExtraMap.get(item.getAssetId());
                if (Objects.nonNull(extraFieldsRes)) {
                    item.setExtraFields(commonDataConverter.toExtraFieldInfoList(extraFieldsRes.getExtraFields()));
                }
            });
            log.info("pageQuery customShareList assetAccountRes result:{}", accountResList);
            return converter.toPageData(pageData, converter::shareListToResList);
        }
        return new PageData<>(null, pageNum, pageSize, 0);
    }

    public List<Integer> getAssetIds(Integer shareId) {
        if (Objects.isNull(shareId)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "共享ID为空，请检查");
        }
        List<CustomShareList> shareLists = shareListRepo.getByShareId(shareId);
        if (CollectionUtils.isNotEmpty(shareLists)) {
            return shareLists.stream().map(CustomShareList::getAssetId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 共享清单自定义查询
     *
     * @param req
     * @param shareScene
     * @return
     */
    public ListDataRes<Map<String, String>> customQueryShareAsset(BaseQueryReq req, String shareScene) {
        User user = userInfoService.getUserByUserName(AuthFacade.authedUserName());
        ShareQueryReq shareQueryReq = new ShareQueryReq();
        shareQueryReq.setBusinessLine(req.getBusinessLine());
        shareQueryReq.setUserCode(user.getUserName());
        shareQueryReq.setDeptCode(user.getDeptCode());
        shareQueryReq.setShareScene(shareScene);
        String shareClient = req.getEamSource();
        if (StringUtils.isBlank(shareClient)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "share client cannot be empty");
        }
        // 新增应用的客户端
        shareQueryReq.setShareClient(shareClient);
        shareQueryReq.setShareRecords(new ArrayList<>());
        shareQueryReq.setAllShareLists(new ArrayList<>());
        String whereSql = this.queryShareList(shareQueryReq);
        if (StringUtils.isNotBlank(whereSql)) {
            // 调用通用查询，不走权限的接口
            req.setBusinessLine(null);
            req.setFunId(CommonConstant.ASSET_ACCOUNT_FUN);
            req.setCustomSql(whereSql);
            req.setQueryConditionReqs(CollectionUtils.isEmpty(req.getQueryConditionReqs()) ? new ArrayList<>() : req.getQueryConditionReqs());
            BaseRes<ListDataRes<Map<String, String>>> baseRes = dataPermissionService.queryNoAuth(req);
            if (baseRes.getCode() != 0) {
                throw new ErrorCodeException(ErrorCodes.SERVICE_UNAVAILABLE, baseRes.getMessage());
            }
            ListDataRes<Map<String, String>> dataRes = baseRes.getData();
            List<Map<String, String>> data = dataRes.getList();
            this.handleShareQuantity(data, shareQueryReq.getAllShareLists());
            return dataRes;
        }
        ListDataRes<Map<String, String>> emptyRes = new ListDataRes<>();
        emptyRes.setList(new ArrayList<>());
        emptyRes.setTotal(0);
        return emptyRes;
    }

    // 查询共享资产后，将重复导入自定义清单的资产共享数量取最大值
    public void handleShareQuantity(List<Map<String, String>> data, List<CustomShareList> customShareLists) {
        // 循环查询结果，拼接共享资产数量
        Map<String, Integer> shareMap = new HashMap<>();
        for (CustomShareList shareList : customShareLists) {
            String assetId = Integer.toString(shareList.getAssetId());
            Integer shareQuantity = shareList.getShareQuantity();
            Integer quantity = shareMap.get(assetId);
            if (Objects.isNull(quantity)) {
                quantity = shareQuantity;
            }
            if (shareQuantity > quantity) {
                // 替换最大共享数量
                quantity = shareQuantity;
            }
            shareMap.put(assetId, quantity);
        }
        data.forEach(o -> {
            Integer shareQuantity = shareMap.get(Objects.toString(o.get(CommonConstant.ID)));
            if (Objects.isNull(shareQuantity)) {
                o.put(CommonConstant.SHARE_QUANTITY_CONSTANT, Objects.toString(o.get(CommonConstant.QUANTITY_CONSTANT)));
            } else {
                o.put(CommonConstant.SHARE_QUANTITY_CONSTANT, Integer.toString(shareQuantity));
            }
        });
    }

    /**
     * 共享清单批量导出前先获取查询where条件
     *
     * @return
     */
    public String getShareWhereSql(String businessLine, String shareClient) {
        User user = userInfoService.getUserByUserName(AuthFacade.authedUserName());
        ShareQueryReq shareQueryReq = new ShareQueryReq();
        shareQueryReq.setBusinessLine(Arrays.asList(businessLine.split(";")));
        shareQueryReq.setUserCode(user.getUserName());
        shareQueryReq.setDeptCode(user.getDeptCode());
        shareQueryReq.setShareClient(shareClient);
        String whereSql = this.queryShareList(shareQueryReq);
        // Base64编码加密
        if (StringUtils.isNotBlank(whereSql)) {
            whereSql = CommonUtils.encodeWhereSql(whereSql);
        }
        return whereSql;
    }

    /**
     * 批量移除
     *
     * @param ids
     */
    public void removeCustomShareList(List<Integer> ids) {
        shareListRepo.batchRemove(ids);
    }

    /**
     * 文件导入自定义共享资产
     *
     * @param req
     */
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importCustomShareList(CustomShareListImportReq req) {
        // 导入数据校验有效性
        List<CustomShareListImport> customShareListImports = ExcelUtils.readExcel(req.getFile().getInputStream(), CustomShareListImport.class, 2);
        log.info("importCustomShareList customShareListImports:{}", customShareListImports);
        if (CollectionUtils.isEmpty(customShareListImports)) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "导入数据为空");
        }
        ShareRecord shareRecord = shareRecordRepo.getById(req.getShareId());
        if (Objects.isNull(shareRecord)) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "参数有误，未找到共享记录");
        }
        List<CustomShareListExport> exportList = this.export(shareRecord.getBusinessLine());
        if (CollectionUtils.isEmpty(exportList)) {
            throw new ErrorCodeException(ErrorCodes.NOT_FOUND, "该用户没有可共享资产");
        }
        log.info("importCustomShareList commonQuery assetAccount size:{}", exportList.size());
        // 查询已导入的自定义共享清单
        List<CustomShareList> alreadyLists = shareListRepo.getByShareId(req.getShareId());
        List<Integer> assetIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(alreadyLists)) {
            assetIds = alreadyLists.stream().map(CustomShareList::getAssetId).collect(Collectors.toList());
        }
        Map<String, CustomShareListExport> assetCodeMap = exportList.stream().filter(o -> StringUtils.isNotBlank(o.getAssetCode()))
                .collect(Collectors.toMap(CustomShareListExport::getAssetCode, Function.identity()));
        Map<Integer, CustomShareListExport> assetIdMap = exportList.stream().collect(Collectors.toMap(CustomShareListExport::getId, Function.identity()));
        List<CustomShareList> shareLists = new ArrayList<>();
        for (CustomShareListImport importData : customShareListImports) {
            CustomShareListExport shareListExport = null;
            String assetCode = importData.getAssetCode();
            if (StringUtils.isNotBlank(assetCode)) {
                shareListExport = assetCodeMap.get(assetCode);
                if (Objects.isNull(shareListExport)) {
                    throw new ErrorCodeException(ErrorCodes.NOT_FOUND, MessageFormat.format("资产编号【{0}】不满足条件，无法导入", assetCode));
                }
                // 校验是否重复导入
                if (assetIds.contains(shareListExport.getId())) {
                    throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, MessageFormat.format("资产编号【{0}】重复导入", assetCode));
                }
            }
            String assetId = importData.getAssetId();
            // 填写资产编号的不处理资产台账ID条件
            if (StringUtils.isBlank(assetCode) && StringUtils.isNotBlank(assetId)) {
                try {
                    Integer id = Integer.parseInt(assetId);
                    shareListExport = assetIdMap.get(id);
                    if (Objects.isNull(shareListExport)) {
                        throw new ErrorCodeException(ErrorCodes.NOT_FOUND, MessageFormat.format("资产台账ID【{0}】不满足条件，无法导入", id));
                    }
                    // 校验是否重复导入
                    if (assetIds.contains(shareListExport.getId())) {
                        throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, MessageFormat.format("资产台账ID【{0}】重复导入", assetId));
                    }
                } catch (NumberFormatException e) {
                    throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, MessageFormat.format("资产台账ID【{0}】填写不合法", assetId));
                }
            }
            // 构造保存数据
            if (Objects.nonNull(shareListExport)) {
                CustomShareList shareList = new CustomShareList();
                shareList.setShareId(shareRecord.getId());
                shareList.setAssetId(shareListExport.getId());
                shareList.setAssetQuantity(shareListExport.getQuantity());
                shareList.setShareQuantity(1);
                shareLists.add(shareList);
            }
        }
        // 校验通过保存导入数据
        shareListRepo.batchSaveShareList(shareLists);
    }

    private List<CustomShareListExport> export(String businessLine) {
        List<CustomShareListExport> exportList = new ArrayList<>();
        int pageNum = 1;
        int total;
        int pageSize = 10000;
        // 构造通用查询条件
        BaseQueryReq queryReq = this.buildCommonQuery(businessLine);
        do {
            // 循环分页查询
            queryReq.setPageNum(pageNum);
            queryReq.setPageSize(pageSize);
            BaseRes<ListDataRes<Map<String, String>>> listDataResBaseRes = eamJxsService.baseQueryNeedAuth(queryReq);
            ListDataRes<Map<String, String>> resData = listDataResBaseRes.getData();
            List<CustomShareListExport> exports = GsonUtil.parseArray(GsonUtil.toJsonString(resData.getList()), CustomShareListExport.class);
            if (CollectionUtils.isNotEmpty(exports)) {
                exportList.addAll(exports);
            }
            total = resData.getTotal();
            pageNum++;
            log.info("importCustomShareList commonQuery assetAccount, pageNum:{}, total:{}", pageNum, total);
        } while (0 != total && (pageNum * pageSize) <= total);
        return exportList;
    }

    private BaseQueryReq buildCommonQuery(String businessLine) {
        BaseQueryReq queryReq = new BaseQueryReq();
        queryReq.setFunId(CommonConstant.ASSET_ACCOUNT_FUN);
        queryReq.setBusinessLine(Arrays.asList(businessLine));
        List<QueryConditionReq> conditionReqs = new ArrayList<>();
        // 验收状态：待验收、已到货以外
        QueryConditionReq checkStatus = new QueryConditionReq();
        checkStatus.setFieldCode("checkStatus");
        checkStatus.setQueryCond(QueryConditionEnum.NOT_IN.getCode());
        checkStatus.setFieldValues(Arrays.asList(CheckStatus.WAIT_ACCEPT.getCode(), CheckStatus.ARRIVAL.getCode()));
        conditionReqs.add(checkStatus);
        // 使用状态：已处置、处置中、异动中以外
        QueryConditionReq useStatus = new QueryConditionReq();
        useStatus.setFieldCode("useStatus");
        useStatus.setQueryCond(QueryConditionEnum.NOT_IN.getCode());
        useStatus.setFieldValues(Arrays.asList(AssetUseStatus.DISPOSED.getCode(), AssetUseStatus.DISPOSING.getCode(), AssetUseStatus.MOVING.getCode()));
        conditionReqs.add(useStatus);
        // 可用数量：≥1的正整数
        QueryConditionReq quantity = new QueryConditionReq();
        quantity.setFieldCode(CommonConstant.QUANTITY_CONSTANT);
        quantity.setQueryCond(QueryConditionEnum.GE.getCode());
        quantity.setFieldValues(Arrays.asList("1"));
        conditionReqs.add(quantity);
        queryReq.setQueryConditionReqs(conditionReqs);
        return queryReq;
    }

    /**
     * 保存共享记录
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer saveShareRecord(SaveShareRecordReq req) {
        // 更新前的资产
        String beforeJson = Strings.EMPTY;
        // 新增或更新共享记录
        ShareRecord shareRecord = converter.recordReqToDo(req.getBaseInfo());
        if (StringUtils.isBlank(shareRecord.getShareDeptCode()) && StringUtils.isBlank(shareRecord.getShareUserCode())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "部门范围或人员范围请填写一种");
        }
        shareRecord.setBusinessLine(req.getBusinessLine());
        if (Objects.nonNull(req.getId())) {
            shareRecord.setId(req.getId());
            ShareRecord beforeRecord = shareRecordRepo.getById(req.getId());
            beforeJson = this.getShareChangeJson(beforeRecord);
        } else {
            String recordNo = serialCodeProvider.genSerialCode(CommonConstant.SHARE_RECORD_NO_PREFIX.
                    concat(DateUtils.getNowDay().substring(0, 6)), 4);
            shareRecord.setRecordNo(recordNo);
            User user = AuthFacade.authedUserInfo();
            shareRecord.setCreateDeptCode(user.getDeptCode());
            shareRecord.setCreateDeptName(user.getFullDeptName());
        }
        shareRecord = shareRecordRepo.saveShareRecord(shareRecord);

        // 处理附件
        this.saveAttach(req.getAttachInfo(), shareRecord);

        Integer shareId = shareRecord.getId();
        // 保存更多筛选范围，先删除再保存
        shareRangeRepo.removeByShareId(shareId);
        List<ComplexRange> complexRanges = req.getComplexRanges();
        if (CollectionUtils.isNotEmpty(complexRanges)) {
            List<ShareRange> shareRanges = converter.rangeReqToDoList(complexRanges);
            shareRanges.forEach(range -> range.setShareId(shareId));
            shareRangeRepo.batchSaveShareRange(shareRanges);
        }

        // 保存自定义资产清单
        List<CustomShareListReq> customShareLists = req.getCustomShareLists();
        if (CollectionUtils.isNotEmpty(customShareLists)) {
            List<CustomShareList> shareLists = converter.shareListReqToDoList(customShareLists);
            shareLists.forEach(list -> list.setShareId(shareId));
            shareListRepo.batchSaveShareList(shareLists);
        }
        // 变更后的资产
        String afterJson = this.getShareChangeJson(shareRecord);
        // 写入变更日志
        ShareChangeLog changLog = new ShareChangeLog();
        changLog.setShareId(shareRecord.getId());
        changLog.setBeforeChange(beforeJson);
        changLog.setAfterChange(afterJson);
        shareChangeLogRepo.save(changLog);
        return shareId;
    }

    // 处理共享资产附件
    private void saveAttach(SaveAttach saveAttach, ShareRecord shareRecord) {
        List<AttachInfo> attachInfoList = new ArrayList<>();
        //删除项
        if (CollectionUtils.isNotEmpty(saveAttach.getDeleteItems())) {
            attachInfoRepo.deleteAttach(shareRecord.getId(), saveAttach.getDeleteItems(), CommonConstant.ASSET_SHARE_FUN);
        }
        //新增项
        if (CollectionUtils.isNotEmpty(saveAttach.getNewItems())) {
            saveAttach.getNewItems().forEach(i -> {
                AttachInfo attachInfo = new AttachInfo();
                attachInfo.setRecordId(shareRecord.getId());
                attachInfo.setRecordNo(shareRecord.getRecordNo());
                attachInfo.setRecordType(CommonConstant.ASSET_SHARE_FUN);
                attachInfo.setOriginName(i.getOriginFileName());
                attachInfo.setAttachName(i.getFileName());
                attachInfo.setAttachLink(i.getDownloadUrl());
                attachInfoList.add(attachInfo);
            });
        }
        if (CollectionUtils.isNotEmpty(attachInfoList)) {
            attachInfoRepo.batchSaveAttach(attachInfoList);
        }
    }

    private String getShareChangeJson(ShareRecord shareRecord) {
        if (Objects.isNull(shareRecord)) return Strings.EMPTY;
        // 新增或更新共享规则后，获取拼接条件SQL
        List<CustomShareList> allShareLists = new ArrayList<>();
        Map<Integer, Integer> shareQuantityMap = new HashMap<>();
        String whereSql = mergeAbility.buildShareWhereSql(shareRecord.getBusinessLine(), Arrays.asList(shareRecord), allShareLists);
        if (StringUtils.isBlank(whereSql)) return Strings.EMPTY;
        if (CollectionUtils.isNotEmpty(allShareLists)) {
            shareQuantityMap = allShareLists.stream().collect(Collectors.toMap(CustomShareList::getAssetId, CustomShareList::getShareQuantity));
        }
        // 通用查询获取共享资产
        int pageNum = 1;
        int total;
        int pageSize = 10000;
        BaseQueryReq queryReq = new BaseQueryReq();
        queryReq.setFunId(CommonConstant.ASSET_ACCOUNT_FUN);
        queryReq.setPageSize(pageSize);
        queryReq.setQueryConditionReqs(new ArrayList<>());
        queryReq.setCustomSql("and " + whereSql);
        List<ShareLogJson> jsonList = new ArrayList<>();
        do {
            queryReq.setPageNum(pageNum);
            BaseRes<ListDataRes<Map<String, String>>> baseRes = eamJxsService.baseQueryWithoutAuth(queryReq);
            ListDataRes<Map<String, String>> data = baseRes.getData();
            for (Map<String, String> map : data.getList()) {
                Integer assetId = Integer.parseInt(map.get(CommonConstant.ID));
                Integer quantity = Integer.parseInt(map.get(CommonConstant.QUANTITY_CONSTANT));
                Integer shareQuantity = shareQuantityMap.get(assetId);
                if (Objects.isNull(shareQuantity)) {
                    shareQuantity = quantity;
                }
                ShareLogJson jsonLog = new ShareLogJson();
                jsonLog.setAssetId(assetId);
                jsonLog.setShareQuantity(shareQuantity);
                jsonList.add(jsonLog);
            }
            total = data.getTotal();
            pageNum++;
            log.info("getShareChangeJson commonQuery assetAccount, pageNum:{}, total:{}", pageNum, total);
        } while (0 != total && (pageNum * pageSize) <= total);
        return GsonUtil.toJsonString(jsonList);
    }

    /**
     * 删除共享记录
     *
     * @param id
     */
    public void removeShareRecord(Integer id) {
        shareRecordRepo.removeShareRecord(id);
    }

    /**
     * 查询指定业务线操作场景下操作人所能查询到的所有共享资产
     *
     * @param req
     * @return
     */
    public String queryShareList(ShareQueryReq req) {
        // 查询所有父级部门
        List<String> orgCodes = assetOrgProvider.getParentCodes(req.getDeptCode());
        log.info("queryShareList req:{}", JacksonUtils.bean2Json(req));
        List<ShareRecord> shareRecords = shareRecordRepo.queryList(req.getBusinessLine(), req.getUserCode(), orgCodes, req.getShareScene(), req.getShareClient());
        log.info("queryShareList shareRecords：{}", JacksonUtils.bean2Json(shareRecords));
        StringBuilder sql = new StringBuilder();
        StringBuilder whereSql = new StringBuilder();
        if (CollectionUtils.isNotEmpty(shareRecords)) {
            req.setShareRecords(shareRecords);
            // 通过业务线进行拆分
            Map<String, List<ShareRecord>> shareMap = shareRecords.stream().collect(Collectors.groupingBy(ShareRecord::getBusinessLine));
            shareMap.forEach((k, v) -> {
                String businessSql = mergeAbility.buildShareWhereSql(k, v, req.getAllShareLists());
                whereSql.append(businessSql).append(" or ");
            });
            int length = whereSql.length();
            // 把最后一个or删除
            whereSql.delete(length - 4, length);
            if (whereSql.length() > 0) {
                sql.append("and (").append(whereSql).append(")");
            }
            log.info("queryShareList whereSql:{}", sql);
        }
        return sql.toString();
    }

    /**
     * 共享清单调拨点击通过后端查询资产信息（进行调拨场景授权校验）
     *
     * @param assetAllotReqs
     * @return
     */
    public List<ShareAssetAllotRes> getAssetAllotInfo(List<ShareAssetAllotReq> assetAllotReqs, String shareClient) {
        log.info("getAssetAllotInfo ShareAssetAllotReq：{}", JacksonUtils.bean2Json(assetAllotReqs));
        if (StringUtils.isBlank(shareClient)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "share client cannot be empty");
        }
        User user = userInfoService.getUserByUserName(AuthFacade.authedUserName());
        Map<Integer, Integer> shareMap = new HashMap<>();
        StringBuilder assetSql = new StringBuilder("and id in (");
        // 格式转换
        for (ShareAssetAllotReq req : assetAllotReqs) {
            Integer assetId = req.getAssetId();
            shareMap.put(assetId, req.getShareQuantity());
            assetSql.append(assetId).append(CommonConstant.COMMA);
        }
        assetSql.deleteCharAt(assetSql.length() - 1);
        assetSql.append(")");
        // 查询资产共享调拨场景资产， 不符合进行提示
        ShareQueryReq shareQueryReq = new ShareQueryReq();
        shareQueryReq.setUserCode(user.getUserName());
        shareQueryReq.setDeptCode(user.getDeptCode());
        shareQueryReq.setShareScene(AssetShareSceneEnum.ALLOT.getCode());
        shareQueryReq.setShareClient(shareClient);
        String whereSql = this.queryShareList(shareQueryReq);
        if (StringUtils.isBlank(whereSql)) {
            throw new ErrorCodeException(ErrorCodes.NOT_FOUND, "所选资产未授权调拨场景业务，不能进行调拨操作");
        }
        BaseQueryReq queryReq = new BaseQueryReq();
        queryReq.setFunId(CommonConstant.ASSET_ACCOUNT_FUN);
        queryReq.setPageNum(1);
        queryReq.setPageSize(assetAllotReqs.size());
        queryReq.setQueryConditionReqs(new ArrayList<>());
        queryReq.setCustomSql(whereSql.concat(assetSql.toString()));
        BaseRes<ListDataRes<Map<String, String>>> baseRes = eamJxsService.baseQueryWithoutAuth(queryReq);
        if (baseRes.getCode() != 0) {
            throw new ErrorCodeException(ErrorCodes.SERVICE_UNAVAILABLE, baseRes.getMessage());
        }
        List<ShareAssetAllotRes> res = this.buildShareAllotRes(baseRes.getData().getList(), shareMap);
        if (shareMap.size() > 0) {
            String message = MessageFormat.format("以下资产标识【{0}】未开通调拨场景共享", StringUtils.join(shareMap.keySet(), CommonConstant.COMMA));
            throw new ErrorCodeException(ErrorCodes.SERVICE_UNAVAILABLE, message);
        }
        log.info("getAssetAllotInfo ShareAssetAllotRes：{}", JacksonUtils.bean2Json(res));
        return res;
    }

    // 构造共享调拨资产返回对象
    private List<ShareAssetAllotRes> buildShareAllotRes(List<Map<String, String>> assetList, Map<Integer, Integer> shareMap) {
        List<ShareAssetAllotRes> res = new ArrayList<>();
        for (Map<String, String> assetMap : assetList) {
            Integer assetId = Integer.parseInt(assetMap.get(CommonConstant.ID));
            Integer shareQuantity = shareMap.get(assetId);
            Integer useQuantity = Integer.parseInt(assetMap.get(CommonConstant.QUANTITY_CONSTANT));
            if (useQuantity < shareQuantity) {
                shareQuantity = useQuantity;
            }
            ShareAssetAllotRes allotRes = new ShareAssetAllotRes();
            allotRes.setAssetId(assetId);
            allotRes.setAssetCode(assetMap.get("assetCode"));
            allotRes.setAssetName(assetMap.get("assetName"));
            allotRes.setUseStatus(AssetUseStatus.getByCode(assetMap.get("useStatus")).getDesc());
            allotRes.setBrand(assetMap.get("brand"));
            allotRes.setModel(assetMap.get("model"));
            allotRes.setAvailableQuantity(shareQuantity);
            allotRes.setCurrUseQuantity(shareQuantity);
            allotRes.setOriginValue(new BigDecimal(assetMap.get("originValue")));
            allotRes.setNetValue(new BigDecimal(assetMap.get("netValue")));
            allotRes.setUserName(assetMap.get("userCode"));
            allotRes.setUserDisplayName(assetMap.get("userName"));
            allotRes.setUseDeptCode(assetMap.get("useDeptCode"));
            allotRes.setUseDeptName(assetMap.get("useDeptName"));
            allotRes.setManageDeptCode(assetMap.get("manageDeptCode"));
            allotRes.setManageDeptName(assetMap.get("manageDeptName"));
            allotRes.setCompanyCode(assetMap.get("companyCode"));
            allotRes.setCompanyName(assetMap.get("companyName"));
            allotRes.setCenterCode(assetMap.get("centerCode"));
            allotRes.setLocationCode(assetMap.get("locationCode"));
            allotRes.setLocationName(assetMap.get("locationName"));
            allotRes.setAddress(assetMap.get("address"));
            allotRes.setSn(assetMap.get("sn"));
            res.add(allotRes);

            // 删除处理完的资产
            shareMap.remove(assetId);
        }
        return res;
    }
}
