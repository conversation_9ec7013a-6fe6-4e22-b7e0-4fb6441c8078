package com.mi.oa.asset.commons.config.app.scheduler;

import com.mi.oa.asset.commons.config.app.converter.AssetOrgConverter;
import com.mi.oa.asset.commons.config.domain.assetorganization.repository.AssetOrgRepo;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgStructure;
import com.mi.oa.asset.commons.config.infra.common.CacheKey;
import com.mi.oa.asset.eam.feign.mdm.MdmClient;
import com.mi.oa.asset.eam.feign.mdm.req.MdmBaseReq;
import com.mi.oa.asset.eam.feign.mdm.res.MdmBaseRes;
import com.mi.oa.asset.eam.feign.mdm.res.MdmDepartmentRes;
import com.mi.oa.infra.oaucf.redis.annotation.OACacheEvict;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/2/22 15:37
 */

@Slf4j
@Service
@PlanTask(name = "syncDepartment", description = "定时同步部门", quartzCron = "0 9 5,13 * * ?")
public class SyncDepartment implements PlanExecutor {

    @Resource
    private MdmClient mdmClient;

    @Resource
    private AssetOrgRepo assetOrgRepo;

    @Resource
    private AssetOrgConverter converter;

    @Lazy
    @Resource
    private SyncDepartment self;

    @Override
    public void execute() {
        int page = 1;
        int pageSize = 200;
        int total = 0;
        int done = 0;

        log.info("Start sync department from MDM");
        MdmBaseReq req = MdmBaseReq.builder().pageNum(page).pageSize(pageSize).build();
        do {
            req.setPageNum(page++);
            MdmBaseRes<MdmDepartmentRes> res = mdmClient.getDepartment(req);
            total = res.getTotal();
            List<MdmDepartmentRes> list = res.getList();
            done += list.size();

            // 无效的
            List<MdmDepartmentRes> inactive = list.stream().filter(i -> !i.isActive()).collect(Collectors.toList());
            log.info("Inactivate org structure counts: {}", inactive.size());
            assetOrgRepo.inactivateOrgStructure(inactive.stream().map(MdmDepartmentRes::getDeptCode).collect(Collectors.toList()));

            // 有效的
            List<MdmDepartmentRes> active = list.stream().filter(MdmDepartmentRes::isActive).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(active)) continue;

            List<String> deptCodes = active.stream().map(MdmDepartmentRes::getDeptCode).collect(Collectors.toList());
            List<AssetOrgStructure> exists = assetOrgRepo.getOrgStructuresByCodes(deptCodes);
            List<String> existCodes = exists.stream().map(AssetOrgStructure::getOrgCode).collect(Collectors.toList());

            // 新增不存在的
            List<MdmDepartmentRes> toAdd = active.stream().filter(i -> !existCodes.contains(i.getDeptCode())).collect(Collectors.toList());
            log.info("create org structure counts: {}", toAdd.size());
            assetOrgRepo.createOrgStructures(converter.createAssetOrgStructure(toAdd));

            // 更新存在的
            List<MdmDepartmentRes> toUpdate = active.stream().filter(i -> existCodes.contains(i.getDeptCode())).collect(Collectors.toList());
            List<AssetOrgStructure> updates = converter.updateAssetOrgStructure(toUpdate, exists);
            log.info("update org structure counts: {}", updates.size());
            assetOrgRepo.updateOrgStructures(updates);
        } while (0 != total && done < total);

        log.info("End sync department from MDM");

        self.cacheAssetOrgStructureByLevel();
    }

    public void cacheAssetOrgStructureByLevel() {
        int maxLevel = 0;

        for(int i = 1; i <= 10; i++) {
            self.evictLevelDataCache(i);

            log.info("Cache level {} org structure", i);
            List<AssetOrgStructure> data = assetOrgRepo.getOrgStructuresByLevel(i);
            if(CollectionUtils.isEmpty(data)) {
                log.info("No data for level {}, ending!", i);
                maxLevel = i - 1;
                break;
            }

            cacheAssetOrgStructureFullName(data);
            maxLevel = i;
        }

        RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_LEVEL_MAX.getKey(), maxLevel);
    }

    public void cacheAssetOrgStructureFullName(List<AssetOrgStructure> data) {
        log.info("Cache org structure full name");
        data.forEach(i -> {
            String fullName = i.getOrgName();
            if (i.getLevel() > 1) {
                String parentFullName = (String) RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey(i.getParentCode()));
                fullName = parentFullName + "-" + i.getOrgName();
            }
            RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey(i.getOrgCode()), fullName);
            
            // 缓存英文全称
            String fullNameEn = StringUtils.isNotBlank(i.getOrgNameEn()) ? i.getOrgNameEn() : i.getOrgName();
            if (i.getLevel() > 1) {
                String key = CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey(i.getParentCode());
                String parentFullNameEn = (String) RedisUtils.get(key);
                if (StringUtils.isNotBlank(parentFullNameEn)) {
                    fullNameEn = parentFullNameEn + "-" + fullNameEn;
                }
                log.info("Cache key {} org structure full name {}", key, fullNameEn);
            }
            RedisUtils.set(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey(i.getOrgCode()), fullNameEn);
            // 中文名称作为key
            String deptNameKey = CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_BY_NAME.genKey(fullName);
            RedisUtils.set(deptNameKey, fullNameEn);
            log.info("Cache full name key {} org structure en full name {}", fullName, fullNameEn);
        });
    }

    @OACacheEvict(cacheEnum = CacheKey.class, cacheEnumField = "ORGANIZATION_STRUCTURE_LEVEL", param = "level")
    public void evictLevelDataCache(Integer level) {
        log.info("Evict level {} org structure cache", level);
    }
}
