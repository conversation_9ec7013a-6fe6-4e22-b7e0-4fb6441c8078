package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.user.UserBaseInfoRes;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/31 16:31
 * @desciption: 工作台白名单能力层
 */

@Slf4j
@Service
@Data
@RefreshScope
@ConfigurationProperties(prefix = "workbench")
public class WhitelistAbility {

    private List<String> whitelist;  // 工作台白名单

    private List<String> deptlist;   // 工作台部门白名单

    private List<String> deptblacklist; // 工作台部门黑名单

    @Value("${workbench.enable}")
    private boolean enable;

    @Resource
    private UserInfoService userInfoService;

    public void getWorkbenchAuth(String userName){
        if(enable && !whitelist.contains(userName) && !containsDept(userName) || containsDeptBlackList(userName)){
            throw new ErrorCodeException(ErrorCodes.UNAUTHORIZED, "无权限，请联系系统管理员！");
        }
    }

    private boolean containsDept(String userName) {
        UserBaseInfoRes user = userInfoService.getUserInfoByUserName(userName);
        if (Objects.isNull(user)) return false;
        String fullDeptDescr = user.getFullDeptDescr();
        for (String dept : deptlist) {
            if (fullDeptDescr.contains(dept)) return true;
        }
        return false;
    }

    private boolean containsDeptBlackList(String userName) {
        UserBaseInfoRes user = userInfoService.getUserInfoByUserName(userName);
        if (Objects.isNull(user)) return true;
        String fullDeptDescr = user.getFullDeptDescr();
        for (String dept : deptblacklist) {
            if (fullDeptDescr.contains(dept)) return true;
        }
        return false;
    }
}
