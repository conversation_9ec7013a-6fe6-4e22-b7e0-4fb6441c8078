package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.function.*;
import com.mi.oa.asset.commons.config.domain.function.entity.Dict;
import com.mi.oa.asset.commons.config.domain.function.entity.FuncDict;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.DictPo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FuncDictPo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DictConverter extends CommonConverter {


    Dict toDo(DictReq source);

    List<Dict> toDoList(List<DictReq> source);

    DictRes toRes(Dict source);

    List<DictRes> toResList(List<Dict> source);

    DictRes poToRes(DictPo source);

    List<DictRes> poToResList(List<DictPo> source);

    DictRes funcPoToRes(FuncDictPo source);


    FuncDict toFuncDo(FuncDictReq source);

    List<FuncDict> toFuncDoList(List<FuncDictReq> source);

    FunctionDictRes toFuncRes(FuncDict source);

    List<FunctionDictRes> toFuncResList(List<FuncDict> source);

    FunctionDictRes poToFuncRes(FuncDictPo source);

    List<FunctionDictRes> poToFuncResList(List<FuncDictPo> source);

}
