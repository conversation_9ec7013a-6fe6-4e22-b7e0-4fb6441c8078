package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgProvider;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.CustomShareList;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRecord;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.CustomShareListRepo;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.ShareRecordRepo;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.mi.oa.asset.eam.auth.data.DataMergeService;
import com.mi.oa.asset.eam.jxs.req.BaseQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-12 09:38:29
 */
@Service
@Slf4j
public class AssetShareExportAbility implements DataMergeService {
    @Resource
    private AssetShareAbility assetShareAbility;

    @Resource
    private ShareRecordRepo shareRecordRepo;

    @Resource
    private CustomShareListRepo shareListRepo;

    @Resource
    private AssetOrgProvider assetOrgProvider;

    @Resource
    private UserInfoService userInfoService;

    /**
     * 共享清单导出时增加共享数量的信息
     *
     * @param data
     * @param queryReq
     */
    @Override
    public void handle(List<Map<String, String>> data, BaseQueryReq queryReq) {
        if (CollectionUtils.isEmpty(data)) return;
        if (CommonConstant.ASSET_SHARE_FUN.equals(queryReq.getBusinessType())) {
            log.info("AssetShareExportAbility exportHandle queryReq：{}", queryReq);
            // 查询共享数量信息
            String userName = queryReq.getUserName();
            if (StringUtils.isBlank(userName)) {
                userName = AuthFacade.authedUserName();
            }
            User user = userInfoService.getUserByUserName(userName);
            List<String> orgCodes = assetOrgProvider.getParentCodes(user.getDeptCode());

            log.info("AssetShareExportAbility export handle eamSource:{}", queryReq.getEamSource());
            List<ShareRecord> shareRecords = shareRecordRepo.queryList(null, user.getUserName(), orgCodes, "", queryReq.getEamSource());
            List<CustomShareList> customShareLists = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(shareRecords)) {
                List<Integer> shareIds = shareRecords.stream().map(ShareRecord::getId).collect(Collectors.toList());
                customShareLists = shareListRepo.getByShareIds(shareIds);
            }
            assetShareAbility.handleShareQuantity(data, customShareLists);
            // 特殊导出字段
            queryReq.getHeadMap().put(CommonConstant.SHARE_QUANTITY_CONSTANT, "共享数量");
        }
    }
}
