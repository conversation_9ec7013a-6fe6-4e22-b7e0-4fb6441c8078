package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.assetuseway.AssetUsewayProvider;
import com.mi.oa.asset.commons.config.api.assetuseway.AssetUsewayReasonRes;
import com.mi.oa.asset.commons.config.api.assetuseway.AssetUsewayReq;
import com.mi.oa.asset.commons.config.api.assetuseway.AssetUsewayRes;
import com.mi.oa.asset.commons.config.app.converter.AssetUsewayConverter;
import com.mi.oa.asset.commons.config.domain.assetuseway.entity.AssetUseway;
import com.mi.oa.asset.commons.config.domain.assetuseway.entity.AssetUsewayReason;
import com.mi.oa.asset.commons.config.domain.assetuseway.repository.AssetUsewayReasonRepo;
import com.mi.oa.asset.commons.config.domain.assetuseway.repository.AssetUsewayRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 办公用途 dubbo 接口实现
 *
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@DubboService
@Slf4j
public class AssetUsewayProviderImpl implements AssetUsewayProvider {
    @Resource
    private AssetUsewayConverter converter;
    @Resource
    private AssetUsewayRepo assetUsewayRepo;
    @Resource
    private AssetUsewayReasonRepo assetUsewayReasonRepo;

    @Override
    public List<AssetUsewayRes> list(AssetUsewayReq request) {
        List<AssetUseway> list = assetUsewayRepo.list(converter.toAssetUseway(request));
        return converter.toAssetUsewayResList(list);
    }

    @Override
    public List<AssetUsewayReasonRes> listByUsewayId(String usewayId) {
        List<AssetUsewayReason> list = assetUsewayReasonRepo.list(AssetUsewayReason.builder().usewayId(usewayId).build());
        return converter.toAssetUsewayReasonResList(list);
    }

    @Override
    public List<AssetUsewayReasonRes> all() {
        List<AssetUsewayReason> list = assetUsewayReasonRepo.list(AssetUsewayReason.builder().build());
        return converter.toAssetUsewayReasonResList(list);
    }

}

