package com.mi.oa.asset.commons.config.app.web.controller;


import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyProvider;
import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyReq;
import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyRes;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 货币配置表的 Controller
 */
@RestController
@HttpApiModule(value = "CountryCurrencyController", apiController = CountryCurrencyController.class)
@RequestMapping("/configs/countryCurrency")
@Slf4j
public class CountryCurrencyController {

    @Resource
    private CountryCurrencyProvider countryCurrencyProvider;

    @HttpApiDoc(apiName = "查询列表", value = "/configs/countryCurrency/list", method = MiApiRequestMethod.GET)
    @GetMapping("/list")
    public Result<List<CountryCurrencyRes>> list() {
        return Result.success(countryCurrencyProvider.getCountryCurrencyList());
    }

    @HttpApiDoc(apiName = "根据id查详情", value = "/configs/countryCurrency/detail", method = MiApiRequestMethod.GET)
    @GetMapping("/detail")
    public Result<CountryCurrencyRes> info(@RequestParam("countryCurrencyId") Integer countryCurrencyId) {
        return Result.success(countryCurrencyProvider.getById(countryCurrencyId));
    }

    @HttpApiDoc(apiName = "根据国家code查询", value = "/configs/countryCurrency/byCountryCode", method = MiApiRequestMethod.GET)
    @GetMapping("/byCountryCode")
    public Result<List<CountryCurrencyRes>> byCountryCode(@RequestParam("countryCode") String countryCode) {
        return Result.success(countryCurrencyProvider.getByCountryCode(countryCode));
    }

    @HttpApiDoc(apiName = "根据国家ID查详情", value = "/configs/countryCurrency/byCountryId", method = MiApiRequestMethod.GET)
    @GetMapping("/byCountryId")
    public Result<List<CountryCurrencyRes>> byCountryId(@RequestParam("countryId") Integer countryId) {
        return Result.success(countryCurrencyProvider.getByCountryId(countryId));
    }


    @HttpApiDoc(apiName = "更新", value = "/configs/countryCurrency/edit", method = MiApiRequestMethod.POST)
    @PostMapping("/edit")
    public Result<Integer> edit(@RequestBody @Valid CountryCurrencyReq req) {
        log.info("countryCurrency edit : {}", JacksonUtils.bean2Json(req));
        return Result.success(countryCurrencyProvider.saveOrUpdate(req));
    }

    @HttpApiDoc(apiName = "删除", value = "/configs/countryCurrency/delete", method = MiApiRequestMethod.POST)
    @PostMapping("/delete")
    public Result<Void> delete(@RequestBody CountryCurrencyReq req) {
        log.info("countryCurrency delete : {}", JacksonUtils.bean2Json(req));
        countryCurrencyProvider.removeByIds(Collections.singletonList(req.getId()));

        return Result.success();
    }
}
