package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineRes;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigReq;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigRes;
import com.mi.oa.asset.commons.config.app.converter.CountryBusinessLineConverter;
import com.mi.oa.asset.commons.config.app.converter.CountryConfigConverter;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryBusinessLineDo;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryBusinessLineRepo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryConfigRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/7
 */
@Service
@Slf4j
public class CountryConfigAbility {


    @Resource
    private CountryConfigConverter converter;

    @Resource
    private CountryConfigRepo countryConfigRepo;

    @Resource
    private CountryBusinessLineRepo countryBusinessLineRepo;

    @Resource
    private CountryBusinessLineConverter countryBusinessLineConverter;

    /**
     * 获取国家列表和对应的业务线
     * @param countryConfigResList  国家列表的请求参数实体类
     * @return
     */
    public List<CountryConfigRes>  getCountryConfigList(List<CountryConfigRes> countryConfigResList) {
        if (CollectionUtils.isEmpty(countryConfigResList)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "国家信息为空，请配置国家信息。");
        }
        List<Integer> idList = countryConfigResList.stream()
                .map(CountryConfigRes::getId)
                .collect(Collectors.toList());
        List<CountryBusinessLineDo> byCountryId = countryBusinessLineRepo.getByCountryIds(idList);
        List<CountryBusinessLineRes> countryBusinessLineResList = countryBusinessLineConverter.listDoToRes(byCountryId);

        countryConfigResList.forEach(countryConfigRes -> {
            List<String> businessLines = countryBusinessLineResList.stream()
                    .filter(businessLineRes -> businessLineRes.getCountryConfigId().equals(countryConfigRes.getId()))
                    .map(CountryBusinessLineRes::getBusinessLine)
                    .collect(Collectors.toList());
            countryConfigRes.setBusinessLineResList(businessLines);
        });
        return countryConfigResList;
    }

    /**
     * 根据国家id获取详情和业务线
     * @param countryConfigRes
     * @return
     */
    public CountryConfigRes getById(CountryConfigRes countryConfigRes) {
        List<CountryBusinessLineDo> byCountryId = countryBusinessLineRepo.getByCountryId(countryConfigRes.getId());
        List<String> bindBusinessLineList = byCountryId.stream()
                .map(CountryBusinessLineDo::getBusinessLine)
                .collect(Collectors.toList());
        countryConfigRes.setBusinessLineResList(bindBusinessLineList);
        return countryConfigRes;
    }

    /**
     * 新增或修改 国家数据 和 业务线关系数据
     * @param req 国家数据和绑定的业务线列表请求参数实体类
     */
    public void  saveOrUpdate(CountryConfigReq req) {
        List<String> businessLineResList = req.getBusinessLineResList();
        if ( CollectionUtils.isEmpty(businessLineResList)){
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请选择业务线。");
        }
        CountryConfigDo entity = converter.reqToDo(req);
        // 检查三位码是否重复
        check(entity);
        Integer id = entity.getId();
        if (Objects.isNull(id)) {
            CountryConfigDo byId = countryConfigRepo.getById(id);
            if (byId != null){
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "已存在相同数据，请勿重复添加。");
            }
            id = countryConfigRepo.save(entity);
            // 国家业务线表 复制国家数据，然后再绑定业务线
            saveCountryBusinessLineDo(entity,businessLineResList,id);
        } else {
            Integer updateCountry= countryConfigRepo.updateById(entity);
            // 根据国家id查询 已经绑定的业务线对象列表
            List<CountryBusinessLineDo> alreadyBoundList = countryBusinessLineRepo.getByCountryId(id);
            if (CollectionUtils.isNotEmpty(alreadyBoundList)){
                List<String> bindBusinessLineList = alreadyBoundList.stream()
                        .map(CountryBusinessLineDo::getBusinessLine)
                        .collect(Collectors.toList());
                // 已绑定业务线 和  入参要求业务线 不一致，说明要改业务线
                if (!CollectionUtils.isEqualCollection(bindBusinessLineList, businessLineResList)){
                    // 找出 入参要求业务线 中有而 已绑定业务线 中没有的元素（差集） 说明要新增
                    List<String> addDifferenceBusinessLineList = businessLineResList.stream()
                            .filter(item -> !bindBusinessLineList.contains(item))
                            .collect(Collectors.toList());
                    if (!addDifferenceBusinessLineList.isEmpty()){
                        saveCountryBusinessLineDo(entity,addDifferenceBusinessLineList,id);
                    }

                    // 找出 已绑定业务线 中有而 入参要求业务线 中没有的元素（差集） 说明要删除
                    List<String> delDifferenceBusinessLineList = bindBusinessLineList.stream()
                            .filter(item -> !businessLineResList.contains(item))
                            .collect(Collectors.toList());
                    if (!delDifferenceBusinessLineList.isEmpty()){
                        // 获取要删除的列表
                        List<CountryBusinessLineDo> result = alreadyBoundList.stream()
                                .filter(item -> delDifferenceBusinessLineList.contains(item.getBusinessLine()))
                                .collect(Collectors.toList());
                        List<Integer> collect = result.stream().map(CountryBusinessLineDo::getId).collect(Collectors.toList());
                        countryBusinessLineRepo.deleteByIds(collect);
                    }
                }
                //修改了国家信息，对应业务线关系表的国家信息也要修改
                if (updateCountry>0){
                    updateCountryBusinessLineDo(entity);
                }
            }
        }
    }

    /**
     * 校验国家三位码是否重复
     * @param entity
     */
    public void check(CountryConfigDo entity) {
        CountryConfigDo byCountryCodeAlphaThree = countryConfigRepo.getByCountryCodeAlphaThree(entity.getCountryCodeAlphaThree());
        boolean isNullCode = Objects.isNull(byCountryCodeAlphaThree);
        if (isNullCode){
            return;
        }
        Integer id = entity.getId();
        if (id != null && !Objects.equals(byCountryCodeAlphaThree.getId(), id)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "修改的国家三位码已存在。");
        } else if (id == null) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "国家三位码已存在。");
        }
    }
    /**
     * 批量保存新增
     * @param entity
     * @param businessLineResList
     * @param id
     */
    public void saveCountryBusinessLineDo(CountryConfigDo entity, List<String> businessLineResList,Integer id) {
        List<CountryBusinessLineDo> batchList = businessLineResList.stream()
                .map(businessLine -> {
                    CountryBusinessLineDo dto = converter.countryConfigDoToCountryBusinessLineDo(entity);
                    dto.setId(null);
                    dto.setCountryConfigId(id);
                    dto.setBusinessLine(businessLine);
                    return dto;
                })
                .collect(Collectors.toList());
        countryBusinessLineRepo.saveAll(batchList);
    }

    /**
     * 修改国家详细，关系表也随之对应修改
     * @param entity
     */
    public void updateCountryBusinessLineDo(CountryConfigDo entity) {
        // 要修改的数据
        CountryBusinessLineDo baseData = converter.countryConfigDoToCountryBusinessLineDo(entity);
        List<CountryBusinessLineDo> batchUpdateList = new ArrayList<>();
        // 被修改的数据
        List<CountryBusinessLineDo> byCountryId = countryBusinessLineRepo.getByCountryId(entity.getId());
        if (CollectionUtils.isNotEmpty(byCountryId)){
            for (CountryBusinessLineDo countryBusinessLineDo : byCountryId){
                CountryBusinessLineDo updateDo = countryBusinessLineConverter.clone(baseData); // 拷贝出新对象
                //主键和业务线 从查询的数据里面取
                updateDo.setId(countryBusinessLineDo.getId());
                updateDo.setBusinessLine(countryBusinessLineDo.getBusinessLine());
                // 国家id 从CountryConfig转换的里面取
                updateDo.setCountryConfigId(entity.getId());
                batchUpdateList.add(updateDo);
            }

            // 批量更新
            if (CollectionUtils.isNotEmpty(batchUpdateList )) {
                countryBusinessLineRepo.updateBatchById(batchUpdateList);
            }
        }

    }


    /**
     * 删除国家数据， 关联绑定的国际业务线数据随之删除
     * @param ids 国家表id，也是关联表的 countryConfigId
     */
    public void deleteByIds(List<Integer> ids) {
        countryConfigRepo.deleteByIds(ids);
        countryBusinessLineRepo.deleteByCountryIds(ids);
    }

}
