package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.assetcode.AssetCodeRuleReq;
import com.mi.oa.asset.commons.config.api.assetcode.AssetCodeRuleRes;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.commons.config.domain.systemvar.entity.SystemVar;
import com.mi.oa.asset.commons.config.domain.systemvar.repository.SystemVarRepo;
import com.mi.oa.asset.commons.config.domain.systemvar.valobj.FieldRuleConfig;
import com.mi.oa.asset.commons.config.infra.config.AssetCodeRuleConfig;
import com.mi.oa.asset.commons.config.infra.repository.converter.AssetCodeRuleConverter;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-06-28 17:21
 */
@Service
public class AssetCodeRuleAbility {

    @Resource
    private SystemVarRepo systemVarRepo;

    @Resource
    private AssetCodeRuleConfig ruleConfig;

    @Resource
    private AssetCodeRuleConverter ruleConverter;

    public void save(AssetCodeRuleReq assetCodeRuleReq) {
        // 对象转换
        BusinessLine businessLineEnum = BusinessLine.getByCode(assetCodeRuleReq.getBusinessLine());
        SystemVar systemVar = SystemVar.builder()
                .varCode(MessageFormat.format(CommonConstant.ASSET_CODE_RULE_VAR_CODE, businessLineEnum.getCode()))
                .varDesc(MessageFormat.format(CommonConstant.ASSET_CODE_RULE_VAR_DESC, businessLineEnum.getDesc()))
                .varValue(JacksonUtils.bean2Json(assetCodeRuleReq))
                .businessLine(businessLineEnum)
                .build();
        // 保存资产编码规则到系统变量表
        systemVarRepo.updateSystemVar(systemVar);
    }

    public AssetCodeRuleRes getByBusinessLine(String businessLine) {
        // 查询系统变量获取资产编码规则
        String varCode = MessageFormat.format(CommonConstant.ASSET_CODE_RULE_VAR_CODE, businessLine);
        SystemVar systemVar = systemVarRepo.getSystemVar(varCode, BusinessLine.getByCode(businessLine));
        AssetCodeRuleRes res = new AssetCodeRuleRes();
        if (systemVar == null) {
            // 空对象处理默认值
            res.setBusinessLine(businessLine);
            res.setIsInput(false);
            res.setIsSn(false);
        } else {
            // 不为空对象解析json
            res = JacksonUtils.json2Bean(systemVar.getVarValue(), AssetCodeRuleRes.class);
        }
        // 从nacos获取可选字段配置
        List<FieldRuleConfig> ruleList = ruleConfig.getCodeRuleList();
        List<FieldRuleConfig> optionalList = ruleList.stream().filter(vo -> {
            List<String> list = Arrays.asList(vo.getBusinessLine().split(CommonConstant.SEMICOLON));
            return list.contains(businessLine);
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(optionalList)) {
            // 没配置的业务线取默认模板
            optionalList = ruleConfig.getDefaultList();
        }
        res.setOptionalItemList(ruleConverter.voToRes(optionalList));
        return res;
    }
}
