package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyReq;
import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyRes;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryCurrencyDo;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CountryCurrencyConverter extends CommonConverter {

    CountryCurrencyRes doToRes(CountryCurrencyDo countryCurrencyDo);

    List<CountryCurrencyRes> listDoToRes(List<CountryCurrencyDo> countryCurrencyDoList);

    CountryCurrencyDo reqToDo(CountryCurrencyReq countryCurrencyReq);

    List<CountryCurrencyDo> listReqToDo(List<CountryCurrencyReq> countryCurrencyReqList);

}
