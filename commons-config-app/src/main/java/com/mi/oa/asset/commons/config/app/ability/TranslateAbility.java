package com.mi.oa.asset.commons.config.app.ability;

import com.alibaba.druid.support.json.JSONUtils;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.oa.asset.commons.config.app.util.TranslateUtil;
import com.mi.oa.asset.commons.config.domain.translate.NeptuneConfig;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.mi.xms.sdk.X5Client;
import com.mi.xms.sdk.dto.VersionInfoDTO;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TranslateAbility {

    @javax.annotation.Resource
    private ResourceLoader resourceLoader;

    @javax.annotation.Resource
    private NeptuneConfig neptuneConfig;

    /**
     * NEPTUNE平台翻译
     */
    private static final String NEPTUNE_TRANS = "NEPTUNE_TRANS";

    /**
     * 将Neptune文件下的词条转成JSON
     * @param propertiesName
     * @return
     * @throws IOException
     */
    public String getJsonByProperties(String propertiesName)  {
        Resource resource = resourceLoader.getResource("classpath:neptune/" + propertiesName+".properties");
        Properties props = new Properties();
        try (InputStream inputStream = resource.getInputStream()) {
            props.load(inputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return JSONUtils.toJSONString(props);
    }

    /**
     * 获得hash 是否一致
     * 不一致说明有更新，获取最新的数据。
     *
     * @param isSg 默认false，非海外
     * @return
     */
    public String getData(boolean isSg)  {
        String json = "";
        try {
            List<VersionInfoDTO> versionInfoDTOS = new X5Client().checkUpdate(neptuneConfig.getAppId(),neptuneConfig.getAppKey(),
                    neptuneConfig.isQueryProdVersion(),isSg,null);
            for (VersionInfoDTO versionInfoDTO : versionInfoDTOS) {
                Map<String, Map<String, String>> langTranslationMap =
                        TranslateUtil.loadData(versionInfoDTO.getUrl(), versionInfoDTO.getHash());
                TranslateUtil.reload(langTranslationMap);
            }
            ObjectMapper mapper = new ObjectMapper();
//                mapper.configure(JsonGenerator.Feature.ESCAPE_NON_ASCII, false);  // 关闭非 ASCII 字符转义:ml-citation{ref="2" data="citationList"}
            json = mapper.writeValueAsString(TranslateUtil.data);
        } catch (Exception e) {
            throw new ErrorCodeException(ErrorCodes.FORBIDDEN, "Get Translate Datas Fail ");
        }
        return json;
    }

    /**
     * 拉取翻译平台数据存入缓存
     * @param isSg
     * @return
     */
    public void pullDataToRedis(boolean isSg)  {
        try {
            List<VersionInfoDTO> versionInfoDTOS = new X5Client().checkUpdate(neptuneConfig.getAppId(),neptuneConfig.getAppKey(),
                    neptuneConfig.isQueryProdVersion(),isSg,null);
            for (VersionInfoDTO versionInfoDTO : versionInfoDTOS) {
                Map<String, Map<String, String>> langTranslationMap =
                        TranslateUtil.loadData(versionInfoDTO.getUrl(), versionInfoDTO.getHash());
                TranslateUtil.reload(langTranslationMap);
            }
            Map<String, String> keyMap = TranslateUtil.data.get("key");
            Map<String, String> cnMap = TranslateUtil.data.get("zh-CN");
            Map<String, String> enMap = TranslateUtil.data.get("en-US");
            Map<String, String> entryMap = new HashMap<>();
            for (String key : keyMap.keySet()){
                RedisUtils.setEx("TRANS" + cnMap.get(key), enMap.get(key), 24, TimeUnit.HOURS);
                entryMap.put(cnMap.get(key),enMap.get(key));
            }
            RedisUtils.delete(NEPTUNE_TRANS);
            RedisUtils.hPutAll(NEPTUNE_TRANS,entryMap);
            RedisUtils.expire(NEPTUNE_TRANS,24L,TimeUnit.HOURS);
            log.info("pullDataToRedis done");
        } catch (Exception e) {
            throw new ErrorCodeException(ErrorCodes.FORBIDDEN, "pull data to redis fail ");
        }
    }


}
