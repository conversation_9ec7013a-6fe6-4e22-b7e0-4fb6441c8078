package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.MaterialType;
import com.mi.oa.asset.commons.config.api.assetsku.AssetSkuMgReq;
import com.mi.oa.asset.commons.config.api.assetsku.AssetSkuMgRes;
import com.mi.oa.asset.commons.config.api.assetsku.ManageModel;
import com.mi.oa.asset.commons.config.api.assetsku.SnTypeEnum;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSkuManage;
import com.mi.oa.asset.commons.config.domain.assetsku.enums.StockCostEnum;
import com.mi.oa.asset.commons.config.domain.assetsku.valobj.AssetSkuUpdateData;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.mapstruct.*;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Mapper(componentModel = "spring",imports = {ManageModel.class, SnTypeEnum.class, StockCostEnum.class, MaterialType.class, BusinessLine.class})
public interface AssetSkuMgConverter extends CommonConverter {
    AssetSkuManage toAssetSkuMg(AssetSkuMgReq source);
    @Mappings({
            @Mapping(target = "mgModel", expression = "java(source.getMgModel()==null?null:source.getMgModel().getCode())"),
            @Mapping(target = "mgModelName", expression = "java(source.getMgModel()==null?null:source.getMgModel().getDesc())"),
            @Mapping(target = "serialMg", expression = "java(source.getSerialMg()==null?null:source.getSerialMg().getCode())"),
            @Mapping(target = "serialMgName", expression = "java(source.getSerialMg()==null?null:source.getSerialMg().getDesc())"),
            @Mapping(target = "materialType", expression = "java(source.getMaterialType()==null?null:source.getMaterialType().getCode())"),
            @Mapping(target = "materialTypeName", expression = "java(source.getMaterialType()==null?null:source.getMaterialType().getDesc())"),
            @Mapping(target = "costing", expression = "java(source.getCosting()==null?null:source.getCosting().getCode())"),
            @Mapping(target = "costingName", expression = "java(source.getCosting()==null?null:source.getCosting().getDesc())"),
            @Mapping(target = "businessLine", expression = "java(source.getBusinessLine()==null?null:source.getBusinessLine().getCode())"),
            @Mapping(target = "businessLineName", expression = "java(source.getBusinessLine()==null?null:source.getBusinessLine().getDesc())"),
    })
    AssetSkuMgRes toAssetSkuMgRes(AssetSkuManage source);

    List<AssetSkuMgRes> toAssetSkuMgResList(List<AssetSkuManage> source);
    default List<AssetSkuManage> toAssetSkuMgList(List<AssetSkuManage> source, Integer skuId) {
        if (CollectionUtils.isEmpty(source)) {
            return null;
        }
        source.forEach(i ->i.setSkuId(skuId));
        return source;
    }

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
    void updateAssetSkuManage(AssetSkuUpdateData source, @MappingTarget AssetSkuManage target);

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
    AssetSkuManage updateAssetSkuManage(AssetSkuUpdateData source);
}
