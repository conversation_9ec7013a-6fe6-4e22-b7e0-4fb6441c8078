package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.function.TranslateColumnProvider;
import com.mi.oa.asset.commons.config.app.ability.TranslateColumnAbility;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@Service
@Slf4j
public class TranslateColumnProviderImpl implements TranslateColumnProvider {
    private final TranslateColumnAbility translateColumnAbility;

    public TranslateColumnProviderImpl(TranslateColumnAbility translateColumnAbility) {
        this.translateColumnAbility = translateColumnAbility;
    }

    @Override
    public void translateColumn(MultipartFile file) {
        try {
            translateColumnAbility.translateColumn(file);
        } catch (IOException e) {
            log.error("translateColumn is error", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void translateDict(MultipartFile file) {
        try {
            translateColumnAbility.translateDict(file);
        } catch (IOException e) {
            log.error("translateDict is error", e);
            throw new RuntimeException(e);
        }
    }
}
