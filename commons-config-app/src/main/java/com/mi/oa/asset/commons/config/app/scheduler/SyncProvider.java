package com.mi.oa.asset.commons.config.app.scheduler;

import com.mi.oa.asset.commons.config.domain.common.repository.ProviderRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.Provider;
import com.mi.oa.asset.eam.feign.mdm.MdmClient;
import com.mi.oa.asset.eam.feign.mdm.req.MdmBaseReq;
import com.mi.oa.asset.eam.feign.mdm.res.MdmBaseRes;
import com.mi.oa.asset.eam.feign.mdm.res.MdmSupplierRes;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/2/22 15:36
 */
@Slf4j
@Service
@PlanTask(name = "SyncProvider", quartzCron = "0 20 4 * * ?", description = "定时同步供应商数据，每天 4:20 同步一次")
public class SyncProvider implements PlanExecutor {

    @Resource
    private MdmClient mdmClient;

    @Resource
    private ProviderRepo providerRepo;

    @Override
    public void execute() {
        log.info("Sync supplier data, start at: {}", DateTime.now());

        int pageNum = 1;
        int pageSize = 200;
        int total = 0;
        int done = 0;

        do {
            MdmBaseReq req = MdmBaseReq.builder().pageNum(pageNum++).pageSize(pageSize).build();
            MdmBaseRes<MdmSupplierRes> res = mdmClient.getSupplier(req);
            total = res.getTotal();
            List<MdmSupplierRes> list = res.getList();
            done += list.size();

            List<Provider> data = new ArrayList<>();
            list.forEach(i -> {
                // 线上环境存在编码为空的数据，导致定时任务执行异常
                if (StringUtils.isNotEmpty(i.getSupplierCode())) {
                    data.add(Provider.builder()
                            .providerCode(i.getSupplierCode())
                            .providerName(i.getSupplierName())
                            .disabled(0)
                            .build());
                }
            });

            providerRepo.batchSaveProvider(data);
        } while (0 != total && done < total);

        log.info("Sync supplier data, end at: {}", DateTime.now());
    }
}
