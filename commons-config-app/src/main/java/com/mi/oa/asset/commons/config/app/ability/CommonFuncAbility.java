package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.common.enums.YesNo;
import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.address.AssetReceiveAddressProvider;
import com.mi.oa.asset.commons.config.api.common.DelByIdsReq;
import com.mi.oa.asset.commons.config.api.myfunctions.*;
import com.mi.oa.asset.commons.config.api.user.UserBaseInfoRes;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.domain.common.entity.*;
import com.mi.oa.asset.commons.config.domain.common.repository.*;
import com.mi.oa.asset.commons.config.domain.commonfunc.entity.MyCommonFunc;
import com.mi.oa.asset.commons.config.domain.commonfunc.repository.MyCommonFuncRepo;
import com.mi.oa.asset.commons.config.infra.repository.converter.FunctionConfigConverter;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/13 10:38
 * @description
 */
@Service
@Slf4j
public class CommonFuncAbility {
    @Resource
    private MyCommonFuncRepo commonFuncRepo;

    @Resource
    private FunctionConfigRepo configRepo;

    @Resource
    private FunctionConfigItemRepo configItemRepo;

    @Resource
    private FunctionConfigDeptRepo configDeptRepo;

    @Resource
    private FunctionConfigUserRepo configUserRepo;

    @Resource
    private FunctionConfigConverter converter;

    @Resource
    private CommonFuncAbility selfAbility;

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private AssetReceiveAddressProvider addressProvider;

    @Resource
    private BusinessLineRepo businessLineRepo;

    @Resource
    private ManageLineRepo manageLineRepo;

    public void saveMyFunctionsSort(MyFunctionsSortReq functionsSortReq) {
        User user = AuthFacade.authedUserInfo();
        String resourceCodesStr = String.join(",", functionsSortReq.getResourceCodes());
        MyCommonFunc commonFunc = MyCommonFunc.builder().resourceCodes(resourceCodesStr).createUserName(user.getDisplayName())
                .manageLineCode(functionsSortReq.getManageLineCode()).createUser(user.getUserName()).build();
        commonFuncRepo.saveMyFuncSort(commonFunc);
    }


    public MyFunctionsSortResp listMyFuncSort(String manageLineCode) {
        User user = AuthFacade.authedUserInfo();
        MyCommonFunc commonFunc = commonFuncRepo.listMyFuncSort(manageLineCode, user.getUserName());
        if (commonFunc == null) return null;
        List<String> resourceCodes = Arrays.asList(commonFunc.getResourceCodes().split(","));
        return MyFunctionsSortResp.builder().manageLineCode(manageLineCode).resourceCode(resourceCodes)
                .isDefault(1 == commonFunc.getIsDefault()).build();
    }


    public void submit(SaveFunctionConfigReq req) {
        validateConfigReq(req);
        selfAbility.save(req);
    }

    @Transactional(rollbackFor = Exception.class)
    public void save(SaveFunctionConfigReq req) {
        FunctionConfig functionConfig = converter.reqToConfig(req);
        // 保存
        int configId = configRepo.saveFunctionConfig(functionConfig);
        functionConfig.setConfigId(configId);

        List<FunctionConfigItem> items = functionConfig.getItems();
        items.forEach(item -> {
            item.setItemId(null);
            item.setConfigId(configId);
            List<FunctionConfigDept> authDeptList = item.getAuthDeptList();
            authDeptList.forEach(dept -> {
                dept.setConfigItemId(null);
                dept.setConfigId(configId);
            });
        });

        configItemRepo.deleteByConfigId(configId);
        List<FunctionConfigItem> functionConfigItems = configItemRepo.saveConfigItems(items);

        // 需要保存的授权部门信息
        List<FunctionConfigDept> configDeptList = functionConfigItems.stream().filter(item -> YesNo.YES.getCode().equals(item.getAuthType()))
                .map(FunctionConfigItem::getAuthDeptList).flatMap(Collection::stream)
                .collect(Collectors.toList());
        configDeptRepo.deleteAuthDeptByConfigId(configId);
        configDeptRepo.saveAuthDept(configDeptList);
    }

    private void validateConfigReq(SaveFunctionConfigReq req) {
        if (req == null) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "参数不能为空");
        }
        // 校验业务线配置
        validateBusinessLines(req.getItems());
        validateConfigId(req);
    }

    private void validateBusinessLines(List<SaveFunctionConfigItemReq> configItemReqs) {
        if (CollectionUtils.isEmpty(configItemReqs)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线不能为空");
        }
        Set<String> businessLineSet = new HashSet<>(configItemReqs.size());
        for (SaveFunctionConfigItemReq itemReq : configItemReqs) {
            if (StringUtils.isBlank(itemReq.getBusinessLine())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线编码不能为空");
            }
            if (businessLineSet.contains(itemReq.getBusinessLine())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线编码不能重复");
            }
            businessLineSet.add(itemReq.getBusinessLine());
            List<Country> countries = itemReq.getCountries();
            // 校验国家信息
            validateCountries(countries);
            // 校验授权范围
            validateAuthDeptList(itemReq.getAuthType(), itemReq.getAuthDeptList());
        }
    }

    private void validateCountries(List<Country> countries) {
        if (CollectionUtils.isEmpty(countries)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "国家或地区不能为空");
        }
        Set<String> countrySet = new HashSet<>(countries.size());
        for (Country country : countries) {
            if (StringUtils.isBlank(country.getCountry())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "国家或地区编码不能为空");
            }
            if (StringUtils.isBlank(country.getCountryName())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "国家或地区名称不能为空");
            }
            if (countrySet.contains(country.getCountry())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "国家或地区编码不能重复");
            }
            countrySet.add(country.getCountry());
        }
    }

    private void validateAuthDeptList(Integer authType, List<SaveFunctionConfigDeptReq> authDeptList) {
        if (YesNo.YES.getCode().equals(authType) && CollectionUtils.isNotEmpty(authDeptList)) {
            for (SaveFunctionConfigDeptReq deptReq : authDeptList) {
                if (StringUtils.isBlank(deptReq.getDeptCode())) {
                    throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "授权部门编码不能为空");
                }
                if (StringUtils.isBlank(deptReq.getDeptName())) {
                    throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "授权部门名称不能为空");
                }
            }
        }
    }

    private void validateConfigId(SaveFunctionConfigReq req) {
        FunctionConfig config = configRepo.getConfig(req.getManageLine(), req.getName(), null);
        if (config != null) {
            Integer configId = req.getConfigId();
            if (!Objects.equals(configId, config.getConfigId())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "该管理线下已存在" + req.getName() + "功能配置，不能重复添加");
            }
            String nameEn = req.getNameEn();
            if (StringUtils.isNotBlank(nameEn)) {
                FunctionConfig configEn = configRepo.getConfig(req.getManageLine(), null, nameEn);
                if (configEn != null && !Objects.equals(configId, configEn.getConfigId())) {
                    throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "该管理线下已存在" + req.getNameEn() + "功能配置，不能重复添加");
                }
            }
        }
    }


    public List<FunctionConfigRes> functionConfig(String userName, String country, String eamLanguage) {
        if (StringUtils.isBlank(userName)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "login status was abnormal");
        }
        // 先查询有国家权限的数据
        List<FunctionConfigItem> configItems = configItemRepo.getByCountry(country);

        List<Integer> itemConfigIds = new ArrayList<>(configItems.size());
        List<Integer> deptConfigIds = new ArrayList<>(configItems.size());

        configItems.forEach(configItem -> {
            Integer configId = configItem.getConfigId();
            // 需要部门授权的
            if (YesNo.YES.getCode().equals(configItem.getAuthType())) {
                deptConfigIds.add(configId);
            }
            itemConfigIds.add(configId);
        });

        Map<Integer, List<FunctionConfigItem>> configMap = configItems.stream().collect(Collectors.groupingBy(FunctionConfigItem::getConfigId));

        // 查询有国家权限的功能配置
        List<FunctionConfig> functionConfigs = configRepo.listByIds(itemConfigIds);
        List<Integer> authConfigIds = new ArrayList<>(itemConfigIds.size());
        Map<Integer, List<FunctionConfigDept>> deptMap = new HashMap<>(itemConfigIds.size());
        if (CollectionUtils.isNotEmpty(deptConfigIds)) {
            List<String> allDeptCode;
            // 先查询员工部门信息
            try {
                UserBaseInfoRes employee = userInfoService.getUserInfoByUserName(userName);
                allDeptCode = employee.getAllDeptCode();
                if (CollectionUtils.isNotEmpty(allDeptCode)) {
                    // 查询有国家权限也有部门权限的
                    List<FunctionConfigDept> configDeptList = configDeptRepo.listByDeptCodeAndId(allDeptCode, deptConfigIds);
                    authConfigIds = configDeptList.stream().map(FunctionConfigDept::getConfigId).collect(Collectors.toList());
                    deptMap = configDeptList.stream().collect(Collectors.groupingBy(FunctionConfigDept::getConfigItemId));
                }
            }
            catch (Exception e) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "queryDeptError");
            }
        }
        // 用户个性顺序配置
        List<FunctionConfigUser> configUsers = configUserRepo.listByUserCode(userName);
        return converterConfigRes(functionConfigs, configMap, deptMap, authConfigIds, configUsers, eamLanguage);
    }

    public List<FunctionConfigRes> converterConfigRes(List<FunctionConfig> functionConfigs, Map<Integer, List<FunctionConfigItem>> configMap,
                                                       Map<Integer, List<FunctionConfigDept>> deptMap, List<Integer> authConfigIds,
                                                       List<FunctionConfigUser> configUsers, String eamLanguage) {
        List<FunctionConfigRes> configResList = new ArrayList<>(functionConfigs.size());

        if (CollectionUtils.isNotEmpty(configUsers)) {
            // 按照 configUsers 里的 manageLine 的 sort 对 functionConfigs 进行排序
            functionConfigs.sort(Comparator.comparingInt(config -> {
                // 获取 manageLine 的 sort 值
                return configUsers.stream()
                        .filter(user -> user.getManageLine().equals(config.getManageLine()))
                        .findFirst()
                        .map(FunctionConfigUser::getSort)
                        .orElse(Integer.MAX_VALUE); // 如果没有找到对应的 manageLine，使用最大值进行排序
            }));
        }

        List<ManageLineDo> manageLineList = manageLineRepo.getManageLine();
        Map<String, ManageLineDo> manageLineMap = manageLineList.stream().collect(Collectors.toMap(ManageLineDo::getManageLine, Function.identity()));
        List<BusinessLineDo> businessLineDos = businessLineRepo.searchAll();
        Map<String, BusinessLineDo> businessLineMap = businessLineDos.stream().collect(Collectors.toMap(BusinessLineDo::getBusinessLine, Function.identity()));

        boolean isChinese = EAMConstants.CHINESE.equals(eamLanguage);
        for (FunctionConfig config : functionConfigs) {
            FunctionConfigRes functionConfigRes = converter.toConfigRes(config);
            String manageLine = config.getManageLine();
            String manageLineName = isChinese ?  config.getManageLineName(): manageLineMap.getOrDefault(manageLine, new ManageLineDo()).getManageLineNameEn();
            functionConfigRes.setManageLineName(manageLineName);
            List<FunctionConfigItem> functionConfigItems = configMap.get(config.getConfigId());
            List<FunctionConfigItemRes> itemResList = new ArrayList<>(functionConfigItems.size());
            for (FunctionConfigItem item : functionConfigItems) {
                // 有部门授权
                if (YesNo.YES.getCode().equals(item.getAuthType()) && authConfigIds.contains(config.getConfigId())) {
                    List<FunctionConfigDept> configDeptList = deptMap.get(item.getItemId());
                    if (CollectionUtils.isNotEmpty(configDeptList)) {
                        buildBusinessLineItem(item, isChinese, businessLineMap, itemResList);
                    }
                } else if (YesNo.NO.getCode().equals(item.getAuthType())) {
                    // 全员开放
                    buildBusinessLineItem(item, isChinese, businessLineMap, itemResList);
                }
            }
            if (CollectionUtils.isNotEmpty(itemResList)) {
                functionConfigRes.setItems(itemResList);
                configResList.add(functionConfigRes);
            }
        }

        return configResList;
    }

    public void buildBusinessLineItem(FunctionConfigItem item, boolean isChinese, Map<String, BusinessLineDo> businessLineMap, List<FunctionConfigItemRes> itemResList) {
        String businessLineName = isChinese ? item.getBusinessLineName() : businessLineMap.getOrDefault(item.getBusinessLine(), new BusinessLineDo()).getBusinessLineNameEn();
        itemResList.add(new FunctionConfigItemRes(item.getBusinessLine(), businessLineName));
    }

    public void personalConfig(List<SaveFunctionConfigUserReq> configUserReqs) {
        User user = AuthFacade.authedUserInfo();
        if (user == null) {
            throw new ErrorCodeException(ErrorCodes.ACCESS_TOKEN_EXPIRED);
        }
        validateConfigUser(configUserReqs);

        // 已经存在的配置
        List<FunctionConfigUser> existsConfigs = configUserRepo.listByUserCode(user.getUserName());
        Map<String, FunctionConfigUser> configUserMap = existsConfigs.stream().collect(Collectors.toMap(FunctionConfigUser::getManageLine, Function.identity()));

        List<FunctionConfigUser> functionConfigUsers = new ArrayList<>(configUserReqs.size());
        for (SaveFunctionConfigUserReq configUserReq : configUserReqs) {
            String manageLine = configUserReq.getManageLine();
            FunctionConfigUser existConfigUser = configUserMap.get(manageLine);
            // 已存在，更新
            if (existConfigUser != null) {
                existConfigUser = converter.updateConfigUser(configUserReq, user, existConfigUser);
            } else {
                existConfigUser = converter.reqToConfigUser(configUserReq, user);
            }
            functionConfigUsers.add(existConfigUser);
        }
        configUserRepo.saveConfigUser(functionConfigUsers);
    }

    private void validateConfigUser(List<SaveFunctionConfigUserReq> configUserReqs) {
        if (CollectionUtils.isEmpty(configUserReqs)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "param is null");
        }
        Set<String> manageLineSet = new HashSet<>(configUserReqs.size());
        for (SaveFunctionConfigUserReq req : configUserReqs) {
            if (StringUtils.isBlank(req.getManageLine())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "manage line is null");
            }
            if (manageLineSet.contains(req.getManageLine())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "manage line can't be repeated");
            }
            manageLineSet.add(req.getManageLine());
        }
    }

    public List<FunctionConfigRes> functionConfigByManageLine(String manageLine) {
        if (StringUtils.isBlank(manageLine)) {
            return Collections.emptyList();
        }
        List<FunctionConfig> functionConfigs = configRepo.listByManageLine(manageLine);
        List<Integer> configIds = functionConfigs.stream().map(FunctionConfig::getConfigId).collect(Collectors.toList());

        List<FunctionConfigItem> functionConfigItems = configItemRepo.listByConfigIds(configIds);
        Map<Integer, List<FunctionConfigItem>> configItemMap = functionConfigItems.stream().collect(Collectors.groupingBy(FunctionConfigItem::getConfigId));

        List<FunctionConfigDept> configDeptList = configDeptRepo.listByDeptCodeAndId(null, configIds);
        Map<Integer, List<FunctionConfigDept>> configDeptMap = configDeptList.stream().collect(Collectors.groupingBy(FunctionConfigDept::getConfigItemId));

        List<Country> countries = addressProvider.getCountryV1();
        Map<String, String> countryMap = countries.stream().collect(Collectors.toMap(Country::getCountry, Country::getCountryName));

        return getFunctionConfigRes(functionConfigs, configItemMap, configDeptMap, countryMap);
    }

    private List<FunctionConfigRes> getFunctionConfigRes(List<FunctionConfig> functionConfigs, Map<Integer, List<FunctionConfigItem>> configItemMap,
                                                         Map<Integer, List<FunctionConfigDept>> configDeptMap, Map<String, String> countryMap) {
        List<FunctionConfigRes> configResList = new ArrayList<>(functionConfigs.size());
        for (FunctionConfig functionConfig : functionConfigs) {
            FunctionConfigRes configRes = converter.toConfigRes(functionConfig);
            List<FunctionConfigItem> items = configItemMap.get(functionConfig.getConfigId());
            List<FunctionConfigItemRes> itemResList = new ArrayList<>(items.size());
            for (FunctionConfigItem item : items) {
                FunctionConfigItemRes itemRes = converter.toItemRes(item);
                Integer authType = item.getAuthType();
                if (YesNo.YES.getCode().equals(authType)) {
                    List<FunctionConfigDept> deptList = configDeptMap.get(item.getItemId());
                    List<FunctionConfigDeptRes> deptResList = converter.toDeptResList(deptList);
                    itemRes.setAuthDeptList(deptResList);
                }
                List<Country> countryList = item.getCountries();
                countryList.forEach(country -> {
                    String countryName = countryMap.getOrDefault(country.getCountry(), StringUtils.EMPTY);
                    country.setCountryName(countryName);
                });
                itemResList.add(itemRes);
            }
            configRes.setItems(itemResList);
            configResList.add(configRes);
        }
        return configResList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteFunctionConfig(DelByIdsReq req) {
        List<Integer> ids = req.getIds();
        if (CollectionUtils.isEmpty(ids)) return;
        configRepo.deleteByConfigIds(ids);
        configItemRepo.deleteByConfigIds(ids);
        configDeptRepo.deleteAuthDeptByConfigIds(ids);
    }
}
