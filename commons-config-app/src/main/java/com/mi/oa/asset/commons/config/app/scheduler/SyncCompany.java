package com.mi.oa.asset.commons.config.app.scheduler;

import com.mi.oa.asset.commons.config.domain.common.repository.CompanyRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.Company;
import com.mi.oa.asset.eam.feign.mdm.MdmClient;
import com.mi.oa.asset.eam.feign.mdm.req.MdmBaseReq;
import com.mi.oa.asset.eam.feign.mdm.res.MdmBaseRes;
import com.mi.oa.asset.eam.feign.mdm.res.MdmCompanyRes;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/2/22 15:36
 */
@Slf4j
@Service
@PlanTask(name = "SyncCompany", quartzCron = "0 20 5 * * ?", description = "定时同步公司数据，每天 5:20 点同步一次")
public class SyncCompany implements PlanExecutor {

    @Resource
    private MdmClient mdmClient;

    @Resource
    private CompanyRepo companyRepo;

    private static final String COUNTRY_CODE1 = "CN";
    private static final String COUNTRY_CODE2 = "CHN";
    private static final String LOGOUT_STR = "已注销";

    @Override
    public void execute() {
        log.info("Sync company data, start at: {}", DateTime.now());

        int pageNum = 1;
        int pageSize = 200;
        int total = 0;
        int done = 0;

        do {
            MdmBaseReq req = MdmBaseReq.builder().pageNum(pageNum++).pageSize(pageSize).build();
            MdmBaseRes<MdmCompanyRes> res = mdmClient.getCompany(req);
            total = res.getTotal();
            List<MdmCompanyRes> list = res.getList();
            done += list.size();

            List<Company> data = new ArrayList<>();
            list.forEach(i -> {
                //EAM2.0只查询CHN的数据，暂时只同步CN的数据
                if (COUNTRY_CODE1.equals(i.getCountryCode())) {
                    //忽略空编码和已注销数据
                    if (StringUtils.isBlank(i.getCompanyCode()) || i.getCompanyName().contains(LOGOUT_STR)) return;
                    data.add(Company.builder()
                            .companyCode(i.getCompanyCode())
                            .companyName(i.getCompanyName())
                            .countryCode(COUNTRY_CODE2)
                            .disabled(i.enabled() ? 0 : 1)
                            .build());
                }
            });
            companyRepo.batchSaveCompany(data);
        } while (0 != total && done < total);

        log.info("Sync company data, end at: {}", DateTime.now());
    }
}
