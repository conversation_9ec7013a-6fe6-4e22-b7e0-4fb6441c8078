package com.mi.oa.asset.commons.config.app.web.controller;


import com.mi.oa.asset.commons.config.api.use.UseProvider;
import com.mi.oa.asset.commons.config.api.use.UseReasonRes;
import com.mi.oa.asset.commons.config.api.use.UseReq;
import com.mi.oa.asset.commons.config.api.use.UseRes;
import com.mi.oa.infra.uc.common.util.GsonUtil;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 办公用途 控制器
 *
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@RestController
@HttpApiModule(value = "UseController", apiController = UseController.class)
@RequestMapping("/configs/use")
@Slf4j
@Validated
public class UseController {
    @Resource
    private UseProvider useProvider;

    /**
     * 申请用途-列表信息
     */
    @PostMapping("/list")
    @HttpApiDoc(apiName = "申请用途-列表", value = "/configs/use/list", method = MiApiRequestMethod.GET)
    public Result<List<UseRes>> list(@Valid @RequestBody UseReq req) {
        log.info("use list req = {}", GsonUtil.toJsonString(req));
        List<UseRes> list = useProvider.list(req);
        return Result.success(list);
    }

    /**
     * 申请用途理由-列表信
     */
    @GetMapping("/reason/list/{useId}")
    @HttpApiDoc(apiName = "申请理由-列表", value = "/configs/use/reason/list/{useId}", method = MiApiRequestMethod.GET)
    public Result<List<UseReasonRes>> listReason(@PathVariable("useId") Integer useId) {
        List<UseReasonRes> list = useProvider.listByUseId(useId);
        return Result.success(list);
    }

    /**
     * 申请用途理由-列表信
     */
    @GetMapping("/reason/list")
    @HttpApiDoc(apiName = "申请理由-列表", value = "/configs/use/reason/list", method = MiApiRequestMethod.GET)
    public Result<List<UseReasonRes>> allReason() {
        List<UseReasonRes> list = useProvider.all();
        return Result.success(list);
    }

}
