package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.common.model.CodeNameProperty;
import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.common.CompanyRes;
import com.mi.oa.asset.commons.config.api.common.ProviderRes;
import com.mi.oa.asset.commons.config.api.common.PurchaseCatalogRes;
import com.mi.oa.asset.commons.config.api.function.DictRes;
import com.mi.oa.asset.commons.config.api.function.FunctionProvider;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.app.converter.CommonDataConverter;
import com.mi.oa.asset.commons.config.domain.common.repository.CommonDataRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.FunctionSubscribe;
import com.mi.oa.asset.commons.config.domain.common.valobj.PurchaseCatalog;
import com.mi.oa.asset.commons.config.domain.common.valobj.SapAssetCategory;
import com.mi.oa.asset.commons.config.domain.common.valobj.TemplateUrl;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.mi.oa.asset.eam.feign.service.EcpService;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.mi.oa.asset.common.enums.EAMConstants.CHINESE;
import static com.mi.oa.asset.common.enums.EAMConstants.LANGUAGE;

/**
 * 通用基础数据接口
 *
 * <AUTHOR>
 * @Date 2023/9/11 15:02
 */

@HttpApiModule(value = "CommonDataController", apiController = CommonDataController.class)
@RestController
@RequestMapping("/commons")
public class CommonDataController {

    @Resource
    private CommonDataRepo commonDataRepo;

    @Resource
    private CommonDataConverter commonDataConverter;

    @Resource
    private EcpService ecpService;

    @Autowired
    private HttpServletRequest request;

    @Resource
    private FunctionProvider functionProvider;

    @Resource
    private UserInfoService userInfoService;


    @HttpApiDoc(apiName = "采购目录树", value = "/commons/purchase-catalog-tree", method = MiApiRequestMethod.GET)
    @GetMapping("/purchase-catalog-tree")
    public Result<List<PurchaseCatalogRes>> purchaseCatalogTree() {
        List<PurchaseCatalog> all = commonDataRepo.getAllPurchaseCatalogs();
        String language = request.getHeader(LANGUAGE);
        boolean isCh = CHINESE.equals(language) || StringUtils.isEmpty(language);
        all.forEach(i -> i.setCatalogName(isCh ? i.getCatalogName() : i.getCatalogNameEn()));
        List<PurchaseCatalogRes> roots = commonDataConverter.toPurchaseCatalogsRes(
                all.stream().filter(i -> 1 == i.getLevel()).collect(Collectors.toList())
        );

        roots.forEach(node -> loadPurchaseCatalogSublist(node, all));

        return Result.success(roots);
    }

    @HttpApiDoc(apiName = "公司查询", value = "/commons/company", method = MiApiRequestMethod.GET)
    @GetMapping("/company")
    public Result<List<CompanyRes>> company(@RequestParam(required = false) String keyword,
                                            @RequestParam(required = false) String countryCode) {
        countryCode = StringUtils.defaultString(countryCode, "CHN"); // 默认查询中国大陆的公司

        return Result.success(commonDataConverter.toCompaniesRes(commonDataRepo.getCompanies(keyword, countryCode)));
    }

    @HttpApiDoc(apiName = "SAP资产分类", value = "/commons/sap-asset-category", method = MiApiRequestMethod.GET)
    @GetMapping("/sap-asset-category")
    public Result<List<CodeNameProperty>> sapAssetCategory() {
        String language = request.getHeader(LANGUAGE);
        boolean isCh = CHINESE.equals(language) || StringUtils.isEmpty(language);
        List<SapAssetCategory> sapRet = commonDataRepo.getSapAssetCategories4EAM();
        sapRet.forEach(i -> i.setSapCateName(isCh ? i.getSapCateName() : i.getSapCateNameEn()));
        return Result.success(commonDataConverter.toSapCategoryRes(sapRet));
    }

    private void loadPurchaseCatalogSublist(PurchaseCatalogRes node, List<PurchaseCatalog> all) {
        List<PurchaseCatalogRes> subList = commonDataConverter.toPurchaseCatalogsRes(
                all.stream().filter(i -> node.getCatalogCode().equals(i.getParentCode())).collect(Collectors.toList())
        );

        if (CollectionUtils.isEmpty(subList)) return;

        subList.forEach(sNode -> loadPurchaseCatalogSublist(sNode, all));
        node.setSubList(subList);
    }

    @HttpApiDoc(apiName = "供应商查询", value = "/commons/provider", method = MiApiRequestMethod.GET)
    @GetMapping("/provider")
    public Result<List<ProviderRes>> provider(@RequestParam(required = false) String keyword) {
        return Result.success(commonDataConverter.toProvidersRes(commonDataRepo.getProviders(keyword)));
    }

    @HttpApiDoc(apiName = "物料类型", value = "/commons/material-type", method = MiApiRequestMethod.GET)
    @GetMapping("/material-type")
    public Result<List<CodeNameProperty>> materialType() {
        Map<String, List<DictRes>> dictMap = functionProvider.getDictList(Arrays.asList("amg_material_type"));
        String language = request.getHeader(LANGUAGE);
        boolean isCh = CHINESE.equals(language) || StringUtils.isEmpty(language);
        return Result.success(dictMap.values().stream().flatMap(List::stream).map(i -> new CodeNameProperty(i.getCode(), isCh ? i.getName() : i.getEnName())).collect(Collectors.toList()));
    }

    @HttpApiDoc(apiName = "查询币种兑换人民币汇率", value = "/commons/rate", method = MiApiRequestMethod.GET)
    @GetMapping("/rate")
    public Result<BigDecimal> getRate(@RequestParam String baseCurrencyCode) {
        String rateDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        return Result.success(ecpService.queryExRateByCurCode(baseCurrencyCode, rateDate));
    }

    @HttpApiDoc(apiName = "查询模板下载地址", value = "/commons/template/url", method = MiApiRequestMethod.GET)
    @GetMapping("/template/url")
    public Result<String> getTemplateUrl(@RequestParam String funId) {
        TemplateUrl templateUrl = commonDataRepo.getTemplateUrl(funId);
        if (Objects.isNull(templateUrl)) {
            return Result.failure(ErrorCodes.BAD_REQUEST, "未查询到模板下载地址");
        }
        String language = request.getHeader(LANGUAGE);
        boolean isCh = CHINESE.equals(language) || StringUtils.isEmpty(language);
        return Result.success(isCh ? templateUrl.getTemplateUrl() : templateUrl.getTemplateUrlEn());
    }

    @HttpApiDoc(apiName = "查询是否订阅", value = "/commons/subscribe/get", method = MiApiRequestMethod.GET)
    @GetMapping("/subscribe/get")
    public Result<Integer> getSubscribe(@RequestParam("businessLine") String businessLine,
                                        @RequestParam("funCode") String funCode) {
        String userName = AuthFacade.authedUserName();
        Integer isSubscribe = 0;
        FunctionSubscribe subscribe = commonDataRepo.getSubscribe(businessLine, funCode, userName);
        if (Objects.nonNull(subscribe)) {
            isSubscribe = subscribe.getIsSubscribe();
        }
        return Result.success(isSubscribe);
    }

    @HttpApiDoc(apiName = "功能订阅", value = "/commons/subscribe", method = MiApiRequestMethod.GET)
    @GetMapping("/subscribe")
    public Result<Void> functionSubscribe(@RequestParam("businessLine") String businessLine,
                                          @RequestParam("funCode") String funCode,
                                          @RequestParam("funName") String funName,
                                          @RequestParam("isSubscribe") Integer isSubscribe,
                                          @RequestParam(required = false) String userName) {
        User user;
        if (userName!= null &&!userName.isEmpty()) {
            user = userInfoService.getUserByUserName(userName);
        } else {
            user = userInfoService.getUserByUserName(AuthFacade.authedUserName());
        }
        // 先查询订阅记录是否存在
        FunctionSubscribe subscribe = commonDataRepo.getSubscribe(businessLine, funCode, user.getUserName());
        if (subscribe == null) {
            subscribe = new FunctionSubscribe();
            subscribe.setBusinessLine(businessLine);
            subscribe.setFunCode(funCode);
            subscribe.setFunName(funName);
            subscribe.setDeptCode(user.getDeptCode());
            subscribe.setDeptName(user.getFullDeptName());
        }
        // 初始化首次发送日期为订阅时间
        subscribe.setSendTime(new Date());
        subscribe.setIsSubscribe(isSubscribe);
        commonDataRepo.createOrUpdateSubscribe(subscribe);
        return Result.success();
    }
}
