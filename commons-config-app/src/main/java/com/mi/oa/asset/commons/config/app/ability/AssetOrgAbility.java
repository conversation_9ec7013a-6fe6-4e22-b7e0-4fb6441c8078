package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigDataRage;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigField;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigLimitData;
import com.mi.oa.asset.commons.config.api.user.EmployeeInfoRes;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.app.converter.AssetOrgConverter;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.assetorganization.repository.AssetOrgRepo;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgStructure;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgUnitQuery;
import com.mi.oa.asset.commons.config.domain.businessrole.entity.BusinessRoleUser;
import com.mi.oa.asset.commons.config.domain.businessrole.repository.BusinessRoleRepo;
import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.domain.common.repository.AllotConfigRepo;
import com.mi.oa.asset.commons.config.domain.common.repository.BusinessLineRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.AllotConfig;
import com.mi.oa.asset.commons.config.infra.common.CacheKey;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/15 18:56
 */

@Slf4j
@Service
public class AssetOrgAbility {

    @Resource
    private AssetOrgRepo assetOrgRepo;

    @Resource
    private BusinessLineRepo businessLineRepo;

    @Resource
    private BusinessRoleRepo businessRoleRepo;

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private AllotConfigRepo allotConfigRepo;

    @Resource
    private AssetOrgConverter converter;

    public void loadNoVirtualUnitInfo(AssetOrgUnit unit) {
        if (null == unit.getBusinessLine()) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线不存在");
        }

        if (null != assetOrgRepo.getOrgUnitByCode(unit.getOrgCode(), unit.getBusinessLine())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "组织单元已存在");
        }

        AssetOrgStructure structure = loadStructure(unit.getOrgCode());
        unit.setLevel(structure.getLevel());
        unit.setDefaultCostCenter(structure.getDefaultCostCenter());
    }

    public void checkOrgCodeExisted(AssetOrgUnit assetOrgUnit) {
        if (null != assetOrgRepo.getOrgStructureByCode(assetOrgUnit.getOrgCode(), Boolean.TRUE)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "编码已存在");
        }
    }

    public void loadInfoFromParent(AssetOrgUnit orgUnit) {
        if (StringUtils.isBlank(orgUnit.getParentCode())) {
            orgUnit.setLevel(1);
            return;
        }

        AssetOrgStructure parent = loadStructure(orgUnit.getParentCode());
        orgUnit.setLevel(parent.getLevel() + 1);
        orgUnit.setDefaultCostCenter(parent.getDefaultCostCenter());
    }

    public void loadInfoFromParent(AssetOrgStructure structure, AssetOrgUnit orgUnit) {
        if (StringUtils.isBlank(structure.getParentCode())) {
            structure.setLevel(1);
        } else {
            AssetOrgStructure parent = loadStructure(structure.getParentCode());
            structure.setLevel(parent.getLevel() + 1);
            structure.setDefaultCostCenter(parent.getDefaultCostCenter());
        }

        orgUnit.setLevel(structure.getLevel());
        orgUnit.setDefaultCostCenter(structure.getDefaultCostCenter());
    }

    public AssetOrgStructure loadStructure(String orgCode) {
        AssetOrgStructure orgStructure = assetOrgRepo.getOrgStructureByCode(orgCode);
        if (null == orgStructure) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, String.format("组织结构【%s】不存在", orgCode));
        }

        return orgStructure;
    }

    public void refreshOrgStructure(String orgCode, String businessLine) {
        log.info("refreshOrgStructure start, orgCode: {}, businessLine: {}", orgCode, businessLine);
        // 根据 orgCode 和 businessLine 查询组织单元表
        AssetOrgUnit orgUnit = assetOrgRepo.getOrgUnitByCode(orgCode, BusinessLine.getByCode(businessLine));
        if (orgUnit == null) {
            throw new ErrorCodeException(ErrorCodes.NOT_FOUND, "该组织单元不存在");
        }

        AssetOrgStructure orgStructure = this.getOrgStructureByCode(orgCode, businessLine);
        if (orgStructure == null) {
            orgStructure = converter.orgUnitToStructure(orgUnit);

            // 新增组织结构节点
            assetOrgRepo.createOrgStructure(orgStructure);
        }
        log.info("refreshOrgStructure end, orgCode: {}, businessLine: {}", orgCode, businessLine);
    }


    public void batchRefreshOrgStructure(String businessLine) {
        log.info("batchRefreshOrgStructure start, businessLine: {}", businessLine);
        // 循环调用getOrgUnitPageByCode 接口，分页查询组织单元，新增组织结构
        int pageSize = 200;
        int pageNum = 1;

        PageData<AssetOrgUnit> pageData;
        while (true) {
            List<AssetOrgStructure> structures = new ArrayList<>();
            PageRequest pageRequest = new PageRequest(pageSize, pageNum);
            pageData = assetOrgRepo.getOrgUnitPageByCode(BusinessLine.getByCode(businessLine), pageRequest);
            int total = pageData.getTotal();

            List<AssetOrgUnit> orgUnits = pageData.getList();
            for (AssetOrgUnit orgUnit : orgUnits) {
                AssetOrgStructure orgStructure = this.getOrgStructureByCode(orgUnit.getOrgCode(), businessLine);
                if (orgStructure == null) {
                    orgStructure = converter.orgUnitToStructure(orgUnit);
                    structures.add(orgStructure);
                }
            }
            if (CollectionUtils.isNotEmpty(structures)) {
                assetOrgRepo.createOrgStructures(structures);
            }

            if (pageNum * pageSize >= total) {
                break;
            }
            pageNum++;
        }

        log.info("batchRefreshOrgStructure end, businessLine: {}", businessLine);
    }

    public AssetOrgStructure getOrgStructureByCode(String code, String businessLine) {
        List<AssetOrgStructure> orgStructures = assetOrgRepo.getOrgStructuresByCodes(Arrays.asList(code));
        // 过滤当前业务线或业务线为空的
        orgStructures = orgStructures.stream()
                .filter(orgStructure -> orgStructure.getBusinessLine() == null || businessLine.equals(orgStructure.getBusinessLine().getCode()))
                .collect(Collectors.toList());
        if (orgStructures.isEmpty()) {
            return null;
        }
        return orgStructures.get(0);
    }

    public List<AssetOrgStructure> loadStructures(List<String> orgCodes) {
        return assetOrgRepo.getOrgStructures(orgCodes, "");
    }

    public AssetOrgUnit loadOrgUnit(String orgCode, BusinessLine businessLine) {
        if (null == businessLine) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线不存在");
        }

        AssetOrgUnit orgUnit = assetOrgRepo.getOrgUnitByCode(orgCode, businessLine);
        if (null == orgUnit) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "数据不存在");
        }

        return orgUnit;
    }

    public AssetOrgUnit loadOrgUnit(Integer orgId) {
        AssetOrgUnit orgUnit = assetOrgRepo.getOrgUnitById(orgId);
        if (null == orgUnit) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "数据不存在");
        }

        return orgUnit;
    }

    public List<AssetOrgUnit> loadOrgUnits(List<Integer> orgIds) {
        return assetOrgRepo.getOrgUnits(AssetOrgUnitQuery.builder().orgIds(orgIds).build());
    }

    public void deleteOrgStructure(String orgCode) {
        AssetOrgStructure orgStructure = loadStructure(orgCode);
        checkStructureCanDelete(orgStructure);
        // 删除组织结构
        assetOrgRepo.deleteOrgStructure(orgStructure);
    }

    public void checkStructureCanDelete(AssetOrgStructure orgStructure) {
        if (assetOrgRepo.hasSubList(orgStructure.getOrgCode())) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "请先删除下级");
        }
    }

    public void checkOrgUnitCanDelete(AssetOrgUnit orgUnit) {
        if (assetOrgRepo.hasSubOrgUnitList(orgUnit.getOrgCode(), orgUnit.getBusinessLine())) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "请先删除下级");
        }
    }

    public boolean getAllotOrgCodeList(AssetOrgUnitQuery params, AllotConfigField fieldCodeEnum) {
        BusinessLineDo businessLineDo = businessLineRepo.searchByBusinessLine(params.getBusinessLine().getCode());
        List<AllotConfig> allotConfigList = allotConfigRepo.allotConfigList(businessLineDo.getBusinessLineId());
        if (CollectionUtils.isEmpty(allotConfigList)) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "【" + params.getBusinessLine().getDesc() + "】业务线的调拨字段属性未配置");
        }
        // 根据选定数据范围， 通过限定内容获取数据
        Map<AllotConfigField, AllotConfig> configMap = allotConfigList.stream().collect(Collectors.toMap(AllotConfig::getFieldCode, Function.identity()));
        AllotConfig allotConfig = configMap.get(fieldCodeEnum);
        if (AllotConfigDataRage.ALL.equals(allotConfig.getDataRage())) return true;
        List<String> downOrgCodes = new ArrayList<>();
        List<String> upOrgCodes = new ArrayList<>();
        for (String bizType : allotConfig.getLimitBizData()) {
            AllotConfigLimitData bizDataEnum = AllotConfigLimitData.getByCode(bizType);
            // 业务角色所属部门
            if (AllotConfigLimitData.ROLE_DEPT == bizDataEnum) {
                // 先查询调入人是否设置数据范围
                AllotConfig inUserName = configMap.get(AllotConfigField.IN_USER_NAME);
                List<String> roleCodeList = new ArrayList<>(10);
                if (AllotConfigDataRage.BIZ_ROLE == inUserName.getDataRage()) {
                    roleCodeList = inUserName.getLimitBizData();
                }
                List<BusinessRoleUser> roleUsers = businessRoleRepo.getRolesByUserCode(params.getInUseCode(), roleCodeList, params.getBusinessLine());
                downOrgCodes.addAll(roleUsers.stream().map(BusinessRoleUser::getOrgCode).collect(Collectors.toList()));
            }
            // 员工所属部门
            if (AllotConfigLimitData.EMP_DEPT == bizDataEnum) {
                User user = userInfoService.getUserByUserName(params.getInUseCode());
                downOrgCodes.add(user.getDeptCode());
            }
            // 使用部门所属管理部门
            if (AllotConfigLimitData.USE_MANAGE_DEPT == bizDataEnum) {
                if (StringUtils.isBlank(params.getInUseDeptCode()))
                    throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "调入使用部门信息必传");
                upOrgCodes.add(params.getInUseDeptCode());
            }
        }
        // 处理下级
        params.setOrgCodes(downOrgCodes.stream().distinct().collect(Collectors.toList()));
        getSubByOrgCode(params);

        // 查询上级
        getUpDeptByOrgCode(upOrgCodes, params);

        return false;
    }

    private void getSubByOrgCode(AssetOrgUnitQuery params) {
        List<String> allOrgCodes = params.getOrgCodes();
        if (CollectionUtils.isEmpty(allOrgCodes)) return;

        int maxLevel = (int) RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_LEVEL_MAX.getKey());
        List<AssetOrgStructure> orgStructures = assetOrgRepo.getOrgStructures(allOrgCodes, "order by level");
        List<String> orgCodeList = new ArrayList<>(10);
        for (AssetOrgStructure structure : orgStructures) {
            if (orgCodeList.contains(structure.getOrgCode())) continue;
            List<String> parentCodes = new ArrayList<>(10);
            parentCodes.add(structure.getOrgCode());
            for (int lv = structure.getLevel() + 1; lv <= maxLevel; ++lv) {
                List<AssetOrgStructure> subList = assetOrgRepo.getOrgStructuresByLevel(lv);
                subList = subList.stream()
                        .filter(i -> parentCodes.contains(i.getParentCode())
                                && (null == i.getBusinessLine() || params.getBusinessLine().equals(i.getBusinessLine())))
                        .collect(Collectors.toList());
                List<String> subOrgCodes = subList.stream().map(AssetOrgStructure::getOrgCode).collect(Collectors.toList());

                parentCodes.clear();
                parentCodes.addAll(subOrgCodes);
                orgCodeList.addAll(subOrgCodes);
            }
        }
        allOrgCodes.addAll(orgCodeList);
        params.setOrgCodes(allOrgCodes.stream().distinct().collect(Collectors.toList()));
    }

    private void getUpDeptByOrgCode(List<String> upOrgCodes, AssetOrgUnitQuery params) {
        if (CollectionUtils.isEmpty(upOrgCodes)) return;
        List<String> allOrgCodes = params.getOrgCodes();

        // 根据当前部门查询各上级部门
        for (String orgCode : upOrgCodes) {
            AssetOrgStructure structure = loadStructure(orgCode);
            while (structure != null && structure.getLevel() > 1) {
                structure = loadStructure(structure.getParentCode());
                if (structure != null) {
                    allOrgCodes.add(structure.getOrgCode());
                }
            }
        }
        allOrgCodes.addAll(upOrgCodes);
        params.setOrgCodes(allOrgCodes.stream().distinct().collect(Collectors.toList()));
    }

    public List<AssetOrgUnit> getLabTypeOrgList(String businessLine, String userCode) {
        // 根据登录用户查询部门下所有实验室组织类型，手机部到三级部门，其他二级部门
        EmployeeInfoRes employeeInfoRes = userInfoService.getEmpInfoByUserName(userCode);
        String orgCode = employeeInfoRes.getDeptIdLv2();
        if ("HW".equals(orgCode) || "AU".equals(orgCode)) {
            orgCode = employeeInfoRes.getDeptIdLv3();
        }
        return assetOrgRepo.getLabTypeOrgList(businessLine, orgCode);
    }
}
