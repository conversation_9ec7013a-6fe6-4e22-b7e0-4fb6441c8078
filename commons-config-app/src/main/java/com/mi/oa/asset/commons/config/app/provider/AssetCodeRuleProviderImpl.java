package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.assetcode.AssetCodeRuleProvider;
import com.mi.oa.asset.commons.config.api.assetcode.AssetCodeRuleReq;
import com.mi.oa.asset.commons.config.api.assetcode.AssetCodeRuleRes;
import com.mi.oa.asset.commons.config.app.ability.AssetCodeRuleAbility;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024-06-28 17:15
 */
@DubboService
public class AssetCodeRuleProviderImpl implements AssetCodeRuleProvider {

    @Resource
    private AssetCodeRuleAbility assetCodeRuleAbility;

    @Override
    public void save(AssetCodeRuleReq assetCodeRuleReq) {
        assetCodeRuleAbility.save(assetCodeRuleReq);
    }

    @Override
    public AssetCodeRuleRes getByBusinessLine(String businessLine) {
        return assetCodeRuleAbility.getByBusinessLine(businessLine);
    }
}
