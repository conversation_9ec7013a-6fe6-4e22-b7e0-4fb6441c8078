package com.mi.oa.asset.commons.config.app.ability;

import com.google.common.base.CaseFormat;
import com.mi.oa.asset.commons.config.api.auth.QueryDataResourceListReq;
import com.mi.oa.asset.commons.config.api.auth.QueryRemoteDataResourceReq;
import com.mi.oa.asset.commons.config.api.auth.ResourceDimensionRemoteResp;
import com.mi.oa.asset.commons.config.api.auth.ResourceDto;
import com.mi.oa.asset.commons.config.domain.dimension.entity.DimensionResource;
import com.mi.oa.asset.commons.config.domain.dimension.enums.DimensionResourceType;
import com.mi.oa.asset.commons.config.domain.dimension.repository.DimensionResourceRepo;
import com.mi.oa.asset.commons.config.infra.repository.converter.DimensionResourceConverter;
import com.mi.oa.asset.eam.mybatis.CommonMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.text.StrBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mi.oa.asset.eam.mybatis.SqlConstants.*;

/**
 * <AUTHOR>
 * @date 2025/2/21 18:46
 * @description
 */
@Service
@Slf4j
public class AssetAuthAbility {
    @Resource
    private DimensionResourceRepo dimensionRepo;
    @Resource
    private CommonMapper commonMapper;
    @Resource
    private DimensionResourceConverter resourceConverter;

    private final static int RECURSION_DEPTH=20;


    public ResourceDimensionRemoteResp listAssetFuncAuthData(QueryRemoteDataResourceReq resourceReq) {
        //根据dimensionCode获取数据表、返回的数据类型、查询的sql
        DimensionResource dimensionResource = dimensionRepo.getByCode(resourceReq.getDimensionCode());
        //通过commonMapper执行查询的sql
        if (DimensionResourceType.FLAT.getCode().equals(dimensionResource.getResourceType())) {
            return toFlatResourceDto(dimensionResource, resourceReq);
        } else if (DimensionResourceType.TREE.getCode().equals(dimensionResource.getResourceType())) {
            return dimensionResource.getIsLayer() ? toLayerTreeResourceDto(dimensionResource, resourceReq) : toTreeResourceDto(dimensionResource, resourceReq);
        }
        return null;
    }


    //平铺的会配两个字段：code+name
    private ResourceDimensionRemoteResp toFlatResourceDto(DimensionResource dimensionResource, QueryRemoteDataResourceReq resourceReq) {
        String sql = buildPageSql(dimensionResource, Integer.parseInt(resourceReq.getPageSize()), Integer.parseInt(resourceReq.getPageNum()));
        List<Map<String, Object>> fieldMap = commonMapper.commonQuery(sql, new HashMap<>());
        List<ResourceDto> resourceDtos = new ArrayList<>();
        for (Map<String, Object> map : fieldMap) {
            ResourceDto resourceDto = resourceConverter.toResourceDto(String.valueOf(map.get(underScoreToCamel(dimensionResource.getFieldCode()))),
                    String.valueOf(map.get(underScoreToCamel(dimensionResource.getFieldName()))), resourceReq.getDimensionCode());
            resourceDtos.add(resourceDto);
        }
        //总页数
        int count = getPageCount(dimensionResource.getResourceSql(), new HashMap<>());
        ResourceDimensionRemoteResp resp = new ResourceDimensionRemoteResp();
        resp.setResourceTreeDtoList(resourceDtos);
        resp.setTotalSize(count);
        resp.setPageNum(Long.parseLong(resourceReq.getPageNum()));
        resp.setPageSize(Long.parseLong(resourceReq.getPageSize()));
        resp.setPageTotal(count / resp.getPageSize());
        return resp;
    }


    private String underScoreToCamel(String field) {
        return CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, field);
    }


    private ResourceDimensionRemoteResp toLayerTreeResourceDto(DimensionResource dimensionResource, QueryRemoteDataResourceReq resourceReq) {
        List<ResourceDto> resources = new ArrayList<>();
        if ("0".equals(resourceReq.getParentCode())) {
            //获取最顶级
            List<Map<String, Object>> fieldMap = getFirstLevelFieldMap(dimensionResource);
            for (Map<String, Object> map : fieldMap) {
                ResourceDto resourceDto = resourceConverter.toResourceDto(String.valueOf(map.get(underScoreToCamel(dimensionResource.getFieldCode())))
                        , String.valueOf(map.get(underScoreToCamel(dimensionResource.getFieldName()))), resourceReq.getDimensionCode());
                resources.add(resourceDto);
            }
        } else {
            //根据父code获取子集
            List<Map<String, Object>> fieldMap = getFieldMapByParent(dimensionResource, resourceReq.getParentCode());
            for (Map<String, Object> map : fieldMap) {
                ResourceDto resourceDto = resourceConverter.toResourceDto(String.valueOf(map.get(underScoreToCamel(dimensionResource.getFieldCode()))),
                        String.valueOf(map.get(underScoreToCamel(dimensionResource.getFieldName()))), dimensionResource.getDimensionCode());
                resources.add(resourceDto);
            }
        }
        ResourceDimensionRemoteResp resp = new ResourceDimensionRemoteResp();
        resp.setResourceTreeDtoList(resources);
        return resp;
    }

    //树状的结构，层级结构字段，父节点编码字段
    private ResourceDimensionRemoteResp toTreeResourceDto(DimensionResource dimensionResource, QueryRemoteDataResourceReq resourceReq) {
        List<ResourceDto> resources = new ArrayList<>();
        //获取父级
        List<Map<String, Object>> fieldMap = getFirstLevelFieldMap(dimensionResource);
        for (Map<String, Object> map : fieldMap) {
            ResourceDto resourceDto = resourceConverter.toResourceDto(String.valueOf(map.get(underScoreToCamel(dimensionResource.getFieldCode())))
                    , String.valueOf(map.get(underScoreToCamel(dimensionResource.getFieldName()))), resourceReq.getDimensionCode());
            List<ResourceDto> children = loadChildResources(resourceDto, dimensionResource, resourceReq.getDimensionCode(),RECURSION_DEPTH);
            resourceDto.setChildren(children);
            resources.add(resourceDto);
        }
        ResourceDimensionRemoteResp resp = new ResourceDimensionRemoteResp();
        resp.setResourceTreeDtoList(resources);
        return resp;
    }


    private List<ResourceDto> loadChildResources(ResourceDto parentDto, DimensionResource dimensionResource, String dimensionCode,int n) {
        if (StringUtils.isEmpty(parentDto.getResourceCode())||n==1) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> fieldMap = getFieldMapByParent(dimensionResource, parentDto.getResourceCode());
        if (CollectionUtils.isEmpty(fieldMap)) {
            return new ArrayList<>();
        }
        List<ResourceDto> resources = new ArrayList<>();
        for (Map<String, Object> map : fieldMap) {
            ResourceDto resourceDto = resourceConverter.toResourceDto(String.valueOf(map.get(underScoreToCamel(dimensionResource.getFieldCode()))),
                    String.valueOf(map.get(underScoreToCamel(dimensionResource.getFieldName()))), dimensionCode);
            List<ResourceDto> children = loadChildResources(resourceDto, dimensionResource, dimensionCode,n-1);
            resourceDto.setChildren(children);
            resources.add(resourceDto);
        }
        return resources;
    }


    private String buildPageSql(DimensionResource dimensionResource, Integer pageSize, Integer pageNum) {
        String sql = dimensionResource.getResourceSql().replace(SPECIAL, getFlatFields(dimensionResource));
        StrBuilder sb = new StrBuilder(sql);
        sb.append(LIMIT).append(BLANK).append((pageNum - 1) * pageSize).append(COMA).append(pageSize);
        sql = sb.toString();
        log.info("DataPermission buildPageSql : {}", sql);
        return sql;
    }

    private int getPageCount(String sql, Map<String, Object> values) {
        String countSql = sql.replace(SPECIAL, COUNT);
        int count = commonMapper.count(countSql, values);
        return count;
    }


    public List<ResourceDto> getDataResourceList(QueryDataResourceListReq resourceReq) {
        if (StringUtils.isEmpty(resourceReq.getDimensionCode())) {
            return new ArrayList<>();
        }
        DimensionResource dimensionResource = dimensionRepo.getByCode(resourceReq.getDimensionCode());
        List<ResourceDto> resourceDtoList = new ArrayList<>();
        String resourceCodes = String.join(QUOTES_COMA, resourceReq.getResourceCodes());
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(dimensionResource.getResourceSql().replace(SPECIAL,
                        DimensionResourceType.FLAT.getCode().equals(dimensionResource.getResourceType()) ? getFlatFields(dimensionResource) : getTreeFields(dimensionResource)))
                .append(BLANK).append(AND).append(BLANK).append(dimensionResource.getFieldCode())
                .append(BLANK).append(IN).append(BLANK).append(LEFT_BRACKET).append(SINGLE_QUOTES)
                .append(resourceCodes).append(SINGLE_QUOTES).append(RIGHT_BRACKET).append(BLANK);
        List<Map<String, Object>> fieldMap = commonMapper.commonQuery(sqlBuilder.toString(), new HashMap<>());
        if (CollectionUtils.isEmpty(fieldMap)) {
            return new ArrayList<>();
        }
        if (resourceReq.getRecursion() && DimensionResourceType.TREE.getCode().equals(dimensionResource.getResourceType())) {
            //如果recursion为true,且为树状结构查询子节点
            List<Map<String, Object>> subfieldMap = getSubNodes(resourceReq.getResourceCodes(), dimensionResource);
            fieldMap.addAll(subfieldMap);
        }
        for (Map<String, Object> map : fieldMap) {
            ResourceDto resourceDto = resourceConverter.toResourceDto(String.valueOf(map.get(underScoreToCamel(dimensionResource.getFieldCode()))),
                    String.valueOf(map.get(underScoreToCamel(dimensionResource.getFieldName()))), resourceReq.getDimensionCode());
            resourceDtoList.add(resourceDto);
        }
        return resourceDtoList;
    }


    private List<Map<String, Object>> getSubNodes(List<String> resourceCodes, DimensionResource dimensionResource) {
        if (CollectionUtils.isEmpty(resourceCodes)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> fieldMap = new ArrayList<>();
        StringBuilder subSqlBuilder = new StringBuilder();
        String resourceCodeStr = String.join(QUOTES_COMA, resourceCodes);
        subSqlBuilder.append(dimensionResource.getResourceSql().replace(SPECIAL, getTreeFields(dimensionResource)))
                .append(BLANK).append(AND).append(BLANK).append(dimensionResource.getParentFieldCode())
                .append(IN).append(LEFT_BRACKET).append(SINGLE_QUOTES).append(resourceCodeStr)
                .append(SINGLE_QUOTES).append(RIGHT_BRACKET).append(BLANK);
        List<Map<String, Object>> subfieldMap = commonMapper.commonQuery(subSqlBuilder.toString(), new HashMap<>());
        List<String> subresourceCodes = subfieldMap.stream().map(o -> String.valueOf(o.get(underScoreToCamel(dimensionResource.getFieldCode())))).collect(Collectors.toList());
        List<Map<String, Object>> childrenMap = getSubNodes(subresourceCodes, dimensionResource);
        subfieldMap.addAll(childrenMap);
        fieldMap.addAll(subfieldMap);
        return fieldMap;
    }

    private List<Map<String, Object>> getFieldMapByParent(DimensionResource dimensionResource, String parentResourceCode) {
        //根据父code获取子集
        StringBuilder subSqlBuilder = new StringBuilder();
        subSqlBuilder.append(dimensionResource.getResourceSql().replace(SPECIAL, getTreeFields(dimensionResource)))
                .append(BLANK).append(AND).append(BLANK).append(dimensionResource.getParentFieldCode())
                .append(EQ).append(SINGLE_QUOTES).append(parentResourceCode).append(SINGLE_QUOTES);
        return commonMapper.commonQuery(subSqlBuilder.toString(), new HashMap<>());
    }

    private List<Map<String, Object>> getFirstLevelFieldMap(DimensionResource dimensionResource) {
        //获取父级
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(dimensionResource.getResourceSql().replace(SPECIAL, getTreeFields(dimensionResource)))
                .append(BLANK).append(AND).append(BLANK)
                .append(dimensionResource.getFieldLevel()).append(EQ).append(SINGLE_QUOTES)
                .append(dimensionResource.getFirstLevelValue())
                .append(SINGLE_QUOTES);
        return commonMapper.commonQuery(sqlBuilder.toString(), new HashMap<>());
    }

    private String getFlatFields(DimensionResource dimensionResource) {
        List<String> fields=new ArrayList<>();
        fields.add(dimensionResource.getFieldCode());
        fields.add(dimensionResource.getFieldName());
        return String.join(COMA, fields);
    }

    private String getTreeFields(DimensionResource dimensionResource) {
        List<String> fields=new ArrayList<>();
        fields.add(dimensionResource.getFieldCode());
        fields.add(dimensionResource.getFieldName());
        fields.add(dimensionResource.getParentFieldCode());
        fields.add(dimensionResource.getFieldLevel());
        return String.join(COMA, fields);
    }
}
