package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.assetorganization.*;
import com.mi.oa.asset.commons.config.api.businessline.enums.AllotConfigField;
import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleProvider;
import com.mi.oa.asset.commons.config.app.ability.AssetOrgAbility;
import com.mi.oa.asset.commons.config.app.ability.BusinessRoleAbility;
import com.mi.oa.asset.commons.config.app.converter.AssetOrgConverter;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.assetorganization.repository.AssetOrgRepo;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgStructure;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.AssetOrgUnitQuery;
import com.mi.oa.asset.commons.config.infra.common.CacheKey;
import com.mi.oa.infra.oaucf.redis.annotation.OACacheSet;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @Date 2023/9/15 16:52
 */

@Slf4j
@DubboService
public class AssetOrgProviderImpl implements AssetOrgProvider {

    @Resource
    private AssetOrgAbility assetOrgAbility;

    @Resource
    private AssetOrgRepo assetOrgRepo;

    @Resource
    private AssetOrgConverter converter;

    @Resource
    private AssetOrgProvider assetOrgProvider;

    @Resource
    private BusinessRoleProvider businessRoleProvider;

    @Resource
    private BusinessRoleAbility businessRoleAbility;

    private ThreadPoolExecutor customThreadPool = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000000));

    @Override
    public void createNoVirtualUnit(SaveAssetOrgUnitReq req) {
        AssetOrgUnit assetOrgUnit = converter.reqToAssetOrgUnit(req);

        assetOrgAbility.loadNoVirtualUnitInfo(assetOrgUnit);
        assetOrgUnit.setIsVirtual(true);
        assetOrgRepo.createOrgUnit(assetOrgUnit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAssetOrgUnit(SaveAssetOrgUnitReq req) {
        AssetOrgUnit orgUnit = converter.reqToAssetOrgUnit(req);
        orgUnit.setIsVirtual(true);

        assetOrgAbility.loadInfoFromParent(orgUnit);
        AssetOrgStructure orgStructure = converter.orgUnitToStructure(orgUnit);

        // 检查编码是否存在
        assetOrgAbility.checkOrgCodeExisted(orgUnit);
        // 新增组织单元数据
        assetOrgRepo.createOrgUnit(orgUnit);
        // 新增组织结构节点
        assetOrgRepo.createOrgStructure(orgStructure);
        //保存组织下角色用户
        if (CollectionUtils.isNotEmpty(req.getRoleInfoList())) {
            businessRoleProvider.saveBusinessRole(orgUnit.getOrgCode(), orgUnit.getBusinessLine(), req.getRoleInfoList());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshOrgStructure(String orgCode, String businessLine) {
        if (StringUtils.isBlank(businessLine)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线参数缺失，请确认查询参数");
        }

        if (StringUtils.isNotBlank(orgCode)) {
            assetOrgAbility.refreshOrgStructure(orgCode, businessLine);
        } else {
            // 根据
            assetOrgAbility.batchRefreshOrgStructure(businessLine);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAssetOrgUnit(SaveAssetOrgUnitReq req) {
        AssetOrgUnit orgUnit = converter.reqToAssetOrgUnit(req);

        assetOrgAbility.loadInfoFromParent(orgUnit);

        // 更新组织单元数据
        assetOrgRepo.updateOrgUnit(orgUnit);
        AssetOrgStructure orgStructure = assetOrgAbility.loadStructure(orgUnit.getOrgCode());
        // 更新非虚拟组织结构
        if (Boolean.TRUE.equals(orgStructure.getIsVirtual())) {
            converter.updateStructure(orgUnit, orgStructure);
            assetOrgRepo.updateOrgStructure(orgStructure);
        }
        //修改组织下的角色用户
        if (CollectionUtils.isEmpty(req.getRoleInfoList())) {
            businessRoleProvider.removeAllRoles(orgUnit.getOrgCode(), orgUnit.getBusinessLine());
        } else {
            businessRoleProvider.saveBusinessRole(orgUnit.getOrgCode(), orgUnit.getBusinessLine(), req.getRoleInfoList());
        }
    }

    @Override
    public List<AssetOrgStructureRes> getOrgStructure(List<String> businessLineCodes) {
        return getOrgStructure(businessLineCodes, null);
    }

    @Override
    public List<AssetOrgStructureRes> getOrgStructure(List<String> businessLineCodes, String orgCode) {
        List<AssetOrgStructureRes> list;
        // 查询指定一级组织信息
        if (StringUtils.isNotEmpty(orgCode)) {
            list = assetOrgProvider.getOrgStructureWithSub(orgCode, businessLineCodes);
        } else {
            int maxLevel = (int) RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_LEVEL_MAX.getKey());
            list = new ArrayList<>();
            for (int lv = 1; lv <= maxLevel; ++lv) {
                List<AssetOrgStructure> lvList = assetOrgRepo.getOrgStructuresByLevel(lv);
                lvList = lvList.parallelStream()
                        .filter(i -> null == i.getBusinessLine() || businessLineCodes.contains(i.getBusinessLine().getCode()))
                        .collect(Collectors.toList());
                list.addAll(converter.toAssetOrgStructureRes(lvList));
            }
        }
        return list;
    }


    @Override
    public List<AssetOrgUnitRes> getBatchOrgUnitByCode(String businessLine, List<String> orgCode) {
        // 两个参数必填校验
        if (StringUtils.isEmpty(businessLine) || CollectionUtils.isEmpty(orgCode)) {
            return Collections.emptyList();
        }
        return converter.toAssetOrgUnitRes(assetOrgRepo.getBatchOrgUnitByCode(orgCode, BusinessLine.getByCode(businessLine)));
    }

    @Override
    public AssetOrgUnitRes getOrgUnitByCode(String orgCode, String businessLine) {
        return converter.toAssetOrgUnitRes(assetOrgAbility.loadOrgUnit(orgCode, BusinessLine.getByCode(businessLine)));
    }

    @Override
    public AssetOrgUnitRes getOrgUnit(String keyword, String businessLine) {
        return converter.toAssetOrgUnitRes(assetOrgRepo.getOrgUnits(keyword, businessLine));
    }

    @Override
    public AssetOrgUnitRes getOrgUnitById(Integer orgId) {
        return getOrgUnitById(orgId, EAMConstants.CHINESE);
    }

    @Override
    public AssetOrgUnitRes getOrgUnitById(Integer orgId, String language) {
        AssetOrgUnit orgUnit = assetOrgAbility.loadOrgUnit(orgId);

        return converter.toAssetOrgUnitRes(orgUnit, businessRoleAbility.getBusinessRoleInfo(orgUnit, language));
    }

    @Transactional
    @Override
    public void deleteOrgStructure(String orgCode) {
        // 删除组织结构
        assetOrgAbility.deleteOrgStructure(orgCode);
        // 删除虚拟节点对应的组织单元
        assetOrgRepo.deleteOrgUnitByOrgCode(orgCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrgUnitByIds(List<Integer> orgIds) {
        List<AssetOrgUnit> list = assetOrgAbility.loadOrgUnits(orgIds);
        List<String> orgCodes = list.stream().map(AssetOrgUnit::getOrgCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgCodes)) {
            return;
        }
        list.forEach(i -> assetOrgAbility.checkOrgUnitCanDelete(i));
        // 删除组织单元
        assetOrgRepo.deleteOrgUnitByIds(orgIds);
        // 先删除组织单元，再删除组织结构
        List<AssetOrgStructure> virtualStructures = getCanDelVirtualStructures(orgCodes);
        assetOrgRepo.deleteOrgStructures(virtualStructures);
        // 删除业务角色
        Map<BusinessLine, List<AssetOrgUnit>> businessLineOrgMap = list.stream().collect(Collectors.groupingBy(AssetOrgUnit::getBusinessLine));
        Set<Map.Entry<BusinessLine, List<AssetOrgUnit>>> entries = businessLineOrgMap.entrySet();
        for (Map.Entry<BusinessLine, List<AssetOrgUnit>> entry : entries) {
            BusinessLine businessLine = entry.getKey();
            List<AssetOrgUnit> orgCodeList = entry.getValue();
            List<String> collect = orgCodeList.stream().map(AssetOrgUnit::getOrgCode).collect(Collectors.toList());
            businessRoleProvider.removeAllRolesByOrgCodes(collect, businessLine);
        }
    }

    private List<AssetOrgStructure> getCanDelVirtualStructures(List<String> orgCodes) {
        // 删除组织结构
        List<AssetOrgStructure> orgStructures = assetOrgAbility.loadStructures(orgCodes);
        List<AssetOrgStructure> virtualStructures = orgStructures.stream().filter(AssetOrgStructure::getIsVirtual).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(virtualStructures)) {
            return new ArrayList<>();
        }

        List<String> virtualOrgCodes = virtualStructures.stream().map(AssetOrgStructure::getOrgCode).collect(Collectors.toList());
        List<AssetOrgStructure> subStructures = assetOrgRepo.getOrgStructuresByParentCodes(virtualOrgCodes);

        if (CollectionUtils.isNotEmpty(subStructures)) {
            List<String> parentOrgCodes = subStructures.stream().map(AssetOrgStructure::getParentCode).collect(Collectors.toList());
            // 从virtualStructures中删除有子节点的节点
            virtualStructures.removeIf(i -> parentOrgCodes.contains(i.getOrgCode()));
        }

        if (CollectionUtils.isEmpty(virtualStructures)) {
            return new ArrayList<>();
        }

        // orgUnits中其他业务线的也不能删除
        List<AssetOrgUnit> otherBusinessLineOrgUnits = assetOrgRepo.getBatchOrgUnitByCode(
                virtualStructures.stream().map(AssetOrgStructure::getOrgCode).collect(Collectors.toList()), null);
        List<String> collect = otherBusinessLineOrgUnits.stream().map(AssetOrgUnit::getOrgCode).collect(Collectors.toList());
        virtualStructures.removeIf(i -> collect.contains(i.getOrgCode()));
        return virtualStructures;
    }

    @Override
    public PageData<AssetOrgUnitRes> getAssetOrgUnitPageData(AssetOrgUnitQueryReq req, Integer pageNum, Integer pageSize) {
        // 添加空值检查，避免NullPointerException
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }

        AssetOrgUnitQuery params = converter.toAssetOrgUnitQuery(req);

        return converter.toPageData(assetOrgRepo.orgUnitPageData(params, PageRequest.of(pageSize, pageNum)), converter::toAssetOrgUnitRes);
    }

    @Override
    public PageData<AssetOrgUnitRes> getAssetOrgUnitPageData(AssetOrgUnitQueryReq req, Integer pageNum, Integer pageSize, String language) {
        // 添加空值检查，避免NullPointerException
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }

        AssetOrgUnitQuery params = converter.toAssetOrgUnitQuery(req);
        PageData<AssetOrgUnit> unitPageData = assetOrgRepo.orgUnitPageData(params, PageRequest.of(pageSize, pageNum));
        return PageData.of(converter.toAssetOrgUnitRes(unitPageData.getList(), language), unitPageData.getPageSize(), unitPageData.getPageNum(), unitPageData.getTotal());
    }

    @Override
    public PageData<AssetOrgUnitRes> getAllotOrgList(AssetOrgUnitQueryReq req, Integer pageNum, Integer pageSize) {
        return getAllotOrgList(req, pageNum, pageSize, EAMConstants.CHINESE);
    }

    @Override
    public PageData<AssetOrgUnitRes> getAllotOrgList(AssetOrgUnitQueryReq req, Integer pageNum, Integer pageSize, String language) {
        // 添加空值检查，避免NullPointerException
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        AssetOrgUnitQuery params = converter.toAssetOrgUnitQuery(req);
        AllotConfigField fieldCodeEnum;
        if (Boolean.TRUE.equals(params.getIsAssetUseOrg())) {
            fieldCodeEnum = AllotConfigField.IN_USE_DEPT_CODE;
        } else if (Boolean.TRUE.equals(params.getIsAssetManageOrg())) {
            fieldCodeEnum = AllotConfigField.IN_MANAGE_DEPT_CODE;
        } else {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "参数缺失，请确认查询参数");
        }
        boolean isAll = assetOrgAbility.getAllotOrgCodeList(params, fieldCodeEnum);
        if (!isAll && CollectionUtils.isEmpty(params.getOrgCodes())) {
            return PageData.of(new ArrayList<>(), pageSize, pageNum, 0);
        }
        PageData<AssetOrgUnit> unitPageData = assetOrgRepo.orgUnitPageData(params, PageRequest.of(pageSize, pageNum));
        return PageData.of(converter.toAssetOrgUnitRes(unitPageData.getList(), language), unitPageData.getPageSize(), unitPageData.getPageNum(), unitPageData.getTotal());
    }

    @Override
    public List<AssetOrgUnitRes> getLabTypeOrgList(String businessLine, String userCode) {
        List<AssetOrgUnit> orgUnitList = assetOrgAbility.getLabTypeOrgList(businessLine, userCode);
        return converter.toAssetOrgUnitRes(orgUnitList);
    }

    @Override
    public AssetOrgStructureRes getOrgStructureByCode(String orgCode) {
        AssetOrgStructureRes orgStructure = converter.toAssetOrgStructureRes(assetOrgAbility.loadStructure(orgCode));
        String fullName = (String) RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey(orgCode));
        orgStructure.setOrgFullName(fullName);
        String fullNameEn = (String) RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey(orgCode));
        orgStructure.setOrgFullNameEn(fullNameEn);
        return orgStructure;
    }

    @Override
    public List<AssetOrgStructureRes> getBatchOrgStructureByCode(List<String> orgCodes) {
        if (CollectionUtils.isEmpty(orgCodes)) return new ArrayList<>();
        List<AssetOrgStructure> assetOrgStructures = assetOrgRepo.getOrgStructuresByCodes(orgCodes);
        List<AssetOrgStructureRes> orgStructureRes = converter.toAssetOrgStructureRes(assetOrgStructures);
        for (AssetOrgStructureRes orgStructureRe : orgStructureRes) {
            String fullName = (String) RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey(orgStructureRe.getOrgCode()));
            orgStructureRe.setOrgFullName(fullName);
            String fullNameEn = (String) RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME_EN.genKey(orgStructureRe.getOrgCode()));
            orgStructureRe.setOrgFullNameEn(fullNameEn);
        }

        return orgStructureRes;
    }

    @Override
    public List<AssetOrgStructureRes> getBatchOrgStructureByCode(List<String> orgCodes, BusinessLine businessLine) {
        if (CollectionUtils.isEmpty(orgCodes)) return new ArrayList<>();
        List<AssetOrgStructure> assetOrgStructures = assetOrgRepo.getOrgStructuresByCodes(orgCodes);
        // assetOrgStructures 过滤出业务线为空和业务线相等的数据
        List<AssetOrgStructure> newAssetOrgStructures = assetOrgStructures.stream()
                .filter(structure -> structure.getBusinessLine() == null || structure.getBusinessLine() == businessLine)
                .collect(Collectors.toList());

        List<AssetOrgStructureRes> orgStructureRes = converter.toAssetOrgStructureRes(newAssetOrgStructures);
        for (AssetOrgStructureRes orgStructureRe : orgStructureRes) {
            String fullName = (String) RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_FULL_NAME.genKey(orgStructureRe.getOrgCode()));
            orgStructureRe.setOrgFullName(fullName);
        }

        return orgStructureRes;
    }

    @Override
    public List<AssetOrgStructureRes> getOrgStructureWithSub(String orgCode) {
        return getOrgStructureWithSub(orgCode, new ArrayList<>());
    }

    @Override
    public List<AssetOrgStructureRes> getOrgStructureWithSub(String orgCode, BusinessLine businessLine) {
        return getOrgStructureWithSub(orgCode, Collections.singletonList(businessLine.getCode()));
    }

    @Override
    public List<AssetOrgStructureRes> getOrgStructureWithSub(String orgCode, List<String> businessLineCodes) {
        List<AssetOrgStructureRes> list = new ArrayList<>();
        AssetOrgStructure orgStructures = assetOrgAbility.loadStructure(orgCode);
        list.add(converter.toAssetOrgStructureRes(orgStructures));

        List<String> parentCodes = new ArrayList<>();
        parentCodes.add(orgCode);
        int maxLevel = (int) RedisUtils.get(CacheKey.ORGANIZATION_STRUCTURE_LEVEL_MAX.getKey());
        for (int lv = orgStructures.getLevel() + 1; lv <= maxLevel; ++lv) {
            List<AssetOrgStructure> subList = assetOrgRepo.getOrgStructuresByLevel(lv);
            subList = subList.stream()
                    .filter(i -> parentCodes.contains(i.getParentCode()) && (null == i.getBusinessLine() || businessLineCodes.contains(i.getBusinessLine().getCode())))
                    .collect(Collectors.toList());
            list.addAll(converter.toAssetOrgStructureRes(subList));

            parentCodes.clear();
            parentCodes.addAll(subList.stream().map(AssetOrgStructure::getOrgCode).collect(Collectors.toList()));
        }

        return list;
    }

    @Override
    public List<AssetOrgUnitRes> getManageOrgUnit(String businessLineCode) {
        return getManageOrgUnit(businessLineCode, EAMConstants.CHINESE);
    }

    @Override
    public List<AssetOrgUnitRes> getManageOrgUnit(String businessLineCode, String language) {
        AssetOrgUnitQuery params = AssetOrgUnitQuery.builder()
                .businessLine(BusinessLine.getByCode(businessLineCode))
                .isAssetManageOrg(true)
                .build();

        return converter.toAssetOrgUnitRes(assetOrgRepo.getOrgUnits(params), language, !EAMConstants.CHINESE.equals(language));
    }

    @Override
    @OACacheSet(cacheEnum = CacheKey.class, cacheEnumField = "ALL_DEPARTMENT", refreshCacheTime = 3600)
    public List<AssetOrgStructureRes> getDepartments() {
        return converter.toAssetOrgStructureRes(assetOrgRepo.getAllOrgStructure(null));
    }

    @Override
    public PageData<AssetOrgStructureRes> getAssetOrgStructurePageData(String businessLine, String keyword, Integer pageNum, Integer pageSize) {
        // 添加空值检查，避免NullPointerException
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }

        PageData<AssetOrgStructure> page = assetOrgRepo.orgStructurePageData(businessLine, keyword, PageRequest.of(pageSize, pageNum));

        return converter.toPageData(page, converter::toAssetOrgStructureRes);
    }

    @Override
    public void importOrgUnit(AssetOrgUnitImportReq req) {
        List<String> orgCodes = req.getOrgCodes();
        if (orgCodes.size() <= 500) {
            importOrgUnitAsync(req.getBusinessLine(), orgCodes);
        } else {
            List<List<String>> lists = splitList(orgCodes, 1000);
            List<CompletableFuture<String>> futureList = new ArrayList<>(lists.size());
            for (List<String> orgCodeList : lists) {
                log.info("importOrgCodeList:{}", orgCodeList);
                List<String> copyArrayList = new CopyOnWriteArrayList<>(orgCodeList);
                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> importOrgUnitAsync(req.getBusinessLine(), copyArrayList), customThreadPool).handle((r, e) -> {
                    if (e != null) {
                        log.error("importOrgUnitAsyncError:{}", e.getMessage());
                    }
                    return r;
                });
                futureList.add(future);
            }
            CompletableFuture<Void> completableFuture = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
            completableFuture.join();
        }
        // 自动更新组织数据
        CompletableFuture.runAsync(() -> autoAsyncOrgUnit(req), customThreadPool);
    }

    private void loadOrgPath(List<AssetOrgStructure> orgCodes, String orgCode) {
        if (StringUtils.isBlank(orgCode)) {
            return;
        }
        AssetOrgStructure orgStructureByCode = assetOrgRepo.getOrgStructureByCode(orgCode);
        if (orgStructureByCode == null) {
            return;
        } else {
            orgCodes.add(orgStructureByCode);
            if (orgStructureByCode.getLevel() == 1) {
                return;
            }
            loadOrgPath(orgCodes, orgStructureByCode.getParentCode());
        }
    }

    private void autoAsyncOrgUnit(AssetOrgUnitImportReq req) {
        List<String> orgCodes = req.getOrgCodes();
        for (String orgCode : orgCodes) {
            AssetOrgUnitQuery unitQuery = AssetOrgUnitQuery.builder().orgCode(orgCode).businessLine(BusinessLine.getByCode(req.getBusinessLine())).build();
            refreshOrgUnitPath(unitQuery);
        }
    }

    private String importOrgUnitAsync(String businessLine, List<String> orgCodes) {
        AssetOrgUnitQuery orgUnitQuery = AssetOrgUnitQuery.builder().orgCodes(orgCodes).businessLine(BusinessLine.getByCode(businessLine)).build();
        List<AssetOrgUnit> orgUnitList = assetOrgRepo.getOrgUnits(orgUnitQuery);
        // 已经存在的数据
        if (CollectionUtils.isNotEmpty(orgUnitList)) {
            List<String> collect = orgUnitList.stream().map(AssetOrgUnit::getOrgCode).collect(Collectors.toList());
            orgCodes.removeAll(collect);
        }
        if (CollectionUtils.isNotEmpty(orgCodes)) {
            // 系统不存在的数据继续导入
            List<AssetOrgStructure> orgStructures = assetOrgRepo.getOrgStructures(orgCodes, "");
            List<AssetOrgUnit> orgUnits = new ArrayList<>(orgStructures.size());
            for (AssetOrgStructure orgStructure : orgStructures) {
                AssetOrgUnit assetOrgUnit = converter.orgStructureToUnit(orgStructure);
                assetOrgUnit.setOrgType(AssetOrgType.DEPARTMENT);
                assetOrgUnit.setOrgCodePath("");
                assetOrgUnit.setOrgNamePath("");
                assetOrgUnit.setBusinessLine(BusinessLine.getByCode(businessLine));
                orgUnits.add(assetOrgUnit);
            }
            assetOrgRepo.batchCreateOrgUnit(orgUnits);
        }
        return null;
    }

    public <T> List<List<T>> splitList(List<T> list, int batchSize) {
        return IntStream.range(0, (list.size() + batchSize - 1) / batchSize)
                .mapToObj(i -> list.subList(i * batchSize, Math.min((i + 1) * batchSize, list.size())))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getParentCodes(String orgCode) {
        List<AssetOrgStructure> orgStructureList = new ArrayList<>();
        loadOrgPath(orgStructureList, orgCode);
        return orgStructureList.stream().map(AssetOrgStructure::getOrgCode).collect(Collectors.toList());
    }

    @Override
    public void refreshOrgUnit(List<String> businessLines) {
        List<BusinessLine> queryBusinessLine = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(businessLines)) {
            for (String businessLine : businessLines) {
                queryBusinessLine.add(BusinessLine.getByCode(businessLine));
            }
        } else {
            queryBusinessLine =  Arrays.asList(BusinessLine.values());
        }
        AssetOrgUnitQuery unitQueryReq = AssetOrgUnitQuery.builder().businessLines(queryBusinessLine).build();
        int pageNum = 1;
        int pageSize = 200;
        int total;
        int done = 0;
        do {
            pageNum++;
            PageData<AssetOrgUnit> unitPageData = assetOrgRepo.orgUnitPageData(unitQueryReq, PageRequest.of(pageSize, pageNum));
            total =  unitPageData.getTotal();
            List<AssetOrgUnit> list = unitPageData.getList();
            done += list.size();
            List<String> orgCodes = list.stream().map(AssetOrgUnit::getOrgCode).collect(Collectors.toList());
            AssetOrgUnitQuery unitQuery = AssetOrgUnitQuery.builder().orgCodes(orgCodes).build();
            refreshOrgUnitPath(unitQuery);
            log.info("total:{}, done:{}", total, done);
        } while (0 != total && done < total);
        log.info("refreshOrgUnit end");
    }

    private void refreshOrgUnitPath(AssetOrgUnitQuery unitQuery) {
        Map<String, AssetOrgUnit> cacheUnitMap = new HashMap<>();
        List<AssetOrgUnit> assetOrgUnits = assetOrgRepo.getOrgUnits(unitQuery);
        assetOrgUnits.forEach(orgUnit -> {
            if (cacheUnitMap.containsKey(orgUnit.getOrgCode())) {
                AssetOrgUnit unit = cacheUnitMap.get(orgUnit.getOrgCode());
                if (unit != null) {
                    orgUnit.setOrgNameEn(unit.getOrgNameEn());
                    orgUnit.setOrgCodePath(unit.getOrgCodePath());
                    orgUnit.setOrgNamePath(unit.getOrgNamePath());
                    orgUnit.setOrgNamePathEn(unit.getOrgNamePathEn());
                    assetOrgRepo.updateOrgUnit(orgUnit);
                }
            } else {
                List<AssetOrgStructure> orgStructureList = new ArrayList<>();
                loadOrgPath(orgStructureList, orgUnit.getOrgCode());
                Collections.reverse(orgStructureList);
                AssetOrgStructure assetOrgStructure = orgStructureList.stream().filter(orgStructure -> Objects.equals(orgStructure.getOrgCode(), orgUnit.getOrgCode()))
                        .findFirst().orElseGet(() -> AssetOrgStructure.builder().build());
                String orgNameEn = StringUtils.isBlank(assetOrgStructure.getOrgNameEn()) ? StringUtils.EMPTY : assetOrgStructure.getOrgNameEn();
                String orgCodePath = orgStructureList.stream().map(AssetOrgStructure::getOrgCode).collect(Collectors.joining("-"));
                String orgNamePath = orgStructureList.stream().map(AssetOrgStructure::getOrgName).collect(Collectors.joining("-"));
                String orgNamePathEn = orgStructureList.stream().map(AssetOrgStructure::getOrgNameEn).collect(Collectors.joining("-"));
                log.info("orgCodePath:{}, orgNamePath:{}, orgNamePathEn:{}", orgCodePath, orgNamePath,  orgNamePathEn);
                orgUnit.setOrgNameEn(orgNameEn);
                orgUnit.setOrgCodePath(orgCodePath);
                orgUnit.setOrgNamePath(orgNamePath);
                orgUnit.setOrgNamePathEn(orgNamePathEn);
                assetOrgRepo.updateOrgUnit(orgUnit);
                AssetOrgUnit build = AssetOrgUnit.builder().orgNameEn(orgNameEn)
                        .orgCodePath(orgCodePath).orgNamePath(orgNamePath).orgNamePathEn(orgNamePathEn).build();
                cacheUnitMap.put(orgUnit.getOrgCode(), build);
            }
        });
    }
}
