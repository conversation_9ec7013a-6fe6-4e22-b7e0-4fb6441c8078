package com.mi.oa.asset.commons.config.app.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.user.EmployeeInfoRes;
import com.mi.oa.asset.commons.config.api.user.UserBaseInfoRes;
import com.mi.oa.asset.commons.config.api.user.UserDeptRes;
import com.mi.oa.asset.commons.config.api.user.UserInfoRes;
import com.mi.oa.asset.eam.feign.res.HrodEmployeeRes;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.idm.api.rep.DeptInfoDto;
import com.mi.oa.infra.oaucf.idm.api.rep.EsAccountDto;
import com.mi.oa.infra.oaucf.idm.api.rep.UserBaseInfoDto;
import com.mi.oa.infra.oaucf.idm.api.rep.UserInfoDto;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/11 14:09
 **/
@Mapper(componentModel = "spring")
public interface UserInfoConverter {

    /**
     * 转换
     *
     * @param userInfoDto
     * @return
     */
    @Mapping(target = "fullDeptName", expression = "java(getFullDeptName(userInfoDto))")
    @Mapping(target = "fullDeptNameEn", expression = "java(getFullDeptNameEn(userInfoDto))")
    UserBaseInfoRes getUserBaseInfo(UserInfoDto userInfoDto);

//    @Mapping(target = "fullDeptName", expression = "java(getFullDeptName(userInfoDto))")
    UserBaseInfoRes getUserBaseInfo(EsAccountDto employeeRes);

    List<UserBaseInfoRes> getUserBaseInfos(List<EsAccountDto> employeeResList);

    @Mapping(target = "deptDesc", source = "deptDescr")
    @Mapping(target = "fullDeptDesc", expression = "java(getFullDeptNameV1(userBaseInfoDto))")
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "companyDescr", ignore = true)
    UserInfoRes userInfoDtoToRes(UserBaseInfoDto userBaseInfoDto);

    List<UserInfoRes> userInfoDtoToResList(List<UserBaseInfoDto> userBaseInfoDtos);

    @Mapping(target = "fullDeptName", expression = "java(getFullDeptNameV1(userBaseInfoDto))")
    UserBaseInfoRes dtoToUserBaseInfo(UserBaseInfoDto userBaseInfoDto);

    List<UserBaseInfoRes> dtoToUserBaseInfos(List<UserBaseInfoDto> userBaseInfoDtos);

    /**
     * 获取部门全称
     *
     * @param userInfoDto
     * @return
     */
    default String getFullDeptName(UserInfoDto userInfoDto) {
        try {
            if (userInfoDto == null) return null;
            if (StringUtils.isNotEmpty(userInfoDto.getFullDeptDescr())) {
                ObjectMapper objectMapper = new ObjectMapper();
                List<UserDeptRes> userDeptDtoList = objectMapper.readValue(userInfoDto.getFullDeptDescr(), new TypeReference<List<UserDeptRes>>() {
                });
                return userDeptDtoList.stream()
                        .filter(userDept -> !( "小米公司".equals(userDept.getDeptName()) && "0".equals(userDept.getLevel()) ) )
                        .map(UserDeptRes::getDeptName)
                        .collect(Collectors.joining("-"));
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }

    /**
     * 获取部门全称
     *
     * @param userInfoDto
     * @return
     */
    default String getFullDeptNameEn(UserInfoDto userInfoDto) {
        try {
            if (userInfoDto == null) return null;
            if (StringUtils.isNotEmpty(userInfoDto.getFullDeptDescr())) {
                ObjectMapper objectMapper = new ObjectMapper();
                List<UserDeptRes> userDeptDtoList = objectMapper.readValue(userInfoDto.getFullDeptDescr(), new TypeReference<List<UserDeptRes>>() {
                });
                return userDeptDtoList.stream()
                        .filter(userDept -> !( "小米公司".equals(userDept.getDeptName()) && "0".equals(userDept.getLevel()) ) )
                        .map(UserDeptRes::getDeptEnName)
                        .collect(Collectors.joining("-"));
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }

    default String getFullDeptNameV1(UserBaseInfoDto userBaseInfoDto) {
        if (Objects.isNull(userBaseInfoDto)) return "";
        try {
            String fullDeptDescr = userBaseInfoDto.getFullDeptDescr();
            if (StringUtils.isNotEmpty(fullDeptDescr)) {
                ObjectMapper objectMapper = new ObjectMapper();
                List<UserDeptRes> userDeptDtoList = objectMapper.readValue(fullDeptDescr, new TypeReference<List<UserDeptRes>>() {
                });

                return userDeptDtoList.stream()
                        .filter(userDept -> !("小米公司".equals(userDept.getDeptName()) && "0".equals(userDept.getLevel())))
                        .map(UserDeptRes::getDeptName)
                        .collect(Collectors.joining("-"));
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }

    @BeanMapping(ignoreByDefault = true)
    @Mapping(source = "uid", target = "uid")
    @Mapping(source = "userName", target = "userName")
    @Mapping(source = "displayName", target = "displayName")
    @Mapping(source = "type", target = "userType")
    @Mapping(source = "deptId", target = "deptCode")
    @Mapping(source = "deptDescr", target = "deptName")
    @Mapping(source = "fullDeptDescr", target = "fullDeptName", qualifiedByName = "fullDeptName")
    @Mapping(source = "headUrl", target = "avatar")
    @Mapping(source = "email", target = "email")
    @Mapping(source = "personId", target = "empCode")
    @Mapping(source = "company", target = "companyCode")
    @Mapping(source = "companyDescr", target = "companyName")
    User toUser(UserBaseInfoDto source);

    List<User> toUser(List<UserBaseInfoDto> source);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(source = "uid", target = "uid")
    @Mapping(source = "userName", target = "userName")
    @Mapping(source = "displayName", target = "displayName")
    @Mapping(source = "type", target = "userType")
    @Mapping(source = "deptId", target = "deptCode")
    @Mapping(source = "deptDescr", target = "deptName")
    @Mapping(source = "fullDeptDescr", target = "fullDeptName", qualifiedByName = "fullDeptName")
    @Mapping(source = "headUrl", target = "avatar")
    @Mapping(source = "email", target = "email")
    @Mapping(source = "personId", target = "empCode")
    @Mapping(source = "costCt", target = "costCenter")
    @Mapping(source = "deptCostCt", target = "deptCostCenter")
    @Mapping(source = "company", target = "companyCode")
    @Mapping(source = "companyDescr", target = "companyName")
    User toUser(UserInfoDto source);

    @Named("fullDeptName")
    default String fullDeptName(String fullDeptDescr) {
        if (StringUtils.isEmpty(fullDeptDescr)) {
            return "";
        }

        List<DeptInfoDto> departments = JacksonUtils.json2List(fullDeptDescr, DeptInfoDto.class);
        return departments.stream()
                .filter(dept -> !"0".equals(dept.getLevel()))
                .map(DeptInfoDto::getDeptName)
                .collect(Collectors.joining("-"));
    }

    EmployeeInfoRes employeeResToEmployeeInfoRes(HrodEmployeeRes employeeRes);
}
