package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.common.enums.BpmLanguage;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.common.enums.YesNo;
import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.domain.bpm.entity.BpmApprovalTask;
import com.mi.oa.asset.commons.config.domain.bpm.entity.BpmApprover;
import com.mi.oa.asset.commons.config.domain.bpm.enums.BpmApproveStatus;
import com.mi.oa.asset.commons.config.domain.bpm.enums.BpmNodeStatus;
import com.mi.oa.infra.oaucf.bpm.rep.ApprovalTaskResp;
import com.mi.oa.infra.oaucf.bpm.rep.BpmUser;
import com.mi.oa.infra.oaucf.bpm.rep.UserTaskSignType;
import com.mi.oa.infra.oaucf.bpm.service.ApprovalService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Description bpm能力层
 * @Date 2024/9/23 11:03
 **/
@Slf4j
@Service
public class BpmApprovalAbility {

    @Resource
    private ApprovalService approvalService;

    @Resource
    private UserInfoService userInfoService;

    private ThreadPoolExecutor bpmThreadPool = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000000));

    public List<BpmApprovalTask> listApprovalHistory(String businessKey, boolean needPredict, String language) {
        language = getLanguage(language);
        BaseResp<List<ApprovalTaskResp>> list = approvalService.list(businessKey, needPredict, language);
        if (list == null || BaseResp.CODE_SUCCESS != list.getCode()) {
            return Collections.emptyList();
        }
        List<ApprovalTaskResp> data = list.getData();
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DateUtils.COMMON_PATTERN);
        List<BpmApprovalTask> approvalHistoryDOList = new ArrayList<>(data.size());
        String lastTaskName = data.get(0).getTaskName();
        boolean firstUncompletedNodeFound = false;
        List<ApprovalTaskResp> taskRespList = new ArrayList<>(data.size());
        List<CompletableFuture<String>> futureList = new ArrayList<>(data.size());
        ConcurrentHashMap<String, String> avatarMap = new ConcurrentHashMap<>();
        // 异步线程池根据userName查询员工头像
        data.stream().filter(task -> task.getAssignee() != null && StringUtils.isNotBlank(task.getAssignee().getUserName()))
                .map(ApprovalTaskResp::getAssignee).map(BpmUser::getUserName).distinct()
                .forEach(userName -> futureList.add(CompletableFuture.supplyAsync(() -> {
                    try {
                        User user = userInfoService.getUserByUserName(userName);
                        if (user != null) {
                            avatarMap.putIfAbsent(userName, user.getAvatar());
                            return user.getAvatar();
                        }
                        return StringUtils.EMPTY;
                    } catch (Exception e) {
                        log.error("查询用户头像失败", e);
                    }
                    return StringUtils.EMPTY;
                }, bpmThreadPool).handle((r, e) -> StringUtils.EMPTY)));
        CompletableFuture<Void> allCompletableFuture = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
        allCompletableFuture.join();
        for (ApprovalTaskResp taskResp : data) {
            String currentTaskName = taskResp.getTaskName();
            // 相同
            if (Objects.equals(currentTaskName, lastTaskName)) {
                taskRespList.add(taskResp);
            } else {
                BpmApprovalTask build = buildBpmApprovalHistory(dateFormat, lastTaskName, taskRespList, avatarMap);
                if (BpmNodeStatus.COMPLETE.getCode().equals(build.getNodeState())) {
                    build.setNodeState(BpmNodeStatus.COMPLETE.getCode());
                    build.setIsPredict(YesNo.NO.getCode());
                } else {
                    if (!firstUncompletedNodeFound) {
                        build.setNodeState(BpmNodeStatus.PENDING.getCode());
                        build.setIsPredict(YesNo.NO.getCode());
                        firstUncompletedNodeFound = true;
                    } else {
                        build.setNodeState(BpmNodeStatus.UNREACHED.getCode());
                        build.setIsPredict(YesNo.YES.getCode());
                    }
                }
                approvalHistoryDOList.add(build);
                taskRespList = new ArrayList<>();
                taskRespList.add(taskResp);
            }
            lastTaskName = currentTaskName;
        }
        if (!taskRespList.isEmpty()) {
            BpmApprovalTask build = buildBpmApprovalHistory(dateFormat, lastTaskName, taskRespList, avatarMap);
            if (BpmNodeStatus.COMPLETE.getCode().equals(build.getNodeState())) {
                build.setNodeState(BpmNodeStatus.COMPLETE.getCode());
                build.setIsPredict(YesNo.NO.getCode());
            } else {
                if (!firstUncompletedNodeFound) {
                    build.setNodeState(BpmNodeStatus.PENDING.getCode());
                    build.setIsPredict(YesNo.NO.getCode());
                } else {
                    build.setNodeState(BpmNodeStatus.UNREACHED.getCode());
                    build.setIsPredict(YesNo.YES.getCode());
                }
            }
            approvalHistoryDOList.add(build);
        }
        return approvalHistoryDOList;
    }

    public String getLanguage(String language) {
        if (StringUtils.isBlank(language)) {
            language = BpmLanguage.ZH.getCode();
        } else {
            if (EAMConstants.CHINESE.equals(language)) {
                language = BpmLanguage.ZH.getCode();
            } else if (EAMConstants.ENGLISH.equals(language)) {
                language = BpmLanguage.EN.getCode();
            }
        }
        return language;
    }

    private BpmApprovalTask buildBpmApprovalHistory(DateTimeFormatter dateFormat, String lastTaskName, List<ApprovalTaskResp> taskRespList, Map<String, String> avatarMap) {
        // 判断节点是审批结束
        boolean isDone = false;
        int taskDoneCount = 0;
        List<BpmApprover> approverDOList = new ArrayList<>(taskRespList.size());
        BpmApprovalTask build = BpmApprovalTask.builder().taskName(lastTaskName).build();
        for (ApprovalTaskResp resp : taskRespList) {
            if (resp.getEndTime() != null) {
                taskDoneCount++;
            }
            BpmApprover approver = BpmApprover.builder()
                    .userName(resp.getAssignee() == null ? StringUtils.EMPTY : resp.getAssignee().getUserName())
                    .displayName(resp.getAssignee() == null ? StringUtils.EMPTY : resp.getAssignee().getDisplayName())
                    .createTime(Objects.isNull(resp.getCreateTime()) ? StringUtils.EMPTY : resp.getCreateTime().plusHours(8).format(dateFormat))
                    .taskId(resp.getTaskId())
                    .operation(resp.getOperation() != null ? resp.getOperation().getCode() : StringUtils.EMPTY)
                    .comment(resp.getComment())
                    .avatar(resp.getAssignee() == null ? StringUtils.EMPTY : StringUtils.isNotBlank(resp.getAssignee().getUserName()) ? avatarMap.getOrDefault(resp.getAssignee().getUserName(), StringUtils.EMPTY) : StringUtils.EMPTY)
                    .build();
            approverDOList.add(approver);
        }
        if (taskRespList.size() == 1) {
            if (taskRespList.get(0).getOperation() != null || taskRespList.get(0).getEndTime() != null) {
                isDone = true;
            }
        } else {
            ApprovalTaskResp approvalTaskResp = taskRespList.get(0);
            // 单人
            if (UserTaskSignType.SINGLE.equals(approvalTaskResp.getSignType())) {
                // isDone = taskDoneCount == 1;
                isDone = taskRespList.stream().allMatch(task -> task.getEndTime() != null);
            } else if (UserTaskSignType.PARALLEL_ONE.equals(approvalTaskResp.getSignType())) {
                // 该节点所有审批人endTime都不为空，则表示已审批
                isDone = taskRespList.stream().allMatch(task -> task.getEndTime() != null);
            } else if (UserTaskSignType.PARALLEL_ALL.equals(approvalTaskResp.getSignType()) || UserTaskSignType.SEQUENTIAL.equals(approvalTaskResp.getSignType())) {
                isDone = taskDoneCount == taskRespList.size();
            }
        }
        for (BpmApprover bpmApprover : approverDOList) {
            if (StringUtils.isBlank(bpmApprover.getOperation()) && !isDone) {
                // 待审批
                bpmApprover.setOperation(BpmApproveStatus.PENDING.getCode());
            }
        }
        build.setTaskList(approverDOList);
        build.setNodeState(isDone ? BpmNodeStatus.COMPLETE.getCode() : BpmNodeStatus.PENDING.getCode());
        UserTaskSignType signType = taskRespList.get(0).getSignType();
        build.setSignType(signType.name());
        return build;
    }

}
