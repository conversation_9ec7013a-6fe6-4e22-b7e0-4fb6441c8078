package com.mi.oa.asset.commons.config.app.web.controller;

/**
 * <AUTHOR>
 * @date 2025/2/21 18:47
 * @description
 */

import com.mi.oa.asset.account.api.assetaccount.AssetAccountProvider;
import com.mi.oa.asset.commons.config.api.auth.QueryDataResourceListReq;
import com.mi.oa.asset.commons.config.api.auth.QueryRemoteDataResourceReq;
import com.mi.oa.asset.commons.config.api.auth.ResourceDimensionRemoteResp;
import com.mi.oa.asset.commons.config.api.auth.ResourceDto;
import com.mi.oa.asset.commons.config.app.ability.AssetAuthAbility;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@HttpApiModule(value = "AssetAuthController", apiController = AssetAuthController.class)
@RestController
@Validated
@Slf4j
public class AssetAuthController {
    @Resource
    private AssetAuthAbility assetAuthAbility;

    @HttpApiDoc(apiName = "权限系统外链获取指定维度下的数据权限", value = "/api/v1/uc/dimension/dataResource", method = MiApiRequestMethod.POST, description = "权限系统外链获取指定维度下的数据权限")
    @PostMapping(value = {"/api/v1/uc/dimension/dataResource"})
    public BaseResp<ResourceDimensionRemoteResp> listAssetFuncAuthData(@RequestBody QueryRemoteDataResourceReq resourceReq) {
        log.info("listAssetFuncAuthData. req:{}", resourceReq);
        return BaseResp.success(assetAuthAbility.listAssetFuncAuthData(resourceReq));
    }


    @HttpApiDoc(apiName = "获取数据权限编码列表，批量查询数据权限列表详情", value = "/api/v1/uc/dimension/dataResource/list", method = MiApiRequestMethod.POST, description = "获取数据权限编码列表，批量查询数据权限列表详情")
    @PostMapping(value = {"/api/v1/uc/dimension/dataResource/list"})
    public Result<List<ResourceDto>> getDataResourceList(@RequestBody QueryDataResourceListReq resourceReq) {
        log.info("getDataResourceList. resourceReq:{}", resourceReq);
        return Result.success(assetAuthAbility.getDataResourceList(resourceReq));
    }
}
