package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.regionconfig.EmployeeRegionCountryRes;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigProvider;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigReq;
import com.mi.oa.asset.commons.config.api.regionconfig.RegionConfigRes;
import com.mi.oa.asset.commons.config.app.ability.RegionConfigAbility;
import com.mi.oa.asset.commons.config.app.converter.RegionConfigConverter;
import com.mi.oa.asset.commons.config.domain.international.repository.RegionConfigRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
@DubboService
public class RegionConfigProviderImpl implements RegionConfigProvider {

    @Resource
    private RegionConfigRepo regionConfigRepo;

    @Resource
    private RegionConfigConverter converter;

    @Resource
    private RegionConfigAbility regionConfigAbility;

    @Override
    public List<RegionConfigRes> getRegionConfigList() {
        return converter.listDoToRes(regionConfigRepo.searchAll());
    }

    @Override
    public List<EmployeeRegionCountryRes> getRegionAndCountrysList() {
        List<EmployeeRegionCountryRes> employeeRegionCountryResList = converter.listDoToEmployeeRegionCountryRes(regionConfigRepo.searchAll());
        return  regionConfigAbility.getRegionAndCountrysList(employeeRegionCountryResList);
    }

    @Override
    public EmployeeRegionCountryRes getRegionAndCountrys(Integer regionConfigId) {
        EmployeeRegionCountryRes employeeRegionCountryRes = converter.doToEmployeeRegionCountryRes(regionConfigRepo.getById(regionConfigId));
        return  regionConfigAbility.getRegionAndCountrys(employeeRegionCountryRes);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(RegionConfigReq req) {
        regionConfigAbility.saveOrUpdate(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByIds(List<Integer> idList) {
        regionConfigAbility.deleteByIds(idList);
    }

}
