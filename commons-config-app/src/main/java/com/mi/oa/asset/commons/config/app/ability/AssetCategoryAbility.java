package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.serialcode.SerialCodeProvider;
import com.mi.oa.asset.commons.config.domain.assetcategory.entity.AssetCategory;
import com.mi.oa.asset.commons.config.domain.assetcategory.repository.AssetCategoryRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:33
 */

@Service
public class AssetCategoryAbility {

    private static final String ROOT_PATH = "0";

    @Resource
    private AssetCategoryRepo assetCategoryRepo;

    @Resource
    private SerialCodeProvider serialCodeProvider;

    @Resource
    private AssetSkuAbility assetSkuAbility;

    public void loadInfoFromParent(AssetCategory assetCategory) {
        if(StringUtils.isBlank(assetCategory.getParentCateCode())) {
            assetCategory.setLevel(1);
            assetCategory.setCatePath(ROOT_PATH);
            return;
        }

        AssetCategory parentCate = assetCategoryRepo.getByCateCode(assetCategory.getBusinessLine().getCode(),assetCategory.getParentCateCode());
        if(null == parentCate) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "上级分类不存在");
        }

        assetCategory.setLevel(parentCate.getLevel() + 1);
        assetCategory.setCatePath(parentCate.getCatePath());
    }

    public AssetCategory loadAssetCategory(Integer cateId) {
        AssetCategory assetCategory = assetCategoryRepo.getAssetCategory(cateId);
        if(null == assetCategory) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "分类不存在");
        }

        return assetCategory;
    }

    public List<AssetCategory> loadAssetCategorys(List<Integer> cateIds) {
        List<AssetCategory> assetCategorys = assetCategoryRepo.getAssetCategories(cateIds);
        if(CollectionUtils.isEmpty(assetCategorys)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "分类不存在");
        }

        return assetCategorys;
    }

    public void genCateCode(AssetCategory assetCategory) {
        if (null == assetCategory.getBusinessLine()) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线不存在");
        }
        //分类编码为空系统自动生成
        if (StringUtils.isBlank(assetCategory.getCateCode())) {
            String cateCode = serialCodeProvider.genSerialCodeWithIndex(assetCategory.getBusinessLine().getCode(), 3, "CATE_CODE");
            assetCategory.setCateCode(cateCode);
        } else {
            //手动填写编码校验所有业务线唯一
            if (assetCategoryRepo.isExists(assetCategory.getCateCode())) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "分类编码重复");
            }
        }
    }

    public void setStdCatePath(List<AssetCategory> stdCategories){
        stdCategories.sort(Comparator.comparingInt(AssetCategory::getLevel));
        stdCategories.forEach(c -> {
            if(c.getLevel() == 1) {
                c.setCatePath(ROOT_PATH + "-" + c.getCateId());
            } else {
                stdCategories.stream()
                        .filter(s -> s.getCateCode().equals(c.getParentCateCode()))
                        .findFirst()
                        .ifPresent(p -> c.setCatePath(p.getCatePath() + "-" + c.getCateId()));
            }
        });
    }

    public List<Integer> getRelationCateIds(AssetCategory assetCategory) {
        if(null == assetCategory) return Collections.emptyList();

        Set<Integer> relationCateIds = new HashSet<>();
        relationCateIds.add(assetCategory.getCateId());
        List<AssetCategory> assetCategories = assetCategoryRepo.getByCatePaths(Collections.singletonList(assetCategory.getCatePath()));
        assetCategories.forEach(c -> relationCateIds.add(c.getCateId()));

        return new ArrayList<>(relationCateIds);
    }

    public List<Integer> getRelationCateIds(Integer cateId) {
        if(null == cateId) return Collections.emptyList();

        AssetCategory assetCategory = loadAssetCategory(cateId);

        return getRelationCateIds(assetCategory);
    }

    public List<Integer> getRelationCateIds(List<String> businessLineCodes) {
        if(CollectionUtils.isEmpty(businessLineCodes)) return Collections.emptyList();

        Set<Integer> relationCateIds = new HashSet<>();
        List<AssetCategory> assetCategories = assetCategoryRepo.getAllAssetCategory(businessLineCodes, true);
        assetCategories.forEach(c -> relationCateIds.add(c.getCateId()));

        return new ArrayList<>(relationCateIds);
    }

    public void loadFullCateName(AssetCategory assetCategory) {
        if(null == assetCategory) return;

        loadFullCateName(Collections.singletonList(assetCategory));
    }

    public void loadFullCateName(List<AssetCategory> assetCategories) {
        Set<Integer> parentIds = new HashSet<>();
        assetCategories.stream().map(AssetCategory::getCatePath)
                .map(s -> s.split("-"))
                .map(s -> Stream.of(s).map(Integer::valueOf).collect(Collectors.toSet()))
                .forEach(parentIds::addAll);

        if(CollectionUtils.isEmpty(parentIds)) {
            return;
        }

        List<AssetCategory> parents = assetCategoryRepo.getAssetCategories(new ArrayList<>(parentIds));
        assetCategories.forEach(i -> {
            List<Integer> ids = Stream.of(i.getCatePath().split("-")).map(Integer::valueOf).collect(Collectors.toList());
            // 按照ids顺序将parents排序,然后拼接成fullCateName
            List<String> names = parents.stream()
                    .filter(p -> ids.contains(p.getCateId()))
                    .sorted(Comparator.comparing(p -> ids.indexOf(p.getCateId())))
                    .map(AssetCategory::getCateName)
                    .collect(Collectors.toList());
            i.setFullCateName(String.join("-", names));
        });
    }

    public void checkCanDelete(AssetCategory assetCategory) {
        List<Integer> relationCateIds = getRelationCateIds(assetCategory);
        if(CollectionUtils.isEmpty(relationCateIds)) return;

        if(assetSkuAbility.hasSku(relationCateIds)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "分类下存在SKU，不可删除");
        }
    }
}
