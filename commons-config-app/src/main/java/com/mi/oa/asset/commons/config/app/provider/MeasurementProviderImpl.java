package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.measurement.MeasurementProvider;
import com.mi.oa.asset.commons.config.api.measurement.MeasurementUnitQueryReq;
import com.mi.oa.asset.commons.config.api.measurement.MeasurementUnitRes;
import com.mi.oa.asset.commons.config.app.converter.MeasurementConverter;
import com.mi.oa.asset.commons.config.domain.assetorganization.valobj.MeasurementUnitQuery;
import com.mi.oa.asset.commons.config.domain.mesurement.entity.MeasurementUnit;
import com.mi.oa.asset.commons.config.domain.mesurement.repository.MeasurementRepo;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.PageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

@Slf4j
@DubboService
public class MeasurementProviderImpl implements MeasurementProvider {

    @Autowired
    private MeasurementRepo measurementRepo;

    @Autowired
    private MeasurementConverter converter;

    @Override
    public PageData<MeasurementUnitRes> getMeasurementUnitPageData(MeasurementUnitQueryReq req, Integer pageNum, Integer pageSize) {
        MeasurementUnitQuery params = converter.toMeasurementUnitQuery(req);

        return converter.toPageData(measurementRepo.measurementUnitPageData(params, PageRequest.of(pageSize, pageNum)), converter::toMeasurementUnitRes);
    }

    @Override
    public List<MeasurementUnitRes> getAllMeasurementUnits() {
        return converter.toMeasurementUnitRes(measurementRepo.queryAllMeasurementUnits());
    }
}
