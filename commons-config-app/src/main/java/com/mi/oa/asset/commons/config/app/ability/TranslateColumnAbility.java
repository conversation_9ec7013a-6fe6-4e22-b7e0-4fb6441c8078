package com.mi.oa.asset.commons.config.app.ability;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.common.enums.ColumnUseWay;
import com.mi.oa.asset.commons.config.domain.function.entity.DictModel;
import com.mi.oa.asset.commons.config.domain.function.entity.ExcelColumnModel;
import com.mi.oa.asset.commons.config.domain.function.repository.FunColumnRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.DictPo;
import com.mi.oa.asset.commons.config.infra.database.mapper.DictMapper;
import com.mi.oa.asset.eam.mybatis.FuncColumnMapper;
import com.mi.oa.asset.eam.mybatis.FuncColumnPo;
import com.mi.oa.asset.eam.mybatis.FunctionMapper;
import com.mi.oa.asset.eam.mybatis.FunctionPo;
import com.mi.oa.asset.eam.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static com.mi.oa.asset.eam.mybatis.SqlConstants.POINT;
import static com.mi.oa.asset.eam.mybatis.SqlConstants.SPECIAL;

@Service
@Slf4j
public class TranslateColumnAbility {
    @Resource
    private FuncColumnMapper funcColumnMapper;

    @Resource
    private FunColumnRepo funColumnRepo;

    @Resource
    private FunctionMapper functionMapper;

    @Resource
    private DictMapper dictMapper;

    @Transactional(rollbackFor = Exception.class)
    public void translateColumn(MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        List<ExcelColumnModel> list = ExcelUtils.readExcel(inputStream, ExcelColumnModel.class, 1);
        if (CollectionUtils.isEmpty(list)) return;

        Map<String, String> map = new HashMap<>();
        for (ExcelColumnModel model : list) {
            map.put(model.getKey() , model.getEnName());
        }

        List<FunctionPo> functionPos = functionMapper.selectList(Wrappers.lambdaQuery(FunctionPo.class));
        Map<String, String> tableMap = new HashMap<>();
        for (FunctionPo functionPo : functionPos) {
            tableMap.put(functionPo.getCode(), functionPo.getTableName());
        }

        List<FuncColumnPo> table = funcColumnMapper.selectList(Wrappers.lambdaQuery(FuncColumnPo.class)
                .eq(FuncColumnPo::getUseWay, ColumnUseWay.TABLE.getCode()));
        if (!CollectionUtils.isEmpty(table)) {
            for (FuncColumnPo column : table) {
                String name = map.get(column.getFuncCode() + POINT + column.getCode());
                if (StringUtils.isEmpty(name)) continue;
                column.setEnCode(name);
            }
        }
        funColumnRepo.batchUpdate(table);
        log.info("table size:{}", table.size());

        List<FuncColumnPo> columnPos = funcColumnMapper.selectList(Wrappers.lambdaQuery(FuncColumnPo.class)
                .eq(FuncColumnPo::getUseWay, ColumnUseWay.FUNC_COLUMN.getCode()));
        if (!CollectionUtils.isEmpty(columnPos)) {
            for (FuncColumnPo column : columnPos) {
                String key = column.getCode();
                if (!key.contains(POINT)) {
                    String funcCode = column.getFuncCode();
                    if (StringUtils.isEmpty(funcCode)) continue;
                    String tableName = tableMap.get(funcCode);
                    if (StringUtils.isEmpty(tableName)) continue;
                    key = tableName + POINT + column.getCode();
                }
                String name = map.get(key);
                if (StringUtils.isEmpty(name)) continue;
                column.setEnCode(name);
            }
        }
        funColumnRepo.batchUpdate(columnPos);
        log.info("columnPos size:{}", columnPos.size());

        List<FuncColumnPo> formList = funcColumnMapper.selectList(Wrappers.lambdaQuery(FuncColumnPo.class)
                .eq(FuncColumnPo::getUseWay, ColumnUseWay.FUNC_FORM.getCode()));
        if (!CollectionUtils.isEmpty(formList)) {
            for (FuncColumnPo column : formList) {
                String funcCode = column.getFuncCode();
                if (StringUtils.isEmpty(funcCode)) continue;
                String tableName = tableMap.get(funcCode);
                if (StringUtils.isEmpty(tableName)) continue;
                String key = tableName + POINT + camelToSnake(column.getCode());
                String name = map.get(key);
                if (StringUtils.isEmpty(name)) continue;
                column.setEnCode(name);
            }
        }
        funColumnRepo.batchUpdate(formList);
        log.info("formList size:{}", formList.size());

    }

    public static String camelToSnake(String str) {
        if (str == null || str.isEmpty()) return str;
        StringBuilder result = new StringBuilder();
        result.append(Character.toLowerCase(str.charAt(0)));
        for (int i = 1; i < str.length(); i++) {
            char ch = str.charAt(i);
            if (Character.isUpperCase(ch)) {
                result.append('_').append(Character.toLowerCase(ch));
            } else {
                result.append(ch);
            }
        }
        return result.toString();
    }

    public void translateDict(MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        List<DictModel> list = ExcelUtils.readExcel(inputStream, DictModel.class, 1);
        if (CollectionUtils.isEmpty(list)) return;
        Map<String, DictModel> dictModelMap = new HashMap<>();
        for (DictModel model : list) {
            dictModelMap.put(model.getCode() + SPECIAL + model.getParentCode(), model);
        }
        List<DictPo> dictList = dictMapper.selectList(Wrappers.lambdaQuery(DictPo.class));
        if (CollectionUtils.isEmpty(dictList)) return;
        for (DictPo po : dictList) {
            String key = po.getCode() + SPECIAL + po.getParentCode();
            DictModel model = dictModelMap.get(key);
            if (Objects.isNull(model)) {
                log.info("translateDict key :{} is null, id: {}", key, po.getId());
                continue;
            }
            po.setEnName(model.getEnName());
            dictMapper.updateById(po);
        }


    }
}
