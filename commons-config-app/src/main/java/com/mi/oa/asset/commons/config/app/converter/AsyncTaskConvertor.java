package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.common.AsyncTaskReq;
import com.mi.oa.asset.commons.config.api.common.AsyncTaskRes;
import com.mi.oa.asset.commons.config.domain.common.entity.AsyncTask;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2024/7/12 10:06
 */

@Mapper(componentModel = "spring")
public interface AsyncTaskConvertor {

    AsyncTask reqToDo(AsyncTaskReq asyncTaskReq);

    AsyncTaskRes doToRes(AsyncTask asyncTask);
}
