package com.mi.oa.asset.commons.config.app.ability;


import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLExpr;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.statement.SQLColumnDefinition;
import com.alibaba.druid.sql.ast.statement.SQLCreateTableStatement;
import com.alibaba.druid.util.JdbcConstants;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.CaseFormat;
import com.mi.info.infra.moon.dto.ClientSingleResponse;
import com.mi.oa.asset.common.enums.ColumnUseWay;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.common.enums.ManageType;
import com.mi.oa.asset.commons.config.api.common.ResultCode;
import com.mi.oa.asset.commons.config.api.function.*;
import com.mi.oa.asset.commons.config.app.converter.DictConverter;
import com.mi.oa.asset.commons.config.app.converter.DownTaskConverter;
import com.mi.oa.asset.commons.config.app.converter.FunctionConverter;
import com.mi.oa.asset.commons.config.app.converter.GlobalValConverter;
import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.domain.common.repository.BusinessLineRepo;
import com.mi.oa.asset.commons.config.domain.function.entity.Dict;
import com.mi.oa.asset.commons.config.domain.function.entity.FuncDict;
import com.mi.oa.asset.commons.config.domain.function.entity.TableModel;
import com.mi.oa.asset.commons.config.domain.function.enums.*;
import com.mi.oa.asset.commons.config.domain.function.repository.FunColumnRepo;
import com.mi.oa.asset.commons.config.domain.function.repository.FuncDictRelRepo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.*;
import com.mi.oa.asset.commons.config.infra.database.mapper.*;
import com.mi.oa.asset.commons.config.infra.repository.converter.DictPoConverter;
import com.mi.oa.asset.commons.config.infra.repository.converter.GlobalValPoConverter;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.mi.oa.asset.eam.auth.data.DataPermissionService;
import com.mi.oa.asset.eam.auth.enums.PermissionType;
import com.mi.oa.asset.eam.feign.service.EamFuncBaseSyncService;
import com.mi.oa.asset.eam.mybatis.*;
import com.mi.oa.infra.oaucf.newauth.autoconfig.authority.AuthorityProperties;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.RoleResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.remote.RoleService;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.mi.oa.infra.uc.common.enmu.RoleStatusEnum;
import com.mi.oa.infra.uc.common.util.PageModel;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mi.oa.asset.eam.auth.data.DataPermissionService.FUN_COL_SQL;
import static com.mi.oa.asset.eam.mybatis.SqlConstants.COLON;

@Service
@Slf4j
public class FunctionAbility {
    @Resource
    private DictConverter dictConverter;

    @Resource
    private BusinessLineMapper businessLineMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private AuthorityProperties authorityProperties;

    @Resource
    private EamFuncBaseSyncService eamFuncSyncService;

    @Resource
    private FuncColMapper funcColMapper;

    @Resource
    private RoleService roleService;

    @Resource
    private DictPoConverter dictPoConverter;

    @Resource
    private FuncColumnMapper funcColumnMapper;

    @Resource
    private FunctionConverter functionConverter;

    @Resource
    private FunColumnRepo funColumnRepo;

    @Resource
    private FunctionDictMapper functionDictMapper;

    @Resource
    private FuncControlMapper funcControlMapper;

    @Resource
    private FuncDictRelRepo funcDictRelRepo;

    @Resource
    private FunctionMapper functionMapper;

    @Resource
    private DataPermissionMapper dataPermissionMapper;

    @Resource
    private DataPermissionService dataPermissionService;

    @Resource
    private FuncTableMapper funcTableMapper;

    @Resource
    private DictMapper dictMapper;

    @Resource
    private FuncDictMapper funcDictMapper;

    @Resource
    private GlobalValMapper globalValMapper;

    @Resource
    private GlobalValConverter globalValConverter;

    @Resource
    private DownTaskConverter downTaskConverter;

    @Resource
    private DownloadTaskMapper downloadTaskMapper;

    @Resource
    private GlobalValPoConverter globalValPoConverter;

    @Resource
    private BusinessLineRepo businessLineRepo;


    public PageData<FunctionRes> listFunc(FuncQueryReq query) {
        String key = query.getKey();
        LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(FunctionPo.class)
                .nested(StringUtils.isNotEmpty(key), q -> q.like(FunctionPo::getEnName, key).or()
                        .like(FunctionPo::getTableName, key).or().like(FunctionPo::getCode, key).or().like(FunctionPo::getFunName, key)).orderByDesc(FunctionPo::getUpdateTime);
        Page<FunctionPo> page = functionMapper.selectPage(new Page<>(query.getPageNum(), query.getPageSize()), wrapper);
        return functionConverter.toPageData(page, functionConverter::toFunctionResList);
    }

    public PageData<FuncTableRes> listTable(FuncQueryReq query) {
        String key = query.getKey();
        LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(FuncTablePo.class)
                .nested(StringUtils.isNotEmpty(key), q -> q.like(FuncTablePo::getTitle, key).or()
                        .like(FuncTablePo::getTableName, key).or().like(FuncTablePo::getRemark, key)).orderByDesc(FuncTablePo::getUpdateTime);
        Page<FuncTablePo> page = funcTableMapper.selectPage(new Page<>(query.getPageNum(), query.getPageSize()), wrapper);
        return functionConverter.toPageData(page, functionConverter::toFuncTableResList);
    }

    public PageData<FuncFieldRes> listFuncField(FieldQueryReq query) {
        Integer id = query.getId();
        if (Objects.nonNull(id)) {
            LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(FuncColumnPo.class).eq(FuncColumnPo::getId, id);
            Page<FuncColumnPo> page = funcColumnMapper.selectPage(new Page<>(1, 1), wrapper);
            return functionConverter.toPageData(page, functionConverter::toFuncFieldList);
        }
        String funcCode = query.getFuncCode();
        String businessLine = query.getBusinessLine();
        if (FuncManageType.SYSTEM.getCode().equals(query.getManageType())) {
            businessLine = StringUtils.EMPTY;
        }
        if (StringUtils.isEmpty(funcCode)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "功能编码不能为空");
        }
        if (!FuncManageType.SYSTEM.getCode().equals(query.getManageType()) && StringUtils.isEmpty(businessLine)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "非系统默认时业务线不能为空");
        }
        String key = query.getKey();
        LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(FuncColumnPo.class)
                .eq(FuncColumnPo::getFuncCode, funcCode)
                .eq(Objects.nonNull(businessLine), FuncColumnPo::getBusinessLine, businessLine)
                .eq(FuncColumnPo::getUseWay, query.getUseWay())
                .nested(StringUtils.isNotEmpty(key), q -> q.like(FuncColumnPo::getCode, key).or()
                        .like(FuncColumnPo::getName, key)).orderByDesc(FuncColumnPo::getUpdateTime);
        Page<FuncColumnPo> page = funcColumnMapper.selectPage(new Page<>(query.getPageNum(), query.getPageSize()), wrapper);
        return functionConverter.toPageData(page, functionConverter::toFuncFieldList);
    }

    public List<FormFieldRes> getFieldMap(FormFieldReq req) {
        String businessLine = req.getBusinessLine();
        String manageLine = req.getManageLine();
        if (StringUtils.isEmpty(businessLine) && StringUtils.isEmpty(manageLine)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "业务线或管理线不能为空");
        }
        LambdaQueryWrapper<FuncColumnPo> wrapper = Wrappers.lambdaQuery(FuncColumnPo.class)
                .eq(FuncColumnPo::getFuncCode, req.getFuncCode())
                .eq(FuncColumnPo::getUseWay, ColumnUseWay.FUNC_FORM.getCode());
        List<FuncColumnPo> list = funcColumnMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(list)) return new ArrayList<>();
        //取当前业务线，没有则取默认系统
        businessLine = StringUtils.isEmpty(businessLine) ? manageLine : businessLine;
        List<FuncColumnPo> businessList = new ArrayList<>();
        List<FuncColumnPo> defaultList = new ArrayList<>();
        for (FuncColumnPo columnPo : list) {
            if (businessLine.equals(columnPo.getBusinessLine())) {
                businessList.add(columnPo);
            }
            if (ManageType.SYSTEM.getCode().equals(columnPo.getManageType())) {
                defaultList.add(columnPo);
            }
        }
        list = CollectionUtils.isEmpty(businessList) ? defaultList : businessList;
        Map<String, List<FuncColumnPo>> map = list.stream().collect(Collectors.groupingBy(FuncColumnPo::getGroupCode));
        Map<String, DictPo> dictMap = getDictMap(map.keySet());
        List<FormFieldRes> result = new ArrayList<>();
        for (String group : map.keySet()) {
            FormFieldRes res = new FormFieldRes();
            res.setGroupCode(group);
            DictPo dictPo = dictMap.get(group);
            if (Objects.nonNull(dictPo)) {
                res.setGroupName(dictPo.getName());
                res.setEnName(dictPo.getEnName());
            }
            List<FuncFieldRes> fieldRes = functionConverter.toFuncFieldList(map.get(group));
            fillDictList(fieldRes);
            res.setFields(fieldRes);
            result.add(res);
        }
        return result;
    }

    protected void fillDictList(List<FuncFieldRes> fieldRes) {
        if (CollectionUtils.isEmpty(fieldRes)) return;
        List<String> controlCodeList = fieldRes.stream().map(FuncFieldRes::getControlCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(controlCodeList)) return;
        List<DictPo> dictList = dictMapper.selectList(Wrappers.lambdaQuery(DictPo.class).in(DictPo::getParentCode, controlCodeList));
        if (CollectionUtils.isEmpty(dictList)) return;
        Map<String, List<DictPo>> dictListMap = dictList.stream().collect(Collectors.groupingBy(DictPo::getParentCode));
        for (FuncFieldRes field : fieldRes) {
            String controlCode = field.getControlCode();
            if (StringUtils.isEmpty(controlCode)) continue;
            field.setDictList(dictConverter.poToResList(dictListMap.get(controlCode)));
        }
    }

    private Map<String, DictPo> getDictMap(Set<String> code) {
        List<DictPo> dictList = dictMapper.selectList(Wrappers.lambdaQuery(DictPo.class)
                .eq(DictPo::getType, DictType.VALUE.getCode()).in(DictPo::getCode, code));
        Map<String, DictPo> dictMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(dictList)) {
            for (DictPo dictPo : dictList) {
                dictMap.put(dictPo.getCode(), dictPo);
            }
        }
        return dictMap;
    }

    public PageData<FuncFieldRes> listTableField(FieldQueryReq query) {
        Integer id = query.getId();
        if (Objects.nonNull(id)) {
            LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(FuncColumnPo.class).eq(FuncColumnPo::getId, id);
            Page<FuncColumnPo> page = funcColumnMapper.selectPage(new Page<>(1, 1), wrapper);
            return functionConverter.toPageData(page, functionConverter::toFuncFieldList);
        }
        String tableCode = query.getTableCode();
        String key = query.getKey();
        if (StringUtils.isEmpty(tableCode)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "表编码不能为空");
        }
        //查询已存在的编码
        Integer manageType = query.getManageType();
        String businessLine = query.getBusinessLine();
        if (FuncManageType.SYSTEM.getCode().equals(query.getManageType())) {
            businessLine = StringUtils.EMPTY;
        }
        Integer useWay = query.getUseWay();
        String funcCode = query.getFuncCode();
        Map<String, String> exsitMap = new HashMap<>();
        if (Objects.nonNull(manageType) && Objects.nonNull(useWay) && StringUtils.isNotEmpty(funcCode)) {
            if (!FuncManageType.SYSTEM.getCode().equals(query.getManageType()) && StringUtils.isEmpty(businessLine)) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "非系统默认时业务线不能为空");
            }
            LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(FuncColumnPo.class)
                    .eq(FuncColumnPo::getFuncCode, funcCode)
                    .eq(FuncColumnPo::getUseWay, useWay)
                    .eq(FuncColumnPo::getManageType, manageType)
                    .eq(FuncColumnPo::getBusinessLine, businessLine);
            List<FuncColumnPo> list = funcColumnMapper.selectList(wrapper);
            if (!CollectionUtils.isEmpty(list)) {
                FunctionPo function = functionMapper.selectOne(Wrappers.lambdaQuery(FunctionPo.class).eq(FunctionPo::getCode, funcCode));
                for (FuncColumnPo funcColumnPo : list) {
                    String colCode = funcColumnPo.getCode();
                    if (!colCode.contains(SqlConstants.POINT)) {
                        colCode = function.getTableName() + SqlConstants.POINT + colCode;
                    }
                    exsitMap.put(colCode, colCode);
                }
                exsitMap = list.stream().collect(Collectors.toMap(FuncColumnPo::getCode, FuncColumnPo::getCode));
            }
        }

        LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(FuncColumnPo.class)
                .eq(FuncColumnPo::getFuncCode, tableCode)
                .eq(FuncColumnPo::getUseWay, ColumnUseWay.TABLE.getCode())
                .nested(StringUtils.isNotEmpty(key), q -> q.like(FuncColumnPo::getCode, key).or()
                        .like(FuncColumnPo::getName, key)).orderByDesc(FuncColumnPo::getUpdateTime);
        Page<FuncColumnPo> page = funcColumnMapper.selectPage(new Page<>(query.getPageNum(), query.getPageSize()), wrapper);
        PageData<FuncFieldRes> resPageData = functionConverter.toPageData(page, functionConverter::toFuncFieldList);
        List<FuncFieldRes> out = resPageData.getList();
        if (!CollectionUtils.isEmpty(out)) {
            for (FuncFieldRes i : out) {
                String code = i.getFuncCode() + SqlConstants.POINT + i.getCode();
                if (Objects.nonNull(exsitMap.get(code))) {
                    i.setExist(true);
                }
            }
        }
        return resPageData;
    }

    public void importField(FieldImportReq importReq) {
        log.info("importField: {}", JacksonUtils.bean2Json(importReq));
        String funcCode = importReq.getFuncCode();
        String businessLine = importReq.getBusinessLine();
        if (FuncManageType.SYSTEM.getCode().equals(importReq.getManageType())) {
            businessLine = StringUtils.EMPTY;
        }
        Integer useWay = importReq.getUseWay();
        List<Integer> fieldIds = importReq.getFieldIds();
        if (StringUtils.isEmpty(funcCode) || Objects.isNull(useWay) || CollectionUtils.isEmpty(fieldIds)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "参数不能为空");
        }
        if (!FuncManageType.SYSTEM.getCode().equals(importReq.getManageType()) && StringUtils.isEmpty(businessLine)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "非系统默认时业务线不能为空");
        }
        ColumnUseWay columnUseWay = ColumnUseWay.getByCode(useWay);
        if (Objects.isNull(columnUseWay)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "用途不合法");
        }
        List<FuncColumnPo> list = funcColumnMapper.selectList(Wrappers.lambdaQuery(FuncColumnPo.class).in(FuncColumnPo::getId, fieldIds));
        if (CollectionUtils.isEmpty(list)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "字段不存在");
        }
        FuncColumnPo columnPo = list.get(0);
        String table = columnPo.getFuncCode();
        if (!ColumnUseWay.TABLE.getCode().equals(columnPo.getUseWay())) {
            FunctionPo functionPo = functionMapper.selectOne(Wrappers.lambdaQuery(FunctionPo.class).eq(FunctionPo::getCode, funcCode));
            table = functionPo.getTableName();
        }
        //过滤已存在的，避免重复导入
        Map<String, String> existMap = getExistMap(table, list, businessLine, importReq);
        List<FuncColumnPo> saveList = new ArrayList<>();

        for (FuncColumnPo po : list) {
            FuncColumnPo column = functionConverter.copy(po);
            if (ColumnUseWay.FUNC_COLUMN.getCode().equals(useWay)
                    && !po.getCode().contains(SqlConstants.POINT)) {
                column.setCode(table + SqlConstants.POINT + po.getCode());
            } else if (ColumnUseWay.FUNC_FORM.getCode().equals(useWay)){
                String code = CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, po.getCode());
                column.setCode(code);
            } else {
                column.setCode(po.getCode());
            }
            if (Objects.nonNull(existMap.get(column.getCode()))) continue;
            column.setBusinessLine(businessLine);
            column.setManageType(importReq.getManageType());
            column.setFuncCode(funcCode);
            column.setUseWay(useWay);
            column.setId(null);
            saveList.add(column);
        }
        funColumnRepo.batchSave(saveList);
        //删除功能列缓存
        if (ColumnUseWay.FUNC_COLUMN.getCode().equals(importReq.getUseWay())) {
            deleteCacheCol(FUN_COL_SQL + funcCode + COLON);
        }
    }

    private List<String> getTableCode(String table, List<FuncColumnPo> list, FieldImportReq importReq) {
        List<String> codeList = new ArrayList<>();
        for (FuncColumnPo po : list) {
            String code = po.getCode();
            if (!code.contains(SqlConstants.POINT) && ColumnUseWay.FUNC_COLUMN.getCode().equals(importReq.getUseWay())) {
                code = table + SqlConstants.POINT + code;
            }
            if (ColumnUseWay.FUNC_FORM.getCode().equals(importReq.getUseWay())) {
                code = CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, po.getCode());
            }
            codeList.add(code);
        }
        return codeList;
    }

    private Map<String, String> getExistMap(String table, List<FuncColumnPo> list, String businessLine, FieldImportReq importReq) {
        Map<String, String> existMap = new HashMap<>();
        String funcCode = importReq.getFuncCode();
        List<String> codeList = getTableCode(table, list, importReq);
        //过滤已存在的，避免重复导入
        List<FuncColumnPo> exist = funcColumnMapper.selectList(Wrappers.lambdaQuery(FuncColumnPo.class)
                .in(FuncColumnPo::getCode, codeList)
                .eq(StringUtils.isNotEmpty(businessLine), FuncColumnPo::getBusinessLine, businessLine)
                .eq(FuncColumnPo::getUseWay, importReq.getUseWay())
                .eq(StringUtils.isNotEmpty(funcCode), FuncColumnPo::getFuncCode, funcCode)
                .eq(Objects.nonNull(importReq.getManageType()), FuncColumnPo::getManageType, importReq.getManageType()));
        if (!CollectionUtils.isEmpty(exist)) {
            exist.forEach(i -> existMap.put(i.getCode(), i.getCode()));
        }
        return existMap;
    }

    public void saveField(List<FuncFieldReq> saveReq) {
        List<FuncColumnPo> list = functionConverter.reqToPoList(saveReq);
        List<FuncColumnPo> addList = new ArrayList<>();
        List<FuncColumnPo> updList = new ArrayList<>();
        for (FuncColumnPo funcColumnPo : list) {
            Integer id = funcColumnPo.getId();
            if (Objects.isNull(id)) {
                addList.add(funcColumnPo);
            } else {
                updList.add(funcColumnPo);
            }
        }
        funColumnRepo.batchSave(addList);
        funColumnRepo.batchUpdate(updList);

    }

    public void saveDictAuth(FuncDictAuthReq saveReq) {
        Integer type = saveReq.getType();
        String businessLine = saveReq.getBusinessLine();
        String funcCode = saveReq.getFuncCode();
        List<FuncDictRelReq> authList = saveReq.getAuthList();
        String code = saveReq.getCode();
        Integer dictId = saveReq.getDictId();
        if (StringUtils.isEmpty(businessLine) || StringUtils.isEmpty(funcCode)
                || Objects.isNull(type) || CollectionUtils.isEmpty(authList) || Objects.isNull(dictId)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "参数不能为空");
        }
        Set<String> roleList = new HashSet<>();
        for (FuncDictRelReq relReq : authList) {
            roleList.add(relReq.getRole());
        }
        //查询已有的角色
        LambdaQueryWrapper<FuncDictRelPo> wrapper = Wrappers.lambdaQuery(FuncDictRelPo.class)
                .eq(FuncDictRelPo::getBusinessLine, businessLine)
                .eq(FuncDictRelPo::getType, type)
                .eq(FuncDictRelPo::getFuncCode, funcCode)
                .eq(FuncDictRelPo::getCode, code)
                .eq(FuncDictRelPo::getDictId, dictId)
                .in(FuncDictRelPo::getRole, roleList);
        List<FuncDictRelPo> existList = functionDictMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(existList)) {
            //过滤已存在的数据
            List<String> exist = existList.stream().map(FuncDictRelPo::getRole).collect(Collectors.toList());
            authList = authList.stream().filter(i -> !exist.contains(i.getRole())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(authList)) return;
        funcDictRelRepo.batchSave(functionConverter.reqToDictPoList(authList));
    }

    public List<FuncDictRelRes> listDictAuth(FuncDictAuthReq saveReq) {
        Integer type = saveReq.getType();
        String businessLine = saveReq.getBusinessLine();
        String funcCode = saveReq.getFuncCode();
        String code = saveReq.getCode();
        if (Objects.isNull(type)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "类型不能为空");
        }
        checkDictAuthReq(saveReq);
        LambdaQueryWrapper<FuncDictRelPo> wrapper = Wrappers.lambdaQuery(FuncDictRelPo.class)
                .eq(FuncDictRelPo::getBusinessLine, businessLine)
                .eq(FuncDictRelPo::getType, type)
                .eq(FuncDictRelPo::getFuncCode, funcCode)
                .eq(FuncDictRelPo::getCode, code);
        return functionConverter.toDictRelResList(functionDictMapper.selectList(wrapper));
    }

    public void deleteDictAuth(List<Integer> ids) {
        functionDictMapper.deleteBatchIds(ids);
    }

    public FunctionRes findFunctionById(Integer id) {
        return functionConverter.toFunctionRes(functionMapper.selectById(id));
    }

    public Integer saveFunction(FunctionReq req) {
        FunctionPo po = functionConverter.toFunctionPo(req);
        if (Objects.isNull(req.getId())) {
            List<FunctionPo> list = functionMapper.selectList(Wrappers.lambdaQuery(FunctionPo.class).eq(FunctionPo::getCode, req.getCode()));
            if (!CollectionUtils.isEmpty(list)) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "功能编码不能重复");
            }
            functionMapper.insert(po);
        } else {
            String code = req.getCode();
            FunctionPo old = functionMapper.selectById(req.getId());
            if (Objects.isNull(old)) return 0;
            String odlCode = old.getCode();
            String newCode = req.getCode();
            if (StringUtils.isNotEmpty(code) && !odlCode.equals(newCode)) {
                List<FunctionPo> list = functionMapper.selectList(Wrappers.lambdaQuery(FunctionPo.class).eq(FunctionPo::getCode, code));
                //如果要修改的功能编码已存在，则不允许修改
                if (!CollectionUtils.isEmpty(list)) {
                    throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "功能编码不能重复");
                }
            }
            boolean hasModified = !odlCode.equals(newCode);
            functionMapper.updateById(po);
            //修改功能编码，需要同步修改关联表的功能编码
            if (!hasModified) return po.getId();
            //修改数据权限 amg_data_permission
            updateDataPermissionFunCode(odlCode, newCode);
            //修改功能列 amg_func_column
            updateFunColumnFunCode(odlCode, newCode);
        }
        return po.getId();
    }


    private void updateDataPermissionFunCode(String oldCode, String newCode) {
        if (StringUtils.isEmpty(newCode)) return;
        List<DataPermissionPo> list = dataPermissionMapper.selectList(Wrappers.lambdaQuery(DataPermissionPo.class).eq(DataPermissionPo::getCode, oldCode));
        if (CollectionUtils.isEmpty(list)) return;
        list.stream().parallel().forEach(i -> {
            i.setCode(newCode);
            dataPermissionMapper.updateById(i);
        });
    }

    private void updateFunColumnFunCode(String oldCode, String newCode) {
        if (StringUtils.isEmpty(newCode)) return;
        List<FuncColumnPo> list = funcColumnMapper.selectList(Wrappers.lambdaQuery(FuncColumnPo.class)
                .eq(FuncColumnPo::getFuncCode, oldCode)
                .ne(FuncColumnPo::getUseWay, ColumnUseWay.TABLE.getCode()));
        if (CollectionUtils.isEmpty(list)) return;
        list.stream().parallel().forEach(i -> {
            i.setFuncCode(newCode);
            funcColumnMapper.updateById(i);
        });
    }

    public void deleteFunctionById(List<Integer> ids) {
        functionMapper.deleteBatchIds(ids);
    }

    public FuncDataPermissionRes getDataAuth(String funCode, String businessLine) {
        List<DataPermissionPo> poList = dataPermissionMapper.selectList(Wrappers.lambdaQuery(DataPermissionPo.class)
                .eq(DataPermissionPo::getCode, funCode)
                .eq(DataPermissionPo::getBusinessLine, StringUtils.isEmpty(businessLine) ? StringUtils.EMPTY : businessLine));
        if (CollectionUtils.isEmpty(poList)) return null;
        FuncDataPermissionRes res = new FuncDataPermissionRes();
        res.setFuncCode(funCode);
        List<FuncExtSqlReq> extList = new ArrayList<>();
        List<FuncResourceReq> resourceList = new ArrayList<>();
        for (DataPermissionPo data : poList) {
            if (Objects.isNull(data.getSourceType())) continue;
            if (PermissionType.EXT.getCode().equals(data.getSourceType())) {
                extList.add(new FuncExtSqlReq(data.getId(), data.getExtSql(), data.getRole(), data.getRoleName()));
            } else if (PermissionType.RESOURCE.getCode().equals(data.getSourceType())) {
                resourceList.add(new FuncResourceReq(data.getId(), data.getDimensionCode(), data.getExtSql(), data.getRole(), data.getRoleName()));
            }
        }
        res.setExtList(extList);
        res.setResourceList(resourceList);

        FunctionPo function = functionMapper.selectOne(Wrappers.lambdaQuery(FunctionPo.class).eq(FunctionPo::getCode, funCode));
        if (Objects.isNull(function)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "功能不存在");
        }
        String fast = function.getRemark();
        if (StringUtils.isNotEmpty(fast)) {
            res.setFuncFastAuthReq(JacksonUtils.json2Bean(fast, FuncFastAuthReq.class));
        }
        return res;
    }

    public void saveDataAuth(FuncDataPermissionReq req) {
        String businessLine = req.getBusinessLine();
        businessLine = StringUtils.isEmpty(businessLine) ? StringUtils.EMPTY : businessLine;
        Map<String, String> roleMap = findRoleMap();
        List<DataPermissionPo> dataPermissionList = buildSaveDataPo(req, roleMap);

        FunctionPo function = functionMapper.selectOne(Wrappers.lambdaQuery(FunctionPo.class).eq(FunctionPo::getCode, req.getFuncCode()));
        if (Objects.isNull(function)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "功能不存在");
        }

        List<DataPermissionPo> list = dataPermissionMapper.selectList(Wrappers.lambdaQuery(DataPermissionPo.class)
                .eq(DataPermissionPo::getCode, req.getFuncCode())
                .eq(DataPermissionPo::getBusinessLine, businessLine));
        Map<Integer, DataPermissionPo> existMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (DataPermissionPo po : list) {
                existMap.put(po.getId(), po);
            }
        }

        List<FuncExtSqlReq> addExtSqlRole = req.getAddExtList();
        if (!CollectionUtils.isEmpty(addExtSqlRole)) {
            for (FuncExtSqlReq sqlReq : addExtSqlRole) {
                Integer id = sqlReq.getId();
                if (Objects.nonNull(id)) {
                    DataPermissionPo po = existMap.get(id);
                    if (Objects.isNull(po)) {
                        throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "id不存在");
                    }
                    po.setRole(sqlReq.getRole());
                    po.setExtSql(sqlReq.getExtSql());
                    po.setRoleName(roleMap.get(sqlReq.getRole()));
                    dataPermissionMapper.updateById(po);
                } else {
                    DataPermissionPo dataPermissionPo = buildDataPermissionPo(roleMap, sqlReq.getRole(), req, PermissionType.EXT);
                    dataPermissionPo.setExtSql(sqlReq.getExtSql());
                    dataPermissionMapper.insert(dataPermissionPo);
                }
            }
        }

        List<FuncResourceReq> addResourceList = req.getAddResourceList();
        if (!CollectionUtils.isEmpty(addResourceList)) {
            for (FuncResourceReq sqlReq : addResourceList) {
                Integer id = sqlReq.getId();
                if (Objects.nonNull(id)) {
                    DataPermissionPo po = existMap.get(id);
                    if (Objects.isNull(po)) {
                        throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "id不存在");
                    }
                    po.setRole(sqlReq.getRole());
                    po.setExtSql(sqlReq.getExtSql());
                    po.setDimensionCode(sqlReq.getResource());
                    po.setRoleName(roleMap.get(sqlReq.getRole()));
                    dataPermissionMapper.updateById(po);
                } else {
                    DataPermissionPo dataPermissionPo = buildDataPermissionPo(roleMap, sqlReq.getRole(), req, PermissionType.RESOURCE);
                    dataPermissionPo.setExtSql(sqlReq.getExtSql());
                    dataPermissionPo.setDimensionCode(sqlReq.getResource());
                    dataPermissionMapper.insert(dataPermissionPo);
                }
            }
        }


        List<Integer> delIds = new ArrayList<>();

        List<FuncExtSqlReq> delExtSqlRole = req.getDelExtList();
        if (!CollectionUtils.isEmpty(delExtSqlRole)) {
            List<Integer> ids = delExtSqlRole.stream().map(FuncExtSqlReq::getId).filter(Objects::nonNull).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(ids)) {
                delIds.addAll(ids);
            }
        }
        List<FuncResourceReq> delResourceList = req.getDelResourceList();
        if (!CollectionUtils.isEmpty(delResourceList)) {
            List<Integer> ids = delResourceList.stream().map(FuncResourceReq::getId).filter(Objects::nonNull).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(ids)) {
                delIds.addAll(ids);
            }
        }
        if (!CollectionUtils.isEmpty(delIds)) {
            dataPermissionMapper.deleteBatchIds(delIds);
        }

        //保存快速授权
        FuncFastAuthReq funcFastAuthReq = req.getFuncFastAuthReq();
        if (Objects.nonNull(funcFastAuthReq)) {
            function.setRemark(JacksonUtils.bean2Json(funcFastAuthReq));
            functionMapper.updateById(function);
        }
        
    }

    private List<DataPermissionPo> buildSaveDataPo(FuncDataPermissionReq req, Map<String, String> roleMap) {
        List<FuncExtSqlReq> addExtSqlRole = req.getAddExtList();
        List<DataPermissionPo> dataPermissionList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(addExtSqlRole)) {
            for (FuncExtSqlReq extSqlReq : addExtSqlRole) {
                String role = extSqlReq.getRole();
                String roleName = roleMap.get(role);
                if (StringUtils.isEmpty(roleName)) {
                    throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "角色编码在IDAAs不存在");
                }
                DataPermissionPo po = buildDataPermissionPo(roleMap, role, req, PermissionType.EXT);
                po.setExtSql(extSqlReq.getExtSql());
                dataPermissionList.add(po);
            }
        }
        List<FuncResourceReq> resourceList = req.getAddResourceList();
        if (!CollectionUtils.isEmpty(resourceList)) {
            for (FuncResourceReq funcResourceReq : resourceList) {
                String resource = funcResourceReq.getResource();
                String column = funcResourceReq.getExtSql();
                String role = funcResourceReq.getRole();
                if (StringUtils.isEmpty(resource) || StringUtils.isEmpty(role)
                        || StringUtils.isEmpty(column)) continue;
                DataPermissionPo po = buildDataPermissionPo(roleMap, role, req, PermissionType.RESOURCE);
                po.setExtSql(column);
                po.setDimensionCode(resource);
                dataPermissionList.add(po);
            }
        }
        return dataPermissionList;
    }


    private DataPermissionPo buildDataPermissionPo(Map<String, String> roleMap, String role, FuncDataPermissionReq req, PermissionType type) {
        String roleName = roleMap.get(role);
        if (StringUtils.isEmpty(roleName)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "角色编码在IDAAs不存在");
        }
        String businessLine = req.getBusinessLine();
        String funcCode = req.getFuncCode();
        DataPermissionPo dataPo = new DataPermissionPo();
        dataPo.setCode(funcCode);
        dataPo.setBusinessLine(businessLine);
        dataPo.setRole(role);
        dataPo.setRoleName(roleName);
        dataPo.setSourceType(type.getCode());
        return dataPo;
    }

    public Map<String, String> findRoleMap() {
        String appCode = authorityProperties.getAppCode();
        ClientSingleResponse<PageModel<RoleResp>> res = roleService.searchRoleListPage(appCode, null, 1000, 1);
        if (!ResultCode.OK.equals(res.getCode()) || Objects.isNull(res.getData())) return new HashMap<>();
        return res.getData().getItems()
                .stream()
                .filter(i -> !RoleStatusEnum.DISABLE.equals(i.getRoleStatusEnum()))
                .collect(Collectors.toMap(RoleResp::getRoleCode, RoleResp::getRoleName));
    }

    private void checkDictAuthReq(FuncDictAuthReq saveReq) {
        String businessLine = saveReq.getBusinessLine();
        String funcCode = saveReq.getFuncCode();
        String code = saveReq.getCode();
        if (StringUtils.isEmpty(businessLine) || StringUtils.isEmpty(funcCode)
                || StringUtils.isEmpty(code)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "参数不能为空");
        }
    }

    public List<FuncDictRes> getAuthDictInfo(FuncDictAuthReq req) {
        checkDictAuthReq(req);
        List<String> roleList = dataPermissionService.getUserRoleList();
        if (CollectionUtils.isEmpty(roleList)) return new ArrayList<>();
        roleList.add(AuthFacade.authedUserName());
        LambdaQueryWrapper<FuncDictRelPo> wrapper = Wrappers.lambdaQuery(FuncDictRelPo.class)
                .eq(FuncDictRelPo::getBusinessLine, req.getBusinessLine())
                .eq(FuncDictRelPo::getFuncCode, req.getFuncCode())
                .eq(FuncDictRelPo::getCode, req.hashCode())
                .in(FuncDictRelPo::getRole, roleList);
        List<FuncDictRelPo> list = functionDictMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(list)) return new ArrayList<>();
        List<FuncDictRes> resList = new ArrayList<>();
        for (FuncDictRelPo po : list) {
            FuncDictRes res = new FuncDictRes();
            res.setDictId(po.getId());
            resList.add(res);
        }
        //todo 调字典查询接口获取具体值
        return resList;
    }

    public FuncTableRes findFuncTableById(Integer id) {
        return functionConverter.toFuncTableRes(funcTableMapper.selectById(id));
    }


    public Integer saveFuncTable(FuncTableReq req) {
        FuncTablePo po = functionConverter.toFuncTable(req);
        if (Objects.isNull(req.getId())) {
            funcTableMapper.insert(po);
        } else {
            funcTableMapper.updateById(po);
        }
        return po.getId();
    }

    public void deleteFuncTable(List<Integer> ids) {
        List<FuncTablePo> list = funcTableMapper.selectBatchIds(ids);
        if (CollectionUtils.isEmpty(list)) return;
        funcTableMapper.deleteBatchIds(ids);
        //删除关联的字段
        funcColumnMapper.delete(Wrappers.lambdaQuery(FuncColumnPo.class)
                .eq(FuncColumnPo::getUseWay, ColumnUseWay.TABLE.getCode())
                .in(FuncColumnPo::getFuncCode, list.stream().map(FuncTablePo::getCode).collect(Collectors.toList())));

    }

    public void deleteField(List<Integer> ids) {
        List<FuncColumnPo> pos =  funcColumnMapper.selectBatchIds(ids);
        if (CollectionUtils.isEmpty(pos)) return;
        funcColumnMapper.deleteBatchIds(ids);
        FuncColumnPo columnPo = pos.get(0);
        if (ColumnUseWay.FUNC_COLUMN.getCode().equals(columnPo.getUseWay())) {
            deleteCacheCol(FUN_COL_SQL + columnPo.getFuncCode() + COLON);
        }

    }

    public void deleteCacheCol(String prefix) {
        Set<String> keys = RedisUtils.keys(prefix + "*");
        if (keys != null && !keys.isEmpty()) {
            RedisUtils.delete(keys);
        }
    }

    public Object getRedisCache(String key) {
        return RedisUtils.get(key);
    }

    public PageData<DictRes> listDict(DictQueryReq query) {
        String key = query.getKey();
        Integer type = query.getType();
        String parentCode = query.getParentCode();
        LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(DictPo.class)
                .eq(Objects.nonNull(type), DictPo::getType, type)
                .eq(StringUtils.isNotEmpty(parentCode), DictPo::getParentCode, parentCode)
                .nested(StringUtils.isNotEmpty(key), q -> q.like(DictPo::getCode, key).or().like(DictPo::getParentCode, key).or()
                        .like(DictPo::getName, key).or().like(DictPo::getEnName, key)).orderByDesc(DictPo::getUpdateTime);
        Page<DictPo> page = dictMapper.selectPage(new Page<>(query.getPageNum(), query.getPageSize()), wrapper);
        return dictConverter.toPageData(page, dictConverter::poToResList);
    }

    public Map<String, List<DictRes>> getDictList(List<String> codeList) {
        Map<String, List<DictRes>> map = new HashMap<>();
        if (CollectionUtils.isEmpty(codeList)) return map;
        LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(DictPo.class)
                .in(!CollectionUtils.isEmpty(codeList), DictPo::getParentCode, codeList);
        List<DictPo> list = dictMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(list)) return map;
        List<DictRes> dictResList = dictConverter.poToResList(list);
        return dictResList.stream().collect(Collectors.groupingBy(DictRes::getParentCode));
    }

    public DictRes getDictById(Integer id) {
        DictPo po = dictMapper.selectById(id);
        if (Objects.isNull(po)) return null;
        return dictConverter.toRes(dictPoConverter.toDo(po));
    }

    public Integer saveDict(DictReq req) {
        Dict dict = dictConverter.toDo(req);
        if (Objects.isNull(req.getId())) {
            List<DictPo> list = dictMapper.selectList(Wrappers.lambdaQuery(DictPo.class).eq(DictPo::getCode, req.getCode()));
            if (!CollectionUtils.isEmpty(list)) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "编码不能重复");
            }
            DictPo po = dictPoConverter.toPo(dict);
            dictMapper.insert(po);
            dict.setId(po.getId());
        } else {
            String code = req.getCode();
            DictPo old = dictMapper.selectById(req.getId());
            if (Objects.isNull(old)) return 0;
            String odlCode = old.getCode();
            String newCode = req.getCode();
            if (StringUtils.isNotEmpty(code) && !odlCode.equals(newCode)) {
                List<DictPo> list = dictMapper.selectList(Wrappers.lambdaQuery(DictPo.class).eq(DictPo::getCode, code));
                //如果要修改的功能编码已存在，则不允许修改
                if (!CollectionUtils.isEmpty(list)) {
                    throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "功能编码不能重复");
                }
            }
            DictPo po = dictPoConverter.toPo(dict);
            dictMapper.updateById(po);
        }
        //保存明细
        saveDictItems(req);
        return dict.getId();
    }

    private void saveDictItems(DictReq req) {
        String code = req.getCode();
        List<DictReq> items = req.getItems();
        if (CollectionUtils.isEmpty(items)) return;
        for (DictReq item : items) {
            item.setParentCode(code);
            Dict dict = dictConverter.toDo(item);
            if (Objects.isNull(dict.getId())) {
                dictMapper.insert(dictPoConverter.toPo(dict));
            } else {
                dictMapper.updateById(dictPoConverter.toPo(dict));
            }
        }
    }

    private void updateDictCode(String oldCode, DictPo po) {
        if (StringUtils.isEmpty(oldCode)) return;
        if (DictType.KEY.getCode().equals(po.getType())) {
            //更新父类的编码
            List<DictPo> list = dictMapper.selectList(Wrappers.lambdaQuery(DictPo.class).eq(DictPo::getParentCode, oldCode));
            if (CollectionUtils.isEmpty(list)) return;
            list.forEach(i -> {
                i.setParentCode(po.getParentCode());
                dictMapper.updateById(i);
            });
        }
    }


    public void deleteDict(List<Integer> ids) {
        List<DictPo> list = dictMapper.selectList(Wrappers.lambdaQuery(DictPo.class)
                .eq(DictPo::getType, DictType.KEY.getCode())
                .in(DictPo::getId, ids));
        dictMapper.deleteBatchIds(ids);
        //删除关联的枚举值
        if (CollectionUtils.isEmpty(list)) return;
        dictMapper.delete(Wrappers.lambdaQuery(DictPo.class)
                .eq(DictPo::getType, DictType.VALUE.getCode())
                .eq(DictPo::getParentCode, list.stream().map(DictPo::getCode).collect(Collectors.toList())));
    }

    public PageData<FunctionDictRes> listDictRel(DictQueryReq query) {
        if (query.getManageType() == null) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "管理线类型或者业务线不能为空");
        }
        if (FuncManageType.SYSTEM.getCode().equals(query.getManageType())) {
            query.setBusinessLine(StringUtils.EMPTY);
        }
        String key = query.getKey();
        String parentCode = query.getParentCode();
        String funcCode = query.getFuncCode();
        LambdaQueryWrapper<FuncDictPo> wrapper = Wrappers.lambdaQuery(FuncDictPo.class)
                .eq(FuncDictPo::getManageType, query.getManageType())
                .eq(FuncDictPo::getBusinessLine, query.getBusinessLine());
                if (StringUtils.isNotEmpty(parentCode)) {
                    wrapper.nested(q -> q.like(FuncDictPo::getParentCode, parentCode));
                } else {
                    wrapper.nested(StringUtils.isNotEmpty(key), q -> q.like(FuncDictPo::getCode, key));
                }
                wrapper.eq(StringUtils.isNotEmpty(funcCode), FuncDictPo::getFuncCode, funcCode);
                wrapper.orderByDesc(FuncDictPo::getUpdateTime);
        Page<FuncDictPo> page = funcDictMapper.selectPage(new Page<>(query.getPageNum(), query.getPageSize()), wrapper);
        PageData<FunctionDictRes> result = dictConverter.toPageData(page, dictConverter::poToFuncResList);
        List<FunctionDictRes> list = result.getList();
        if (CollectionUtils.isEmpty(list)) return result;
        //获取字典名称
        List<DictPo> dictList = dictMapper.selectList(Wrappers.lambdaQuery(DictPo.class)
                .eq(DictPo::getType, DictType.VALUE.getCode())
                .in(DictPo::getParentCode, list.stream().map(FunctionDictRes::getParentCode).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(dictList)) return result;
        Map<String, DictPo> dictPoMap = new HashMap<>();
        for (DictPo dictPo : dictList) {
            dictPoMap.put(dictPo.getCode() + SqlConstants.SPECIAL + dictPo.getParentCode(), dictPo);
        }
        for (FunctionDictRes dictRes : result.getList()) {
            DictPo dictPo = dictPoMap.get(dictRes.getCode() + SqlConstants.SPECIAL + dictRes.getParentCode());
            if (Objects.isNull(dictPo)) continue;
            dictRes.setName(dictPo.getName());
            dictRes.setEnName(dictPo.getEnName());
        }

        return result;
    }

    public void importFuncDict(DictImportReq importReq) {
        List<Integer> dictIds = importReq.getDictIds();
        if (CollectionUtils.isEmpty(dictIds)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "字典不能为空");
        }
        if (Objects.isNull(importReq.getManageType())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "管理类型不能为空");
        }
        //根据字典查询字典值
        List<DictPo> poList = dictMapper.selectList(Wrappers.lambdaQuery(DictPo.class).in(DictPo::getId, dictIds));
        if (CollectionUtils.isEmpty(poList)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "字典id不合法");
        }

        Integer manageType = importReq.getManageType();
        if (FuncManageType.SYSTEM.getCode().equals(manageType)) {
            importReq.setBusinessLine(StringUtils.EMPTY);
        }

        //根据父类查子类的值
        List<String> parentCode = poList.stream().map(DictPo::getCode).collect(Collectors.toList());
        List<DictPo> valueList = dictMapper.selectList(Wrappers.lambdaQuery(DictPo.class).in(DictPo::getParentCode, parentCode));
        if (CollectionUtils.isEmpty(valueList)) return;

        valueList.forEach(i -> {
            FuncDictPo dict = new FuncDictPo();
            dict.setCode(i.getCode());
            dict.setParentCode(i.getParentCode());
            dict.setBusinessLine(importReq.getBusinessLine());
            dict.setManageType(manageType);
            dict.setFuncCode(importReq.getFuncCode());
            funcDictMapper.insert(dict);
        });
    }

    public void saveFuncDict(FuncDictReq dictReq) {
        FuncDict funcDict = dictConverter.toFuncDo(dictReq);
        if (Objects.isNull(dictReq.getId())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "字典id不能为空");
        }
        if (StringUtils.isEmpty(dictReq.getParentCode())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "字典父级编码不能为空");
        }
        funcDictMapper.updateById(dictPoConverter.toFuncDictPo(funcDict));

    }

    public void deleteDictRel(List<Integer> ids) {
        funcDictMapper.deleteBatchIds(ids);
    }

    public List<DictRes> listDictRelByBusinessLine(DictBusinessLineQueryReq queryReq) {
        if (StringUtils.isEmpty(queryReq.getManageLine()) || StringUtils.isEmpty(queryReq.getCode())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "字典编码或者管理线不能为空");
        }
        String businessLine = queryReq.getBusinessLine();
        String manageLine = queryReq.getManageLine();
        businessLine = StringUtils.isEmpty(businessLine) ? manageLine : businessLine;
        if (StringUtils.isEmpty(manageLine)) {
            BusinessLineDo businessLineDo = businessLineRepo.searchByBusinessLine(businessLine);
            if (Objects.isNull(businessLineDo)) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "business line does not exist");
            }
        }
        List<String> businessLineList = new ArrayList<>();
        businessLineList.add(businessLine);
        businessLineList.add(manageLine);
        businessLineList.add(StringUtils.EMPTY);
        List<FuncDictPo> poList = funcDictMapper.selectList(Wrappers.lambdaQuery(FuncDictPo.class)
                .in(FuncDictPo::getBusinessLine, businessLineList)
                .eq(FuncDictPo::getFuncCode, queryReq.getFuncCode())
                .eq(FuncDictPo::getValid, 1)
                .eq(FuncDictPo::getParentCode, queryReq.getCode()));
        poList = getFuncDictList(poList);

        LambdaQueryWrapper<DictPo> wrapper = Wrappers.lambdaQuery(DictPo.class)
                .eq(DictPo::getParentCode, queryReq.getCode());

        if (!CollectionUtils.isEmpty(poList)) {
            wrapper.in(DictPo::getCode, poList.stream().map(FuncDictPo::getCode).collect(Collectors.toList()));
        }
        List<DictPo> list = dictMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(list)) return new ArrayList<>();
        return dictConverter.poToResList(list);
    }

    private List<FuncDictPo> getFuncDictList(List<FuncDictPo> poList) {
        List<FuncDictPo> systemList = new ArrayList<>();
        List<FuncDictPo> manageList = new ArrayList<>();
        List<FuncDictPo> businessList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(poList)) {
            for (FuncDictPo func : poList) {
                Integer type = func.getManageType();
                if (ManageType.SYSTEM.getCode().equals(type)) {
                    systemList.add(func);
                } else if (ManageType.MANAGE_LINE.getCode().equals(type)) {
                    manageList.add(func);
                } else if (ManageType.BUSINESS_LINE.getCode().equals(type)) {
                    businessList.add(func);
                }
            }
        }

        if (!CollectionUtils.isEmpty(businessList)) {
            return businessList;
        } else if (!CollectionUtils.isEmpty(manageList)) {
            return manageList;
        } else {
            return systemList;
        }
    }

    public Map<String, List<DictRes>> listDictRelByBusinessLineBatch(DictBusinessLineBatchReq queryReq) {
        if (CollectionUtils.isEmpty(queryReq.getCode())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "function code is empty");
        }
        String businessLine = queryReq.getBusinessLine();
        String manageLine = queryReq.getManageLine();
        businessLine = StringUtils.isEmpty(businessLine) ? manageLine : businessLine;
        if (StringUtils.isEmpty(manageLine)) {
            BusinessLineDo businessLineDo = businessLineRepo.searchByBusinessLine(businessLine);
            if (Objects.isNull(businessLineDo)) throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "business line does not exist");
        }
        List<String> businessLineList = new ArrayList<>();
        businessLineList.add(businessLine);
        businessLineList.add(manageLine);
        businessLineList.add(StringUtils.EMPTY);
        List<FuncDictPo> pos = funcDictMapper.selectList(Wrappers.lambdaQuery(FuncDictPo.class)
                .in(FuncDictPo::getBusinessLine, businessLineList)
                .eq(FuncDictPo::getFuncCode, queryReq.getFuncCode())
                 .eq(FuncDictPo::getValid, 1)
                .in(FuncDictPo::getParentCode, queryReq.getCode()));

        pos = getFuncDictList(pos);

        LambdaQueryWrapper<DictPo> wrappers = Wrappers.lambdaQuery(DictPo.class)
                .in(DictPo::getParentCode, queryReq.getCode());
        if (!CollectionUtils.isEmpty(pos)) {
            wrappers.in(DictPo::getCode, pos.stream().map(FuncDictPo::getCode).collect(Collectors.toList()));
        }
        List<DictPo> dictPoList = dictMapper.selectList(wrappers);
        if (CollectionUtils.isEmpty(dictPoList)) return new HashMap<>();
        Map<String, DictPo> dictPoMap = new HashMap<>();
        for (DictPo dict : dictPoList) {
            dictPoMap.put(dict.getParentCode() + dict.getCode(), dict);
        }
        Map<String, List<DictRes>> result = new HashMap<>();
        boolean isCh = EAMConstants.CHINESE.equals(queryReq.getLanguage()) || StringUtils.isEmpty(queryReq.getLanguage());
        for (FuncDictPo po : pos) {
            String parentCode = po.getParentCode();
            List<DictRes> dictRes = result.get(parentCode);
            if (CollectionUtils.isEmpty(dictRes)) {
                dictRes = new ArrayList<>();
            }
            DictRes res = dictConverter.funcPoToRes(po);
            DictPo dict = dictPoMap.get(parentCode + po.getCode());
            if (Objects.nonNull(dict)) {
                res.setName(isCh ? dict.getName() : dict.getEnName());
            }
            dictRes.add(res);
            result.put(parentCode, dictRes);
        }
        return result;
    }

    public PageData<GlobalValRes> listGlobalVal(GlobalValQueryReq query) {
        String key = query.getKey();
        String businessLine = query.getBusinessLine();
        LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(GlobalValPo.class)
                .eq(GlobalValPo::getManageType, query.getManageType())
                .eq(StringUtils.isNotEmpty(businessLine), GlobalValPo::getBusinessLine, query.getBusinessLine())
                .nested(StringUtils.isNotEmpty(key), q -> q.like(GlobalValPo::getCode, key)
                        .or().like(GlobalValPo::getValue, key).or().like(GlobalValPo::getGroupCode, key)
                        .or().like(GlobalValPo::getGroupName, key)).orderByDesc(GlobalValPo::getUpdateTime);
        Page<GlobalValPo> page = globalValMapper.selectPage(new Page<>(query.getPageNum(), query.getPageSize()), wrapper);
        return globalValConverter.toPageData(page, globalValConverter::poToResList);
    }

    public Integer saveGlobalVal(GlobalValReq req) {
        String code = req.getCode();
        if (StringUtils.isEmpty(code)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "编码不能为空");
        }
        Integer id = req.getId();
        GlobalValPo po = globalValPoConverter.reqToPo(req);
        if (Objects.isNull(id)) {
            List<GlobalValPo> exList = globalValMapper.selectList(Wrappers.lambdaQuery(GlobalValPo.class)
                    .eq(GlobalValPo::getCode, code));
            if (!CollectionUtils.isEmpty(exList)) {
                throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "编码不能重复");
            }
            globalValMapper.insert(po);
        } else {
            globalValMapper.updateById(po);
        }
        return po.getId();
    }

    public void deleteGlobalVal(List<Integer> ids) {
        globalValMapper.deleteBatchIds(ids);
    }

    /**
     * 导入全局变量
     * @param req
     */
    public void importGlobalVal(GlobalImportReq req) {
        List<Integer> ids = req.getIds();
        if (CollectionUtils.isEmpty(ids)) return;
        List<GlobalValPo> list = globalValMapper.selectBatchIds(ids);
        if (CollectionUtils.isEmpty(list)) return;
        Integer manageType = req.getManageType();
        String businessLine = req.getBusinessLine();
        List<String> codeList = list.stream().map(GlobalValPo::getCode).collect(Collectors.toList());
        List<GlobalValPo> exList = globalValMapper.selectList(Wrappers.lambdaQuery(GlobalValPo.class)
                .eq(GlobalValPo::getManageType, manageType).eq(GlobalValPo::getBusinessLine, businessLine)
                .in(GlobalValPo::getCode, codeList));
        Map<String, GlobalValPo> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(exList)) {
            for (GlobalValPo valPo : exList) {
                map.put(valPo.getCode(), valPo);
            }
        }
        for (GlobalValPo valPo : list) {
            if (Objects.nonNull(map.get(valPo.getCode()))) {
                continue;
            }
            valPo.setBusinessLine(businessLine);
            valPo.setManageType(manageType);
            valPo.setId(null);
            globalValMapper.insert(valPo);
        }
    }

    public List<GlobalValRes> getGlobalVal(List<String> code, String businessLine) {
        if (CollectionUtils.isEmpty(code)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "code不能为空");
        }
        if (StringUtils.isEmpty(businessLine)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "businessLine不能为空");
        }
        LambdaQueryWrapper<GlobalValPo> wrapper = Wrappers.lambdaQuery(GlobalValPo.class).in(GlobalValPo::getCode, code);
        List<GlobalValPo> list = globalValMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(list)) return null;
        List<GlobalValPo> businessList = new ArrayList<>();
        List<GlobalValPo> manageList = new ArrayList<>();
        List<GlobalValPo> systemList = new ArrayList<>();
        for (GlobalValPo po : list) {
            String businessLineCode = po.getBusinessLine();
            Integer type = po.getManageType();
            if (ManageType.BUSINESS_LINE.getCode().equals(type) && businessLineCode.equals(businessLine)) {
                businessList.add(po);
            } else if (ManageType.MANAGE_LINE.getCode().equals(type) && businessLineCode.equals(businessLine)) {
                manageList.add(po);
            } else if (ManageType.SYSTEM.getCode().equals(type)) {
                systemList.add(po);
            }
        }
        if (!CollectionUtils.isEmpty(businessList)) {
            list = businessList;
        } else if (!CollectionUtils.isEmpty(manageList)){
            list = manageList;
        } else {
            list = systemList;
        }
        return globalValConverter.poToResList(list);
    }

    public List<GlobalValRes> getGlobalValByCode(List<String> code) {
        if (CollectionUtils.isEmpty(code)) {
            return new ArrayList<>();
        }
        List<GlobalValPo> list = globalValMapper.selectList(Wrappers.lambdaQuery(GlobalValPo.class).in(GlobalValPo::getCode, code));
        return globalValConverter.poToResList(list);
    }

    /**
     * 从1.0同步字典值
     */
    public void syncDictFromControl() {
        List<FunControlPo> list = funcControlMapper.selectList(Wrappers.lambdaQuery(FunControlPo.class));
        if (CollectionUtils.isEmpty(list)) return;
        Map<String, List<FunControlPo>> eamMap = list.stream().collect(Collectors.groupingBy(FunControlPo::getControlCode));
        //查询所有枚举值
        List<DictPo> funcDictPos = dictMapper.selectList(Wrappers.lambdaQuery(DictPo.class).eq(DictPo::getType, DictType.VALUE.getCode()));
        Map<String, List<DictPo>> existMap = funcDictPos.stream().collect(Collectors.groupingBy(DictPo::getParentCode));
        List<DictPo> all = new ArrayList<>();
        for (String code : eamMap.keySet()) {
            List<FunControlPo> funControlPoList = eamMap.get(code);
            if (CollectionUtils.isEmpty(funControlPoList)) continue;
            List<DictPo> dictPoList = existMap.get(code);
            //全不包含
            if (CollectionUtils.isEmpty(dictPoList)) {
                //枚举值
                List<DictPo> addList = controlToDictList(funControlPoList, DictType.VALUE.getCode());
                all.addAll(addList);
                //枚举key
                DictPo keyDict = controlToDictKey(funControlPoList.get(0));
                all.add(keyDict);
            } else {//部分包含
                List<String> exist = dictPoList.stream().map(DictPo::getCode).collect(Collectors.toList());
                for (FunControlPo funControlPo : funControlPoList) {
                    if (!exist.contains(code)) {
                        all.add(controlToDict(funControlPo, DictType.VALUE.getCode()));
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(all)) {
            all.forEach(o -> dictMapper.insert(o));
        }
    }


    private List<DictPo> controlToDictList(List<FunControlPo> controlPos, Integer type) {
        List<DictPo> out = new ArrayList<>();
        if (CollectionUtils.isEmpty(controlPos)) return out;
        for (FunControlPo controlPo : controlPos) {
            out.add(controlToDict(controlPo, type));
        }
        return out;

    }

    private DictPo controlToDict(FunControlPo controlPo, Integer type) {
        DictPo dictPo = new DictPo();
        dictPo.setParentCode(controlPo.getControlCode());
        dictPo.setName(controlPo.getDisplayData());
        dictPo.setCode(controlPo.getValueData());
        dictPo.setValid(1);
        dictPo.setType(type);
        return dictPo;
    }

    private DictPo controlToDictKey(FunControlPo controlPo) {
        DictPo dictPo = new DictPo();
        dictPo.setName(controlPo.getControlName());
        dictPo.setCode(controlPo.getControlCode());
        dictPo.setValid(1);
        dictPo.setType(DictType.KEY.getCode());
        return dictPo;
    }

    /**
     * 导入表
     *
     * @param file
     */
    public void importTable(MultipartFile file) {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        } catch (IOException e) {
            log.error("importTable is error");
        }
        if (StringUtils.isEmpty(content)) {
            log.error("importTable is empty");
            return;
        }
        Map<String, List<TableModel>> tableMap = parseCreateTableSQL(content.toString());
        if (tableMap.isEmpty()) {
            log.error("importTable map is empty");
            return;
        }
        for (String table : tableMap.keySet()) {
            List<TableModel> list = tableMap.get(table);
            if (CollectionUtils.isEmpty(list)) continue;
            //先查询是否存在
            FuncTablePo tablePo = funcTableMapper.selectOne(Wrappers.lambdaQuery(FuncTablePo.class).eq(FuncTablePo::getTableName, table));
            if (Objects.nonNull(tablePo)) {
                continue;
            }
            TableModel model = list.get(0);
            FuncTablePo tableNew = new FuncTablePo();
            tableNew.setCode(table);
            tableNew.setTitle(model.getTitle());
            tableNew.setValid(1);
            tableNew.setTableName(table);
            tableNew.setRemark(model.getTitle());
            funcTableMapper.insert(tableNew);

            for (TableModel tableModel : list) {
                String colType = tableModel.getType();
                FuncColumnPo column = new FuncColumnPo();
                column.setFuncCode(table);
                column.setCode(tableModel.getColName());
                column.setName(tableModel.getColComment());
                column.setUseWay(ColumnUseWay.TABLE.getCode());
                column.setDataType(MysqlDataType.getDataTypeByMysql(colType));
                column.setColControl(ColControlType.getColControlByMysql(colType));
                column.setFormatId(DataFormat.getFormatByMysql(colType));
                funcColumnMapper.insert(column);
            }
        }
    }

    public static Map<String, List<TableModel>> parseCreateTableSQL(String sql) {
        Map<String, List<TableModel>> map = new HashMap<>();

        List<SQLStatement> stmtList = SQLUtils.parseStatements(sql, JdbcConstants.MYSQL);

        for (SQLStatement stmt : stmtList) {
            if (stmt instanceof SQLCreateTableStatement) {
                SQLCreateTableStatement createTableStmt = (SQLCreateTableStatement) stmt;
                List<SQLColumnDefinition> columnDefinitions = createTableStmt.getColumnDefinitions();
                String tableName = createTableStmt.getTableName();
                tableName = tableName.replace("`", StringUtils.EMPTY);
                String comment = createTableStmt.getComment().toString();
                comment = comment.replace("'", StringUtils.EMPTY);
                List<TableModel> list = new ArrayList<>();
                for (SQLColumnDefinition columnDefinition : columnDefinitions) {
                    String columnName = columnDefinition.getName().getSimpleName();
                    columnName = columnName.replace("`", StringUtils.EMPTY);
                    String columnType = columnDefinition.getDataType().getName();
                    SQLExpr sqlExpr = columnDefinition.getComment();
                    String colComment = "";
                    if (Objects.nonNull(sqlExpr)) {
                        colComment = sqlExpr.toString().replace("'", StringUtils.EMPTY);
                    }
                    System.out.println("tableName: " + tableName + " comment: " + comment + " columnName: " + columnName + " columnType: " + columnType + " colComment: " + colComment);
                    TableModel model = new TableModel();
                    model.setType(columnType);
                    model.setColName(columnName);
                    model.setColComment(colComment);
                    model.setTableName(tableName);
                    model.setTitle(comment);
                    list.add(model);
                }
                map.put(tableName, list);
            }
        }
        return map;
    }

    /**
     * 同步功能列字段
     */
    public void syncFuncCol() {
        log.info("syncFuncCol start");
        RLock lock = redissonClient.getLock("syncTableCol");
        if (lock.isLocked()) {
            log.error("syncTableCol get RLock is fail");
            throw new ErrorCodeException(ErrorCodes.NOT_IMPLEMENTED, "syncTableCol reject to get RLock");
        }
        try {
            if (lock.tryLock(2, -1, TimeUnit.SECONDS)) {
                eamFuncSyncService.syncColFromEam();
            }

        } catch (Exception ex) {
            try {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                log.error("syncFuncCol unlock is fail", e);
            }
        }
        log.info("syncFuncCol start");
    }

    /**
     * 同步表的列字段
     */
    public void syncTableCol(String funcCode) {
        log.info("syncTableCol start");
        RLock lock = redissonClient.getLock("syncTableCol");
        if (lock.isLocked()) {
            log.error("syncTableCol get RLock is fail");
            throw new ErrorCodeException(ErrorCodes.NOT_IMPLEMENTED, "syncTableCol reject to get RLock");
        }
        try {
            if (lock.tryLock(2, -1, TimeUnit.SECONDS)) {
                List<FunctionPo> functionPoList;
                if (StringUtils.isNotEmpty(funcCode)) {
                    functionPoList = functionMapper.selectList(Wrappers.lambdaQuery(FunctionPo.class).eq(FunctionPo::getCode, funcCode));
                } else {
                    functionPoList = functionMapper.selectList(Wrappers.lambdaQuery());
                }

                Map<String, String> tableMap = new HashMap<>();
                for (FunctionPo functionPo : functionPoList) {
                    tableMap.put(functionPo.getCode(), functionPo.getTableName());
                }
                List<String> funcCodes = new ArrayList<>(tableMap.keySet());
                List<FunColPo> funcColumnPos = funcColMapper.selectList(Wrappers.lambdaQuery(FunColPo.class).in(FunColPo::getFunId, funcCodes));
                if (CollectionUtils.isEmpty(funcColumnPos)) return;
                List<FuncColumnPo> columnExist = funcColumnMapper.selectList(Wrappers.lambdaQuery(FuncColumnPo.class).eq(FuncColumnPo::getUseWay, ColumnUseWay.TABLE.getCode()));
                Map<String, FuncColumnPo> existMap = new HashMap<>();
                Map<String, String> hasInsertMap = new HashMap<>();
                for (FuncColumnPo po : columnExist) {
                    if (StringUtils.isEmpty(po.getColControl())) continue;
                    existMap.put(po.getFuncCode() + po.getCode(), po);
                }
                List<FuncColumnPo> newList = new ArrayList<>();
                List<FuncColumnPo> updList = new ArrayList<>();
                for (FunColPo po : funcColumnPos) {
                    //code可能是连表查询，去调表名
                    String code = po.getColCode();
                    if (StringUtils.isEmpty(code)) continue;
                    String tableName = tableMap.get(po.getFunId());
                    if (StringUtils.isEmpty(tableName)) continue;

                    if (code.contains(SqlConstants.POINT) && !code.contains(tableName)) {
                        //非本表的字段跳过
                        continue;
                    }
                    if (code.contains(SqlConstants.POINT)) {
                        String[] str = code.split("\\.");
                        if (str.length > 1) {
                            code = str[1];
                        }
                    }
                    String key = tableName + code;
                    if (Objects.nonNull(hasInsertMap.get(key))) {
                        log.info("重复编码：{}", key);
                        continue;
                    }
                    FuncColumnPo funcColumnPo = existMap.get(key);
                    if (Objects.isNull(funcColumnPo)) {
                        funcColumnPo = new FuncColumnPo();
                        newList.add(funcColumnPo);
                    } else {
                        updList.add(funcColumnPo);
                    }

                    funcColumnPo.setFuncCode(tableName);
                    funcColumnPo.setCode(code);
                    funcColumnPo.setEnCode(po.getColCode());
                    funcColumnPo.setName(po.getColName());
                    funcColumnPo.setUseWay(ColumnUseWay.TABLE.getCode());
                    funcColumnPo.setDataType(po.getDataType());
                    funcColumnPo.setColControl(po.getColControl());
                    funcColumnPo.setControlCode(po.getControlName());
                    funcColumnPo.setDefaultValue(po.getDefaultValue());
                    funcColumnPo.setFormatId(po.getFormatId());
                    hasInsertMap.put(tableName + code, code);
                }
                if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(newList)) {
                    newList.forEach(i -> funcColumnMapper.insert(i));
                }

                if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(updList)) {
                    updList.forEach(i -> funcColumnMapper.updateById(i));
                }

                //注册表
                List<FuncTablePo> list = funcTableMapper.selectList(new LambdaQueryWrapper<>(FuncTablePo.class));
                Map<String, FuncTablePo> existTable = list.stream().filter( i -> StringUtils.isNotEmpty(i.getTableName())).collect(Collectors.toMap(FuncTablePo::getTableName, Function.identity()));
                Map<String, String> insertTable = new HashMap<>();
                for (FunctionPo functionPo : functionPoList) {
                    String tableName = functionPo.getTableName();
                    if (StringUtils.isEmpty(tableName) || Objects.nonNull(existTable.get(tableName))) {
                        continue;
                    }
                    if (Objects.nonNull(insertTable.get(tableName))) {
                        log.info("表重复：{}", tableName);
                        continue;
                    }
                    FuncTablePo tablePo = new FuncTablePo();
                    tablePo.setCode(tableName);
                    tablePo.setTableName(tableName);
                    tablePo.setTitle(functionPo.getFunName());
                    tablePo.setValid(1);
                    funcTableMapper.insert(tablePo);
                    insertTable.put(tableName, tableName);
                }
            }
        } catch (InterruptedException e) {
            log.error("syncTableCol", e);
            Thread.currentThread().interrupt();
            throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, e);
        } finally {
            try {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            } catch (Exception ex) {
                log.error("syncTableCol unlock is fail");
            }
        }
        log.info("syncTableCol end");
    }

    public PageData<DownTaskRes> listDownTask(DownTaskQueryReq query) {
        String key = query.getKey();
        String taskStatus = query.getTaskStatus();
        String userName = AuthFacade.authedUserName();
        LambdaQueryWrapper<DownloadTaskPo> wrapper = Wrappers.lambdaQuery(DownloadTaskPo.class)
                .eq(DownloadTaskPo::getCreateUser, userName)
                .eq(StringUtils.isNotEmpty(taskStatus), DownloadTaskPo::getTaskStatus, taskStatus)
                .nested(StringUtils.isNotEmpty(key), q -> q.like(DownloadTaskPo::getTaskName, key)).orderByDesc(DownloadTaskPo::getUpdateTime);
        Page<DownloadTaskPo> page = downloadTaskMapper.selectPage(new Page<>(query.getPageNum(), query.getPageSize()), wrapper);
        List<DownloadTaskPo> list = page.getRecords();
        if (!CollectionUtils.isEmpty(list) && !EAMConstants.CHINESE.equals(query.getLanguage())) {
            Map<String, FunctionPo> map = getFunctionMap(list.stream().map(DownloadTaskPo::getFuncCode).collect(Collectors.toList()));
            for (DownloadTaskPo po : list) {
                FunctionPo functionPo = map.get(po.getFuncCode());
                if (Objects.isNull(functionPo)) continue;
                String name = functionPo.getEnName();
                if (StringUtils.isEmpty(name)) {
                    name = functionPo.getFunName().replace("amg_", StringUtils.EMPTY).replace(SqlConstants.DOWN, SqlConstants.BLANK);
                }
                po.setTaskName(name + " download");
            }
        }
        return downTaskConverter.toPageData(page, downTaskConverter::poToResList);
    }

    public Map<String, FunctionPo> getFunctionMap(List<String> codeList) {
        Map<String, FunctionPo> map = new HashMap<>();
        if (CollectionUtils.isEmpty(codeList)) return map;
        List<FunctionPo> list = functionMapper.selectList(Wrappers.lambdaQuery(FunctionPo.class).in(FunctionPo::getCode, codeList));
        if (CollectionUtils.isEmpty(list)) return map;
        list.forEach(i -> map.put(i.getCode(), i));
        return map;
    }

}
