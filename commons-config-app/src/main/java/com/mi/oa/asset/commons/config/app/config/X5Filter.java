package com.mi.oa.asset.commons.config.app.config;


import com.mi.oa.asset.eam.feign.x5.X5Response;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.xiaomi.core.auth.x5.config.X5ServerProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;


@Slf4j
public class X5Filter extends OncePerRequestFilter {

    private final X5ServerProperties x5Server;

    public X5Filter(X5ServerProperties x5Server) {
        this.x5Server = x5Server;
    }

    @Override
    public void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        log.info("----------------------doFilterInternal");
        if (!HttpMethod.POST.matches(request.getMethod())) {
            doResponse(response, JacksonUtils.bean2Json(X5Response.fail("expect POST method")));
            return;
        }
        try {
            X5RequestWrapper x5RequestWrapper = new X5RequestWrapper(request);
            filterChain.doFilter(x5RequestWrapper, response);
        } catch (BizException e) {
            log.error("-------------------doFilterInternal BizException:{}", e.getMessage(), e);
            doResponse(response, JacksonUtils.bean2Json(X5Response.fail(String.valueOf(e.getCode()), e.getMessage())));
        } catch (Exception e) {
            log.error("-------------------doFilterInternal Exception:{}", e.getMessage(), e);
            doResponse(response, JacksonUtils.bean2Json(X5Response.fail(e.getMessage())));
        }
    }

    private void doResponse(HttpServletResponse response, String data) throws IOException {
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.getOutputStream().write(data.getBytes(StandardCharsets.UTF_8));
        response.flushBuffer();
    }
}


