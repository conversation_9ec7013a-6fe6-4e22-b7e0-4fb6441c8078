package com.mi.oa.asset.commons.config.app.config;

import com.xiaomi.core.auth.x5.config.X5ServerProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;


@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<X5Filter> x5FilterRegistrationBean(X5ServerProperties x5Server) {
        FilterRegistrationBean<X5Filter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new X5Filter(x5Server));
        registrationBean.setOrder(-1);
        registrationBean.setUrlPatterns(Collections.singletonList("/configs/x5/*"));
        registrationBean.setName("X5Filter");

        return registrationBean;
    }
}


