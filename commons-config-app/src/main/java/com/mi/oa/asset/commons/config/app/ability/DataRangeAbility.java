package com.mi.oa.asset.commons.config.app.ability;


import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.commons.config.api.datarange.DataRangeReq;
import com.mi.oa.asset.commons.config.domain.assetorganization.entity.AssetOrgUnit;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.Warehouse;
import com.mi.oa.asset.commons.config.infra.database.dataobject.OrganizationUnitPo;
import com.mi.oa.asset.commons.config.infra.database.dataobject.WarehousePo;
import com.mi.oa.asset.commons.config.infra.database.mapper.OrganizationUnitPoMapper;
import com.mi.oa.asset.commons.config.infra.database.mapper.WarehouseMapper;
import com.mi.oa.asset.commons.config.infra.repository.converter.AssetOrgRepoConverter;
import com.mi.oa.asset.commons.config.infra.repository.converter.WarehouseConverter;
import com.mi.oa.asset.commons.config.infra.repository.service.AssetOrgUnitService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.idm.api.IdmAccountV2Service;
import com.mi.oa.infra.oaucf.idm.api.IdmUserService;
import com.mi.oa.infra.oaucf.idm.api.enums.AccountStatusEnum;
import com.mi.oa.infra.oaucf.idm.api.rep.AccountInfoDto;
import com.mi.oa.infra.oaucf.idm.api.rep.Resp;
import com.mi.oa.infra.oaucf.idm.api.rep.UserInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant.COMMA;

@Service
@Slf4j
public class DataRangeAbility {
    @Resource
    private AssetOrgUnitService orgUnitService;

    @Resource
    private AssetOrgRepoConverter orgConverter;

    @Resource
    private IdmAccountV2Service idmAccountV2Service;

    @Resource
    private IdmUserService idmUserService;

    @Resource
    private OrganizationUnitPoMapper organizationUnitPoMapper;

    @Resource
    private WarehouseMapper warehouseMapper;

    @Resource
    private WarehouseConverter warehouseConverter;

    @Resource
    private AssetOrgRepoConverter assetOrgRepoConverter;


    public AssetOrgUnit getOrgUnitByCode(DataRangeReq req) {
        if (StringUtils.isEmpty(req.getUseDeptCode())) return null;
        String businessLine = req.getBusinessLine();
        List<String> businessLineList = Arrays.asList(businessLine.split(","));
        List<OrganizationUnitPo> list = orgUnitService.list(Wrappers.lambdaQuery(OrganizationUnitPo.class)
                .eq(OrganizationUnitPo::getOrgCode, req.getUseDeptCode())
                .in(OrganizationUnitPo::getBusinessLine, businessLineList));
        if (CollectionUtils.isEmpty(list)) return null;
        return orgConverter.toAssetOrgUnit(list.get(0));
    }

    public UserInfoDto getUserInfo(String userName) {
        BaseResp<AccountInfoDto> baseResp =  idmAccountV2Service.getAccount(userName);
        AccountInfoDto accountInfoDto = baseResp.getData();
        if (Objects.isNull(accountInfoDto)) return null;
        AccountStatusEnum accountStatus = accountInfoDto.getAccountStatus();
        if (Objects.isNull(accountStatus) || AccountStatusEnum.CLOSE.equals(accountStatus)
                || AccountStatusEnum.DISABLE.equals(accountStatus)) return null;
        Resp<UserInfoDto> userInfoDtoResp = idmUserService.findUseInfoByUid(accountInfoDto.getUid());
        return userInfoDtoResp.getData();
    }

    public Warehouse getHouse(String houseCode) {
        List<WarehousePo> list = warehouseMapper.selectList(Wrappers.lambdaQuery(WarehousePo.class).eq(WarehousePo::getHouseCode, houseCode));
        if (CollectionUtils.isEmpty(list)) return null;
        return warehouseConverter.toDo(list.get(0));
    }

    public List<AssetOrgUnit> getAssetOrgUnit(DataRangeReq req, boolean useDept) {
        List<String> businessLine = Arrays.asList(req.getBusinessLine().split(COMMA));
        List<OrganizationUnitPo> list = useDept ? organizationUnitPoMapper.getUseOrgDataRange(req.getUserName(), businessLine)
                : organizationUnitPoMapper.getManageOrgDataRange(req.getUserName(), businessLine);
        return assetOrgRepoConverter.toAssetOrgUnits(list);
    }
}
