package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.BusinessLineEn;
import com.mi.oa.asset.commons.config.api.warehouse.SaveWarehouseReq;
import com.mi.oa.asset.commons.config.api.warehouse.WarehouseRes;
import com.mi.oa.asset.commons.config.api.warehouse.enums.*;
import com.mi.oa.asset.commons.config.domain.warehouse.entity.Warehouse;
import com.mi.oa.asset.commons.config.domain.warehouse.valobj.WarehouseData;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mi.oa.asset.common.enums.EAMConstants.ENGLISH;

@Mapper(componentModel = "spring")
public interface AppWarehouseConverter extends CommonConverter {
    @Mappings({
            @Mapping(target = "servicesName", expression = "java(getServicesName(source.getServices()))"),
    }
    )
    Warehouse toWarehouse(SaveWarehouseReq source);

    WarehouseRes toWarehouseRes(Warehouse source);

    List<WarehouseRes> toWarehouseResList(List<Warehouse> sources);

    @Mappings({
            @Mapping(target = "houseStatus", expression = "java(houseStatus(source.getHouseStatus(), language))"),
            @Mapping(target = "logisticsType", expression = "java(getLogisticsType(source.getLogisticsType(), language))"),
            @Mapping(target = "services", expression = "java(getServices(source.getServices(), language))"),
            @Mapping(target = "servicesName", expression = "java(getServices(source.getServices(), language))"),
            @Mapping(target = "shelves", expression = "java(shelves(source.getShelves(), language))"),
            @Mapping(target = "businessLine", expression = "java(businessLineDescMapping(source.getBusinessLine(), language))"),
            @Mapping(target = "houseType", expression = "java(getHouseTypeByDesc(source.getHouseType(), language))"),
            @Mapping(target = "country", constant = "CHN"),
    })
    Warehouse excelToHouse(WarehouseData source, String language);

    // 添加语言参数并实现映射逻辑
    default List<Warehouse> excelToHouses(List<WarehouseData> source, String language) {
        return source.stream()
                .map(data -> excelToHouse(data, language))
                .collect(Collectors.toList());
    }
    @Named("businessLineDescMapping")
    default BusinessLine businessLineDescMapping(String businessLine, String language) {
        if (ENGLISH.equals(language)) {
            BusinessLineEn businessLineEn = BusinessLineEn.getByDesc(businessLine);
            return BusinessLine.getByCode(businessLineEn.getCode());
        }
        return BusinessLine.getByDesc(businessLine);
    }
    @Named("houseStatus")
    default String houseStatus(String status, String language) {
        if (ENGLISH.equals(language)) {
            return "Valid".equals(status) ? "1" : "0";
        }
        return "生效".equals(status) ? "1" : "0";
    }

    @Named("shelves")
    default Boolean shelves(String status, String language) {
        if (ENGLISH.equals(language)) {
            return "Yes".equals(status);
        }
        return "是".equals(status);
    }
    @Named("getHouseTypeByDesc")
    default WarehouseType getHouseTypeByDesc(String houseType, String language) {
        if (ENGLISH.equals(language)) {
            WarehouseTypeEn byDesc = WarehouseTypeEn.getByDesc(houseType);
            return WarehouseType.getByCode(byDesc.getCode());
        }
        return WarehouseType.getByDesc(houseType);
    }
    @Named("getLogisticsType")
    default String getLogisticsType(String logisticsType, String language) {
        if (StringUtils.isBlank(logisticsType)) {
            return "";
        }
        String[] strings = logisticsType.split("-");
        // 优化后的代码
        List<String> logisticList = Arrays.stream(strings)
                .map(string -> {
                    if (ENGLISH.equals(language)) {
                        LogisticsTypeEn logisticsEn = LogisticsTypeEn.getByDesc(string);
                        return logisticsEn != null ? logisticsEn.getCode() : null;
                    } else {
                        LogisticsType logistics = LogisticsType.getByDesc(string);
                        return logistics != null ? logistics.getCode() : null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return StringUtils.join(logisticList, ",");
    }


    @Named("getServices")
    default String getServices(String services, String language) {
        if (StringUtils.isBlank(services)) {
            return "";
        }
        String[] strings = services.split(",");
        // 优化后的代码
        List<String> serviceNames = Arrays.stream(strings)
                .map(string -> {
                    if (ENGLISH.equals(language)) {
                        WarehouseServicesEn serEn = WarehouseServicesEn.getByDesc(string);
                        return serEn != null ? serEn.getCode() : null;
                    } else {
                        WarehouseServices ser = WarehouseServices.getByDesc(string);
                        return ser != null ? ser.getCode() : null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return StringUtils.join(serviceNames, ",");
    }
    @Named("getServicesName")
    default String getServicesName(String services) {
        if (StringUtils.isBlank(services)) {
            return "";
        }
        String[] strings = services.split(",");
        List<String> serviceNames = new ArrayList<>(strings.length);
        for (String string : strings) {
            WarehouseServices ser = WarehouseServices.getByCode(string);
            if (Objects.isNull(ser)) {
                continue;
            }
            serviceNames.add(ser.getDesc());
        }
        return StringUtils.join(serviceNames, ",");
    }
}
