package com.mi.oa.asset.commons.config.app.web.controller;


import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigProvider;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigReq;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigRes;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 国家地区基础信息表的 Controller
 */
@RestController
@HttpApiModule(value = "CountryConfigController", apiController = CountryConfigController.class)
@RequestMapping("/configs/countryConfig")
@Slf4j
public class CountryConfigController {

    @Resource
    private CountryConfigProvider countryConfigProvider;

    @HttpApiDoc(apiName = "查询列表", value = "/configs/countryConfig/list", method = MiApiRequestMethod.GET)
    @GetMapping("/list")
    public Result<List<CountryConfigRes>> list() {
        return Result.success(countryConfigProvider.getCountryConfigList());
    }

    @HttpApiDoc(apiName = "查询区域下的可配置的国家列表", value = "/configs/countryConfig/listForRegionConfig", method = MiApiRequestMethod.GET)
    @GetMapping("/listForRegionConfig")
    public Result<List<CountryConfigRes>> listForRegionConfig() {
        return Result.success(countryConfigProvider.getByRegionId(0));
    }

    @HttpApiDoc(apiName = "详情", value = "/configs/countryConfig/detail", method = MiApiRequestMethod.GET)
    @GetMapping("/detail")
    public Result<CountryConfigRes> info(@RequestParam("countryConfigId") Integer countryConfigId) {
        return Result.success(countryConfigProvider.getById(countryConfigId));
    }


    @HttpApiDoc(apiName = "更新", value = "/configs/countryConfig/edit", method = MiApiRequestMethod.POST)
    @PostMapping("/edit")
    public Result<Void> edit(@RequestBody @Valid CountryConfigReq req) {
        log.info("countryConfig edit : {}", JacksonUtils.bean2Json(req));
        countryConfigProvider.saveOrUpdate(req);
        return Result.success();
    }

    @HttpApiDoc(apiName = "删除", value = "/configs/countryConfig/delete", method = MiApiRequestMethod.POST)
    @PostMapping("/delete")
    public Result<Void> delete(@RequestBody CountryConfigReq req) {
        log.info("countryConfig delete : {}", JacksonUtils.bean2Json(req));
        countryConfigProvider.removeByIds(Collections.singletonList(req.getId()));
        return Result.success();
    }


    @HttpApiDoc(apiName = "查询全部国家", value = "/configs/countryConfig/all", method = MiApiRequestMethod.GET)
    @GetMapping("/all")
    public Result<List<CountryConfigRes>> getAllCountries() {
        List<CountryConfigRes> countryList = countryConfigProvider.getAllCountries();
        log.info("Retrieved all countries: {}", JacksonUtils.bean2Json(countryList));
        return Result.success(countryList);
    }

    @HttpApiDoc(apiName = "根据三位编码查询国家信息", value = "/configs/countryConfig/getByThreeCode", method = MiApiRequestMethod.GET)
    @GetMapping("/getByThreeCode")
    public Result<CountryConfigRes> getByThreeCode(@RequestParam("threeCode") String threeCode) {
        log.info("countryConfig getByThreeCode : {}", threeCode);
        CountryConfigRes byThreeCode = countryConfigProvider.getByThreeCode(threeCode);
        return Result.success(byThreeCode);
    }
}
