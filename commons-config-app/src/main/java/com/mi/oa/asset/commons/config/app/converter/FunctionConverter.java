package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.function.*;
import com.mi.oa.asset.commons.config.domain.function.entity.FuncTable;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.commons.config.infra.database.dataobject.FuncTablePo;
import com.mi.oa.asset.eam.mybatis.DataPermissionPo;
import com.mi.oa.asset.eam.mybatis.FuncColumnPo;
import com.mi.oa.asset.eam.mybatis.FuncDictRelPo;
import com.mi.oa.asset.eam.mybatis.FunctionPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface FunctionConverter extends CommonConverter {

    FuncFieldRes toFuncFieldRes(FuncColumnPo po);

    List<FuncFieldRes> toFuncFieldList(List<FuncColumnPo> po);

    FuncColumnPo copy(FuncColumnPo po);

    FuncColumnPo reqToPo(FuncFieldReq req);

    List<FuncColumnPo> reqToPoList(List<FuncFieldReq> req);

    FuncDictRelPo reqToDictPo(FuncDictRelReq req);

    List<FuncDictRelPo> reqToDictPoList(List<FuncDictRelReq> req);

    FuncDictRelRes toDictRelRes(FuncDictRelPo po);

    List<FuncDictRelRes> toDictRelResList(List<FuncDictRelPo> po);

    FunctionRes toFunctionRes(FunctionPo po);

    List<FunctionRes> toFunctionResList(List<FunctionPo> po);

    FunctionPo toFunctionPo(FunctionReq req);

    List<FunctionPo> toFunctionPoList(List<FunctionReq> req);

    FuncDataPermissionRes toDataPermissionRes(DataPermissionPo po);

    DataPermissionPo dataPermissionReqToPo(FuncDataPermissionReq req);

    List<FuncTableRes> toFuncTableResList(List<FuncTablePo> po);

    FuncTableRes toFuncTableRes(FuncTablePo po);

    @Mappings({
            @Mapping(target = "code", source = "tableName")
    })
    FuncTablePo toFuncTable(FuncTableReq po);

}
