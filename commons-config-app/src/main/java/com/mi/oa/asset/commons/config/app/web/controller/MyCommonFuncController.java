package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.common.DelByIdsReq;
import com.mi.oa.asset.commons.config.api.myfunctions.*;
import com.mi.oa.asset.commons.config.app.ability.CommonFuncAbility;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/6 19:06
 * @description 我的常用功能
 */
@Slf4j
@RequestMapping("/configs")
@HttpApiModule(value = "MyCommonFuncController", apiController = MyCommonFuncController.class)
@RestController
public class MyCommonFuncController {
    @Resource
    private CommonFuncAbility commonFuncAbility;

    @GetMapping("/my-functions-sort/list")
    @HttpApiDoc(value = "/configs/my-functions-sort/list", method = MiApiRequestMethod.GET, description = "查询我的功能列表的排序", apiName = "查询我的功能列表的排序")
    public Result<MyFunctionsSortResp> getMyFunctionsSort(@RequestParam(value = "manageLineCode", required = true) String manageLineCode) {
        return Result.success(commonFuncAbility.listMyFuncSort(manageLineCode));
    }

    @PostMapping("/my-functions-sort/save")
    @HttpApiDoc(value = "/configs/my-functions-sort/save", method = MiApiRequestMethod.POST, description = "保存我的功能列表的排序", apiName = "保存我的功能列表的排序")
    public Result<Void> saveMyFunctionsSort(@RequestBody @Valid MyFunctionsSortReq functionsSortReq) {
        commonFuncAbility.saveMyFunctionsSort(functionsSortReq);
        return Result.success();
    }

    @PostMapping("/function-config/save")
    @HttpApiDoc(value = "/configs/function-config/save", method = MiApiRequestMethod.POST, description = "保存我的功能列表的排序", apiName = "保存我的功能列表的排序")
    public Result<Void> save(@RequestBody @Valid SaveFunctionConfigReq functionsSortReq) {
        commonFuncAbility.submit(functionsSortReq);
        return Result.success();
    }

    /**
     * 根据账号查询首页功能权限
     */
    @GetMapping("/function-config/list")
    @HttpApiDoc(value = "/configs/function-config/list", method = MiApiRequestMethod.POST, description = "根据账号查询首页功能权限", apiName = "根据账号查询首页功能权限")
    public Result<List<FunctionConfigRes>> list(@RequestParam(value = "userName", required = false) String userName,
                                                @RequestParam(value = "country", required = false, defaultValue = "CHN") String country,
                                                @RequestHeader(value = "eam-language", required = false, defaultValue = "zh-CN") String eamLanguage) {
        return Result.success(commonFuncAbility.functionConfig(StringUtils.isNotBlank(userName) ? userName : AuthFacade.authedUserName(), country, eamLanguage));
    }

    @GetMapping("/function-config/list-by-manage-line")
    @HttpApiDoc(value = "/configs/function-config/list-by-manage-line", method = MiApiRequestMethod.POST, description = "根据管理线首页功能配置数据", apiName = "根据管理线首页功能配置数据")
    public Result<List<FunctionConfigRes>> listByManageLine(@RequestParam(value = "manageLine", required = false) String manageLine) {
        return Result.success(commonFuncAbility.functionConfigByManageLine(manageLine));
    }

    @PostMapping("/function-config/delete")
    @HttpApiDoc(value = "/configs/function-config/delete", method = MiApiRequestMethod.POST, description = "删除首页功能配置", apiName = "删除首页功能配置")
    public Result<Void> delete(@RequestBody DelByIdsReq req) {
        commonFuncAbility.deleteFunctionConfig(req);
        return Result.success();
    }

    @PostMapping("/function-config/personal-save")
    @HttpApiDoc(value = "/configs/function-config/personal-save", method = MiApiRequestMethod.POST, description = "员工端保存我的功能列表的排序", apiName = "员工端保存我的功能列表的排序")
    public Result<Void> personalConfig(@RequestBody List<SaveFunctionConfigUserReq> configUserReq) {
        commonFuncAbility.personalConfig(configUserReq);
        return Result.success();
    }
}
