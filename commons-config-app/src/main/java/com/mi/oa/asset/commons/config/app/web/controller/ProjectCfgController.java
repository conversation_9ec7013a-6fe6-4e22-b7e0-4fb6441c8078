package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.project.ProjectCfgProvider;
import com.mi.oa.asset.commons.config.api.project.ProjectCfgRes;
import com.mi.oa.asset.commons.config.api.project.ProjectStatusEnum;
import com.mi.oa.asset.commons.config.api.project.SaveProjectCfgReq;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@HttpApiModule(value = "ProjectCfgController", apiController = ProjectCfgController.class)
@RestController
@RequestMapping("/configs/project")
@Slf4j
public class ProjectCfgController {

    @Resource
    private ProjectCfgProvider provider;

    @HttpApiDoc(apiName = "项目配置暂存", value = "/configs/project/save", method = MiApiRequestMethod.POST)
    @PostMapping("/save")
    public Result<Integer> save(@RequestBody SaveProjectCfgReq req) {
        req.setRecordStatus(ProjectStatusEnum.WAIT_CREATE.getCode());
        return Result.success(provider.saveProjectCfg(req));
    }

    @HttpApiDoc(apiName = "项目配置提交", value = "/configs/project/submit", method = MiApiRequestMethod.POST)
    @PostMapping("/submit")
    public Result<Integer> submit(@Valid @RequestBody SaveProjectCfgReq req) {
        req.setRecordStatus(ProjectStatusEnum.CREATED.getCode());
        return Result.success(provider.saveProjectCfg(req));
    }

    @HttpApiDoc(apiName = "项目配置详情", value = "/configs/project/detail", method = MiApiRequestMethod.GET)
    @GetMapping("/detail")
    public Result<ProjectCfgRes> detail(@RequestParam("id") Integer id) {
        return Result.success(provider.getProjectCfg(id));
    }

}
