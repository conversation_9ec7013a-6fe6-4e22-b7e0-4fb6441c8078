package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.function.DownTaskRes;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.eam.mybatis.DownloadTaskPo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DownTaskConverter extends CommonConverter {

    DownTaskRes poToRes(DownloadTaskPo source);

    List<DownTaskRes> poToResList(List<DownloadTaskPo> source);
}
