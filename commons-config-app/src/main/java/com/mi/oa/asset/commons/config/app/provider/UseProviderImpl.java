package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.use.UseProvider;
import com.mi.oa.asset.commons.config.api.use.UseReasonRes;
import com.mi.oa.asset.commons.config.api.use.UseReq;
import com.mi.oa.asset.commons.config.api.use.UseRes;
import com.mi.oa.asset.commons.config.api.user.EmployeeInfoRes;
import com.mi.oa.asset.commons.config.api.user.UserInfoService;
import com.mi.oa.asset.commons.config.app.converter.UseConverter;
import com.mi.oa.asset.commons.config.domain.use.entity.Use;
import com.mi.oa.asset.commons.config.domain.use.entity.UseReason;
import com.mi.oa.asset.commons.config.domain.use.repository.UseReasonRepo;
import com.mi.oa.asset.commons.config.domain.use.repository.UseRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 办公用途 dubbo 接口实现
 *
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@DubboService
@Slf4j
public class UseProviderImpl implements UseProvider {

    @Resource
    private UseConverter converter;
    @Resource
    private UseRepo useRepo;
    @Resource
    private UseReasonRepo useReasonRepo;

    @Autowired
    private UserInfoService userInfoService;

    @Override
    public List<UseRes> list(UseReq request) {
        List<String> deptCodes = this.getDeptCodesByUserCode(request.getUserCode());
        Use use = converter.toUse(request);
        List<Use> uses = useRepo.list(use);
        List<Use> useByUserCodes = useRepo.listUseByUserCode(request.getBusinessLine(), request.getUserCode());
        List<Use> useByDeptCode = useRepo.listUseByDeptCode(request.getBusinessLine(), deptCodes);
        uses.addAll(useByUserCodes);
        uses.addAll(useByDeptCode);
        return converter.toUseResList(uses);
    }

    private List<String> getDeptCodesByUserCode(String userCode) {
        EmployeeInfoRes employeeInfo = userInfoService.getEmpInfoByUserName(userCode);
        if (employeeInfo != null) {
            List<String> deptCodes = new ArrayList<>(4);
            if (StringUtils.isNotBlank(employeeInfo.getDeptIdLv1())) {
                deptCodes.add(employeeInfo.getDeptIdLv1());
            }
            if (StringUtils.isNotBlank(employeeInfo.getDeptIdLv2())) {
                deptCodes.add(employeeInfo.getDeptIdLv2());
            }
            if (StringUtils.isNotBlank(employeeInfo.getDeptIdLv3())) {
                deptCodes.add(employeeInfo.getDeptIdLv3());
            }
            if (StringUtils.isNotBlank(employeeInfo.getDeptIdLv4())) {
                deptCodes.add(employeeInfo.getDeptIdLv4());
            }
            if (StringUtils.isNotBlank(employeeInfo.getDeptIdLv5())) {
                deptCodes.add(employeeInfo.getDeptIdLv5());
            }
            return deptCodes;
        }
        return Collections.emptyList();
    }

    @Override
    public List<UseReasonRes> listByUseId(Integer useId) {
        if (Objects.isNull(useId)) {
            throw new IllegalArgumentException("useId不能为空");
        }
        List<UseReason> list = useReasonRepo.list(UseReason.builder().useId(useId).build());
        return converter.toUseReasonResList(list);
    }

    @Override
    public List<UseReasonRes> all() {
        List<UseReason> list = useReasonRepo.list(UseReason.builder().build());
        return converter.toUseReasonResList(list);
    }
}

