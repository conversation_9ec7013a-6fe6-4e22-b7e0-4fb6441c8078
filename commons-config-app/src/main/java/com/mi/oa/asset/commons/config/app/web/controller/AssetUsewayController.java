package com.mi.oa.asset.commons.config.app.web.controller;


import com.mi.oa.asset.commons.config.api.assetuseway.AssetUsewayProvider;
import com.mi.oa.asset.commons.config.api.assetuseway.AssetUsewayReasonRes;
import com.mi.oa.asset.commons.config.api.assetuseway.AssetUsewayReq;
import com.mi.oa.asset.commons.config.api.assetuseway.AssetUsewayRes;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 办公用途 控制器
 *
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@RestController
@HttpApiModule(value = "AssetUsewayController", apiController = AssetUsewayController.class)
@RequestMapping("/configs/assetuseway")
public class AssetUsewayController {
    @Resource
    private AssetUsewayProvider assetUsewayProvider;

    /**
     * 申请用途-列表信息
     */
    @GetMapping("/list")
    @HttpApiDoc(apiName = "申请用途-列表", value = "/configs/assetuseway/list", method = MiApiRequestMethod.GET)
    public Result<List<AssetUsewayRes>> list(AssetUsewayReq request) {
        List<AssetUsewayRes> list = assetUsewayProvider.list(request);
        return Result.success(list);
    }

    /**
     * 申请用途理由-列表信息
     */
    @GetMapping("/reason/list/{usewayId}")
    @HttpApiDoc(apiName = "申请理由-列表", value = "/configs/assetuseway/reason/list/{usewayId}", method = MiApiRequestMethod.GET)
    public Result<List<AssetUsewayReasonRes>> listReason(@PathVariable("usewayId") String usewayId) {
        List<AssetUsewayReasonRes> list = assetUsewayProvider.listByUsewayId(usewayId);
        return Result.success(list);
    }

    /**
     * 申请用途理由-列表信息
     */
    @GetMapping("/reason/list")
    @HttpApiDoc(apiName = "申请理由-列表", value = "/configs/assetuseway/reason/list", method = MiApiRequestMethod.GET)
    public Result<List<AssetUsewayReasonRes>> listReason() {
        List<AssetUsewayReasonRes> list = assetUsewayProvider.all();
        return Result.success(list);
    }

}
