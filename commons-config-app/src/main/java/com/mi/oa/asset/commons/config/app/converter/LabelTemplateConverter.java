package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.labelprint.LabelTemplateRes;
import com.mi.oa.asset.commons.config.api.labelprint.SaveLabelTemplateReq;
import com.mi.oa.asset.commons.config.domain.labeltemplate.entity.LabelTemplate;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface LabelTemplateConverter {

    LabelTemplateRes toLabelTemplateRes(LabelTemplate labelTemplate);

    LabelTemplate toLabelTemplate(SaveLabelTemplateReq saveLabelTemplateReq);

}
