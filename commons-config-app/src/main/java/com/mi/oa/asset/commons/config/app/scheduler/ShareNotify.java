package com.mi.oa.asset.commons.config.app.scheduler;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.queryfield.FieldConfigInfo;
import com.mi.oa.asset.commons.config.api.queryfield.FieldConfigRes;
import com.mi.oa.asset.commons.config.app.ability.AssetShareAbility;
import com.mi.oa.asset.commons.config.app.ability.QueryFieldAbility;
import com.mi.oa.asset.commons.config.app.converter.QueryFieldConverter;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareChangeLog;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRecord;
import com.mi.oa.asset.commons.config.domain.assetshare.enums.ShareValidityType;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.ShareChangeLogRepo;
import com.mi.oa.asset.commons.config.domain.assetshare.valobj.ShareLogJson;
import com.mi.oa.asset.commons.config.domain.assetshare.valobj.ShareMessageParams;
import com.mi.oa.asset.commons.config.domain.assetshare.valobj.ShareQueryReq;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.commons.config.domain.common.repository.CommonDataRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.FunctionSubscribe;
import com.mi.oa.asset.commons.config.domain.queryfield.entity.FieldConfig;
import com.mi.oa.asset.commons.config.domain.queryfield.repository.FieldConfigRepo;
import com.mi.oa.asset.eam.auth.data.DataPermissionService;
import com.mi.oa.asset.eam.feign.enums.DateVacationEnum;
import com.mi.oa.asset.eam.feign.res.HrodDayScheduleAroundDateRes;
import com.mi.oa.asset.eam.feign.service.HrodService;
import com.mi.oa.asset.eam.jxs.controller.CommonController;
import com.mi.oa.asset.eam.jxs.req.BaseExportReq;
import com.mi.oa.asset.eam.jxs.utils.CommonUtils;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import com.mi.oa.asset.eam.utils.LarkUtil;
import com.mi.oa.asset.eam.utils.TemplateEnginUtil;
import com.mi.oa.infra.oaucf.fds.service.FDSService;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.mi.oa.infra.uc.common.util.GsonUtil;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 共享消息通知
 */
@Slf4j
@Service
@PlanTask(name = "ShareNotify", quartzCron = "0 40 9 * * ?", description = "工作日每天早上九点四十，给订阅用户发送共享资产变更消息")
public class ShareNotify implements PlanExecutor {
    private final ThreadPoolExecutor customThreadPool = new ThreadPoolExecutor(5, 10, 60,
            TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000));

    @Value("${lark-button.share-url}")
    private String jumpToUrl;

    @Resource
    private HrodService hrodService;

    @Resource
    private CommonDataRepo commonDataRepo;

    @Resource
    private ShareChangeLogRepo shareChangeLogRepo;

    @Resource
    FieldConfigRepo fieldConfigRepo;

    @Resource
    private AssetShareAbility assetShareAbility;

    @Resource
    QueryFieldAbility queryFieldAbility;

    @Resource
    QueryFieldConverter queryFieldConverter;

    @Resource
    private TemplateEnginUtil templateUtil;

    @Autowired(required = false)
    private FDSService fdsService;

    @Resource
    private DataPermissionService dataPermissionService;

    @Override
    public void execute() {
        // 查询当天日期是否为工作日
        String now = LocalDate.now().toString();
        List<HrodDayScheduleAroundDateRes> res = hrodService.getDayScheduleAroundDate(now, "", "1");
        if (CollectionUtils.isNotEmpty(res)) {
            String shiftId = res.get(0).getShiftId();
            if (DateVacationEnum.MI_SFT.getCode().equals(shiftId)) {
                // 工作日，查询订阅用户，发送消息
                List<FunctionSubscribe> subscribeList = commonDataRepo.getSubscribeByFunCode("", CommonConstant.ASSET_SHARE_FUN);
                if (CollectionUtils.isNotEmpty(subscribeList)) {
                    Map<String, FunctionSubscribe> subscribeMap = subscribeList.stream()
                            .collect(Collectors.toMap(FunctionSubscribe::getCreateUser, Function.identity(), (v1, v2) -> v1));
                    // 启用异步线程执行
                    subscribeMap.forEach((k, v) ->
                            CompletableFuture.runAsync(() -> this.handleTemplateData(k, v), customThreadPool)
                    );
                }
            }
        }
    }

    private void handleTemplateData(String k, FunctionSubscribe v) {
        // 查询用户可见的共享资产
        ShareQueryReq req = new ShareQueryReq();
        req.setUserCode(k);
        req.setDeptCode(v.getDeptCode());
        String shareWhereSql = assetShareAbility.queryShareList(req);
        List<ShareRecord> shareRecords = req.getShareRecords();
        log.info("ShareNotify sendMessage start userCode:{}，shareRecords：{}", k, JacksonUtils.bean2Json(shareRecords));
        // 未取消对该用户的授权资产时，查询可见规则内的变动日志（查询该用户上次发送时间与当前时间之间变动的日志数据）
        if (CollectionUtils.isNotEmpty(shareRecords)) {
            Map<Integer, ShareRecord> shareMap = new HashMap<>();
            List<Integer> shareIds = new ArrayList<>();
            for (ShareRecord shareRecord : shareRecords) {
                Integer shareId = shareRecord.getId();
                shareIds.add(shareId);
                shareMap.put(shareId, shareRecord);
            }
            List<ShareChangeLog> shareChangeLogs = shareChangeLogRepo.getByShareIds(shareIds, v.getSendTime());
            // 没变动不发送消息，有变动根据shareId分组，匹配最早一次和最后一次的差异数量
            if (CollectionUtils.isNotEmpty(shareChangeLogs)) {
                Map<Integer, List<ShareChangeLog>> shareChangeLogMap = shareChangeLogs.stream()
                        .collect(Collectors.groupingBy(ShareChangeLog::getShareId));
                // 先提前进行导出调用，获取fds的下载地址
                String fdsFileName = this.getFdsFileName(k, shareWhereSql);
                log.info("ShareNotify sendMessage start userCode:{}，fdsFileName：{}", k, fdsFileName);
                // 获取消息模板需要拼接的参数信息
                List<ShareMessageParams> shareMessageParams = this.buildMessageParams(shareMap, shareChangeLogMap);
                if (CollectionUtils.isNotEmpty(shareMessageParams)) {
                    log.info("ShareNotify sendMessage start userCode:{}，shareMessageParams：{}", k, JacksonUtils.bean2Json(shareMessageParams));
                    // 替换模板参数
                    String content = this.replaceTemplateParams(shareMessageParams, fdsFileName);
                    content = content.replace("\r\n", "");
                    // 发送消息，有日志表记录，不用做其他处理
                    LarkUtil.sendMessage(k, content);
                    // 更新发送时间
                    v.setSendTime(new Date());
                    commonDataRepo.createOrUpdateSubscribe(v);
                }
            }
        }
        log.info("ShareNotify sendMessage end userCode:{}", k);
    }

    private String getFdsFileName(String userName, String shareWhereSql) {
        String fdsFileName = "";
        // 查询用户收藏或默认的列配置字段
        FieldConfig fieldConfig = fieldConfigRepo.searchFieldConfig(StringUtils.EMPTY, CommonConstant.ASSET_SHARE_FUN, userName);
        // 如果没有配置，取配置文件中默认配置
        if (null == fieldConfig) {
            fieldConfig = queryFieldAbility.getDefaultConfig(StringUtils.EMPTY, CommonConstant.ASSET_SHARE_FUN);
        }
        FieldConfigRes defaultConfig = queryFieldConverter.fieldConfigToRes(fieldConfig);
        List<FieldConfigInfo> configInfos = defaultConfig.getFieldConfig();
        if (Objects.isNull(configInfos)) return fdsFileName;
        List<String> exportFields = configInfos.stream()
                .filter(o -> Boolean.FALSE.equals(o.getHidden()))
                .map(FieldConfigInfo::getDataKey).collect(Collectors.toList());
        // 导出请求对象
        BaseExportReq req = new BaseExportReq();
        // Base64编码加密
        if (StringUtils.isNotBlank(shareWhereSql)) {
            shareWhereSql = CommonUtils.encodeWhereSql(shareWhereSql);
        }
        req.setCustomSql(shareWhereSql);
        req.setFunId(CommonConstant.ASSET_ACCOUNT_FUN);
        req.setBusinessType(CommonConstant.ASSET_SHARE_FUN);
        req.setExportFields(exportFields);
        req.setQueryConditionReqs(new ArrayList<>());
        req.setAuthFlag(Boolean.FALSE);
        req.setUserName(userName);
        try {
            // 生成唯一标识作为fds原始文件名
            String key = UUID.randomUUID().toString();
            String fileName = "共享清单".concat(DateUtils.getNowDay()).concat(CommonController.XLSX);
            fileName = fileName.replace(CommonController.XLSX, StringUtils.EMPTY) + CommonController.CN + key + CommonController.XLSX;
            File tempFile = File.createTempFile(key, CommonController.XLSX);
            FileOutputStream os = new FileOutputStream(tempFile);
            dataPermissionService.export(req, os);
            fdsFileName = fdsService.putFile(tempFile, fileName, StringUtils.EMPTY);
        } catch (Exception e) {
            log.error("ShareNotify getFdsFileName is error, req: {}, info：{}", JacksonUtils.bean2Json(req), e);
        }
        return fdsFileName;
    }

    private List<ShareMessageParams> buildMessageParams(Map<Integer, ShareRecord> shareMap,
                                                        Map<Integer, List<ShareChangeLog>> shareChangeLogMap) {
        int count = 1;
        List<ShareMessageParams> messageParamsList = new ArrayList<>();
        for (Map.Entry<Integer, List<ShareChangeLog>> entry : shareChangeLogMap.entrySet()) {
            // 最多匹配五条
            if (count > 5) break;
            ShareRecord shareRecord = shareMap.get(entry.getKey());
            List<ShareChangeLog> changeLogs = entry.getValue();
            // 重新排序获取最早时间和最新时间更新的记录
            changeLogs.sort(Comparator.comparing(ShareChangeLog::getUpdateTime));
            ShareChangeLog shareChangeLog = changeLogs.get(0);
            String earliestLog = shareChangeLog.getBeforeChange();
            String lastLog = changeLogs.get(changeLogs.size() - 1).getAfterChange();
            // 解析对比
            List<ShareLogJson> earliestList = GsonUtil.parseArray(earliestLog, ShareLogJson.class);
            List<ShareLogJson> lastList = GsonUtil.parseArray(lastLog, ShareLogJson.class);
            Map<Integer, Integer> earliestMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(earliestList)) {
                earliestMap = earliestList.stream().collect(Collectors.toMap(ShareLogJson::getAssetId, ShareLogJson::getShareQuantity));
            }
            AtomicInteger shareQuantity = new AtomicInteger();
            Map<Integer, Integer> earliestFinalMap = earliestMap;
            lastList.parallelStream().forEach(shareLog -> {
                Integer earliestQuantity = earliestFinalMap.get(shareLog.getAssetId());
                Integer lastQuantity = shareLog.getShareQuantity();
                if (Objects.isNull(earliestQuantity)) {
                    // 表示后来新增共享的资产
                    shareQuantity.addAndGet(lastQuantity);
                } else {
                    Integer changeQuantity = lastQuantity - earliestQuantity;
                    if (changeQuantity > 0) shareQuantity.addAndGet(changeQuantity);
                }
            });
            // 数量减少或没有变动的，不发送消息
            if (shareQuantity.get() > 0) {
                ShareMessageParams params = new ShareMessageParams();
                params.setBusinessName(BusinessLine.getByCode(shareRecord.getBusinessLine()).getDesc());
                params.setShareTitle(shareRecord.getRecordTitle());
                params.setShareQuantity(shareQuantity.get());
                ShareValidityType validityType = ShareValidityType.PERMANENT;
                String dateRange = validityType.getDesc();
                if (!validityType.getCode().equals(shareRecord.getValidityType())) {
                    String startTime = DateUtils.dateToStringByType(shareRecord.getShareStartTime(), DateUtils.YEAR_MONTH_DATE);
                    String endTime = DateUtils.dateToStringByType(shareRecord.getShareEndTime(), DateUtils.YEAR_MONTH_DATE);
                    dateRange = startTime.concat(CommonConstant.WAVE).concat(endTime);
                }
                params.setShareTime(dateRange);
                params.setMemo(shareRecord.getMemo());
                messageParamsList.add(params);
                count++;
            }
        }
        return messageParamsList;
    }

    private String replaceTemplateParams(List<ShareMessageParams> shareMessageParams, String fileName) {
        StringBuilder content = new StringBuilder();
        int totalQuantity = shareMessageParams.stream().mapToInt(ShareMessageParams::getShareQuantity).sum();
        content.append(String.format("共新增%s件共享的资产\\n", totalQuantity));
        for (ShareMessageParams messageParams : shareMessageParams) {
            content.append("**").append(messageParams.getShareTitle()).append("**\\n");
            content.append(String.format("共享资产：【%s】共新增%s件共享的资产\\n", messageParams.getBusinessName(), messageParams.getShareQuantity()));
            content.append(String.format("起至时间：%s\\n", messageParams.getShareTime()));
            content.append(String.format("备注说明：%s\\n", messageParams.getMemo()));
        }
        Map<String, String> params = new HashMap<>();
        params.put("content", content.toString());
        params.put("jumpToUrl", jumpToUrl);
        params.put("downloadUrl", fileName);
        return templateUtil.compose("share-message-two-button.ftl", params);
    }
}
