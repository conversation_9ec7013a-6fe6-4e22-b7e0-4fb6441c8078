package com.mi.oa.asset.commons.config.app.web.controller;


import com.mi.oa.asset.common.model.AdsDTO;
import com.mi.oa.asset.common.model.User;
import com.mi.oa.asset.commons.config.api.address.*;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 地址
 *
 * <AUTHOR>
 * @date 2024-04-08 11:14:07
 */
@RestController
@HttpApiModule(value = "AddressController", apiController = AddressController.class)
@RequestMapping("/configs/ads")
@Slf4j
public class AddressController {
    @Resource
    private AssetReceiveAddressProvider assetReceiveAddressProvider;

    /**
     * 默认查询中国大陆地址库
     *
     * @param type
     * @param parentId
     * @return
     */
    @GetMapping("/list")
    @HttpApiDoc(apiName = "地址区域列表", value = "/configs/ads/list", method = MiApiRequestMethod.GET)
    public Result<List<AdsDTO>> qryAdsList(@RequestParam("type") String type,
                                           @RequestParam(value = "parentId", required = false) Integer parentId) {
        return Result.success(assetReceiveAddressProvider.qryAdsList(type, parentId));
    }

    @GetMapping("/parse")
    @HttpApiDoc(apiName = "解析详细地址", value = "/configs/ads/parse", method = MiApiRequestMethod.GET)
    public Result<Object> parseAddress(@RequestParam(value = "address", required = false) String address) {
        return Result.success(assetReceiveAddressProvider.parseAddress(address));
    }

    /**
     * 详情数据
     */
    @GetMapping("/detail/{id}")
    @HttpApiDoc(apiName = "详情", value = "/configs/ads/detail/{id}", method = MiApiRequestMethod.GET)
    public Result<AssetReceiveAddressRes> detail(@PathVariable("id") Long id) {
        return Result.success(assetReceiveAddressProvider.getById(id));
    }

    /**
     * 保存地址
     */
    @PostMapping("/edit")
    @HttpApiDoc(apiName = "保存", value = "/configs/ads/edit", method = MiApiRequestMethod.POST)
    public Result<Long> edit(@RequestBody AssetReceiveAddressReq request) {
        User user = AuthFacade.authedUserInfo();
        if (Objects.isNull(user)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "用户信息错误！");
        }
        if (request.getId() == null) {
            request.setUserId(user.getUserName());
            request.setUserName(user.getDisplayName());
            request.setCreateUser(user.getUserName());
            request.setCreateTime(ZonedDateTime.now().toEpochSecond());
        }
        request.setUpdateUser(user.getUserName());
        return Result.success(assetReceiveAddressProvider.saveOrUpdateReceiveAddress(request));
    }

    /**
     * 删除:根据id集合 逻辑删除
     */
    @PostMapping("/delete")
    @HttpApiDoc(apiName = "删除", value = "/configs/ads/delete", method = MiApiRequestMethod.POST)
    public Result<Void> delete(@RequestBody AddressIdReq req) {
        assetReceiveAddressProvider.removeByIds(Collections.singletonList(req.getId()));
        return Result.success();
    }

    /**
     * 查询当前用户收货地址
     */
    @GetMapping("/user/address-list")
    @HttpApiDoc(apiName = "查询当前用户收货地址", value = "/configs/ads/user/address-list", method = MiApiRequestMethod.GET)
    public Result<List<AssetReceiveAddressRes>> getAllReceiveAddress() {
        AssetReceiveAddressReq req = new AssetReceiveAddressReq();
        req.setUserId(AuthFacade.authedUserName());
        if (StringUtils.isBlank(req.getUserId())) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "用户信息错误！");
        }
        return Result.success(assetReceiveAddressProvider.list(req));
    }

    /**
     * 获取默认地址
     */
    @GetMapping("/default")
    @HttpApiDoc(apiName = "获取默认地址", value = "/configs/ads/default", method = MiApiRequestMethod.GET)
    public Result<AssetReceiveAddressRes> getDefault() {
        return Result.success(assetReceiveAddressProvider.getDefault(AuthFacade.authedUserName()));
    }

    /**
     * 设置默认地址
     */
    @PostMapping("/default/set")
    @HttpApiDoc(apiName = "设置默认地址", value = "/configs/ads/default/set", method = MiApiRequestMethod.POST)
    public Result<Boolean> setDefault(@RequestBody AddressIdReq req) {
        return Result.success(assetReceiveAddressProvider.setDefault(req.getId()));
    }

    @GetMapping("/city")
    @HttpApiDoc(apiName = "根据城市名模糊查询城市信息", value = "/configs/ads/city", method = MiApiRequestMethod.GET)
    public Result<List<AddressCityRes>> getCity(@RequestParam("cityName") String name) {
        return Result.success(assetReceiveAddressProvider.getCityByName(name));
    }

    @GetMapping("/country")
    @HttpApiDoc(apiName = "查询所有国家信息", value = "/configs/ads/country", method = MiApiRequestMethod.GET)
    public Result<List<AdsDTO>> getCountry() {
        return Result.success(assetReceiveAddressProvider.getCountry());
    }

    @GetMapping("/country/v1")
    @HttpApiDoc(apiName = "查询EAM1.0国家地区数据", value = "/configs/ads/country/v1", method = MiApiRequestMethod.GET)
    public Result getCountryV1() {
        return Result.success(assetReceiveAddressProvider.getCountryV1());
    }
}
