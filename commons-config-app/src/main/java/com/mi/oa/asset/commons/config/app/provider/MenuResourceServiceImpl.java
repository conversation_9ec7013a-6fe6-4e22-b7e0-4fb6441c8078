package com.mi.oa.asset.commons.config.app.provider;

import com.mi.info.infra.moon.dto.ClientSingleResponse;
import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.commons.config.api.menuresource.*;
import com.mi.oa.asset.commons.config.app.converter.MenuResourceConverter;
import com.mi.oa.asset.commons.config.domain.common.entity.BusinessLineDo;
import com.mi.oa.asset.commons.config.domain.common.entity.ManageLineDo;
import com.mi.oa.asset.commons.config.domain.common.repository.BusinessLineRepo;
import com.mi.oa.asset.commons.config.domain.common.repository.ManageLineRepo;
import com.mi.oa.asset.commons.config.infra.rpc.role.SysRoleClient;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.newauth.autoconfig.authority.AuthorityProperties;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.AccountAuthorityResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.ResourceTreeResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.dto.RoleNewResp;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.remote.AuthorityClientService;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.remote.ResourceService;
import com.mi.oa.infra.oaucf.newauth.core.authority.api.remote.RoleService;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.mi.oa.infra.uc.common.enmu.AccountTypeEnum;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mi.oa.asset.common.enums.EAMConstants.*;

/**
 * 菜单功能权限
 *
 * <AUTHOR>
 * @date 2023/9/18 10:52
 */
@DubboService
@Service
@Slf4j
public class MenuResourceServiceImpl implements MenuResource {

    @Resource
    private AuthorityProperties authorityProperties;

    @Resource
    private ResourceService resourceService;

    @Resource
    private MenuResourceConverter resourceConverter;

    @Resource
    private AuthorityClientService authorityClientService;

    @Resource
    private RoleService roleService;

    @Resource
    private BusinessLineRepo businessLineRepo;

    @Resource
    private ManageLineRepo manageLineRepo;

    @Resource
    private SysRoleClient roleClient;

    private static final String MANAGE_LINE = "MANAGE_LINE";

    private static final String REGISTER_ROLE = "eam:register:role:v2:";

    private ThreadPoolExecutor registerThreadPool = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000000));

    @Override
    public MenuResourceRes getMenuResourceByUserName(String userName, String appCode, Integer accountType, Integer isFilterShow, String eamLanguage) {
        if (StringUtils.isBlank(appCode)) {
            appCode = authorityProperties.getAppCode();
        }
        AccountTypeEnum item = AccountTypeEnum.getItem(accountType);
        if (item == null) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "未找到对应账户类型");
        }

        ClientSingleResponse<List<ResourceTreeResp>> clientSingleResponse = resourceService.queryMenuFunction(appCode, userName, item.name());
        if (clientSingleResponse != null) {
            MenuResourceRes resourceRes = new MenuResourceRes();
            String code = clientSingleResponse.getCode();
            if (String.valueOf(BaseResp.CODE_SUCCESS).equals(code)) {
                List<ResourceTreeResp> data = clientSingleResponse.getData();
                if (CollectionUtils.isEmpty(data)) {
                    throw new ErrorCodeException(ErrorCodes.FORBIDDEN, "请先申请权限");
                }
                // 过滤掉isShow = false的节点数据
                List<MenuRes> menuRes = resourceConverter.resourceTreeListToResList(data);
                // isFilterShow 控制是否过滤隐藏菜单
                if (isFilterShow == 1) {
                    List<MenuRes> filterMenusList = filterMenus(menuRes);
                    resourceRes.setMenuResource(filterMenusList);
                } else {
                    resourceRes.setMenuResource(menuRes);
                }
                List<ResourceRes> allMenuResource = new ArrayList<>();
                List<ResourceRes> permissionResources = getPermissionResources(allMenuResource, resourceRes.getMenuResource());
                resourceRes.setPermissionResource(permissionResources);
                // 查询用户业务线权限
                List<MenuBusinessLineRes> accountAuthority = getMenuBusinessLineRes(userName, appCode, accountType);
                resourceRes.setBusinessLineResource(accountAuthority);
                return resourceRes;
            }
        }
        return null;
    }

    @Override
    public List<MenuFunctionRes> getMenuFunctionResource(String userName, String appCode, Integer accountType,
                                                         Integer isFilterShow, String eamSource, String eamLanguage) {
        if (StringUtils.isBlank(appCode)) {
            appCode = authorityProperties.getAppCode();
        }
        AccountTypeEnum item = AccountTypeEnum.getItem(accountType);
        if (item == null) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "账户类型异常");
        }
        List<MenuRes> menuResList = null;
        ClientSingleResponse<List<ResourceTreeResp>> clientSingleResponse = resourceService.queryMenuFunction(appCode, userName, item.name());
        if (clientSingleResponse != null) {
            String code = clientSingleResponse.getCode();
            if (String.valueOf(BaseResp.CODE_SUCCESS).equals(code)) {
                List<ResourceTreeResp> data = clientSingleResponse.getData();
                if (CollectionUtils.isEmpty(data)) {
                    throw new ErrorCodeException(ErrorCodes.FORBIDDEN, "请先申请权限");
                }
                // data返回的数据里有 ext 字段，里面的数据结构是 {"zh":"中文菜单名","en":"英文菜单名"}，如果 eamLanguage 为 en，则取 ext.en 的值，否则取 ext.zh 的值
                menuResList = resourceConverter.resourceTreeListToResList(data, eamLanguage);
            }
        }
        if (CollectionUtils.isEmpty(menuResList)) {
            throw new ErrorCodeException(ErrorCodes.FORBIDDEN, "请先申请权限");
        }
        BaseResp<AccountAuthorityResp> accountAuthorityResp = authorityClientService.queryAccountAuth(userName, item.name(), appCode);
        if (BaseResp.CODE_SUCCESS != accountAuthorityResp.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "查询用户数据权限异常");
        }

        AccountAuthorityResp authorityResp = accountAuthorityResp.getData();
        List<String> roleCodeList = authorityResp.getRoleCodeList();
        //如果是管理端，但用户有且只有员工角色时，则说明没有管理端权限
        if (!CollectionUtils.isEmpty(roleCodeList) && EAM_ADMIN.equals(eamSource)) {
            roleCodeList.removeIf(i -> i.startsWith("__"));
            if (roleCodeList.size() == 1 && roleCodeList.contains(CLIENT_USER_ROLE)) {
                throw new ErrorCodeException(ErrorCodes.FORBIDDEN, "请先申请权限");
            }
        }
        //默认员工端角色
        if (EAM_CLIENT.equals(eamSource)) {
            roleCodeList = new ArrayList<>(Arrays.asList(CLIENT_USER_ROLE));
        }
        //如果是管理端，移除员工端角色
        if (EAM_ADMIN.equals(eamSource) && CollectionUtils.isNotEmpty(roleCodeList)) {
            roleCodeList.removeIf(r -> CLIENT_USER_ROLE.equals(r));
        }
        // 异步自动注册1.0默认角色和数据权限，并且同步2.0角色到1.0
        Object o = RedisUtils.get(REGISTER_ROLE + userName);
        if (o == null) {
            List<String> finalRoleCodeList = roleCodeList;
            CompletableFuture.runAsync(() -> {
                try {
                    roleClient.registerRole(userName, finalRoleCodeList);
                    RedisUtils.setEx(REGISTER_ROLE + userName, userName, 2, TimeUnit.HOURS);
                } catch (Exception e) {
                    log.error("registerRoleError, userName:{}", userName);
                }
            }, registerThreadPool);
        }
        // 查询全部管理线
        List<ManageLineDo> manageLineDoList = manageLineRepo.getManageLine();
        Map<String, ManageLineDo> manageLineDoMap = new HashMap<>(manageLineDoList.size());
        List<String> allManageList = new ArrayList<>(manageLineDoList.size());
        manageLineDoList.forEach(manageLineDo -> {
            String manageLine = manageLineDo.getManageLine();
            manageLineDoMap.put(manageLine, manageLineDo);
            allManageList.add(manageLine);
        });

        Map<String, Map<String, List<String>>> roleMenusResMap = getRoleMenusRes(appCode, roleCodeList, eamSource, allManageList);
        Map<String, List<String>> roleCodeMap = roleMenusResMap.get("roleCode");
        Map<String, List<String>> functionCodeMap = roleMenusResMap.get("functionCode");

        Map<String, List<String>> dimensionResourceCodeMap = authorityResp.getDimensionResourceCodeMap();
        if (Objects.isNull(dimensionResourceCodeMap)) {
            dimensionResourceCodeMap = new HashMap<>();
        }
        // 管理线数据
        List<String> manageLineList = dimensionResourceCodeMap.getOrDefault(MANAGE_LINE, Collections.emptyList());

        // 如果是员工端，则取所有管理线
        if (EAM_CLIENT.equals(eamSource)) {
            manageLineList = allManageList;
        }
        Map<String, List<MenuBusinessRes>> menuBusinessMap = getBusinessMap(dimensionResourceCodeMap, eamSource, eamLanguage);

        Map<String, List<String>> manageLineMap = new HashMap<>(manageLineList.size());
        List<MenuFunctionRes> buildMenuResList = new ArrayList<>(manageLineMap.size());

        try {
            for (String manageLine : manageLineList) {
                List<MenuRes> copyMenus = new ArrayList<>(menuResList.size());
                for (MenuRes res : menuResList) {
                    MenuRes deepCloneRes = res.deepClone();
                    copyMenus.add(deepCloneRes);
                }
                MenuFunctionRes menuFunctionRes = new MenuFunctionRes();
                menuFunctionRes.setId(manageLine);
                ManageLineDo manageLineDo = manageLineDoMap.get(manageLine);
                String manageLineName = getManageLineName(eamLanguage, manageLineDo);
                menuFunctionRes.setName(manageLineName);
                List<String> menuCodeList = roleCodeMap.get(manageLine);
                List<String> functionCodeList = functionCodeMap.get(manageLine);
                List<MenuRes> filterMenu = filterMenuAndFunction(copyMenus, menuCodeList, functionCodeList);
                // 根据角色合并菜单
                menuFunctionRes.setChildren(filterMenu);
                List<String> allMenuResource = new ArrayList<>();
                List<String> permissionResources = getPermissionResourceCodes(allMenuResource, filterMenu);
                menuFunctionRes.setPermissionResource(permissionResources);
                List<MenuBusinessRes> businessLineRes = menuBusinessMap.getOrDefault(manageLine, Collections.emptyList());
                menuFunctionRes.setBusinessLineResource(businessLineRes);
                buildMenuResList.add(menuFunctionRes);
            }
        } catch (Exception e) {
            log.error("获取用户菜单数据异常", e);
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "获取用户菜单数据异常");
        }
        return buildMenuResList;
    }

    private String getManageLineName(String eamLanguage, ManageLineDo manageLineDo) {
        if (manageLineDo != null) {
            return CHINESE.equals(eamLanguage) ? manageLineDo.getManageLineName() : manageLineDo.getManageLineNameEn();
        }
        return StringUtils.EMPTY;
    }

    @NotNull
    private Map<String, List<MenuBusinessRes>> getBusinessMap(Map<String, List<String>> dimensionResourceCodeMap, String eamSource, String eamLanguage) {
        // 查询用户业务线权限
        List<String> businessLineList = new ArrayList<>();
        Map<String, List<BusinessLineDo>> businessMappingMap = new HashMap<>();
        List<BusinessLineDo> businessLineDos = businessLineRepo.searchAll();
        if (EAM_ADMIN.equals(eamSource)) {
            businessLineList = dimensionResourceCodeMap.getOrDefault("BUSINESS_LINE", Collections.emptyList());
            List<String> finalBusinessLineList = businessLineList;
            businessMappingMap = businessLineDos.stream()
                    .filter(o -> finalBusinessLineList.contains(o.getBusinessLine()))
                    .collect(Collectors.groupingBy(BusinessLineDo::getManageLineCode));
        } else {
            businessLineList = businessLineDos.stream().map(BusinessLineDo::getBusinessLine).collect(Collectors.toList());
            businessMappingMap = businessLineDos.stream().collect(Collectors.groupingBy(BusinessLineDo::getManageLineCode));
        }

        // 如果管理线时集团资产，就包含全部业务线
        List<String> finalBusinessLineList1 = businessLineList;
        businessMappingMap.put("group_asset", businessLineDos.stream().filter(
                o -> finalBusinessLineList1.contains(o.getBusinessLine())).collect(Collectors.toList()));

        Map<String, List<MenuBusinessRes>> menuBusinessMap = new HashMap<>(businessMappingMap.size());
        for (Map.Entry<String, List<BusinessLineDo>> entry : businessMappingMap.entrySet()) {
            String manageLine = entry.getKey();
            List<BusinessLineDo> value = entry.getValue();
            List<MenuBusinessRes> businessResList = new ArrayList<>(value.size());
            for (BusinessLineDo businessLineDo : value) {
                MenuBusinessRes businessRes = new MenuBusinessRes();
                businessRes.setId(businessLineDo.getBusinessLine());
                businessRes.setTitle(CHINESE.equals(eamLanguage) ? businessLineDo.getBusinessLineName() : businessLineDo.getBusinessLineNameEn());
                businessRes.setIsNewLine(businessLineDo.getIsNewLine());
                businessResList.add(businessRes);
            }
            List<MenuBusinessRes> businessLine = menuBusinessMap.get(manageLine);
            if (CollectionUtils.isEmpty(businessLine)) {
                menuBusinessMap.put(manageLine, businessResList);
            } else {
                List<MenuBusinessRes> retainAll = ListUtils.retainAll(businessLine, businessResList);
                menuBusinessMap.put(manageLine, retainAll);
            }
        }
        return menuBusinessMap;
    }

    @Override
    public List<MenuBusinessLineRes> getMenuBusinessLineRes(String userName, String appCode, Integer accountType) {
        if (StringUtils.isBlank(appCode)) {
            appCode = authorityProperties.getAppCode();
        }
        AccountTypeEnum item = AccountTypeEnum.getItem(accountType);
        if (item == null) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "账户类型异常");
        }
        BaseResp<AccountAuthorityResp> accountAuthorityResp = authorityClientService.queryAccountAuth(userName, item.name(), appCode);
        if (BaseResp.CODE_SUCCESS != accountAuthorityResp.getCode()) {
            throw new ErrorCodeException(ErrorCodes.BAD_REQUEST, "查询用户数据权限异常");
        }
        AccountAuthorityResp authorityResp = accountAuthorityResp.getData();
        Map<String, List<String>> dimensionResourceCodeMap = authorityResp.getDimensionResourceCodeMap();
        // 业务线数据
        List<String> businessLines = dimensionResourceCodeMap.getOrDefault("BUSINESS_LINE", Collections.emptyList());
        List<BusinessLineDo> businessLineDos = businessLineRepo.searchAll();
        Map<String, BusinessLineDo> businessLineDoMap = businessLineDos.stream()
                .collect(Collectors.toMap(BusinessLineDo::getBusinessLine, Function.identity()));

        Map<String, List<String>> businessMap = new HashMap<>(businessLines.size());
        for (String businessLine : businessLines) {
            BusinessLineDo businessLineDo = businessLineDoMap.get(businessLine);
            if (Objects.isNull(businessLineDo)) continue;

            String manageLine = businessLineDo.getManageLineCode();
            List<String> businessLineCodes = businessMap.get(manageLine);
            if (CollectionUtils.isEmpty(businessLineCodes)) {
                businessLineCodes = new ArrayList<>();
                businessLineCodes.add(businessLine);
            } else if (!businessLineCodes.contains(businessLine)) {
                businessLineCodes.add(businessLine);
            }
            businessMap.put(manageLine, businessLineCodes);
        }
        List<MenuBusinessLineRes> businessLineRes = new ArrayList<>(businessLines.size());
        for (Map.Entry<String, List<String>> entry : businessMap.entrySet()) {
            String key = entry.getKey();
            List<String> value = entry.getValue();
            MenuBusinessLineRes menuBusinessLineRes = new MenuBusinessLineRes();
            List<MenuBusinessLineRes.Children> businessLineChildren = new ArrayList<>(value.size());
            menuBusinessLineRes.setBusinessCode(key);
            for (String childCode : value) {
                MenuBusinessLineRes.Children children = new MenuBusinessLineRes.Children();
                children.setId(childCode);
                BusinessLineDo businessLineDo = businessLineDoMap.get(childCode);
                children.setTitle(businessLineDo.getBusinessLineName());
                businessLineChildren.add(children);
            }
            menuBusinessLineRes.setChildren(businessLineChildren);
            businessLineRes.add(menuBusinessLineRes);
        }
        return businessLineRes;
    }

    @Override
    public List<String> getInventoryMenuAuth(String userName) {
        ClientSingleResponse<List<ResourceTreeResp>> clientSingleResponse = resourceService.queryMenuFunction(authorityProperties.getAppCode(), userName, AccountTypeEnum.INTRA.name());
        if (clientSingleResponse != null) {
            String code = clientSingleResponse.getCode();
            if (String.valueOf(BaseResp.CODE_SUCCESS).equals(code)) {
                List<ResourceTreeResp> data = clientSingleResponse.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    List<MenuRes> menuResList = resourceConverter.resourceTreeListToResList(data);
                    BaseResp<AccountAuthorityResp> accountAuthorityResp = authorityClientService.queryAccountAuth(userName, AccountTypeEnum.INTRA.name(), authorityProperties.getAppCode());
                    if (BaseResp.CODE_SUCCESS == accountAuthorityResp.getCode()) {
                        AccountAuthorityResp authorityResp = accountAuthorityResp.getData();
                        List<String> roleCodeList = authorityResp.getRoleCodeList();

                        Map<String, List<String>> roleCodeMap = this.getRoleMenuResource(authorityProperties.getAppCode(), roleCodeList);

                        Map<String, List<String>> dimensionResourceCodeMap = authorityResp.getDimensionResourceCodeMap();

                        // 子业务线数据
                        List<String> manageLineList = dimensionResourceCodeMap.getOrDefault(MANAGE_LINE, Collections.emptyList());

                        List<String> inventoryManagerLineList = new ArrayList<>(manageLineList.size());
                        for (String manageLine : manageLineList) {
                            List<String> menuCodeList = roleCodeMap.get(manageLine);
                            List<MenuRes> inventoryMenuRes = new ArrayList<>();
                            filterInventory(menuResList, menuCodeList, inventoryMenuRes);
                            if (!inventoryMenuRes.isEmpty()) {
                                inventoryManagerLineList.add(manageLine);
                            }
                        }
                        return inventoryManagerLineList;
                    }
                }
            }
        }

        return Collections.emptyList();
    }

    private List<ResourceRes> getPermissionResources(List<ResourceRes> allMenuResource, List<MenuRes> menuResList) {
        if (CollectionUtils.isNotEmpty(menuResList)) {
            for (MenuRes menuRes : menuResList) {
                List<ResourceRes> resourceList = menuRes.getResourceList();
                if (CollectionUtils.isNotEmpty(resourceList)) {
                    allMenuResource.addAll(resourceList);
                }
                getPermissionResources(allMenuResource, menuRes.getChildren());
            }
        }
        return allMenuResource;
    }

    private List<String> getPermissionResourceCodes(List<String> allMenuResource, List<MenuRes> menuResList) {
        if (CollectionUtils.isNotEmpty(menuResList)) {
            for (MenuRes menuRes : menuResList) {
                List<ResourceRes> resourceList = menuRes.getResourceList();
                if (CollectionUtils.isNotEmpty(resourceList)) {
                    List<String> resourceCodeList = resourceList.stream().map(ResourceRes::getResourceCode).collect(Collectors.toList());
                    allMenuResource.addAll(resourceCodeList);
                }
                getPermissionResourceCodes(allMenuResource, menuRes.getChildren());
            }
        }
        return allMenuResource;
    }

    private List<MenuRes> filterMenus(List<MenuRes> menuResList) {
        List<MenuRes> filterMenus = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(menuResList)) {
            Iterator<MenuRes> iterator = menuResList.iterator();
            while (iterator.hasNext()) {
                MenuRes menuRes = iterator.next();
                if (Boolean.TRUE.equals(menuRes.getIsShow())) {
                    List<MenuRes> childrenMenus = filterMenus(menuRes.getChildren());
                    childrenMenus.clear();
                    List<MenuRes> children = menuRes.getChildren();
                    if (children == null) {
                        menuRes.setChildren(childrenMenus);
                    } else {
                        children.addAll(childrenMenus);
                    }
                    filterMenus.add(menuRes);
                } else {
                    iterator.remove();
                }
            }
        }
        return filterMenus;
    }

    private Map<String, Map<String, List<String>>> getRoleMenusRes(String appCode, List<String> roleCodeList, String eamSource, List<String> allManageLineList) {
        Map<String, Map<String, List<String>>> roleMenuRes = new HashMap<>();
        Map<String, List<String>> manageLineMenuMap = new HashMap<>();
        Map<String, List<String>> functionResourceMap = new HashMap<>();
        for (String roleCode : roleCodeList) {
            BaseResp<RoleNewResp> roleNewRespBaseResp = roleService.queryRoleLoadResource(appCode, roleCode);
            int code = roleNewRespBaseResp.getCode();
            if (BaseResp.CODE_SUCCESS == code) {
                RoleNewResp data = roleNewRespBaseResp.getData();
                List<String> menuResourceCodeList = data.getMenuResourceCodeList();
                // 没有菜单权限直接跳过
                if (CollectionUtils.isEmpty(menuResourceCodeList)) continue;

                List<String> resourceCodeList = data.getFunctionResourceCodeList();

                Map<String, List<String>> dimensionDataResourceCodeMapList = data.getDimensionDataResourceCodeMapList();
                if (EAM_CLIENT.equals(eamSource)) {
                    Map<String, List<String>> manageLineMap = new HashMap<>();
                    manageLineMap.put(MANAGE_LINE, allManageLineList);
                    dimensionDataResourceCodeMapList = manageLineMap;
                }
                if (dimensionDataResourceCodeMapList == null) continue;
                List<String> manageLineList = dimensionDataResourceCodeMapList.get(MANAGE_LINE);
                if (CollectionUtils.isEmpty(manageLineList)) continue;

                for (String manageLine : manageLineList) {
                    // 菜单资源
                    buildResourceMap(manageLineMenuMap, menuResourceCodeList, manageLine);
                    // 功能权限
                    buildResourceMap(functionResourceMap, resourceCodeList, manageLine);
                }
            }
        }
        roleMenuRes.put("roleCode", manageLineMenuMap);
        roleMenuRes.put("functionCode", functionResourceMap);
        return roleMenuRes;
    }

    private Map<String, List<String>> getRoleMenuResource(String appCode, List<String> roleCodeList) {
        Map<String, List<String>> manageLineMenuMap = new HashMap<>();
        for (String roleCode : roleCodeList) {
            BaseResp<RoleNewResp> roleNewRespBaseResp = roleService.queryRoleLoadResource(appCode, roleCode);
            int code = roleNewRespBaseResp.getCode();
            if (BaseResp.CODE_SUCCESS == code) {
                RoleNewResp data = roleNewRespBaseResp.getData();
                List<String> menuResourceCodeList = data.getMenuResourceCodeList();
                Map<String, List<String>> dimensionDataResourceCodeMapList = data.getDimensionDataResourceCodeMapList();
                if (Objects.isNull(dimensionDataResourceCodeMapList)) continue;
                List<String> manageLineList = dimensionDataResourceCodeMapList.get(MANAGE_LINE);
                if (CollectionUtils.isEmpty(manageLineList)) {
                    continue;
                }
                for (String manageLine : manageLineList) {
                    // 菜单资源
                    buildResourceMap(manageLineMenuMap, menuResourceCodeList, manageLine);
                }
            }
        }
        return manageLineMenuMap;
    }

    private void buildResourceMap(Map<String, List<String>> resourceMap, List<String> menuResourceCodeList, String manageLine) {
        List<String> menuCodes = resourceMap.get(manageLine);
        if (CollectionUtils.isEmpty(menuCodes)) {
            resourceMap.put(manageLine, menuResourceCodeList);
        } else {
            // 同一个管理线菜单，功能权限取并集
            if (CollectionUtils.isEmpty(menuResourceCodeList)) {
                return;
            }
            List<String> union = (List) CollectionUtils.union(menuCodes, menuResourceCodeList);
            resourceMap.put(manageLine, union);
        }
    }

    private List<MenuRes> filterMenuAndFunction(List<MenuRes> menuResList, List<String> menuCodeList, List<String> functionCodeList) {
        if (CollectionUtils.isEmpty(menuCodeList) || CollectionUtils.isEmpty(menuResList)) {
            return Collections.emptyList();
        }
        List<MenuRes> filterMenus = new ArrayList<>();
        Iterator<MenuRes> iterator = menuResList.iterator();
        while (iterator.hasNext()) {
            MenuRes menuRes = iterator.next();
            if (menuCodeList.contains(menuRes.getResourceCode())) {
                List<MenuRes> childrenMenus = filterMenuAndFunction(menuRes.getChildren(), menuCodeList, functionCodeList);
                childrenMenus.clear();
                List<MenuRes> children = menuRes.getChildren();
                if (children == null) {
                    menuRes.setChildren(childrenMenus);
                } else {
                    children.addAll(childrenMenus);
                }
                List<ResourceRes> resourceList = menuRes.getResourceList();
                if (CollectionUtils.isNotEmpty(resourceList)) {
                    // 不为空才过滤
                    if (CollectionUtils.isNotEmpty(functionCodeList)) {
                        List<ResourceRes> filterFunctionRes = resourceList.stream().filter(resourceRes -> functionCodeList.contains(resourceRes.getResourceCode()))
                                .collect(Collectors.toList());
                        menuRes.setResourceList(filterFunctionRes);
                    } else {
                        menuRes.setResourceList(new ArrayList<>());
                    }
                }
                filterMenus.add(menuRes);
            } else {
                iterator.remove();
            }
        }
        return filterMenus;
    }

    private void filterInventory(List<MenuRes> menuResList, List<String> menuCodeList, List<MenuRes> inventoryMenuRes) {
        for (MenuRes menus : menuResList) {
            if (menus.getResourceName().startsWith("盘点") && menuCodeList.contains(menus.getResourceCode())) {
                inventoryMenuRes.add(menus);
                break;
            }
            if (CollectionUtils.isNotEmpty(menus.getChildren())) {
                filterInventory(menus.getChildren(), menuCodeList, inventoryMenuRes);
            }
        }
    }
}
