package com.mi.oa.asset.commons.config.app.config;

import io.micrometer.core.instrument.util.IOUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;

import javax.servlet.ReadListener;
import javax.servlet.ServletException;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Enumeration;



@Slf4j
public class X5RequestWrapper extends HttpServletRequestWrapper {

    @Getter
    private String x5Data;

    private InputStream in;

    public X5RequestWrapper(HttpServletRequest request) throws IOException, ServletException {
        super(request);

        String contentType = request.getContentType();
        log.info("------------contentType:{}",contentType);

        String body = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
        log.info("------------------request io body: {}",body);
        if(!StringUtils.isEmpty(body)){
            x5Data = URLDecoder.decode(body.split("=")[1], "UTF-8");
            log.info("X5 request url {}, body {}, x5 data {}", request.getPathInfo(), body, x5Data);
        }else {
            String base64 = request.getParameter("data");
            log.info("---------------data parameter base64:{}",base64);
            x5Data = URLDecoder.decode(base64, "UTF-8");
            log.info("X5 request url {}, body {}, x5 data {}", request.getPathInfo(), base64, x5Data);
        }

        this.in = new ByteArrayInputStream(x5Data.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        return new ServletInputStreamDelegator(in);
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(in));
    }

    @Override
    public String getContentType() {
        return MediaType.APPLICATION_JSON_VALUE;
    }

    @Override
    public String getHeader(String name) {
        if ("Content-Type".equalsIgnoreCase(name)) {
            return this.getContentType();
        }

        return super.getHeader(name);
    }

    @Override
    public Enumeration<String> getHeaders(String name) {
        if ("Content-Type".equalsIgnoreCase(name)) {
            return Collections.enumeration(Collections.singletonList(this.getContentType()));
        }

        return super.getHeaders(name);
    }

    public void setInputStream(String data) {
        this.in = new ByteArrayInputStream(data.getBytes(StandardCharsets.UTF_8));
    }

    private class ServletInputStreamDelegator extends ServletInputStream {

        final InputStream source;

        boolean finished = false;

        protected ServletInputStreamDelegator(InputStream in) {
            this.source = in;
        }

        @Override
        public int read() throws IOException {
            int data = this.source.read();
            if (data == -1) {
                this.finished = true;
            }

            return data;
        }

        @Override
        public boolean isFinished() {
            return finished;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener readListener) {
        }
    }
}


