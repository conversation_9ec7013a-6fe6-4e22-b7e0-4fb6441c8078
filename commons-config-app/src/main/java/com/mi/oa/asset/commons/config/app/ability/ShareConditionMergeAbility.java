package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.commons.config.api.assetcategory.AssetCategoryProvider;
import com.mi.oa.asset.commons.config.api.assetcategory.AssetCategoryRes;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgProvider;
import com.mi.oa.asset.commons.config.api.assetorganization.AssetOrgStructureRes;
import com.mi.oa.asset.commons.config.api.position.PositionProvider;
import com.mi.oa.asset.commons.config.api.position.PositionRes;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.CustomShareList;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRange;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRecord;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.CustomShareListRepo;
import com.mi.oa.asset.commons.config.domain.assetshare.repository.ShareRangeRepo;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.eam.jxs.enums.QueryConditionEnum;
import com.mi.oa.asset.eam.jxs.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资产共享处理配置规则SQL条件拼接
 *
 * <AUTHOR>
 * @date 2025-02-27 18:27
 */
@Slf4j
@Service
public class ShareConditionMergeAbility {

    private static final String QUOTE_AND = "' and ";
    private static final String BRACKET_AND = "') and ";

    @Resource
    private ShareRangeRepo shareRangeRepo;

    @Resource
    private CustomShareListRepo shareListRepo;

    @Resource
    private AssetOrgProvider assetOrgProvider;

    @Resource
    private PositionProvider positionProvider;

    @Resource
    private AssetCategoryProvider categoryProvider;

    public String buildShareWhereSql(String businessLine, List<ShareRecord> shareRecords, List<CustomShareList> allShareLists) {
        List<String> manageDeptCodes = new ArrayList<>();
        List<String> useDeptCodes = new ArrayList<>();
        List<Integer> locationIds = new ArrayList<>();
        List<Integer> typeIds = new ArrayList<>();
        Set<String> assetTypes = new HashSet<>();
        Set<String> useStatus = new HashSet<>();
        Set<String> checkStatus = new HashSet<>();
        List<Integer> shareIds = new ArrayList<>();
        // 将相同的共享记录条件进行组合
        for (ShareRecord shareRecord : shareRecords) {
            // 管理部门和使用部门
            this.buildListString(manageDeptCodes, shareRecord.getManageDeptCode());
            this.buildListString(useDeptCodes, shareRecord.getUseDeptCode());
            // 位置和分类
            this.buildListInteger(locationIds, shareRecord.getLocationId());
            this.buildListInteger(typeIds, shareRecord.getAssetCateId());
            // 资产属性条件构造
            this.buildSetObject(assetTypes, shareRecord.getAssetType());
            this.buildSetObject(useStatus, shareRecord.getUseStatus());
            this.buildSetObject(checkStatus, shareRecord.getCheckStatus());
            // 共享主键集合
            shareIds.add(shareRecord.getId());
        }
        // 拼接whereSql
        StringBuilder whereSql = new StringBuilder();
        // 查询获取管理部门子级
        this.getAllSubDept(businessLine, manageDeptCodes, whereSql, "manage_dept_code in ('");
        // 查询获取使用部门子级, 预约场景获取实验室编码信息
        this.getAllSubDept(businessLine, useDeptCodes, whereSql, "use_dept_code in ('");
        // 查询获取位置子级
        this.getLocationWithSubById(locationIds, whereSql);
        // 查询获取分类子级
        this.getAssetCategoryByIds(typeIds, whereSql);
        // 资产属性条件添加
        if (CollectionUtils.isNotEmpty(assetTypes)) {
            whereSql.append(" asset_type in ('");
            whereSql.append(String.join(CommonConstant.IN_JOINT, assetTypes));
            whereSql.append(BRACKET_AND);
        }
        if (CollectionUtils.isNotEmpty(useStatus)) {
            whereSql.append(" use_status in ('");
            whereSql.append(String.join(CommonConstant.IN_JOINT, useStatus));
            whereSql.append(BRACKET_AND);
        }
        if (CollectionUtils.isNotEmpty(checkStatus)) {
            whereSql.append(" check_status in ('");
            whereSql.append(String.join(CommonConstant.IN_JOINT, checkStatus));
            whereSql.append(BRACKET_AND);
        }
        log.info("queryShareList where params info:{}", whereSql);
        // 查询共享更多条件范围
        List<ShareRange> shareRanges = shareRangeRepo.getByShareIds(shareIds);
        String extendSql = this.buildExtendSql(shareRanges);
        // 查询自定义共享清单
        List<CustomShareList> shareLists = shareListRepo.getByShareIds(shareIds);
        if (CollectionUtils.isNotEmpty(shareLists)) {
            allShareLists.addAll(shareLists);
        }
        String customSql = this.buildCustomSql(shareLists);
        // 不符合条件共享规则，返回空字符串
        if (StringUtils.isBlank(whereSql) && StringUtils.isBlank(extendSql) && StringUtils.isBlank(customSql)) {
            return "";
        }
        StringBuilder sql = new StringBuilder();
        sql.append("(").append("business_line = '").append(businessLine).append(QUOTE_AND);
        sql.append(whereSql).append(extendSql);
        int length = sql.length();
        // 把最后一个and删除
        sql.delete(length - 4, length);
        if (StringUtils.isNotBlank(customSql)) {
            if (StringUtils.isBlank(whereSql) && StringUtils.isBlank(extendSql)) {
                sql.append("and ");
            } else {
                sql.append("or ");
            }
            sql.append(customSql);
        }
        sql.append(")");
        return sql.toString();
    }

    private void buildListString(List<String> list, String deptCodes) {
        if (StringUtils.isNotBlank(deptCodes)) {
            if (deptCodes.contains(CommonConstant.SEMICOLON)) {
                Collections.addAll(list, deptCodes.split(CommonConstant.SEMICOLON));
            } else {
                list.add(deptCodes);
            }
        }
    }

    private void buildListInteger(List<Integer> list, String ids) {
        if (StringUtils.isNotBlank(ids)) {
            if (ids.contains(CommonConstant.SEMICOLON)) {
                for (String id : ids.split(CommonConstant.SEMICOLON)) {
                    list.add(Integer.parseInt(id));
                }
            } else {
                list.add(Integer.parseInt(ids));
            }
        }
    }

    private void buildSetObject(Set<String> set, String obj) {
        if (StringUtils.isNotBlank(obj)) {
            if (obj.contains(CommonConstant.SEMICOLON)) {
                Collections.addAll(set, obj.split(CommonConstant.SEMICOLON));
            } else {
                set.add(obj);
            }
        }
    }

    // 查询组织机构相关子级部门
    private void getAllSubDept(String businessLine, List<String> deptCodes, StringBuilder whereSql, String whereFiled) {
        Set<String> allDept = new HashSet<>();
        for (String deptCode : deptCodes) {
            List<AssetOrgStructureRes> orgStructureRes = assetOrgProvider.getOrgStructureWithSub(deptCode, Collections.singletonList(businessLine));
            List<String> collect = orgStructureRes.stream().map(AssetOrgStructureRes::getOrgCode).collect(Collectors.toList());
            allDept.addAll(collect);
        }
        // 拼接部门where
        if (CollectionUtils.isNotEmpty(allDept)) {
            whereSql.append(whereFiled);
            whereSql.append(String.join(CommonConstant.IN_JOINT, allDept));
            whereSql.append(BRACKET_AND);
        }
    }

    // 查询位置相关子级
    private void getLocationWithSubById(List<Integer> locationIds, StringBuilder whereSql) {
        if (CollectionUtils.isEmpty(locationIds)) return;
        List<PositionRes> positionRes = positionProvider.getPositions(locationIds, true);
        Set<String> positionCodes = positionRes.stream().map(PositionRes::getPositionCode).collect(Collectors.toSet());
        // 拼接位置where
        if (CollectionUtils.isNotEmpty(positionCodes)) {
            whereSql.append("location_code in ('");
            whereSql.append(String.join(CommonConstant.IN_JOINT, positionCodes));
            whereSql.append(BRACKET_AND);
        }
    }

    // 查询资产分类相关子级
    public void getAssetCategoryByIds(List<Integer> assetCateIds, StringBuilder whereSql) {
        if (CollectionUtils.isEmpty(assetCateIds)) return;
        List<AssetCategoryRes> assetCategoryList = categoryProvider.getAssetCategoryByIds(assetCateIds, true);
        Set<String> cateSet = assetCategoryList.stream().map(AssetCategoryRes::getCateCode).collect(Collectors.toSet());
        // 拼接分类where
        if (CollectionUtils.isNotEmpty(cateSet)) {
            whereSql.append("asset_cate_code in ('");
            whereSql.append(String.join(CommonConstant.IN_JOINT, cateSet));
            whereSql.append(BRACKET_AND);
        }
    }

    public String buildExtendSql(List<ShareRange> rangeList) {
        StringBuilder sql = new StringBuilder();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(rangeList)) {
            for (ShareRange shareRange : rangeList) {
                String fieldCode = shareRange.getFieldCode();
                String symbol = shareRange.getQueryCond();
                String rangeValue = shareRange.getFieldValues();
                String fieldName = CommonUtils.toUnderscore(fieldCode);
                if (StringUtils.isBlank(fieldName) || StringUtils.isBlank(symbol) || StringUtils.isBlank(rangeValue))
                    continue;

                QueryConditionEnum cond = QueryConditionEnum.getByCode(symbol);
                switch (cond) {
                    case EQ:     // 等于
                        sql.append(fieldName).append(" = '").append(rangeValue).append(QUOTE_AND);
                        break;
                    case NE:   // 不等于
                        sql.append(fieldName).append(" <> '").append(rangeValue).append(QUOTE_AND);
                        break;
                    case LIKE:   // 包含
                        sql.append(fieldName).append(" like '%").append(rangeValue).append("%").append(QUOTE_AND);
                        break;
                    case NOT_LIKE:   // 不包含
                        sql.append(fieldName).append(" not like '%").append(rangeValue).append("%").append(QUOTE_AND);
                        break;
                    case LEFT_LIKE:
                        sql.append(fieldName).append(" like '").append(rangeValue).append("%").append(QUOTE_AND);
                        break;
                    case GT:    // 大于
                        sql.append(fieldName).append(" > '").append(rangeValue).append(QUOTE_AND);
                        break;
                    case GE:    // 大于等于
                        sql.append(fieldName).append(" >= '").append(rangeValue).append(QUOTE_AND);
                        break;
                    case LT:   // 小于
                        sql.append(fieldName).append(" < '").append(rangeValue).append(QUOTE_AND);
                        break;
                    case LE:    // 小于等于
                        sql.append(fieldName).append(" <= '").append(rangeValue).append(QUOTE_AND);
                        break;
                    case IN:
                        sql.append(fieldName).append(" in ('");
                        sql.append(String.join("','", rangeValue.split(CommonConstant.SEMICOLON)));
                        sql.append(BRACKET_AND);
                        break;
                    case NOT_IN:
                        sql.append(fieldName).append(" not in ('");
                        sql.append(String.join("','", rangeValue.split(CommonConstant.SEMICOLON)));
                        sql.append(BRACKET_AND);
                        break;
                    default:
                }
            }
        }
        log.info("buildExtendSql info:{}", sql);
        return sql.toString();
    }

    public String buildCustomSql(List<CustomShareList> customShareList) {
        StringBuilder sql = new StringBuilder();
        if (CollectionUtils.isNotEmpty(customShareList)) {
            sql.append("id in (");
            for (CustomShareList customAsset : customShareList) {
                sql.append(customAsset.getAssetId()).append(CommonConstant.COMMA);
            }
            sql.deleteCharAt(sql.length() - 1);
            sql.append(")");
        }
        log.info("buildCustomSql info:{}", sql);
        return sql.toString();
    }
}
