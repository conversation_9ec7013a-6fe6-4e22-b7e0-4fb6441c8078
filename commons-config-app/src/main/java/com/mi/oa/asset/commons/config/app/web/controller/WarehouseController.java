package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.commons.config.api.warehouse.*;
import com.mi.oa.asset.commons.config.api.common.DelByIdsReq;
import com.mi.oa.asset.commons.config.api.warehouse.SaveWarehouseReq;
import com.mi.oa.asset.commons.config.api.warehouse.WarehouseProvider;
import com.mi.oa.asset.commons.config.api.warehouse.WarehouseRes;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RequestMapping("/configs/warehouse")
@HttpApiModule(value = "WarehouseController", apiController = WarehouseController.class)
@RestController
public class WarehouseController {

    @Resource
    private WarehouseProvider warehouseProvider;

    @Resource
    private WarehouseAreaProvider warehouseAreaProvider;

    @PostMapping("/save")
    @HttpApiDoc(value = "/configs/warehouse/save", method = MiApiRequestMethod.POST, description = "保存/编辑仓库", apiName = "保存/编辑仓库")
    public Result<Integer> save(@RequestBody SaveWarehouseReq req) {
        return Result.success(warehouseProvider.saveWarehouse(req));
    }

    @PostMapping("/delete")
    @HttpApiDoc(value = "/configs/warehouse/delete", method = MiApiRequestMethod.POST, description = "批量删除仓库", apiName = "批量删除仓库")
    public Result<Integer> delete(@RequestBody DelByIdsReq req) {
        warehouseProvider.deleteWarehouse(req.getIds());
        return Result.success();
    }

    @HttpApiDoc(apiName = "批量导入", value = "/configs/warehouse/import", method = MiApiRequestMethod.POST)
    @PostMapping("/import")
    public Result<Void> importWarehouse(@HttpApiDocClassDefine(value = "file", description = "导入文件", required = true) @RequestParam("file") MultipartFile file) {
        warehouseProvider.importWarehouse(file);
        return Result.success();
    }

    @GetMapping("/list")
    @HttpApiDoc(value = "/configs/warehouse/list", method = MiApiRequestMethod.GET, description = "批量查询生效的仓库", apiName = "批量查询仓库（无权限控制，提供员工端使用）")
    public Result<List<WarehouseRes>> list(@RequestParam(value = "businessLine") String businessLine,
                                           @RequestParam(value = "service") String service,
                                           @RequestParam(value = "key", required = false) String key) {
        return Result.success(warehouseProvider.getBatchByServiceType(businessLine, service, key));
    }

    @GetMapping("/findByBusinessLine")
    @HttpApiDoc(value = "/configs/warehouse/findByBusinessLine", method = MiApiRequestMethod.GET, description = "根据业务线查询仓库", apiName = "根据业务线查询仓库")
    public Result<List<WarehouseRes>> findByBusinessLine(@RequestParam String businessLine) {
        return Result.success(warehouseProvider.getByBusinessLine(businessLine));
    }

    @PostMapping("/saveWarehouseArea")
    @HttpApiDoc(value = "/configs/warehouse/saveWarehouseArea", method = MiApiRequestMethod.POST, description = "保存/编辑仓库区域", apiName = "保存/编辑印度仓库")
    public Result<Integer> saveWarehouseArea(@RequestBody WarehouseAreaReq req) {
        return Result.success(warehouseAreaProvider.saveWarehouse(req));
    }


    @PostMapping("/removeWarehouseArea")
    @HttpApiDoc(value = "/configs/warehouse/removeWarehouseArea", method = MiApiRequestMethod.POST, description = "移除仓库区域", apiName = "移除仓库区域")
    public Result<Void> removeWarehouseArea(@RequestBody RemoveWarehouseAreaReq req) {
        warehouseAreaProvider.removeWarehouse(req);
        return Result.success();
    }
    /**
     * 根据业务线, 地区查询仓库列表
     */
    @GetMapping("/listByBizAndRegion")
    @HttpApiDoc(value = "/configs/warehouse/listByBizAndRegion", method = MiApiRequestMethod.GET, description = "根据业务线, 地区查询仓库列表", apiName = "根据业务线, 地区查询仓库列表")
    public Result<List<WarehouseAreaRes>> listByBizAndRegion(@RequestParam(value = "businessLine") List<String> businessLines,
                                                             @RequestParam(value = "areaId", required = false) String region,
                                                             @RequestParam(value = "areaName", required = false) String areaName) {
        return Result.success(warehouseAreaProvider.listByBizAndRegion(businessLines, region, areaName));
    }


    /**
     * 根据关键词（编码或名称）模糊查询仓库区域，支持业务线过滤
     */
    @GetMapping("/searchWarehouseArea")
    @HttpApiDoc(value = "/configs/warehouse/searchWarehouseArea", method = MiApiRequestMethod.GET, description = "根据关键词（编码或名称）模糊查询仓库区域", apiName = "模糊查询仓库区域")
    public Result<List<WarehouseAreaRes>> searchWarehouseArea(
            @RequestParam(value = "keyWord", required = false) String keyWord,
            @RequestParam(value = "businessLines") List<String> businessLines) {
        return Result.success(warehouseAreaProvider.searchWarehouseArea(keyWord, businessLines));
    }

}
