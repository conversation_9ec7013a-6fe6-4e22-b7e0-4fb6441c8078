package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.assetshare.CustomShareListReq;
import com.mi.oa.asset.commons.config.api.assetshare.CustomShareListRes;
import com.mi.oa.asset.commons.config.api.assetshare.ShareRecordBaseReq;
import com.mi.oa.asset.commons.config.api.assetshare.ShareRecordBaseRes;
import com.mi.oa.asset.commons.config.api.common.ComplexRange;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.CustomShareList;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRange;
import com.mi.oa.asset.commons.config.domain.assetshare.entity.ShareRecord;
import com.mi.oa.asset.commons.config.domain.common.constant.CommonConstant;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Mapper(componentModel = "spring")
public interface AssetShareConverter extends CommonConverter {
    /**
     * 共享保存请求entity转DO
     */
    ShareRecord recordReqToDo(ShareRecordBaseReq req);

    /**
     * 共享记录查询DO转RES
     *
     * @param shareRecord
     * @return
     */
    ShareRecordBaseRes recordToRes(ShareRecord shareRecord);

    /**
     * 共享资产更多筛选条件请求entity转DO
     *
     * @param req
     * @return
     */
    ShareRange rangeReqToDo(ComplexRange req);

    List<ShareRange> rangeReqToDoList(List<ComplexRange> reqList);

    /**
     * 共享资产更多筛选条件DO转RES
     *
     * @param shareRange
     * @return
     */
    ComplexRange rangeToRes(ShareRange shareRange);

    List<ComplexRange> rangToResList(List<ShareRange> shareRangeList);

    /**
     * 自定义共享清单请求entity转DO
     *
     * @param req
     * @return
     */
    CustomShareList shareListReqToDo(CustomShareListReq req);

    List<CustomShareList> shareListReqToDoList(List<CustomShareListReq> reqList);

    /**
     * 自定义共享清单DO转RES
     *
     * @param customShareList
     * @return
     */
    CustomShareListRes shareListToRes(CustomShareList customShareList);

    List<CustomShareListRes> shareListToResList(List<CustomShareList> doList);


    // str转list
    default List<String> str2List(String src) {
        if (StringUtils.isBlank(src)) return new ArrayList<>();
        String[] split = src.split(CommonConstant.SEMICOLON);
        return Arrays.asList(split);
    }

    // list转str
    default String list2Str(List<String> src) {
        return String.join(CommonConstant.SEMICOLON, src);
    }
}
