package com.mi.oa.asset.commons.config.app.ability;

import com.mi.oa.asset.account.api.accountmodel.AssetAccountModelProvider;
import com.mi.oa.asset.account.api.accountmodel.res.ModelFieldConfigRes;
import com.mi.oa.asset.account.api.assetaccount.AssetBizFunc;
import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.queryfield.FieldConfigInfo;
import com.mi.oa.asset.commons.config.api.queryfield.FieldConfigRes;
import com.mi.oa.asset.commons.config.api.queryfield.QueryConfigReq;
import com.mi.oa.asset.commons.config.api.queryfield.QueryConfigValidateGroup;
import com.mi.oa.asset.commons.config.domain.queryfield.enums.FieldConfigTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/24 14:12
 */

@Service
@Slf4j
public class QueryConfigAbility {
    private static final String DEFAULT_NAME = "eam-%s-advanced-search-default-query-config";
    private static final List<String> EXCLUDE_ACCOUNT_FIELD = Arrays.asList("id", "photos", "businessLine");

    @Resource
    private Validator validator;

    @DubboReference(check = false)
    private AssetAccountModelProvider assetAccountModelProvider;

    public List<ConstraintViolation<QueryConfigReq>> validateSaveReq(QueryConfigReq queryConfigReq, boolean isDefault) {
        List<Class> groups = new ArrayList<>();

        if (!isDefault) {
            groups.add(QueryConfigValidateGroup.NotDefault.class);
        }

        Class[] validateGroups = groups.toArray(new Class[groups.size()]);
        Set<ConstraintViolation<QueryConfigReq>> errors = validator.validate(queryConfigReq, validateGroups);

        return new ArrayList<>(errors);
    }

    public String getDefaultConfigName(String funId) {
        return String.format(DEFAULT_NAME, funId);
    }

    public List<FieldConfigInfo> getFieldConfigInfos(String businessLine) {
        if (StringUtils.isBlank(businessLine))
            return Collections.emptyList();

        List<ModelFieldConfigRes> modelFieldConfigResList;
        try {
            modelFieldConfigResList = assetAccountModelProvider.modelFieldList(BusinessLine.getByCode(businessLine), AssetBizFunc.ASSET_ACCOUNT);
        } catch (Exception e) {
            log.error("获取模型字段配置失败", e);
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(modelFieldConfigResList)) {
            return Collections.emptyList();
        }
        modelFieldConfigResList = modelFieldConfigResList.stream().sorted(Comparator.comparing(ModelFieldConfigRes::getSort)).collect(Collectors.toList());

        Set<String> fieldCodes = new HashSet<>();
        List<FieldConfigInfo> fieldConfigInfos = new ArrayList<>();
        modelFieldConfigResList.forEach(modelFieldConfigRes -> {
            if (EXCLUDE_ACCOUNT_FIELD.contains(modelFieldConfigRes.getFieldCode())) return;
            if (!fieldCodes.contains(modelFieldConfigRes.getFieldCode())) {
                FieldConfigInfo fieldConfigInfo = new FieldConfigInfo();
                fieldConfigInfo.setDataKey(modelFieldConfigRes.getFieldCode());
                fieldConfigInfo.setDataValue(modelFieldConfigRes.getFieldName());
                fieldConfigInfo.setDisabled(false);
                fieldConfigInfo.setHidden(true);
                fieldConfigInfo.setAccountField(true);
                fieldConfigInfos.add(fieldConfigInfo);
                fieldCodes.add(modelFieldConfigRes.getFieldCode());
            }
        });
        return fieldConfigInfos;
    }


    public void fillFields(List<FieldConfigInfo> accountModelFields, List<FieldConfigInfo> fieldConfigInfos) {
        if (CollectionUtils.isEmpty(accountModelFields)) return;
        Set<String> fields = fieldConfigInfos.stream().map(FieldConfigInfo::getDataKey).collect(Collectors.toSet());
        //去重明细在台账存在的字段
        accountModelFields.forEach(field -> {
            if (!fields.contains(field.getDataKey())) {
                fieldConfigInfos.add(field);
                fields.add(field.getDataKey());
            }
        });
    }

    public void loadAssetAccountFields(FieldConfigRes res, String businessLine, String type) {
        if (StringUtils.isBlank(businessLine) || res == null || !FieldConfigTypeEnum.ALL.getCode().equals(type)) return;
        //查询全部字段包含台账模型字段  用于列设置提交
        List<FieldConfigInfo> accountFields = this.getFieldConfigInfos(businessLine);
        //台账模型字段去重
        this.fillFields(accountFields, res.getFieldConfig());
    }
}
