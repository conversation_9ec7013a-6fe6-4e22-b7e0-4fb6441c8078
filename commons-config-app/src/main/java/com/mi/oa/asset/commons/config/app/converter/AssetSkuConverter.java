package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.common.enums.MaterialType;
import com.mi.oa.asset.commons.config.api.assetsku.AssetSkuRes;
import com.mi.oa.asset.commons.config.api.assetsku.ManageModel;
import com.mi.oa.asset.commons.config.api.assetsku.SaveAssetSkuReq;
import com.mi.oa.asset.commons.config.api.assetsku.SnTypeEnum;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSku;
import com.mi.oa.asset.commons.config.domain.assetsku.entity.AssetSkuManage;
import com.mi.oa.asset.commons.config.domain.assetsku.enums.StockCostEnum;
import com.mi.oa.asset.commons.config.domain.assetsku.valobj.AssetSkuImportData;
import com.mi.oa.asset.commons.config.domain.assetsku.valobj.AssetSkuUpdateData;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/10/9 17:55
 */

@Mapper(componentModel = "spring", imports = {ManageModel.class, SnTypeEnum.class, StockCostEnum.class, MaterialType.class, BusinessLine.class})
public interface AssetSkuConverter extends CommonConverter {

    AssetSku toAssetSku(SaveAssetSkuReq req);

    AssetSku importDataToAssetSku(AssetSkuImportData source);

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
    @Mapping(target = "businessLine", ignore = true)
    @Mapping(target = "cateId", ignore = true)
    @Mapping(target = "cateCode", ignore = true)
    @Mapping(target = "cateName", ignore = true)
    void updateDataToAssetSku(AssetSkuUpdateData source, @MappingTarget AssetSku assetSku);

    AssetSkuRes toAssetSkuRes(AssetSku assetSku);

    List<AssetSkuRes> toAssetSkuRes(List<AssetSku> assetSkus);
    @Mappings({
            @Mapping(target = "mgModel", expression = "java(source.getMgModel()==null?\"\":source.getMgModel().getDesc())"),
            @Mapping(target = "serialMg", expression = "java(source.getSerialMg()==null?\"\":source.getSerialMg().getDesc())"),
            @Mapping(target = "materialType", expression = "java(source.getMaterialType()==null?\"\":source.getMaterialType().getDesc())"),
            @Mapping(target = "businessLine", expression = "java(source.getBusinessLine()==null?\"\":source.getBusinessLine().getDesc())"),
    })
    void updateExportSku(AssetSkuManage source, @MappingTarget AssetSkuImportData target);

    AssetSkuImportData toAssetSkuImportData(AssetSku source);

    default List<AssetSkuImportData> toAssetSkuImportData(List<AssetSku> data, Map<Integer, List<AssetSkuManage>> manageMap) {
        List<AssetSkuImportData> exports = new ArrayList<>(data.size());
        data.forEach(d -> {
            List<AssetSkuManage> manages = manageMap.get(d.getSkuId());
            if(CollectionUtils.isEmpty(manages)) return;
            manages.forEach(m -> {
                AssetSkuImportData exportData = toAssetSkuImportData(d);
                updateExportSku(m, exportData);
                exports.add(exportData);
            });
        });
        return exports;
    }
}
