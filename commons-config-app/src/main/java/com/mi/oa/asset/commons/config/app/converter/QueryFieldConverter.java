package com.mi.oa.asset.commons.config.app.converter;

import com.mi.oa.asset.commons.config.api.queryfield.*;
import com.mi.oa.asset.commons.config.domain.queryfield.entity.FieldConfig;
import com.mi.oa.asset.commons.config.domain.queryfield.entity.QueryConfig;
import com.mi.oa.asset.commons.config.domain.queryfield.enums.FieldConfigEnum;
import com.mi.oa.asset.commons.config.infra.common.CommonConverter;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/1/8 11:31
 */

@Mapper(componentModel = "spring")
public interface QueryFieldConverter extends CommonConverter {

    QueryConfigRes queryConfigToRes(QueryConfig queryConfig);

    List<QueryConfigRes> queryConfigToResList(List<QueryConfig> queryConfigs);

    QueryConfig queryConfigReqToDo(QueryConfigReq queryConfigReq);

    default String toQueryCondString(List<QueryCondition> queryCond) {
        return JacksonUtils.bean2Json(queryCond);
    }

    default List<QueryCondition> toQueryCondBean(String queryCond) {
        return JacksonUtils.json2List(queryCond, QueryCondition.class);
    }

    default FieldConfigRes fieldConfigToRes(FieldConfig fieldConfig) {
        if(fieldConfig==null) return null;
        return FieldConfigRes.builder()
                .fieldId(fieldConfig.getFieldId())
                .fieldConfig(toFieldConfigBean(fieldConfig.getFieldConfig(), fieldConfig.getExtra()))
                .extra(toExtraConfigBean(fieldConfig.getExtra()))
                .manageLine(fieldConfig.getManageLine())
                .funId(fieldConfig.getFunId())
                .build();
    }

    default FieldConfig fieldConfigReqToDo(FieldConfigReq fieldConfigReq) {
        return FieldConfig.builder()
                .fieldId(fieldConfigReq.getFieldId())
                .fieldConfig(toFieldConfigString(fieldConfigReq.getFieldConfig()))
                .extra(toExtraConfigString(fieldConfigReq.getFieldConfig()))
                .manageLine(fieldConfigReq.getManageLine())
                .funId(fieldConfigReq.getFunId())
                .build();
    }

    default Set<String> getExtraKeys(String extraConfig) {
        Set<String> extraKeys = new HashSet<>();
        if(StringUtils.isBlank(extraConfig)) return extraKeys;
        extraConfig = extraConfig.replace("\n", "");
        extraConfig = extraConfig.replace(" ", "");

        String[] extraArray = extraConfig.split(";");
        for (String extra : extraArray) {
            extraKeys.add(extra.split("\\|")[0]);
        }
        return extraKeys;
    }

    default List<FieldConfigInfo> toFieldConfigBean(String fieldConfig, String extraConfig) {
        if(StringUtils.isBlank(fieldConfig)) return Collections.emptyList();
        fieldConfig = fieldConfig.replace("\n", "");
        fieldConfig = fieldConfig.replace(" ", "");
        String[] fieldConfigs = fieldConfig.split(";");
        Set<String> extraKeys = getExtraKeys(extraConfig);
        List<FieldConfigInfo> result = new ArrayList<>();
        for (String config : fieldConfigs) {
            if (StringUtils.isBlank(config))
                continue;
            FieldConfigInfo fieldConfigInfo = new FieldConfigInfo();
            String[] split = config.split("\\|");
            fieldConfigInfo.setDataKey(split[0]);
            if (CollectionUtils.isNotEmpty(extraKeys) && extraKeys.contains(split[0])) {
                fieldConfigInfo.setAccountField(true);
            } else {
                fieldConfigInfo.setAccountField(false);
            }
            String type = split[1];
            //兼容明细下包含台账的字段
            if (split.length > 2) {
                fieldConfigInfo.setDataValue(split[2]);
            }
            if (FieldConfigEnum.DISABLED.getCode().equals(type)) {
                fieldConfigInfo.setDisabled(true);
                fieldConfigInfo.setHidden(false);
            } else if (FieldConfigEnum.SHOW.getCode().equals(type)) {
                fieldConfigInfo.setDisabled(false);
                fieldConfigInfo.setHidden(false);
            } else if (FieldConfigEnum.HIDDEN.getCode().equals(type)) {
                fieldConfigInfo.setDisabled(false);
                fieldConfigInfo.setHidden(true);
            }
            result.add(fieldConfigInfo);
        }

        return result;
    }

    default List<FieldConfigInfo> toExtraConfigBean(String extraConfig) {
        if(StringUtils.isBlank(extraConfig)) return Collections.emptyList();

        extraConfig = extraConfig.replace("\n", "");
        extraConfig = extraConfig.replace(" ", "");
        String[] fieldConfigs = extraConfig.split(";");
        List<FieldConfigInfo> result = new ArrayList<>();
        for (String config : fieldConfigs) {
            if (StringUtils.isBlank(config))
                continue;

            FieldConfigInfo fieldConfigInfo = new FieldConfigInfo();
            String[] split = config.split("\\|");
            fieldConfigInfo.setAccountField(true);
            fieldConfigInfo.setDataKey(split[0]);
            String type = split[1];
            //兼容明细下包含台账的字段
            if (split.length > 2) {
                fieldConfigInfo.setDataValue(split[2]);
            }

            if (FieldConfigEnum.DISABLED.getCode().equals(type)) {
                fieldConfigInfo.setDisabled(true);
                fieldConfigInfo.setHidden(false);
            } else if (FieldConfigEnum.SHOW.getCode().equals(type)) {
                fieldConfigInfo.setDisabled(false);
                fieldConfigInfo.setHidden(false);
            } else if (FieldConfigEnum.HIDDEN.getCode().equals(type)) {
                fieldConfigInfo.setDisabled(false);
                fieldConfigInfo.setHidden(true);
            }
            result.add(fieldConfigInfo);
        }

        return result;
    }

    default String toFieldConfigString(List<FieldConfigInfo> fieldConfigInfos) {
        StringBuilder fieldConfig = new StringBuilder();
        for (FieldConfigInfo fieldConfigInfo : fieldConfigInfos) {
            Boolean disabled = fieldConfigInfo.getDisabled();
            Boolean hidden = fieldConfigInfo.getHidden();

            FieldConfigEnum fieldConfigEnum;
            if (disabled) {  // 当前字段必须勾选
                fieldConfigEnum = FieldConfigEnum.DISABLED;
            } else if (hidden) {
                fieldConfigEnum = FieldConfigEnum.HIDDEN;
            } else {
                fieldConfigEnum = FieldConfigEnum.SHOW;
            }
            if (StringUtils.isNotBlank(fieldConfigInfo.getDataValue())) {
                fieldConfig.append(fieldConfigInfo.getDataKey())
                        .append("|")
                        .append(fieldConfigEnum.getCode())
                        .append("|")
                        .append(fieldConfigInfo.getDataValue())
                        .append(";");
            } else {
                fieldConfig.append(fieldConfigInfo.getDataKey())
                        .append("|")
                        .append(fieldConfigEnum.getCode())
                        .append(";");
            }
        }
        return fieldConfig.toString();
    }

    default String toExtraConfigString(List<FieldConfigInfo> fieldConfigInfos) {
        StringBuilder fieldConfig = new StringBuilder();
        for (FieldConfigInfo fieldConfigInfo : fieldConfigInfos) {
            if (Boolean.TRUE.equals(fieldConfigInfo.getAccountField())) {
                Boolean disabled = fieldConfigInfo.getDisabled();
                Boolean hidden = fieldConfigInfo.getHidden();
                FieldConfigEnum fieldConfigEnum;
                if (disabled) {  // 当前字段必须勾选
                    fieldConfigEnum = FieldConfigEnum.DISABLED;
                } else if (hidden) {
                    fieldConfigEnum = FieldConfigEnum.HIDDEN;
                } else {
                    fieldConfigEnum = FieldConfigEnum.SHOW;
                }
                if (StringUtils.isNotBlank(fieldConfigInfo.getDataValue())) {
                    fieldConfig.append(fieldConfigInfo.getDataKey())
                            .append("|")
                            .append(fieldConfigEnum.getCode())
                            .append("|")
                            .append(fieldConfigInfo.getDataValue())
                            .append(";");
                } else {
                    fieldConfig.append(fieldConfigInfo.getDataKey())
                            .append("|")
                            .append(fieldConfigEnum.getCode())
                            .append(";");
                }
            }
        }
        return fieldConfig.toString();
    }
}