package com.mi.oa.asset.commons.config.app.web.controller;

/**
 * <AUTHOR>
 * @date 2025/3/18 09:47
 * @description 功能配置
 */

import com.mi.oa.asset.commons.config.api.common.DelByIdsReq;
import com.mi.oa.asset.commons.config.api.function.*;
import com.mi.oa.asset.commons.config.app.ability.FunctionAbility;
import com.mi.oa.asset.eam.auth.AuthFacade;
import com.mi.oa.asset.eam.feign.service.EamFuncBaseSyncService;
import com.mi.oa.asset.eam.oplog.OperationLog;
import com.mi.oa.asset.eam.utils.JacksonUtils;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.mi.oa.asset.common.enums.EAMConstants.LANGUAGE;

@HttpApiModule(value = "FunctionController", apiController = FunctionController.class)
@RestController
@Validated
@Slf4j
@RequestMapping("/configs/func")
public class FunctionController {

    @Resource
    private FunctionProvider functionProvider;

    @Autowired
    private HttpServletRequest request;

    @Resource
    private FunctionAbility functionAbility;

    @Resource
    private EamFuncBaseSyncService syncService;

    @Value("${cache.admins}")
    private String admins;

    @HttpApiDoc(apiName = "功能列表查询", value = "/configs/func/list", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/list"})
    public Result<PageData<FunctionRes>> listFunc(@RequestBody FuncQueryReq funcQueryReq) {
        return Result.success(functionProvider.listFunc(funcQueryReq));
    }

    @HttpApiDoc(apiName = "功能详情", value = "/configs/func/info", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @GetMapping(value = {"/info"})
    public Result<FunctionRes> getFunctionRes(@RequestParam("id") Integer id) {
        return Result.success(functionProvider.findFunctionById(id));
    }

    @HttpApiDoc(apiName = "功能保存", value = "/configs/func/save", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/save"})
    @OperationLog(name = "功能保存", param = "#{#req}", businessNo = "#{#req.code}")
    public Result<Integer> saveFunction(@RequestBody FunctionReq req) {
        return Result.success(functionProvider.saveFunction(req));
    }

    @HttpApiDoc(apiName = "功能删除", value = "/configs/func/batch/delete", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/batch/delete"})
    @OperationLog(name = "功能删除", param = "#{#req}")
    public Result<Void> delFunction(@RequestBody DelByIdsReq req) {
        log.info("delete function req: {}", JacksonUtils.bean2Json(req));
        functionProvider.deleteFunction(req.getIds());
        return Result.success();
    }

    @HttpApiDoc(apiName = "数据库表查询", value = "/configs/func/table/list", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/table/list"})
    public Result<PageData<FuncTableRes>> listTable(@RequestBody FuncQueryReq funcQueryReq) {
        return Result.success(functionProvider.listTable(funcQueryReq));
    }

    @HttpApiDoc(apiName = "数据库表详情", value = "/configs/func/table/info", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @GetMapping(value = {"/table/info"})
    public Result<FuncTableRes> getFuncTableRes(@RequestParam("id") Integer id) {
        return Result.success(functionProvider.findFuncTableById(id));
    }

    @HttpApiDoc(apiName = "数据库表保存", value = "/configs/func/table/save", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/table/save"})
    @OperationLog(name = "数据库表保存", param = "#{#req}", businessNo = "#{#req.tableName}")
    public Result<Integer> saveFuncTable(@RequestBody FuncTableReq req) {
        return Result.success(functionProvider.saveFuncTable(req));
    }

    @HttpApiDoc(apiName = "数据库表删除", value = "/configs/func/table/delete", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/table/delete"})
    @OperationLog(name = "数据库表删除", param = "#{#req}")
    public Result<Void> delFuncTable(@RequestBody DelByIdsReq req) {
        functionProvider.deleteFuncTable(req.getIds());
        return Result.success();
    }

    @HttpApiDoc(apiName = "表单字段列表查询）", value = "/configs/func/field/form/list", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/field/form/list"})
    public Result<List<FormFieldRes>> listFormField(@RequestBody FormFieldReq fieldReq) {
        return Result.success(functionProvider.getFieldMap(fieldReq));
    }

    @HttpApiDoc(apiName = "字段列表查询（功能、表单、数据库表）", value = "/configs/func/field/list", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/field/list"})
    public Result<PageData<FuncFieldRes>> listFuncField(@RequestBody FieldQueryReq fieldReq) {
        return Result.success(functionProvider.listFuncField(fieldReq));
    }

    @HttpApiDoc(apiName = "数据库表字段列表查询", value = "/configs/func/table/field/list", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/table/field/list"})
    public Result<PageData<FuncFieldRes>> listTableField(@RequestBody FieldQueryReq fieldReq) {
        return Result.success(functionProvider.listTableField(fieldReq));
    }

    @HttpApiDoc(apiName = "批量字段导入", value = "/configs/func/field/import", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/field/import"})
    public Result<Void> importFuncField(@RequestBody FieldImportReq fieldReq) {
        functionProvider.importField(fieldReq);
        return Result.success();
    }

    @HttpApiDoc(apiName = "批量字段修改", value = "/configs/func/field/save", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/field/save"})
    @OperationLog(name = "功能字段修改", param = "#{#saveReq}")
    public Result<Void> saveFuncField(@RequestBody FieldSaveReq saveReq) {
        functionProvider.saveField(saveReq.getList());
        return Result.success();
    }

    @HttpApiDoc(apiName = "批量字段删除", value = "/configs/func/field/delete", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/field/delete"})
    @OperationLog(name = "功能字段删除", param = "#{#delByIdsReq}")
    public Result<Void> delFuncField(@RequestBody DelByIdsReq delByIdsReq) {
        functionProvider.deleteField(delByIdsReq.getIds());
        return Result.success();
    }

    @HttpApiDoc(apiName = "字典授权角色列表", value = "/configs/func/dict/auth/list", method = MiApiRequestMethod.POST, description = "字典授权角色列表")
    @PostMapping(value = {"/dict/auth/list"})
    public Result<List<FuncDictRelRes>> listDictAuth(@RequestBody FuncDictAuthReq authReq) {
        return Result.success(functionProvider.listDictAuth(authReq));
    }
    @HttpApiDoc(apiName = "批量字典授权", value = "/configs/func/dict/auth", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/dict/auth"})
    public Result<Void> authDict(@RequestBody FuncDictAuthReq saveReq) {
        functionProvider.saveDictAuth(saveReq);
        return Result.success();
    }

    @HttpApiDoc(apiName = "批量删除字典授权", value = "/configs/func/dict/auth/delete", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/dict/auth/delete"})
    public Result<Void> delAuthDict(@RequestBody DelByIdsReq delByIdsReq) {
        functionProvider.deleteDictAuth(delByIdsReq.getIds());
        return Result.success();
    }

    @HttpApiDoc(apiName = "角色列表查询", value = "/configs/role/list", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/role/list"})
    public Result<PageData<FunctionRoleRes>> listRole(@RequestBody FieldQueryReq fieldReq) {
        return Result.success(functionProvider.getPageRole(fieldReq));
    }

    @HttpApiDoc(apiName = "数据权限查详情", value = "/configs/func/data/auth/info", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @GetMapping(value = {"/data/auth/info"})
    public Result<FuncDataPermissionRes> getDataAuth(@RequestParam("funCode") String funCode, @RequestParam(value = "businessLine", required = false) String businessLine) {
        return Result.success(functionProvider.getDataAuth(funCode, businessLine));
    }

    @HttpApiDoc(apiName = "数据权限保存", value = "/configs/func/data/auth/save", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/data/auth/save"})
    public Result<Integer> saveDataAuth(@RequestBody FuncDataPermissionReq req) {
        functionProvider.saveDataAuth(req);
        return Result.success();
    }

    @HttpApiDoc(apiName = "获取授权的字典集合", value = "/configs/func/auth/dict/list", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/auth/dict/list"})
    public Result<List<FuncDictRes>> getAuthDictInfo(@RequestBody FuncDictAuthReq req) {
        return Result.success(functionProvider.getAuthDictInfo(req));
    }

    @HttpApiDoc(apiName = "配置元数据保存", value = "/configs/func/schema/save", method = MiApiRequestMethod.POST, description = "配置元数据保存")
    @PostMapping(value = {"/schema/save"})
    public Result<Integer> saveSchema(@RequestBody FunSchemaReq req) {
        return Result.success();
    }

    @HttpApiDoc(apiName = "配置元数据删除", value = "/configs/func/schema/delete", method = MiApiRequestMethod.POST, description = "配置元数据删除")
    @GetMapping(value = {"/schema/delete"})
    public Result<Void> delSchema(@RequestParam("id") Integer id) {
        return Result.success();
    }

    @HttpApiDoc(apiName = "配置元数据详情", value = "/configs/func/schema/info", method = MiApiRequestMethod.POST, description = "配置元数据详情")
    @GetMapping(value = {"/schema/info"})
    public Result<FunSchemaRes> getSchema(@RequestParam("id") Integer id) {
        return Result.success();
    }

    @HttpApiDoc(apiName = "配置数据保存", value = "/configs/func/schema/data/save", method = MiApiRequestMethod.POST, description = "配置数据保存")
    @PostMapping(value = {"/schema/data/save"})
    public Result<Integer> saveSchemaData(@RequestBody FunSchemaDataReq req) {
        return Result.success();
    }

    @HttpApiDoc(apiName = "配置数据删除", value = "/configs/func/schema/data/delete", method = MiApiRequestMethod.POST, description = "配置数据删除")
    @GetMapping(value = {"/schema/data/delete"})
    public Result<Void> delSchemaData(@RequestParam("id") Integer id) {
        return Result.success();
    }

    @HttpApiDoc(apiName = "配置数据列表", value = "/configs/func/schema/data/list", method = MiApiRequestMethod.POST, description = "配置数据详情")
    @PostMapping(value = {"/schema/data/list"})
    public Result<List<FunSchemaDataRes>> getSchemaData(@RequestBody FunSchemaDataReq dataReq) {
        return Result.success();
    }

    @HttpApiDoc(apiName = "字典查询", value = "/configs/func/dict/list", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/dict/list"})
    public Result<PageData<DictRes>> listDict(@RequestBody DictQueryReq queryReq) {
        return Result.success(functionProvider.listDict(queryReq));
    }

    @HttpApiDoc(apiName = "字典详情", value = "/configs/func/dict/info", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @GetMapping(value = {"/dict/info"})
    public Result<DictRes> getDict(@RequestParam("id") Integer id) {
        return Result.success(functionProvider.getDict(id));
    }

    @HttpApiDoc(apiName = "字典保存", value = "/configs/func/dict/save", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/dict/save"})
    public Result<Integer> saveDict(@RequestBody DictReq req) {
        return Result.success(functionProvider.saveDict(req));
    }

    @HttpApiDoc(apiName = "字典删除", value = "/configs/func/dict/delete", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/dict/delete"})
    @OperationLog(name = "字典删除", param = "#{#req}")
    public Result<Void> delDict(@RequestBody DelByIdsReq req) {
        functionProvider.deleteDict(req.getIds());
        return Result.success();
    }

    @HttpApiDoc(apiName = "功能关联字典列表查询", value = "/configs/func/dict-rel/list", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/dict-rel/list"})
    public Result<PageData<FunctionDictRes>> listDictRel(@RequestBody DictQueryReq queryReq) {
        return Result.success(functionProvider.listDictRel(queryReq));
    }

    @HttpApiDoc(apiName = "功能关联字典保存", value = "/configs/func/dict-rel/save", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/dict-rel/save"})
    public Result<Void> saveDictRel(@RequestBody FuncDictReq dictReq) {
        functionProvider.saveFuncDict(dictReq);
        return Result.success();
    }

    @HttpApiDoc(apiName = "导入功能关联字典列表", value = "/configs/func/dict-rel/import", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/dict-rel/import"})
    public Result<Void> importDictRel(@RequestBody DictImportReq importReq) {
        functionProvider.importFuncDict(importReq);
        return Result.success();
    }

    @HttpApiDoc(apiName = "字典删除", value = "/configs/func/dict-rel/delete", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/dict-rel/delete"})
    @OperationLog(name = "功能字典删除", param = "#{#req}")
    public Result<Void> delDictRel(@RequestBody DelByIdsReq req) {
        functionProvider.deleteDictRel(req.getIds());
        return Result.success();
    }

    @HttpApiDoc(apiName = "业务线关联字典列表查询", value = "/configs/func/dict-rel/businessLine", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/dict-rel/businessLine"})
    public Result<List<DictRes>> listDictRelByBusinessLine(@RequestBody DictBusinessLineQueryReq queryReq) {
        return Result.success(functionProvider.listDictRelByBusinessLine(queryReq));
    }

    @HttpApiDoc(apiName = "业务线关联字典列表查询(批量)", value = "/configs/func/dict-rel/businessLine/batch", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/dict-rel/businessLine/batch"})
    public Result<Map<String,List<DictRes>>> listDictRelByBusinessLineBatch(@RequestBody DictBusinessLineBatchReq queryReq) {
        queryReq.setLanguage(request.getHeader(LANGUAGE));
        return Result.success(functionProvider.listDictRelByBusinessLineBatch(queryReq));
    }

    @HttpApiDoc(apiName = "全局变量列表查询", value = "/configs/func/global/list", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/global/list"})
    public Result<PageData<GlobalValRes>> listGlobalVal(@RequestBody GlobalValQueryReq queryReq) {
        return Result.success(functionProvider.listGlobalVal(queryReq));
    }
    @HttpApiDoc(apiName = "业全局变量保存", value = "/configs/func/global/save", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/global/save"})
    public Result<Integer> saveGlobalVal(@RequestBody GlobalValReq req) {
        return Result.success(functionProvider.saveGlobalVal(req));
    }

    @HttpApiDoc(apiName = "业全局变量删除", value = "/configs/func/global/delete", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/global/delete"})
    @OperationLog(name = "业全局变量删除", param = "#{#req}")
    public Result<Integer> saveGlobalVal(@RequestBody DelByIdsReq req) {
        functionProvider.deleteGlobalVal(req.getIds());
        return Result.success();
    }

    @HttpApiDoc(apiName = "业全局变量导入", value = "/configs/func/global/import", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/global/import"})
    public Result<Integer> importGlobalVal(@RequestBody GlobalImportReq req) {
        functionProvider.importGlobalVal(req);
        return Result.success();
    }

    @HttpApiDoc(apiName = "全局变量值查询", value = "/configs/func/global/info", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @GetMapping(value = {"/global/info"})
    public Result<GlobalValRes> getGlobalVal(@RequestParam("code") String code, @RequestParam("businessLine") String businessLine) {
        List<GlobalValRes> list = functionProvider.getGlobalVal(Arrays.asList(code), businessLine);
        if (CollectionUtils.isEmpty(list)) return Result.success();
        return Result.success(list.get(0));
    }

    @HttpApiDoc(apiName = "全局变量值查询(批量)", value = "/configs/func/global/info/batch", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @GetMapping(value = {"/global/info/batch"})
    public Result<List<GlobalValRes>> getGlobalValBatch(@RequestParam("code") List<String> code, @RequestParam("businessLine") String businessLine) {
        return Result.success(functionProvider.getGlobalVal(code, businessLine));
    }

    @HttpApiDoc(apiName = "同步字典值", value = "/configs/func/sync/dict", method = MiApiRequestMethod.POST, description = "同步字典值")
    @GetMapping(value = {"/sync/dict"})
    public Result<Integer> syncDict() {
        functionAbility.syncDictFromControl();
        return Result.success();
    }

    @HttpApiDoc(apiName = "同步功能列字段", value = "/configs/func/sync/col", method = MiApiRequestMethod.POST, description = "同步功能列字段")
    @GetMapping(value = {"/sync/col"})
    public Result<Integer> syncFuncCol(@RequestParam(value = "code", required = false) String code) {
        functionAbility.syncFuncCol();
        return Result.success();
    }

    @HttpApiDoc(apiName = "同步表列字段", value = "/configs/func/sync/table", method = MiApiRequestMethod.POST, description = "同步表列字段")
    @GetMapping(value = {"/sync/table"})
    public Result<Integer> syncTableCol(@RequestParam(value = "code", required = false) String code) {
        functionAbility.syncTableCol(code);
        return Result.success();
    }

    @HttpApiDoc(apiName = "批量导入表SQL", value = "/configs/func/table/import", method = MiApiRequestMethod.POST)
    @PostMapping("/table/import")
    public Result<Void> importTable(@HttpApiDocClassDefine(value = "file", description = "导入文件", required = true) @RequestParam("file") MultipartFile file) {
        functionAbility.importTable(file);
        return Result.success();
    }

    @HttpApiDoc(apiName = "同步功能", value = "/configs/func/sync", method = MiApiRequestMethod.POST, description = "同步表列字段")
    @GetMapping(value = {"/sync"})
    public Result<Integer> syncFunc(@RequestParam(value = "code", required = false) String code) {
        syncService.syncFuncFromEAM1(code);
        return Result.success();
    }

    @HttpApiDoc(apiName = "下载任务列表", value = "/configs/func/down-task/list", method = MiApiRequestMethod.POST, description = "功能字段列表查询")
    @PostMapping(value = {"/down-task/list"})
    public Result<PageData<DownTaskRes>> listDownTask(@RequestBody DownTaskQueryReq queryReq, @RequestHeader(value = "eam-language", required = false, defaultValue = "zh-CN") String language) {
        queryReq.setLanguage(language);
        return Result.success(functionAbility.listDownTask(queryReq));
    }

    @GetMapping(value = {"/redis/delete"})
    public Result<Void> deleteRedis(@RequestParam("key") String key) {
        functionAbility.deleteCacheCol(key);
        return Result.success();
    }

    @GetMapping(value = {"/redis/info"})
    public Result<Object> getRedis(@RequestParam("key") String key) {
        return Result.success(functionAbility.getRedisCache(key));
    }

    @GetMapping(value = {"/redis/update"})
    public Result<Void> updateRedis(@RequestParam("key") String key, @RequestParam("value") String value, @RequestParam(value = "time", required = false) Long time) {
        if (!admins.contains(AuthFacade.authedUserName())) return Result.success();
        if (Objects.isNull(time) || time == 0) {
            RedisUtils.set(key, value);
        } else {
            RedisUtils.setEx(key, value, time, TimeUnit.SECONDS);
        }
        return Result.success();
    }
}
