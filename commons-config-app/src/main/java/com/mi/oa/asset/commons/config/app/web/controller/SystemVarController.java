package com.mi.oa.asset.commons.config.app.web.controller;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.systemvar.SystemVarDetail;
import com.mi.oa.asset.commons.config.api.systemvar.SystemVarProvider;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/3/19 15:39
 */


@HttpApiModule(value = "SystemVarController", apiController = SystemVarController.class)
@RestController
@RequestMapping("/configs/system-var")
public class SystemVarController {

    @Resource
    private SystemVarProvider provider;

    @HttpApiDoc(apiName = "保存系统变量", value = "/configs/system-var/save", method = MiApiRequestMethod.POST)
    @PostMapping("/save")
    public Result<Void> save(@RequestBody SystemVarDetail req) {
        provider.saveSystemVar(req);

        return Result.success();
    }

    @HttpApiDoc(apiName = "系统变量详情", value = "/configs/system-var/detail", method = MiApiRequestMethod.GET)
    @GetMapping("/detail")
    public Result<SystemVarDetail> detail(@RequestParam String varCode, @RequestParam String businessLine) {
        return Result.success(provider.getSystemVar(varCode, BusinessLine.getByCode(businessLine)));
    }

    @HttpApiDoc(apiName = "多个系统变量值", value = "/configs/system-var/values", method = MiApiRequestMethod.GET)
    @GetMapping("/values")
    public Result<Map<String, String>> values(@RequestParam String varCodes, @RequestParam String businessLine) {
        List<String> codes = Arrays.asList(StringUtils.split(varCodes, ","));

        return Result.success(provider.getSystemVarValue(codes, BusinessLine.getByCode(businessLine)));
    }

    @HttpApiDoc(apiName = "单个系统变量值", value = "/configs/system-var/value", method = MiApiRequestMethod.GET)
    @GetMapping("/value")
    public Result<String> value(@RequestParam String varCode, @RequestParam String businessLine) {
        return Result.success(provider.getSystemVarValue(varCode, BusinessLine.getByCode(businessLine)));
    }
}
