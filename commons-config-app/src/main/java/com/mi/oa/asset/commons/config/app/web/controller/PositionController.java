package com.mi.oa.asset.commons.config.app.web.controller;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.mi.oa.asset.commons.config.api.common.DelByIdsReq;
import com.mi.oa.asset.commons.config.api.position.*;
import com.mi.oa.asset.commons.config.app.converter.PositionConverter;
import com.xiaomi.mit.api.PageData;
import com.xiaomi.mit.api.Result;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.mi.oa.asset.common.enums.EAMConstants.CHINESE;
import static com.mi.oa.asset.common.enums.EAMConstants.LANGUAGE;

/**
 * <AUTHOR>
 * @Date 2023/10/16 11:12
 */

@HttpApiModule(value = "PositionController", apiController = PositionController.class)
@RestController
@RequestMapping("/configs/position")
public class PositionController {

    @Resource
    private PositionProvider provider;

    @Resource
    private PositionConverter converter;

    @Resource
    private HttpServletRequest request;


    @HttpApiDoc(apiName = "空间系统位置树", value = "/configs/position/std-tree", method = MiApiRequestMethod.POST)
    @GetMapping("/std-tree")
    public Result<List<PositionTreeRes>> stdPositionTree() {
        return Result.success(provider.stdPositionTree(request.getHeader(LANGUAGE)));
    }

    @HttpApiDoc(apiName = "保存位置", value = "/configs/position/save", method = MiApiRequestMethod.POST)
    @PostMapping("/save")
    public Result<Void> save(@Valid @RequestBody SavePositionReq req) {
        if(null == req.getPositionId()) {
            provider.createPosition(req);
        } else {
            provider.updatePosition(req);
        }

        return Result.success();
    }

    @HttpApiDoc(apiName = "批量删除位置", value = "/configs/position/delete", method = MiApiRequestMethod.POST)
    @PostMapping("/delete")
    public Result<Void> delete(@RequestBody DelByIdsReq req) {
        provider.deleteByIds(req.getIds());

        return Result.success();
    }

    @HttpApiDoc(apiName = "位置分页查询", value = "/configs/position/list/{positionId}", method = MiApiRequestMethod.GET)
    @GetMapping({"/list/{positionId}", "/list"})
    public Result<PageData<PositionRes>> list(
            @PathVariable(required = false) Integer positionId,
            @RequestParam(required = false, defaultValue = "") String businessLine,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize
    ) {
        if(null != positionId) {
            return Result.success(provider.getPositionPageData(positionId, keyword, pageNum, pageSize));
        } else {
            return Result.success(provider.getPositionPageData(Arrays.asList(businessLine.split(",")), keyword, pageNum, pageSize));
        }
    }

    @HttpApiDoc(apiName = "位置详情", value = "/configs/position/detail/{positionId}", method = MiApiRequestMethod.GET)
    @GetMapping("/detail/{positionId}")
    public Result<PositionRes> detail(@PathVariable Integer positionId) {
        return Result.success(provider.getPosition(positionId));
    }

    @HttpApiDoc(apiName = "导入空间数据", value = "/configs/position/import-std", method = MiApiRequestMethod.POST)
    @PostMapping("/import-std")
    public Result<Void> importFromStd(@RequestBody ImportStdPositionReq req) {
        provider.importStdPosition(req);

        return Result.success();
    }

    @HttpApiDoc(apiName = "位置树", value = "/configs/position/tree", method = MiApiRequestMethod.POST)
    @GetMapping("/tree")
    public Result<List<PositionTreeRes>> tree(@RequestParam(required = false, defaultValue = "") String businessLine) {
        List<PositionRes> all = provider.getAllPosition(Arrays.asList(StringUtils.split(businessLine, ",")));
        if (CollectionUtils.isEmpty(all)) return Result.success();
        String language = request.getHeader(LANGUAGE);
        if (StringUtils.isNotBlank(language) && !CHINESE.equals(language)) {
            all.forEach(i -> i.setPositionName(i.getPositionNameEn()));
        }
        List<PositionTreeRes> roots = converter.toPositionTreeRes(
                all.stream().filter(i -> StringUtils.isBlank(i.getParentCode())).collect(Collectors.toList())
        );

        roots.forEach(node -> loadPositionSublist(node, all));

        return Result.success(roots);
    }

    private void loadPositionSublist(PositionTreeRes node, List<PositionRes> all) {
        List<PositionTreeRes> subList = converter.toPositionTreeRes(
                all.stream().filter(i -> node.getPositionCode().equals(i.getParentCode())
                                && node.getBusinessLine().equals(i.getBusinessLine())).collect(Collectors.toList())
        );

        node.setSubList(subList);
        subList.forEach(n -> loadPositionSublist(n, all));
    }
}
