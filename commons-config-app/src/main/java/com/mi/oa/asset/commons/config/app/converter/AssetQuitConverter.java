package com.mi.oa.asset.commons.config.app.converter;


import com.mi.oa.asset.commons.config.api.assetquit.AssetQuitReq;
import com.mi.oa.asset.commons.config.api.assetquit.AssetQuitRes;
import com.mi.oa.asset.commons.config.domain.assetquit.entity.AssetQuit;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 离职员工信息表 appConverter 转换器
 * <AUTHOR>
 * @date 2024-04-12 02:05:55
 */
@Mapper(componentModel = "spring")
public interface AssetQuitConverter {

    AssetQuitRes toAssetQuitRes(AssetQuit source);

    List<AssetQuitRes> toAssetQuitResList(List<AssetQuit> source);

    AssetQuit toAssetQuit(AssetQuitReq source);
}