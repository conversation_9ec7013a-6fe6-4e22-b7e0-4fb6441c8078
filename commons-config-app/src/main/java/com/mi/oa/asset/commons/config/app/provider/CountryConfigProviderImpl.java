package com.mi.oa.asset.commons.config.app.provider;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigProvider;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigReq;
import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigRes;
import com.mi.oa.asset.commons.config.app.ability.CountryConfigAbility;
import com.mi.oa.asset.commons.config.app.converter.CountryConfigConverter;
import com.mi.oa.asset.commons.config.domain.international.entity.CountryConfigDo;
import com.mi.oa.asset.commons.config.domain.international.repository.CountryConfigRepo;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Service
@Slf4j
@DubboService
public class CountryConfigProviderImpl implements CountryConfigProvider {

    @Resource
    private CountryConfigRepo countryConfigRepo;

    @Resource
    private CountryConfigConverter converter;

    @Resource
    private CountryConfigAbility countryConfigAbility;

    @Override
    public List<CountryConfigRes> getCountryConfigList() {
        List<CountryConfigRes> countryConfigRes = converter.listDoToRes(countryConfigRepo.searchAll());
        return countryConfigAbility.getCountryConfigList(countryConfigRes);
    }

    @Override
    public List<CountryConfigRes> getByRegionId(int regionId) {
        return converter.listDoToRes(countryConfigRepo.getByRegionId(regionId));
    }

    @Override
    public List<CountryConfigRes> getAllCountries() {
        List<CountryConfigDo> countryConfigDos = countryConfigRepo.searchAll();
        if (CollectionUtils.isEmpty(countryConfigDos)) {
            return Collections.emptyList();
        }
        return converter.listDoToRes(countryConfigDos);
    }

    @Override
    public CountryConfigRes getByThreeCode(String countryCodeAlphaThree) {
        if (countryCodeAlphaThree == null) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "国家代码不能为空！");
        }
        CountryConfigDo countryConfigDo = countryConfigRepo.getByCountryCodeAlphaThree(countryCodeAlphaThree);
        if (countryConfigDo == null) {
            return null;
        }
        return countryConfigAbility.getById(converter.doToRes(countryConfigDo));
    }

    @Override
    public CountryConfigRes getById(Integer countryConfigId) {
        CountryConfigDo countryConfigDo = countryConfigRepo.getById(countryConfigId);
        if (countryConfigDo == null) return null;
        return countryConfigAbility.getById(converter.doToRes(countryConfigDo));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(CountryConfigReq req) {
        countryConfigAbility.saveOrUpdate(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ErrorCodeException(ErrorCodes.BAD_PARAMETER, "请传入删除的id！");
        }
        countryConfigAbility.deleteByIds(ids);
    }

}
