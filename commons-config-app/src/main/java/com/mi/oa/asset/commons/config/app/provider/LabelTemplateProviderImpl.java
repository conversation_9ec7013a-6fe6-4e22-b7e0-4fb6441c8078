package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.labelprint.LabelTemplateProvider;
import com.mi.oa.asset.commons.config.api.labelprint.LabelTemplateRes;
import com.mi.oa.asset.commons.config.app.converter.LabelTemplateConverter;
import com.mi.oa.asset.commons.config.domain.labeltemplate.entity.LabelTemplate;
import com.mi.oa.asset.commons.config.domain.labeltemplate.repository.LabelTemplateRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * 标签模版
 *
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@DubboService
@Slf4j
public class LabelTemplateProviderImpl implements LabelTemplateProvider {

    @Resource
    private LabelTemplateRepo labelTemplateRepo;

    @Resource
    private LabelTemplateConverter labelTemplateConverter;

    @Override
    public LabelTemplateRes getLabelTemplateById(Integer templateId) {
        LabelTemplate labelTemplate = labelTemplateRepo.findHistoryById(templateId);
        if (null == labelTemplate) {
            return null;
        }
        return labelTemplateConverter.toLabelTemplateRes(labelTemplate);
    }

}
