package com.mi.oa.asset.commons.config.app.provider;

import com.mi.oa.asset.commons.config.api.serialcode.RollingType;
import com.mi.oa.asset.commons.config.api.serialcode.SerialCodeProvider;
import com.mi.oa.asset.commons.config.api.serialcode.SerialCodeReq;
import com.mi.oa.asset.commons.config.domain.common.repository.SerialCodeRepo;
import com.mi.oa.asset.commons.config.domain.common.valobj.SerialCode;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.xiaomi.mit.api.error.ErrorCodeException;
import com.xiaomi.mit.api.error.ErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Date 2023/9/19 14:22
 */

@DubboService
@Slf4j
public class SerialCodeProviderImpl implements SerialCodeProvider {

    private static final int GEN_BATCH_SIZE = 50;
    private static final String LOCK_KEY = "serial_code:%s:%s";
    private static final int MAX_ATTEMPTS = 10; // 最大尝试次数
    private static final int WAIT_TIME = 5000; // 每次尝试之间的等待时间
    private static final int LOCK_TIMEOUT = 10000; // 最长等待时间
    @Resource
    private SerialCodeRepo serialCodeRepo;

    @Resource
    private RedissonClient redissonClient;

    private ThreadPoolExecutor customThreadPool = new ThreadPoolExecutor(5, 10, 60,
            TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000000));

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> genSerialCodes(String prefix, RollingType rollingType, int zeroPadSize, int counts, String indexCode) {
        String rollingPrefix = "";
        if (null != rollingType) {
            rollingPrefix = DateUtils.dateToStringByType(new Date(), rollingType.getFormatter());
        }

        String codePrefix = prefix + rollingPrefix;

        if (!serialCodeRepo.isSerialCodeExisted(codePrefix, indexCode)) {
            serialCodeRepo.createSerialCode(codePrefix, indexCode);
        }

        SerialCode serialCode = serialCodeRepo.getSerialCode(codePrefix, indexCode);
        int endNum = serialCode.getStartNum() + counts;

        List<String> codes = new ArrayList<>(counts);
        for (int i = serialCode.getStartNum() + 1; i <= endNum; ++i) {
            String code = codePrefix + StringUtils.leftPad(String.valueOf(i), zeroPadSize, '0');
            codes.add(code);
        }

        serialCode.setStartNum(endNum);
        serialCodeRepo.updateSerialCode(serialCode, indexCode);

        return codes;
    }

    @Override
    public List<String> genSerialCodesWithLock(String prefix, RollingType rollingType, int zeroPadSize, int counts, String indexCode) {
        String rollingPrefix = "";
        if (null != rollingType) {
            rollingPrefix = DateUtils.dateToStringByType(new Date(), rollingType.getFormatter());
        }

        String codePrefix = prefix + rollingPrefix;

        String key = String.format(LOCK_KEY, codePrefix, indexCode);
        int attempts = 0;
        while (attempts < MAX_ATTEMPTS) {
            boolean isLockAcquired = tryLock(key, TimeUnit.MILLISECONDS, WAIT_TIME, LOCK_TIMEOUT);
            if (isLockAcquired) {
                List<String> codes = new ArrayList<>(counts);
                try {
                    if (!serialCodeRepo.isSerialCodeExisted(codePrefix, indexCode)) {
                        serialCodeRepo.createSerialCode(codePrefix, indexCode);
                    }

                    SerialCode serialCode = serialCodeRepo.getSerialCode(codePrefix, indexCode);
                    int endNum = serialCode.getStartNum() + counts;
                    for (int i = serialCode.getStartNum() + 1; i <= endNum; ++i) {
                        String code = codePrefix + StringUtils.leftPad(String.valueOf(i), zeroPadSize, '0');
                        codes.add(code);
                    }

                    serialCode.setStartNum(endNum);
                    serialCodeRepo.updateSerialCode(serialCode, indexCode);
                } finally {
                    // 确保释放锁
                    unlock(key);
                }
                return codes;
            } else {
                attempts++;
                try {
                    Thread.sleep(500); // 等待一段时间后重试
                } catch (Exception e) { // NOSONAR
                    log.error("等待失败");
                }
            }
        }

        return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String genSerialCode(String prefix, RollingType rollingType, int zeroPadSize) {
        List<String> codes = genSerialCodes(prefix, rollingType, zeroPadSize, 1, null);

        return CollectionUtils.isNotEmpty(codes) ? codes.get(0) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> genSerialCodes(String prefix, int zeroPadSize, int counts) {
        return genSerialCodes(prefix, null, zeroPadSize, counts, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String genSerialCode(String prefix, int zeroPadSize) {
        return genSerialCode(prefix, null, zeroPadSize);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> genSerialCodesWithIndex(String prefix, int zeroPadSize, int counts, String indexCode) {
        return genSerialCodes(prefix, null, zeroPadSize, counts, indexCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String genSerialCodeWithIndex(String prefix, int zeroPadSize, String indexCode) {
        List<String> codes = genSerialCodesWithIndex(prefix, zeroPadSize, 1, indexCode);

        return CollectionUtils.isNotEmpty(codes) ? codes.get(0) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, List<String>> batchGenSerialCodes(List<SerialCodeReq> serialCodeReqs) {
        Map<String, List<String>> resultCodeMap = new HashMap<>(10);
        int size = serialCodeReqs.size();
        if (size < GEN_BATCH_SIZE) {
            for (SerialCodeReq req : serialCodeReqs) {
                List<String> codes = genSerialCodesWithLock(req.getPrefix(), null, req.getZeroPadSize(), req.getCounts(), req.getIndexCode());
                resultCodeMap.put(req.getPrefix(), codes);
            }
        } else {
            batchGenSerialCodeAsync(serialCodeReqs, size, resultCodeMap);
        }
        return resultCodeMap;
    }

    private void batchGenSerialCodeAsync(List<SerialCodeReq> serialCodeReqs, int size, Map<String, List<String>> resultCodeMap) {
        // 启用异步并发处理，避免dubbo接口超时
        List<CompletableFuture<Void>> futureList = new ArrayList<>(size);
        for (SerialCodeReq req : serialCodeReqs) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                List<String> codes = genSerialCodesWithLock(req.getPrefix(), null, req.getZeroPadSize(), req.getCounts(), req.getIndexCode());
                resultCodeMap.put(req.getPrefix(), codes);
            }, customThreadPool);
            futureList.add(future);
        }
        // 等待线程全部执行完成处理返回
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        try {
            allFutures.get();
        } catch (InterruptedException | ExecutionException e) {
            // 重新中断当前线程
            Thread.currentThread().interrupt();
            log.info("batchGenSerialCodes error:{}", e);
            throw new ErrorCodeException(ErrorCodes.INTERNAL_SERVER_ERROR, "生成流水号失败");
        }
    }

    public boolean tryLock(String lockKey, TimeUnit unit, long waitTime, long leaseTime) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            return lock.tryLock(waitTime, leaseTime, unit); // NOSONAR
        } catch (InterruptedException e) { // NOSONAR
            log.error("tryLock:{}", e.getMessage(), e);
            return false;
        }
    }

    public void unlock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        } catch (Exception e) {
            log.error("unlock:{}", e.getMessage(), e);
        }
    }

}
