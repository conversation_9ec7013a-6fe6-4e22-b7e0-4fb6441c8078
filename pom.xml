<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xiaomi.mit</groupId>
        <artifactId>parent-pom</artifactId>
        <version>1.1.14</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.mi.oa.asset</groupId>
    <artifactId>commons-config</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>commons-config</name>
    <description>Commons config for asset business</description>
    <modules>
        <module>commons-config-api</module>
        <module>commons-config-domain</module>
        <module>commons-config-app</module>
        <module>commons-config-infra</module>
    </modules>
    <properties>
        <asset-account-be.version>*******-SNAPSHOT</asset-account-be.version>

        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <spring-cloud-nacos.version>2.2.1.RELEASE</spring-cloud-nacos.version>
        <dubbo.version>2.7.24-XIAOMI</dubbo.version>
        <java.version>1.8</java.version>
        <nacos-client.version>2.1.1</nacos-client.version>
        <revision>*******-SNAPSHOT</revision>
        <lombok.version>1.18.8</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <easyexcel.version>3.3.2</easyexcel.version>
        <poi.version>4.1.2</poi.version>
        <mybatis-plus-join.version>1.2.4</mybatis-plus-join.version>
        <eam-spring-boot-starter.version>*******-SNAPSHOT</eam-spring-boot-starter.version>
        <miplan-springboot-starter.version>2.1.8.release</miplan-springboot-starter.version>

        <!-- oaucf -->
        <oaucf-base.version>1.1.4</oaucf-base.version>
        <oaucf-cache-spring-boot-starter.version>${oaucf-base.version}</oaucf-cache-spring-boot-starter.version>
        <oaucf-idm-api-spring-boot-starter.version>${oaucf-base.version}</oaucf-idm-api-spring-boot-starter.version>
        <oaucf-fds-spring-boot-starter.version>${oaucf-base.version}</oaucf-fds-spring-boot-starter.version>
        <mibpm-bpm-spring-boot-starter.version>1.2.5-SNAPSHOT</mibpm-bpm-spring-boot-starter.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.mi.oa.asset</groupId>
                <artifactId>commons-config-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.asset</groupId>
                <artifactId>commons-config-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.asset</groupId>
                <artifactId>commons-config-infra</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.oaucf</groupId>
                <artifactId>oaucf-idm-api-spring-boot-starter</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.mi.oa.infra.oaucf</groupId>
                        <artifactId>oaucf-autoconfig-spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
                <version>${oaucf-idm-api-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.oaucf</groupId>
                <artifactId>oaucf-fds-spring-boot-starter</artifactId>
                <version>${oaucf-fds-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mi.oa.infra.oaucf</groupId>
                <artifactId>oaucf-cache-spring-boot-starter</artifactId>
                <version>${oaucf-cache-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.cloud</groupId>
                <artifactId>miplan-springboot-starter</artifactId>
                <version>${miplan-springboot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mi.oa.asset</groupId>
                <artifactId>eam-common</artifactId>
                <version>${eam-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.asset</groupId>
                <artifactId>eam-core</artifactId>
                <version>${eam-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.asset</groupId>
                <artifactId>eam-spring-boot-starter</artifactId>
                <version>${eam-spring-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.mi.oa.infra.oaucf</groupId>
                        <artifactId>oaucf-bpm-spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.mibpm</groupId>
                <artifactId>mibpm-bpm-spring-boot-starter</artifactId>
                <version>${mibpm-bpm-spring-boot-starter.version}</version>
            </dependency>
            <!-- ORM框架依赖 -->
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <testSource>${java.version}</testSource>
                    <testTarget>${java.version}</testTarget>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>false</skipTests>
                    <failIfNoTests>false</failIfNoTests>
                    <excludes>
                        <!-- 排除特定的包及其子包下的所有类 -->
                        <exclude>com/mi/oa/asset/commons/config/domain/**</exclude>
                        <exclude>com/mi/oa/asset/commons/config/app/PullErrorMessageEntryTest.java</exclude>
                        <exclude>com/mi/oa/asset/commons/config/api/**</exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
