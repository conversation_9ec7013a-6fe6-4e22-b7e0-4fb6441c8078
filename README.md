# 工程简介

## 简介

- 该工程通过信息部脚手架生成，脚手架[地址](http://start.infra.b2c.srv/)

- 工程项目结构基于MIT-DDD设计，[MIT-DDD介绍](https://xiaomi.f.mioffice.cn/docs/dock4qlPFw9eXkPfmKxeZcGAtQ9#)

- 工程集成了信息parent-pom、基础库（Core包、Common包、Starter包）、统一cicd、包/类命名规范、基础监控、日志规范

# 工程说明

### 工程中集成内容说明

- 工程中根pom使用的是信息部父[parent-pom](https://docs.mit.mi.com/mit-commons-java/home/<USER>

- 工程中引用了信息部基础库，包含core包、common包、starter包。基础库包含常用的通用工具类、小米内部工具类和一些starter，详情可以参见[信息部java基础库](https://docs.mit.mi.com/mit-commons-java/home/<USER>

- 工程中通过.gitlab-ci.yml引用信息部统一的CI-CD模板进行打包、sonar扫描、发布镜像等等，业务无需改动。

- 工程中使用了信息部的包、类命名规范。参考[DDD 命名类规范(建议) v1.0 (草稿)](https://xiaomi.f.mioffice.cn/docs/dock4homIalbtMKkrm1kNK7g1rf) 

- 工程的基础镜像中默认集成了中国区和云平台的opentelemetry agent，进行监控数据采集，会根据环境进行自动选择，数据都会上报到Hera平台，业务无需做改动。

- 项目中包含默认logback.xml配置，用来规范日志格式，业务无需修改。

- 工程中默认包含中国区Nacos注册中心、Dubbo、keycenter，其他组件需要在[脚手架](http://start.infra.b2c.srv/)页面选配。

- 工程默认使用本地配置，如果要使用分布式配置中心，需要在中国区[dayu](http://dayu.test.mi.com/)平台新增配置文件

### 工程包结构如下
![架构](./static/img.png)

各个包结构的简要功能描述，如下表所示：

| module名                              | package名        | 子Package | 说明                                          |
| :------------------------------------ | :--------------- | --------- | :-------------------------------------------- |
| ***-app                               | controller       |           | 处理Http请求                                  |
|                                       | scheduler        |           | 定时任务                                      |
|                                       | consumer         |           | 处理外部message                               |
|                                       | provider         |           | 对外rpc接口的实现逻辑                         |
|                                       | startApplication |           | spring boot启动类                             |
| ***-domain（领域层）                  | common           |           | 公共Util、公共常量                            |
|                                       | domain-module    |           | 各个子领域                                    |
|                                       |                  | entity    | 领域对象                                      |
|                                       |                  | repsitory | 代理领域层外部数据依赖                        |
|                                       |                  | gateway   | 代理领域层外部基础设施依赖                    |
|                                       |                  | valobj    | 值对象                                        |
| ***-infra<br />（基础设施层、数据层） | gateway          |           | domain层gateway实现，<br />非数据相关操作     |
|                                       | repository       |           | domain层repository实现 ，<br />数据库相关操作 |
|                                       | rpc              |           | rpc接口代理层，防腐                           |
|                                       | config           |           | 配置信息                                      |
| ***-api（对外接口包）                 | api              |           | 对外提供api（dubboapi ）                      |

上图包结构详细说明：

1. app: 主要包含三部分内容：
   - 负责系统输入信息的处理，主要包括参数校验、组装上下文、权限校验、controller、mq Consumer等。
   - 调用领域层做业务逻辑组装等。
   - 同时spring boot的启动类、配置文件也在这里。
2. domain: 领域层，封装了核心业务逻辑。领域是应用的核心，领域层不依赖任何其他层次。gateway接口、respository接口定义在这层。其中respository主要负责处理领域对象的数据处理，gateway主要代理领域层对基础设施的依赖。
3. infra： 基础设施层，主要包含数据库操作、基础设施和外部服务的代理。比如：数据库的CRUD、搜索引擎、文件系统、分布式服务的RPC等。respository和gateway的实现也在这一层，外部依赖需要通过gateway或respository的转义处理，才能被上面的App层和Domain层使用。
4. api: 服务对外透出的API、DTO 等，提供给使用方引用。

# 如何使用

- 项目基于一个简单订单场景的Demo构建，可直接通过StartApplication本地启动或部署到部署平台测试，[部署说明](https://xiaomi.f.mioffice.cn/docs/dock41FFqO2Bs9zLKRNjbFZlKke)

- 在项目根目录执行./mitag.sh进行镜像打包，本地环境默认打test包，如果打其他环境的包需要加上环境参数，例如：./mitag.sh pre。目前支持，test、pre、prod三种镜像。镜像推送说明参见，[MIT-DDD脚手架使用文档](https://xiaomi.f.mioffice.cn/docs/dock41FFqO2Bs9zLKRNjbFZlKke) 

- 在脚手架页面选择相应的smples后，如果有配置文件，会默认添加到application.yml配置文件中，如果有多环境，需要手动将文件拷贝到多环境配置文件中并修改配置信息为真实值

- 项目中默认集成keycenter，明文密码需要通过keycenter进行加密，如果没有keycenter秘钥，需要申请，申请地址，[keyCenter使用文档](https://docs.mit.mi.com/guidelines/security/secret)

- 如果使用分布式配置中心，需要在配置管理中心[dayu](http://dayu.test.mi.com/)中添加相应的配置文件。[dayu使用文档](https://xiaomi.f.mioffice.cn/docs/dock4vW6ILlkxipeLcef5Cr06Be)

## 命名规范（建议）

建议使用[命名规范](https://xiaomi.f.mioffice.cn/docs/dock4homIalbtMKkrm1kNK7g1rf)来命名相关类

