ALTER TABLE amg_asset_category ADD is_multiple_manage int(2) AFTER data_source;
ALTER TABLE amg_asset_category ADD material_type varchar(64) AFTER is_multiple_manage;

ALTER TABLE `asset`.`amg_asset_sku`
    ADD COLUMN `brand` varchar(255) NOT NULL DEFAULT '' COMMENT '品牌' AFTER `mi_goods_id`,
ADD COLUMN `spec` varchar(255) NOT NULL COMMENT '规格' AFTER `brand`,
ADD COLUMN `material_type` varchar(64) NOT NULL DEFAULT '' COMMENT '物料类型' AFTER `spec`;