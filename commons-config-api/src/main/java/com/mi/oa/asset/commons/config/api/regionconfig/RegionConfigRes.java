package com.mi.oa.asset.commons.config.api.regionconfig;

import lombok.Data;

import java.io.Serializable;

@Data
public class RegionConfigRes implements Serializable {

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 区域名称（英文）
     */
    private String regionEnglishName;

    /**
     * 排序序号
     */
    private Integer regionSortOrder;

    /**
     * 是否删除
     */
    private Integer isDeleted;


}
