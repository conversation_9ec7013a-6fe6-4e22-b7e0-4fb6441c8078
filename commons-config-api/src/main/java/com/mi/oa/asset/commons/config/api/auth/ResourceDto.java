package com.mi.oa.asset.commons.config.api.auth;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/21 18:45
 * @description
 */
@Data
public class ResourceDto {
    /**
     * 应用编码
     */
    private String appCode;
    /**
     * 资源维度编码
     */
    private String dimensionCode;
    /**
     * 资源编码
     */
    private String resourceCode;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 资源描述
     */
    private String description="";
    /**
     * 资源类型，固定写死1，表示数据资源
     */
    private String resourceType;
    /**
     * 子节点数据权限列表
     */
    private List<ResourceDto> children;
}
