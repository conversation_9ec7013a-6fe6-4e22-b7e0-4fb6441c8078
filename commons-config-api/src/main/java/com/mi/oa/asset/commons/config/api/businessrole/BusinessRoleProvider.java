package com.mi.oa.asset.commons.config.api.businessrole;

import com.mi.oa.asset.common.enums.BusinessLine;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/10/27 15:35
 */
public interface BusinessRoleProvider {

    void saveBusinessRole(String orgCode, BusinessLine businessLine, List<BusinessRoleInfoReq> roleInfo);

    void removeAllRoles(String orgCode, BusinessLine businessLine);

    /**
     * 查询角色账号，只查询当前组织，不向上所搜
     * @param orgCode
     * @param businessLine
     * @param roleCode
     * @return
     */
    Set<String> getBusinessRole(String orgCode, String businessLine, String roleCode);

    /**
     * 查询角色账号
     * @param orgCode
     * @param businessLine
     * @param roleCode
     * @return
     */
    Set<String> getBusinessRoleWithUp(String orgCode, String businessLine, String roleCode);

    /**
     * 查询角色信息
     * @param orgCode
     * @param businessLine
     * @param roleCode
     * @return
     */
    List<BusinessRoleUserRes> getBusinessRoleInfoWithUp(String orgCode, String businessLine, String roleCode);

    /**
     * 查询角色账号，批量查询多个部门
     * @param orgCode
     * @param businessLine
     * @param roleCode
     * @return
     */
    Set<String> getBatchBusinessRoleWithUp(List<String> orgCode, String businessLine, String roleCode);


    /**
     * 查询所有业务角色列表
     * @return
     */
    List<BusinessRoleRes> getBusinessRoleList();

    /**
     * 查询所有业务角色列表
     * @param language
     * @return
     */
    List<BusinessRoleRes> getBusinessRoleList(String language);

    /**
     * 批量删除业务角色
     * @param orgCode
     * @param businessLine
     */
    void removeAllRolesByOrgCodes(List<String> orgCode, BusinessLine businessLine);

}
