package com.mi.oa.asset.commons.config.api.labelprint;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/02/17 19:15
 * @description 模版请求对象
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SaveLabelTemplateReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模版id
     */
    @HttpApiDocClassDefine(value = "模版id")
    private Integer id;

    /**
     * 模版类型 业务模板：business、系统模板：system
     */
    @HttpApiDocClassDefine(value = "模版类型 业务模板：business、系统模板：system", required = true)
    @NotBlank(message = "模版类型不能为空！")
    private String templateType = "business";

    /**
     * 业务线
     */
    @HttpApiDocClassDefine(value = "业务线", required = true)
    @NotBlank(message = "业务线不能为空！")
    private String businessLine;

    /**
     * 是否默认模版，0-否，1-是
     */
    @HttpApiDocClassDefine(value = "是否默认模版，0-否，1-是")
    private Integer isDefault = 0;

    /**
     * 扫码展示字段
     */
    @HttpApiDocClassDefine(value = "扫码展示字段")
    private String scanDisplayField;

    /**
     * 是否展示编码
     */
    @HttpApiDocClassDefine(value = "是否展示编码,0:不展示,1:展示")
    private Integer isShow = 0;

    /**
     * 扩展配置
     */
    @HttpApiDocClassDefine(value = "是否展示编码,0:不展示,1:展示")
    private String  extConf;

    /**
     * 扩展配置
     */
    @HttpApiDocClassDefine(value = "标签数据来源")
    private String dataSource;
}

