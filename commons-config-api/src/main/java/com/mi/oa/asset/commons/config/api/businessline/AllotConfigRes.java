package com.mi.oa.asset.commons.config.api.businessline;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/09 18:42
 */

@Data
public class AllotConfigRes implements Serializable {

    private static final long serialVersionUID = 8790166486900134535L;

    @HttpApiDocClassDefine(value = "配置ID")
    private Integer configId;

    @HttpApiDocClassDefine(value = "字段编码", description = "调入人: inUserName，调入位置：inLocationCode，调入使用部门：inUseDeptCode，调入管理部门：inManageDeptCode，调入公司主体：inCompanyCode")
    private String fieldCode;

    @HttpApiDocClassDefine(value = "字段属性", description = "字段属性：隐藏-hide，选填-optional，必填-required")
    private String fieldProperty;

    @HttpApiDocClassDefine(value = "数据范围", description = "数据范围：biz_role-限定业务角色、use-dept-限定使用部门、manage-dept-限定管理部门、all-全部")
    private String dataRage;

    @HttpApiDocClassDefine(value = "限定内容",description = "限定内容：业务角色（多选），biz_role_dept-业务角色所属部门 user_dept-员工所属部门 use_manage_dept-使用部门所属管理部门")
    private List<String> limitBizData;

}
