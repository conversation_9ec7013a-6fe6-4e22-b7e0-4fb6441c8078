package com.mi.oa.asset.commons.config.api.bpm;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/23 10:55
 **/
@Data
public class BpmApprovalTaskRes implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 任务id
     */
    @HttpApiDocClassDefine(value = "任务id")
    private String taskId;
    /**
     * 节点名称
     */
    @HttpApiDocClassDefine(value = "节点名称")
    private String taskName;
    /**
     * 审批类型
     */
    @HttpApiDocClassDefine(value = "审批类型")
    private String signType;

    /**
     * 节点状态
     *     COMPLETE("Completed node", "已完成"),
     *     PENDING("Node in progress", "待处理"),
     *     UNREACHED("Unreached node", "未到达的节点"),
     *     UNCLAIMED("Unclaimed node", "未领取节点");
     */
    @HttpApiDocClassDefine(value = "节点状态",description ="Completed node 已完成  Node in progress 待处理  Unreached node 未到达的节点  Unclaimed node 未领取节点" )
    private String nodeState;

    /**
     * 审批人
     */
    @HttpApiDocClassDefine(value = "审批人")
    private List<BpmApproverRes> taskList;

    /**
     * 是否预测节点
     */
    @HttpApiDocClassDefine(value = "是否预测节点")
    private Integer isPredict;
}
