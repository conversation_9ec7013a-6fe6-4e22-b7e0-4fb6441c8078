package com.mi.oa.asset.commons.config.api.assetcategory;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/12 15:06
 */

@Data
public class AssetCategoryTreeRes implements Serializable {

    private static final long serialVersionUID = -8694226146558461136L;

    @HttpApiDocClassDefine(value = "分类id")
    private Integer cateId;

    @HttpApiDocClassDefine(value = "分类编码")
    private String cateCode;

    @HttpApiDocClassDefine(value = "分类名称")
    private String cateName;

    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "排序")
    private Integer sort;

    @HttpApiDocClassDefine(value = "禁用")
    private Boolean disabled;

    @HttpApiDocClassDefine(value = "下级列表")
    private List<AssetCategoryTreeRes> subList;
}
