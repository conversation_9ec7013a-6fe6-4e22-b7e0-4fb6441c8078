package com.mi.oa.asset.commons.config.api.queryfield;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:28
 */

@Data
public class QueryConfigRes implements Serializable {

    private static final long serialVersionUID = 6023044534958129847L;

    @HttpApiDocClassDefine(value = "查询方案配置主键")
    private Integer queryId;

    @HttpApiDocClassDefine(value = "查询方案名称")
    private String queryName;

    @HttpApiDocClassDefine(value = "查询条件")
    private List<QueryCondition> queryCond;
}