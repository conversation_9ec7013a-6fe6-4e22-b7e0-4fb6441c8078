package com.mi.oa.asset.commons.config.api.position;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/16 11:25
 */

@Data
@Builder
public class PositionTreeRes implements Serializable {

    private static final long serialVersionUID = 4761406312169688071L;
    @HttpApiDocClassDefine(value = "位置id")
    private Integer positionId;

    @HttpApiDocClassDefine(value = "位置编码")
    private String positionCode;

    @HttpApiDocClassDefine(value = "位置名称")
    private String positionName;

    @HttpApiDocClassDefine(value = "英文名称")
    private String positionNameEn;

    @HttpApiDocClassDefine(value = "上级编码")
    private String parentCode;

    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    @Builder.Default
    @HttpApiDocClassDefine(value = "下级")
    private List<PositionTreeRes> subList = new ArrayList<>();
}
