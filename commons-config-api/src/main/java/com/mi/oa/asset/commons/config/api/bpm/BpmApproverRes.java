package com.mi.oa.asset.commons.config.api.bpm;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/9/23 10:55
 **/
@Data
public class BpmApproverRes implements Serializable {
    private static final long serialVersionUID = 3465238581913622370L;
    @HttpApiDocClassDefine(value = "任务ID")
    private String taskId;
    @HttpApiDocClassDefine(value = "任务名称")
    private String taskName;
    @HttpApiDocClassDefine(value = "账户")
    private String userName;
    @HttpApiDocClassDefine(value = "姓名")
    private String displayName;
    @HttpApiDocClassDefine(value = "头像")
    private String avatar;
    @HttpApiDocClassDefine(value = "创建时间")
    private String createTime;
    @HttpApiDocClassDefine(value = "结束时间")
    private String endTime;
    /**
     *     AGREE("agree", "同意"),
     *     REJECT("reject", "驳回"),
     *     SIGN("sign", "加签"),
     *     TRANSFER("transfer", "转审"),
     *     DELEGATE("delegate", "委托"),
     *     TERMINATE("interrupt", "终止"),
     *     ROLLBACK("rollback", "退回"),
     *     CC("cc", "抄送"),
     *     RESOLVE("resolve", ""),
     *     SUBMIT("submit", "提交"),
     *     END("end", "审批通过"),
     *     PIN("pin", "pin"),
     *     RECALL("recall", "撤回"),
     *     SIGNATURE("signature", "加签（新）"),
     *     PENDING("pending", "待审批");
     */
    @HttpApiDocClassDefine(value = "审核结果", description = "agree 同意 reject 驳回 sign 加签 transfer 转审 delegate 委托 interrupt 终止 rollback 退回 cc 抄送 resolve 审批通过 submit 提交 end 审批通过 pin pin撤回 signature 加签（新） pending 待审批")
    private String operation;

    private String operationColor;

    private String activityType;
    @HttpApiDocClassDefine(value = "审批意见")
    private String comment;
}
