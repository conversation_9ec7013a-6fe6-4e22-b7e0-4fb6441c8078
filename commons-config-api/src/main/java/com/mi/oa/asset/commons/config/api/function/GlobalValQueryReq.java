package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GlobalValQueryReq extends PageRequest {
    @HttpApiDocClassDefine(value = "管理类型", required = false, description = "管理类型")
    private Integer manageType;

    @HttpApiDocClassDefine(value = "业务线", required = false, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "key", required = false, description = "key")
    private String key;
}
