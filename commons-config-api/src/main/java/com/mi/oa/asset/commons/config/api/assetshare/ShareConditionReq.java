package com.mi.oa.asset.commons.config.api.assetshare;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-25 15:10
 */
@Data
public class ShareConditionReq implements Serializable {
    private static final long serialVersionUID = 297973250478169090L;

    @HttpApiDocClassDefine(value = "字段代码", description = "字段代码")
    private String fieldCode;

    @HttpApiDocClassDefine(value = "条件", description = "条件")
    private String queryCond;

    @HttpApiDocClassDefine(value = "数据值", description = "数据值")
    private List<String> fieldValues;

    @HttpApiDocClassDefine(value = "字段类型", description = "字段类型")
    private String filedType;

    @HttpApiDocClassDefine(value = "连接关系", description = "连接关系")
    private String connRelate;
}
