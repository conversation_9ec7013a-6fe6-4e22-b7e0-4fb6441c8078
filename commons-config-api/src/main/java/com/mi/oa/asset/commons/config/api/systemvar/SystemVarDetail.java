package com.mi.oa.asset.commons.config.api.systemvar;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/3/19 14:52
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SystemVarDetail implements Serializable {

    private static final long serialVersionUID = 785127445771446957L;

    @HttpApiDocClassDefine(value = "变量编码", required = true)
    private String varCode;

    @HttpApiDocClassDefine(value = "变量描述", required = true)
    private String varDesc;

    @HttpApiDocClassDefine(value = "变量值", required = true)
    private String varValue;

    @HttpApiDocClassDefine(value = "业务线", required = true)
    private String businessLine;
}
