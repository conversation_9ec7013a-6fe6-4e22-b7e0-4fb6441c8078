package com.mi.oa.asset.commons.config.api.assetcategory;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/9/12 15:06
 */

@Data
public class AssetCategoryRes implements Serializable {

    private static final long serialVersionUID = 5549676477395293972L;

    @HttpApiDocClassDefine(value = "分类id")
    private Integer cateId;

    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "分类编码")
    private String cateCode;

    @HttpApiDocClassDefine(value = "分类名称")
    private String cateName;

    @HttpApiDocClassDefine(value = "分类名称（英文）")
    private String cateNameEn;

    @HttpApiDocClassDefine(value = "分类名称全")
    private String fullCateName;

    @HttpApiDocClassDefine(value = "上级分类编码")
    private String parentCateCode;

    @HttpApiDocClassDefine(value = "分类路径")
    private String catePath;

    @HttpApiDocClassDefine(value = "排序")
    private Integer sort;

    @HttpApiDocClassDefine(value = "级别")
    private Integer level;

    @HttpApiDocClassDefine(value = "禁用")
    private Boolean disabled;

    @HttpApiDocClassDefine(value = "sap资产分类编码")
    private String sapCateCode;

    @HttpApiDocClassDefine(value = "sap资产分类名称")
    private String sapCateName;

    @HttpApiDocClassDefine(value = "使用年限")
    private Integer useYear;

    @HttpApiDocClassDefine(value = "使用期间")
    private Integer useMonth;

    @HttpApiDocClassDefine(value = "是否SN管理")
    private Boolean isSerialCodeManage;

    @HttpApiDocClassDefine(value = "关联采购目录编码")
    private String purchaseCatalogCode;

    @HttpApiDocClassDefine(value = "关联采购目录编码（全路径）")
    private String purchaseCatalogCodePath;

    @HttpApiDocClassDefine(value = "关联采购目录名称")
    private String purchaseCatalogName;

    @HttpApiDocClassDefine(value = "关联采购目录名称（全路径）")
    private String purchaseCatalogNamePath;

    @HttpApiDocClassDefine(value = "创建时间")
    private Date createTime;

    @HttpApiDocClassDefine(value = "创建来源")
    private String dataSource;

    @HttpApiDocClassDefine(value = "是否一卡多物管理")
    private Boolean isMultipleManage;

    @HttpApiDocClassDefine(value = "品类经理账号")
    private String managerCode;

    @HttpApiDocClassDefine(value = "品类经理")
    private String managerName;

    @HttpApiDocClassDefine(value = "交期(天)",description = "整数，选填")
    private Integer deliveryDays;

    @HttpApiDocClassDefine(value = "物料类型")
    private String materialType;

    @HttpApiDocClassDefine(value = "物料类型")
    private String materialTypeName;

    @HttpApiDocClassDefine(value = "管理模式", description = "asset_account：资产台账管理 asset_stock：资产库存管理 stock：库存管理")
    private String mgModel;

    @HttpApiDocClassDefine(value = "管理模式")
    private String mgModelName;

    @HttpApiDocClassDefine(value = "编码生成方式")
    private Integer codeGenType;

    @HttpApiDocClassDefine(value = "国家编码")
    private String country;

}
