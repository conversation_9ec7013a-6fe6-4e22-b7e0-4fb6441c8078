package com.mi.oa.asset.commons.config.api.use;

import com.mi.oa.infra.oaucf.core.dto.Req;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.util.Date;

/**
 * 用途原因
 */
@Data
public class UseReasonRes extends Req {

    private static final long serialVersionUID = -1572356270977917121L;
    @HttpApiDocClassDefine(value = "ID")
    private Integer id;

    @HttpApiDocClassDefine(value = "用途ID")
    private Integer useId;

    @HttpApiDocClassDefine(value = "用途ID")
    private String useReason;

    @HttpApiDocClassDefine(value = "创建人账号")
    private String createUser;

    @HttpApiDocClassDefine(value = "创建人姓名")
    private String createUserName;

    @HttpApiDocClassDefine(value = "创建时间")
    private Date createTime;

    @HttpApiDocClassDefine(value = "修改人账号")
    private String updateUser;

    @HttpApiDocClassDefine(value = "修改人姓名")
    private String updateUserName;

    @HttpApiDocClassDefine(value = "修改时间")
    private Date updateTime;
}