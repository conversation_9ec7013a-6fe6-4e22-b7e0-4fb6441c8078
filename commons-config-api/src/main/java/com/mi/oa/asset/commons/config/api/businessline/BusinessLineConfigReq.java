package com.mi.oa.asset.commons.config.api.businessline;


import com.mi.oa.asset.commons.config.api.businessline.enums.BusinessLineKeyEnum;
import com.mi.oa.asset.excel.utils.JacksonUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class BusinessLineConfigReq implements Serializable {
    private static final long serialVersionUID = 8790166486900134535L;

    public static void main(String[] args) {
        Map<String, Object> config = new HashMap<>();
        List<String> asset = Arrays.asList(BusinessLineKeyEnum.ASSET_RECEIPT_REC.getKey() , BusinessLineKeyEnum.ASSET_RECEIPT_ACC.getKey());
        config.put(BusinessLineKeyEnum.ASSET_RECEIPT.getKey(), String.join(",", asset));
        //默认管理部门
        config.put(BusinessLineKeyEnum.DEFAULT_MN_DEPT.getKey(), "IT550105");
        //领用出库设置
        config.put(BusinessLineKeyEnum.REQUISITION_COMPANY.getKey(), BusinessLineKeyEnum.APPLY.getKey());
        //库存调拨
        config.put(BusinessLineKeyEnum.STOCK_ALLOT_OUT.getKey(), BusinessLineKeyEnum.STOCK_ALLOT_OUT_AUTO.getKey());
        config.put(BusinessLineKeyEnum.STOCK_ALLOT_IN.getKey(), BusinessLineKeyEnum.STOCK_ALLOT_IN_AUTO.getKey());
        System.out.println(JacksonUtils.bean2Json(config));
    }

}
