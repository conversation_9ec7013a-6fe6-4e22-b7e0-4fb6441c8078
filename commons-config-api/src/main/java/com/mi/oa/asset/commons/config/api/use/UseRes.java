package com.mi.oa.asset.commons.config.api.use;

import com.mi.oa.infra.oaucf.core.dto.Req;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用途
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UseRes extends Req {
    private static final long serialVersionUID = 5668853626811331250L;
    @HttpApiDocClassDefine(value = "主键")
    private Integer id;

    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "用途编码")
    private String useCode;

    @HttpApiDocClassDefine(value = "用途名称")
    private String useName;

    @HttpApiDocClassDefine(value = "用途名称（英文）")
    private String useNameEn;

    @HttpApiDocClassDefine(value = "用途类别")
    private Integer useType;

    @HttpApiDocClassDefine(value = "适用范围")
    private Integer scope;

    @HttpApiDocClassDefine(value = "是否禁用", description = "1-是，0-否")
    private Integer disabled;

    @HttpApiDocClassDefine(value = "排序")
    private Integer sorted;

    @HttpApiDocClassDefine(value = "备注")
    private String remark;

    @HttpApiDocClassDefine(value = "备注（英文）")
    private String remarkEn;

    @HttpApiDocClassDefine(value = "创建人账号")
    private String createUser;

    @HttpApiDocClassDefine(value = "创建人姓名")
    private String createUserName;

    @HttpApiDocClassDefine(value = "创建时间")
    private Date createTime;

    @HttpApiDocClassDefine(value = "修改人账号")
    private String updateUser;

    @HttpApiDocClassDefine(value = "修改人姓名")
    private String updateUserName;

    @HttpApiDocClassDefine(value = "修改时间")
    private Date updateTime;
}