package com.mi.oa.asset.commons.config.api.common;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.mi.oa.asset.commons.config.api.common.enums.ExecutionStatus;
import com.mi.oa.asset.commons.config.api.common.enums.AsyncTaskType;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/12 9:59
 */

@Data
public class AsyncTaskReq implements Serializable {

    private static final long serialVersionUID = -8422883298253343286L;

    private Integer taskId;

    /**
     * 执行状态
     */
    private ExecutionStatus executionStatus;

    /**
     * 导入任务类型
     */
    private AsyncTaskType taskType;

    /**
     * 业务线
     */
    private BusinessLine businessLine;

    /**
     * 任务编号
     */
    private String recordNo;

    /**
     * 执行结果
     */
    private String executionResult;

    /*
     * 原始文件链接
     */
    private String originFileUrl;

    /**
     * 错误结果链接
     */
    private String resultFileUrl;
}
