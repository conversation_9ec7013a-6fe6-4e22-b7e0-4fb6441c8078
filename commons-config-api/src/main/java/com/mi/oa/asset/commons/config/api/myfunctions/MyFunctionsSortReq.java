package com.mi.oa.asset.commons.config.api.myfunctions;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/6 19:15
 * @description 我的常用功能排序
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MyFunctionsSortReq implements Serializable {
    private static final long serialVersionUID = 3166833408631083114L;
    /**
     * 管理线编码
     */
    @NotBlank(message = "管理线编码不能为空")
    @HttpApiDocClassDefine(value = "管理线编码")
    private String manageLineCode;
    /**
     * 功能编码
     */
    @HttpApiDocClassDefine(value = "功能编码顺序列表")
    private List<String> resourceCodes;

}
