
package com.mi.oa.asset.commons.config.api.assetsku;

import com.fasterxml.jackson.annotation.JsonValue;
import com.mi.oa.asset.common.enums.IEnum;
import com.mi.oa.asset.excel.enums.ExcelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2024/7/30 11:09
 */
@Getter
@AllArgsConstructor
public enum SnTypeEnum implements IEnum<SnTypeEnum>, ExcelEnum<SnTypeEnum> {

    SN("sn", "SN管理"),
    MULTIPLE("multiple", "一卡多物管理"),
    ;
    @JsonValue
    private final String code;

    private final String desc;

    public static SnTypeEnum getByCode(String code) {
        if(null == code) {
            return null;
        }

        for (SnTypeEnum compensateType: SnTypeEnum.values()) {
            if (compensateType.code.equals(code)) {
                return compensateType;
            }
        }
        return null;
    }

    public static SnTypeEnum getByDesc(String desc) {
        if(StringUtils.isBlank(desc)) {
            return null;
        }

        for (SnTypeEnum compensateType: SnTypeEnum.values()) {
            if (compensateType.desc.equals(desc)) {
                return compensateType;
            }
        }
        return null;
    }
}
