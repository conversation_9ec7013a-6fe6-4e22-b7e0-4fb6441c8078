package com.mi.oa.asset.commons.config.api.function;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/07 14:14
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictRes implements Serializable {

    private Integer id;

    private String parentCode;

    private Integer type;

    private String code;

    private String enName;

    private String name;

    private String moduleCode;

    private String moduleName;

    private Integer valid;

    private Integer sort;

    private String funcCode;


    /**
     * 创建人账号
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人账号
     */
    private String updateUser;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

    private String relatedCode;

}
