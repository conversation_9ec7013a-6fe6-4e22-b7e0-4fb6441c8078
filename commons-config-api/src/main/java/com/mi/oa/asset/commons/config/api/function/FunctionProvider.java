package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mit.api.PageData;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


public interface FunctionProvider {

    /**
     * 查询功能列表
     * @param funcQueryReq
     * @return
     */
    PageData<FunctionRes> listFunc(FuncQueryReq funcQueryReq);

    /**
     * 查询表列表
     * @param funcQueryReq
     * @return
     */
    PageData<FuncTableRes> listTable(FuncQueryReq funcQueryReq);

    /**
     * 查询功能
     * @param id
     * @return
     */
    FunctionRes findFunctionById(Integer id);

    /**
     * 保存/更新功能
     * @param req
     * @return
     */
    Integer saveFunction(FunctionReq req);

    /**
     * 删除功能
     * @param ids
     */
    void deleteFunction(List<Integer> ids);

    /**
     * 查询字段列表
     * @param fieldReq
     * @return
     */
    PageData<FuncFieldRes> listFuncField(FieldQueryReq fieldReq);

    /**
     *
     * @param req
     * @return
     */
    List<FormFieldRes> getFieldMap(FormFieldReq req);

    /**
     * 查询字段列表
     * @param fieldReq
     * @return
     */
    PageData<FuncFieldRes> listTableField(FieldQueryReq fieldReq);

    /**
     * 导入字段
     * @param importReq
     */
    void importField(FieldImportReq importReq);

    /**
     * 保存字段
     * @param saveReq
     */
    void saveField(List<FuncFieldReq> saveReq);

    /**
     * 批量删除字段
     * @param ids
     */
    void deleteField(List<Integer> ids);

    /**
     * 字段授权
     * @param saveReq
     */
    void saveDictAuth(FuncDictAuthReq saveReq);

    /**
     * 查询授权列表
     * @param authReq
     * @return
     */
    List<FuncDictRelRes> listDictAuth(FuncDictAuthReq authReq);

    /**
     * 批量删除
     * @param ids
     */
    void deleteDictAuth(List<Integer> ids);

    /**
     * 查询系统角色
     * @param fieldReq
     * @return
     */
    PageData<FunctionRoleRes> getPageRole(FieldQueryReq fieldReq);

    /**
     * 查询功能权限详情
     * @param funCode
     * @return
     */
    FuncDataPermissionRes getDataAuth(String funCode, String businessLine);

    /**
     * 保存数据权限
     * @param req
     */
    void saveDataAuth(FuncDataPermissionReq req);

    /**
     * 表单字典查询
     * @param req
     * @return
     */
    List<FuncDictRes> getAuthDictInfo(FuncDictAuthReq req);

    /**
     * 查询表
     * @param id
     * @return
     */
    FuncTableRes findFuncTableById(Integer id);

    /**
     * 保存/更新表
     * @param req
     * @return
     */
    Integer saveFuncTable(FuncTableReq req);

    /**
     * 删除表
     * @param ids
     */
    void deleteFuncTable(List<Integer> ids);

    /**
     * 查询字典列表
     * @param funcQueryReq
     * @return
     */
    PageData<DictRes> listDict(DictQueryReq funcQueryReq);

    /**
     * 查询字典列表
     * @param codeList
     * @return
     */
    Map<String, List<DictRes>> getDictList(List<String> codeList);

    /**
     * 查询字典详情
     * @param id
     * @return
     */
    DictRes getDict(Integer id);

    /**
     * 字典保存
     * @param dictReq
     * @return
     */
    Integer saveDict(DictReq dictReq);

    /**
     * 删除字典值
     * @param ids
     */
    void deleteDict(List<Integer> ids);

    /**
     * 功能关联字典
     * @param funcQueryReq
     * @return
     */
    PageData<FunctionDictRes> listDictRel(DictQueryReq funcQueryReq);

    /**
     * 导入功能关联字典
     * @param importReq
     */
    void importFuncDict(DictImportReq importReq);

    /**
     * 功能关联字典保存
     * @param dictReq
     */
    void saveFuncDict(FuncDictReq dictReq);

    /**
     * 删除功能字典值
     * @param ids
     */
    void deleteDictRel(List<Integer> ids);

    /**
     * 查询业务线关联的字典
     * @param queryReq
     * @return
     */
    List<DictRes> listDictRelByBusinessLine(DictBusinessLineQueryReq queryReq);

    /**
     * 查询业务线关联的字典
     * @param queryReq
     * @return
     */
    Map<String,List<DictRes>> listDictRelByBusinessLineBatch(DictBusinessLineBatchReq queryReq);

    /**
     *  全局变量列表
     * @param queryReq
     * @return
     */
    PageData<GlobalValRes> listGlobalVal(GlobalValQueryReq queryReq);
    /**
     * 保存变量
     * @param req
     * @return
     */
    Integer saveGlobalVal(GlobalValReq req);

    /**
     * 功能关联字典
     * @param ids
     * @return
     */
    void deleteGlobalVal(List<Integer> ids);

    /**
     * 导入变量
     * @param req
     */
    void importGlobalVal(GlobalImportReq req);

    /**
     * 查询变量
     * @param code
     * @param businessLine
     * @return
     */
    List<GlobalValRes> getGlobalVal(List<String> code, String businessLine);


    /**
     * 查询变量
     * @param code
     * @return
     */
    List<GlobalValRes> getGlobalValByCode(List<String> code);

    /**
     * 查询下载任务列表
     * @param queryReq
     * @return
     */
    PageData<DownTaskRes> listDownTask(DownTaskQueryReq queryReq);

}
