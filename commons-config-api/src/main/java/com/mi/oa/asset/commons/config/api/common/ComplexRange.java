package com.mi.oa.asset.commons.config.api.common;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 资产条件更多字段筛选范围配置（通用类）
 *
 * <AUTHOR>
 * @date 2025-01-06 17:39
 */
@Data
public class ComplexRange implements Serializable {
    private static final long serialVersionUID = 2244122255452325058L;

    @HttpApiDocClassDefine(value = "主键", description = "主键")
    private Integer id;

    @HttpApiDocClassDefine(value = "筛选字段编码", required = true, description = "筛选字段编码")
    @NotBlank(message = "筛选字段不能为空")
    private String fieldCode;

    @HttpApiDocClassDefine(value = "运算逻辑符号", required = true, description = "运算逻辑符号")
    @NotBlank(message = "运算逻辑不能为空")
    private String queryCond;

    @HttpApiDocClassDefine(value = "目标值", required = true, description = "目标值")
    @NotNull(message = "目标值不能为空")
    private List<String> fieldValues;
}
