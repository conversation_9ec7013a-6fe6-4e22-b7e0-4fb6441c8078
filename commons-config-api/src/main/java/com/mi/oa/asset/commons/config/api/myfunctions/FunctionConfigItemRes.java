package com.mi.oa.asset.commons.config.api.myfunctions;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/6 19:16
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FunctionConfigItemRes implements Serializable {
    private static final long serialVersionUID = -6591504865136888611L;

    /**
     * 明细id
     */
    @HttpApiDocClassDefine(value = "明细id")
    private Integer itemId;

    /**
     * 配置id
     */
    @HttpApiDocClassDefine(value = "配置id")
    private Integer configId;

    /**
     * 业务线 business_line
     */
    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    /**
     * 业务线名称 business_line_name
     */
    @HttpApiDocClassDefine(value = "业务线名称")
    private String businessLineName;

    /**
     * 授权范围 auth_type
     */
    @HttpApiDocClassDefine(value = "授权范围")
    private Integer authType;

    /**
     * 授权部门
     */
    @HttpApiDocClassDefine(value = "授权部门")
    private List<FunctionConfigDeptRes> authDeptList;

    /**
     * 启用的国家或地区 country
     */
    @HttpApiDocClassDefine(value = "启用的国家或地区")
    private List<Country> countries;

    public FunctionConfigItemRes(String businessLine, String businessLineName) {
        this.businessLine = businessLine;
        this.businessLineName = businessLineName;
    }
}
