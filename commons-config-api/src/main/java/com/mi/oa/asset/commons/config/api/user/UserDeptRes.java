package com.mi.oa.asset.commons.config.api.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * 用户部门信息
 *
 * <AUTHOR>
 * @date 2023/9/11 20:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UserDeptRes implements Serializable {

    private static final long serialVersionUID = 7945225524537434242L;

    private String deptId;

    private String deptName;

    private String deptEnName;

    private String level;
}
