package com.mi.oa.asset.commons.config.api.assetcode;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-06-25 16:24
 */
@Data
public class AssetCodeRuleRes implements Serializable {
    private static final long serialVersionUID = 3242717910302455911L;

    @HttpApiDocClassDefine(value = "业务线", description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "是否支持手动录入", description = "是否支持手动录入")
    private Boolean isInput;

    @HttpApiDocClassDefine(value = "是否使用SN作为资产编码", description = "是否使用SN作为资产编码")
    private Boolean isSn;

    @HttpApiDocClassDefine(value = "可选择项目", description = "根据业务线取默认配置")
    private List<FieldRuleConfigRes> optionalItemList;

    @HttpApiDocClassDefine(value = "已选择项目", description = "保存在数据表中的json")
    private List<FieldRuleConfigRes> selectedItemList;
}
