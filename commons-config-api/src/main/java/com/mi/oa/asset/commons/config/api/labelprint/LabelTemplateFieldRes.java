package com.mi.oa.asset.commons.config.api.labelprint;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/02/24 15:32
 */
@Data
public class LabelTemplateFieldRes implements Serializable {

    private static final long serialVersionUID = 722464880891302246L;

    /**
     * 字段编码
     */
    private String fieldName;

    /**
     * 字段名称
     */
    private String fieldDesc;

    /**
     * 字段上间距
     */
    private String fieldMarginTop;

    /**
     * 字段下间距
     */
    private String fieldMarginBottom;

    /**
     * 字段顺序
     */
    private String fieldOrder;

    /**
     * 字段字号
     */
    private String fontSize;

    /**
     * 字段字体样式 正常：normal、加粗：bold
     */
    private String fontStyle;

    /**
     * 字段对齐方式 左对齐：left，居中对齐:center，右对齐:right
     */
    private String alignment;

    /**
     * 是否隐藏字段名称，0-否，1-是
     */
    private Integer isHide;
}
