package com.mi.oa.asset.commons.config.api.myfunctions;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 首页功能配置res
 * @Date 2025/5/6 19:14
 **/
@Data
public class FunctionConfigRes implements Serializable {
    private static final long serialVersionUID = 4651937612379290806L;

    /**
     * 配置id
     */
    @HttpApiDocClassDefine(value = "配置id")
    private Integer configId;

    /**
     * 管理线 manage_line
     */
    @HttpApiDocClassDefine(value = "管理线编码")
    private String manageLine;

    /**
     * 管理线名称 manage_line_name
     */
    @HttpApiDocClassDefine(value = "管理线名称")
    private String manageLineName;

    /**
     * 功能名称 name
     */
    @HttpApiDocClassDefine(value = "功能名称")
    private String name;

    /**
     * 功能英文名称 name_en
     */
    @HttpApiDocClassDefine(value = "功能名称（英文）")
    private String nameEn;

    /**
     * icon url icon_url
     */
    @HttpApiDocClassDefine(value = "icon url")
    private String iconUrl;

    /**
     * 功能url function_url
     */
    @HttpApiDocClassDefine(value = "功能url")
    private String functionUrl;

    /**
     * 功能描述 description
     */
    @HttpApiDocClassDefine(value = "功能描述")
    private String description;

    /**
     * 功能英文描述 description_en
     */
    @HttpApiDocClassDefine(value = "功能描述（英文）")
    private String descriptionEn;

    /**
     * 排序值 sort
     */
    @HttpApiDocClassDefine(value = "排序")
    private Integer sort;

    /**
     * 业务线 business_line
     */
    @HttpApiDocClassDefine(value = "业务线")
    private List<FunctionConfigItemRes> items;
}
