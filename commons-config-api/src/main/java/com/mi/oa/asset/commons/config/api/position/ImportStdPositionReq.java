package com.mi.oa.asset.commons.config.api.position;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/26 11:32
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportStdPositionReq implements Serializable {

    private static final long serialVersionUID = -6410772115898840553L;
    @HttpApiDocClassDefine(value = "业务线", required = true, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "导入项目", required = true, description = "导入项目")
    @Builder.Default
    private List<Item> items = new ArrayList<>();

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item implements Serializable {

        private static final long serialVersionUID = -1407781320388975836L;
        @HttpApiDocClassDefine(value = "位置编码", required = true, description = "位置编码")
        private String positionCode;

        @HttpApiDocClassDefine(value = "位置名称", required = true, description = "位置名称")
        private String positionName;

        @HttpApiDocClassDefine(value = "上级编码", required = true, description = "上级编码")
        private String parentCode;
    }
}
