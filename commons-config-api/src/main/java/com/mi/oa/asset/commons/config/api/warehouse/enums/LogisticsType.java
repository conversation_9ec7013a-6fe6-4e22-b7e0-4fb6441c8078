
package com.mi.oa.asset.commons.config.api.warehouse.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.mi.oa.asset.common.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/7/20 10:32
 */
@Getter
@AllArgsConstructor
public enum LogisticsType implements IEnum<LogisticsType> {

    SELF("self", "自提"),
    MAIL("mail", "邮寄"),
    ;
    @JsonValue
    private final String code;

    private final String desc;

    public static LogisticsType getByCode(String code) {
        if(null == code) {
            return null;
        }

        for (LogisticsType compensateType: LogisticsType.values()) {
            if (compensateType.code.equals(code)) {
                return compensateType;
            }
        }
        return null;
    }

    public static LogisticsType getByDesc(String desc) {
        if(StringUtils.isBlank(desc)) {
            return null;
        }

        for (LogisticsType compensateType: LogisticsType.values()) {
            if (compensateType.desc.equals(desc)) {
                return compensateType;
            }
        }
        return null;
    }
}
