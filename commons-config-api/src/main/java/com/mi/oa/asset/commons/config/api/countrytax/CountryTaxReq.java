package com.mi.oa.asset.commons.config.api.countrytax;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Data
@NoArgsConstructor
public class CountryTaxReq {

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 税码
     */
    private String taxCode;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 默认税率（多个币种）
     */
    private Integer defaultTaxRate;

    /**
     * 国家数据表id
     */
    private Integer countryConfigId;

    /**
     * 国家/地区代码（3位）
     */
    private String countryCodeAlphaThree;

    /**
     * 国家/地区代码（2位）
     */
    private String countryCodeAlphaTwo;

    /**
     * 国家/地区
     */
    private String countryName;

    /**
     * 是否删除
     */
    private Integer isDeleted;

}
