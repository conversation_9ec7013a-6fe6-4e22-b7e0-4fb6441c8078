package com.mi.oa.asset.commons.config.api.auth;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/21 18:44
 * @description
 */
@Data
public class QueryDataResourceListReq {
    /**
     * 资源名称（必填）
     */
    private String dimensionCode;
    /**
     * 资源编码列表（必填）
     */
    private List<String> resourceCodes;
    /**
     * 是否查出子节点信息（非必填）
     * 1. 为true的时候返回resourceCodes列表详情以resourceCodes列表下的所有子节点列表详情
     * 2. 为false的时候只返回resourceCodes列表的详情
     */
    private Boolean recursion;
}
