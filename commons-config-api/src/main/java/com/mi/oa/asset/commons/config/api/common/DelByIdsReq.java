package com.mi.oa.asset.commons.config.api.common;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/21 20:53
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DelByIdsReq implements Serializable {

    private static final long serialVersionUID = 3575527466578899770L;
    @HttpApiDocClassDefine(value = "id列表")
    private List<Integer> ids;
}
