package com.mi.oa.asset.commons.config.api.assetshare;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/14 17:05
 **/
@Data
public class ShareAllotReq implements Serializable {
    private static final long serialVersionUID = 1940207772717691816L;

    @Valid
    @Size(min = 1)
    @NotEmpty(message = "调拨资产信息不能为空")
    @NotNull(message = "调拨资产信息不能为空")
    private List<ShareAssetAllotReq> allot;

    @NotNull(message = "应用客户端不能为空")
    private String shareClient;
}
