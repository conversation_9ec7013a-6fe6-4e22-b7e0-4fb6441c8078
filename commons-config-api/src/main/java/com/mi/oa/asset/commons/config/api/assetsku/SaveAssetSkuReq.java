package com.mi.oa.asset.commons.config.api.assetsku;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/9 14:49
 */

@Data
public class SaveAssetSkuReq implements Serializable {

    private static final long serialVersionUID = -3498364901995276050L;
    @HttpApiDocClassDefine(value = "skuId", required = false, description = "更新时必传")
    private Integer skuId;

    @HttpApiDocClassDefine(value = "skuCode", required = false, description = "更新时必传")
    @Length(max = 20, message = "物料编码：非必填，0-20字")
    private String skuCode;

    @HttpApiDocClassDefine(value = "sku名称", required = true, description = "sku名称")
    @NotBlank(message = "物料名称不能为空")
    private String skuName;

    @HttpApiDocClassDefine(value = "sku名称(英文)", required = false, description = "sku名称(英文)")
    private String skuNameEn;

    @HttpApiDocClassDefine(value = "别名", required = false, description = "别名")
    private String aliasName;

    @HttpApiDocClassDefine(value = "单价", required = true, description = "单价")
    private Double price;

    @HttpApiDocClassDefine(value = "分类id", required = false, description = "分类id")
    private String cateId;

    @HttpApiDocClassDefine(value = "小米sku编码", required = false, description = "小米sku编码")
    private String miSkuCode;

    @HttpApiDocClassDefine(value = "小米商品id", required = false, description = "小米商品id")
    private String miGoodsId;

    @HttpApiDocClassDefine(value = "品牌", required = false, description = "品牌")
    private String brand;

    @HttpApiDocClassDefine(value = "规格", required = false, description = "规格")
    private String spec;

    @HttpApiDocClassDefine(value = "物料类型", required = false, description = "物料类型")
    private String materialType;

    @HttpApiDocClassDefine(value = "是否一卡多物管理", required = false, description = "是否一卡多物管理")
    private Integer isMultipleManage;

    @HttpApiDocClassDefine(value = "是否SN管理", required = false, description = "是否SN管理")
    private Integer isSn;

    @HttpApiDocClassDefine(value = "计量单位编码", required = true, description = "计量单位编码")
    @NotBlank(message = "计量单位不能为空")
    private String measureCode;

    @HttpApiDocClassDefine(value = "计量单位名称", required = true, description = "计量单位名称")
    private String measureName;

    @HttpApiDocClassDefine(value = "国家编码", required = true, description = "国家编码")
    @NotBlank(message = "国家不能为空")
    private String country;

    @HttpApiDocClassDefine(value = "国家名称", required = true, description = "国家名称")
    private String countryName;

    @HttpApiDocClassDefine(value = "物料状态", required = true, description = "物料状态")
    @NotNull(message = "物料状态不能为空")
    private Boolean disabled;

    @HttpApiDocClassDefine(value = "备注", required = false, description = "备注")
    private String remark;

    @HttpApiDocClassDefine(value = "商品名称", required = false, description = "商品名称")
    @Length(max = 50, message = "商品名称：非必填，0-50字")
    private String productName;

    @HttpApiDocClassDefine(value = "项目编码", required = false, description = "项目编码")
    @Length(max = 20, message = "项目编号：非必填，0-20字")
    private String projectCode;

    @HttpApiDocClassDefine(value = "项目阶段", required = false, description = "项目编码")
    @Length(max = 20, message = "项目阶段：非必填，0-20字")
    private String projectPhase;

    @HttpApiDocClassDefine(value = "上市时间", required = false, description = "上市时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date saleDate;
    @HttpApiDocClassDefine(value = "管理信息", required = true, description = "管理信息")
    private List<AssetSkuMgReq> manages;

    @HttpApiDocClassDefine(value = "型号", required = false, description = "型号")
    private String model;
}
