package com.mi.oa.asset.commons.config.api.queryfield;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:28
 */

@Data
public class QueryConfigReq implements Serializable {

    private static final long serialVersionUID = 7692448728812297923L;

    @HttpApiDocClassDefine(value = "查询方案主键")
    private Integer queryId;

    @HttpApiDocClassDefine(value = "管理线", required = true, description = "管理线")
    @NotBlank(message = "管理线不能为空")
    private String manageLine;

    @HttpApiDocClassDefine(value = "功能ID", required = true, description = "功能ID")
    @NotBlank(message = "功能ID不能为空")
    private String funId;

    @HttpApiDocClassDefine(value = "查询方案名称", required = true, description = "查询方案名称")
    @NotBlank(message = "查询方案名称不能为空")
    private String queryName;

    @HttpApiDocClassDefine(value = "查询条件", required = true, description = "查询条件")
    @NotNull(message = "查询条件不能为空")
    @Valid
    private List<QueryCondition> queryCond;
}