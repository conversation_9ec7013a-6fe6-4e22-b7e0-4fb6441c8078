package com.mi.oa.asset.commons.config.api.serialcode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/9/19 14:17
 */

public enum RollingType implements Serializable {

    Y("y", "yyyy"),
    YM("ym", "yyyyMM"),
    YMD("ymd", "yyyyMMdd"),
    ;

    private final String code;

    private final String formatter;

    RollingType(String code, String formatter) {
        this.code = code;
        this.formatter = formatter;
    }

    public String getCode() {
        return code;
    }

    public String getFormatter() {
        return formatter;
    }
}
