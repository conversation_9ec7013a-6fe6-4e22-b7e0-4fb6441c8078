package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DownTaskQueryReq extends PageRequest {
    @HttpApiDocClassDefine(value = "key", required = false, description = "key")
    private String key;

    @HttpApiDocClassDefine(value = "任务状态", required = false, description = "任务状态")
    private String taskStatus;

    private String language;
}
