package com.mi.oa.asset.commons.config.api.assetshare;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-01-08 09:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareRecordBaseRes implements Serializable {
    private static final long serialVersionUID = 4535345830504467039L;

    @HttpApiDocClassDefine(value = "单据标题", description = "单据标题")
    private String recordTitle;

    @HttpApiDocClassDefine(value = "单据状态", description = "单据状态：draft-草稿/暂存,submitted-已提交/提交")
    private String recordStatus;

    @HttpApiDocClassDefine(value = "共享状态", description = "共享状态：1-生效,0-失效")
    private Integer isValid = 1;

    @HttpApiDocClassDefine(value = "有效期类型", description = "有效期类型：start_stop-起止时间,permanent-永久有效")
    private String validityType;

    @HttpApiDocClassDefine(value = "共享开始时间", description = "共享开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date shareStartTime;

    @HttpApiDocClassDefine(value = "共享结束时间", description = "共享结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date shareEndTime;

    @HttpApiDocClassDefine(value = "共享场景", description = "共享场景:reservation-设备预约")
    private String shareScene;

    @HttpApiDocClassDefine(value = "应用的客户端", required = true, description = "应用的客户端 app_emp：员工端APP，app_manage：管理端APP，client：EAM员工端，admin：EAM管理端")
    private String shareClient;

    @HttpApiDocClassDefine(value = "通知范围", description = "通知范围")
    private String notifyRange;

    @HttpApiDocClassDefine(value = "共享说明", description = "共享说明")
    private String memo;

    @HttpApiDocClassDefine(value = "业务线范围编码", description = "业务线范围编码")
    private String businessLineCode;

    @HttpApiDocClassDefine(value = "业务线范围名称", description = "业务线范围名称")
    private String businessLineName;

    @HttpApiDocClassDefine(value = "部门范围编码", description = "部门范围编码")
    private String shareDeptCode;

    @HttpApiDocClassDefine(value = "部门范围名称", description = "部门范围名称")
    private String shareDeptName;

    @HttpApiDocClassDefine(value = "人员范围账号", required = true, description = "人员范围账号")
    private String shareUserCode;

    @HttpApiDocClassDefine(value = "人员范围", required = true, description = "人员范围")
    private String shareUserName;

    @HttpApiDocClassDefine(value = "人员范围json串", description = "人员范围")
    private String shareUser;

    @HttpApiDocClassDefine(value = "使用部门范围编码", description = "使用部门范围编码")
    private String useDeptCode;

    @HttpApiDocClassDefine(value = "使用部门范围名称", description = "使用部门范围名称")
    private String useDeptName;

    @HttpApiDocClassDefine(value = "管理部门范围编码", description = "管理部门范围编码")
    private String manageDeptCode;

    @HttpApiDocClassDefine(value = "管理部门范围名称", description = "管理部门范围名称")
    private String manageDeptName;

    @HttpApiDocClassDefine(value = "资产所属位置编码", description = "资产所属位置编码")
    private String locationCode;

    @HttpApiDocClassDefine(value = "资产所属位置名称", description = "资产所属位置名称")
    private String locationName;

    @HttpApiDocClassDefine(value = "资产所属位置ID", description = "资产所属位置ID")
    private String locationId;

    @HttpApiDocClassDefine(value = "资产分类编码", description = "资产分类编码")
    private String assetCateCode;

    @HttpApiDocClassDefine(value = "资产分类名称", description = "资产分类名称")
    private String assetCateName;

    @HttpApiDocClassDefine(value = "资产分类Id", description = "资产分类Id")
    private String assetCateId;

    @HttpApiDocClassDefine(value = "资产类型范围", description = "资产类型范围")
    private String assetType;

    @HttpApiDocClassDefine(value = "使用状态范围", description = "使用状态范围")
    private String useStatus;

    @HttpApiDocClassDefine(value = "验收状态范围", description = "验收状态范围")
    private String checkStatus;

    @HttpApiDocClassDefine(value = "是否勾选自定义共享清单", description = "是否勾选自定义共享清单")
    private Boolean isCheckCustom;

    @HttpApiDocClassDefine(value = "编制部门编码", description = "编制部门编码")
    private String createDeptCode;

    @HttpApiDocClassDefine(value = "编制部门名称", description = "编制部门名称")
    private String createDeptName;

    @HttpApiDocClassDefine(value = "创建人用户名", description = "创建人用户名")
    private String createUser;

    @HttpApiDocClassDefine(value = "创建人姓名", description = "创建人姓名")
    private String createUserName;

    @HttpApiDocClassDefine(value = "更新人用户名", description = "更新人用户名")
    private String updateUser;

    @HttpApiDocClassDefine(value = "更新人姓名", description = "更新人姓名")
    private String updateUserName;

    @HttpApiDocClassDefine(value = "创建时间", description = "创建时间")
    private Date createTime;

    @HttpApiDocClassDefine(value = "更新时间", description = "更新时间")
    private Date updateTime;
}
