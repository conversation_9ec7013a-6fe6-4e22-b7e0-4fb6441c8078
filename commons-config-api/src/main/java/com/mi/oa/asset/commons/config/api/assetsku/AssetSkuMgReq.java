package com.mi.oa.asset.commons.config.api.assetsku;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/19 16:43
 */

@Data
public class AssetSkuMgReq implements Serializable {

    private static final long serialVersionUID = -3498364901995276050L;
    @HttpApiDocClassDefine(value = "id",  description = "管理线ID")
    private Integer id;

    @HttpApiDocClassDefine(value = "skuId", description = "skuId")
    private Integer skuId;

    @HttpApiDocClassDefine(value = "业务线", required = true, description = "业务线")
    @NotBlank(message = "管理信息业务线不能为空")
    private String businessLine;

    @HttpApiDocClassDefine(value = "分类id", required = true)
    @NotNull(message = "管理信息分类不能为空")
    private Integer cateId;

    @HttpApiDocClassDefine(value = "物料类型", required = true, description = "物料类型")
    @NotBlank(message = "管理信息物料类型不能为空")
    private String materialType;

    @HttpApiDocClassDefine(value = "管理模式", required = true, description = "asset_account：资产台账管理 asset_stock：资产库存管理 stock：库存管理")
    @NotBlank(message = "管理信息管理模式不能为空")
    private String mgModel;

    @HttpApiDocClassDefine(value = "串号管理", required = true, description = "sn：SN管理  multiple：一卡多物管理")
    @NotBlank(message = "管理信息串号管理不能为空")
    private String serialMg;

    @HttpApiDocClassDefine(value = "存货成本核算", required = true, description = "moving_avg:移动加权平均")
    @NotBlank(message = "管理信息存货成本核算不能为空")
    private String costing;

    @HttpApiDocClassDefine(value = "安全库存", required = false, description = "安全库存")
    private BigDecimal secureQuantity;

    @HttpApiDocClassDefine(value = "最高库存", required = false, description = "最高库存")
    private BigDecimal highestQuantity;

    @HttpApiDocClassDefine(value = "最低库存", required = false, description = "最低库存")
    private BigDecimal miniQuantity;


}
