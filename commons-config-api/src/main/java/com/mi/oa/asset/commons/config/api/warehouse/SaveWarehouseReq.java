package com.mi.oa.asset.commons.config.api.warehouse;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class SaveWarehouseReq implements Serializable {
    @HttpApiDocClassDefine(value = "主键")
    private Integer id;

    @HttpApiDocClassDefine(value = "仓库编码")
    private String houseCode;

    @HttpApiDocClassDefine(value = "仓库名称")
    private String houseName;

    @HttpApiDocClassDefine(value = "仓库类型")
    private String houseType;

    @HttpApiDocClassDefine(value = "管理员编码，多个用英文逗号分隔")
    private String adminCodes;

    @HttpApiDocClassDefine(value = "管理员名称，多个用英文逗号分隔")
    private String adminNames;

    @HttpApiDocClassDefine(value = "地址")
    private String country;

    @HttpApiDocClassDefine(value = "供应商编码")
    private String currency;

    @HttpApiDocClassDefine(value = "供应商编码")
    private String address;

    @HttpApiDocClassDefine(value = "是否货架管理")
    private Boolean shelves;

    @HttpApiDocClassDefine(value = "货架编码")
    private String shelvesNum;

    @HttpApiDocClassDefine(value = "其他仓库编码")
    private String thirdHouseCode;

    @HttpApiDocClassDefine(value = "sap仓库编码")
    private String sapHouseCode;

    @HttpApiDocClassDefine(value = "工厂编码")
    private String factoryCode;

    @HttpApiDocClassDefine(value = "公司编码")
    private String companyCode;

    @HttpApiDocClassDefine(value = "服务")
    private String services;

    @HttpApiDocClassDefine(value = "自提地区")
    private String zitiArea;

    @HttpApiDocClassDefine(value = "邮寄地区")
    private String mailArea;

    @HttpApiDocClassDefine(value = "仓库状态")
    private String houseStatus;

    @HttpApiDocClassDefine(value = "物流属性")
    private String businessLine;

    @HttpApiDocClassDefine(value = "供应商编码")
    private String logisticsType;

    @HttpApiDocClassDefine(value = "部门编码")
    private String departCode;

    @HttpApiDocClassDefine(value = "部门全名称")
    private String departName;

    @HttpApiDocClassDefine(value = "供应商编码")
    private String departFullName;

    @HttpApiDocClassDefine(value = "联系电话")
    private String phone;

    @HttpApiDocClassDefine(value = "排序")
    private String sort;

    @HttpApiDocClassDefine(value = "地区")
    private String area;

    @HttpApiDocClassDefine(value = "邮箱")
    private String email;

    @HttpApiDocClassDefine(value = "可支持产品类别 OBM,OEM,ODM")
    private String productTypes;

    @HttpApiDocClassDefine(value = "库存调拨设置")
    private Map<String, String> stockAllotConfig;
}
