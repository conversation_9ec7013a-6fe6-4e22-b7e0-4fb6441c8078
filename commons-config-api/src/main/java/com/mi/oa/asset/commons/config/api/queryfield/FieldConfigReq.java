package com.mi.oa.asset.commons.config.api.queryfield;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/9 14:59
 */

@Data
public class FieldConfigReq implements Serializable {
    private static final long serialVersionUID = 4786950314269136668L;

    @HttpApiDocClassDefine(value = "方案主键")
    private Integer fieldId;

    @HttpApiDocClassDefine(value = "管理线")
    private String manageLine;

    @HttpApiDocClassDefine(value = "功能ID")
    @NotBlank(message = "功能ID不能为空")
    private String funId;

    @HttpApiDocClassDefine(value = "字段配置信息")
    @NotNull(message = "字段配置信息不能为空")
    private List<FieldConfigInfo> fieldConfig;

}
