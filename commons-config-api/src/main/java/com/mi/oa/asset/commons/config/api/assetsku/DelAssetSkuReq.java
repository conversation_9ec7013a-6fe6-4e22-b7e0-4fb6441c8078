package com.mi.oa.asset.commons.config.api.assetsku;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DelAssetSkuReq implements Serializable {

    private static final long serialVersionUID = -1572356270977917121L;

    @HttpApiDocClassDefine(value = "id列表")
    private List<Integer> ids;
    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;


}
