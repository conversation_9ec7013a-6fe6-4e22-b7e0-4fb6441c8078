package com.mi.oa.asset.commons.config.api.businessline;

import lombok.Data;

import java.util.Map;

@Data
public class BusinessLineMgRes {
    private Integer id;
    private String businessLineName;
    private String businessLineNameEn;
    private String businessLine;
    private String fatherId;
    private String manageLineCode;
    private String manageLineName;
    private String isNewLine;
    private String isEffective;
    private String isModifyCompany;
    /**
     * assetReceipt: "assetReceiptReceived,assetReceiptAcceptance"
     */
    private Map<String, Object> config;

}
