package com.mi.oa.asset.commons.config.api.assetcategory;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/9/12 15:06
 */

@Data
public class AssetCategoryX5Res implements Serializable {

    private static final long serialVersionUID = 5549676477395293972L;

    @HttpApiDocClassDefine(value = "分类id")
    private Integer cateId;

    @HttpApiDocClassDefine(value = "分类编码")
    private String cateCode;

    @HttpApiDocClassDefine(value = "分类名称")
    private String cateName;

    @HttpApiDocClassDefine(value = "分类名称全")
    private String fullCateName;

    @HttpApiDocClassDefine(value = "上级分类编码")
    private String parentCateCode;

    @HttpApiDocClassDefine(value = "分类路径")
    private String catePath;

    @HttpApiDocClassDefine(value = "排序")
    private Integer sort;

    @HttpApiDocClassDefine(value = "级别")
    private Integer level;

    @HttpApiDocClassDefine(value = "sap资产分类编码")
    private String sapCateCode;

    @HttpApiDocClassDefine(value = "sap资产分类名称")
    private String sapCateName;


}
