package com.mi.oa.asset.commons.config.api.assetshare;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025-01-07 11:06
 */
@Data
public class CustomShareListReq {
    @HttpApiDocClassDefine(value = "主键", description = "不为空时更新")
    private Integer id;

    @HttpApiDocClassDefine(value = "资产台账主键", description = "资产台账主键")
    @NotNull(message = "资产信息不能为空")
    private Integer assetId;

    @HttpApiDocClassDefine(value = "台账数量", description = "台账数量")
    @NotNull(message = "台账数量不能为空")
    private Integer assetQuantity;

    @HttpApiDocClassDefine(value = "共享数量", description = "共享数量")
    @NotNull(message = "共享数量不能为空")
    private Integer shareQuantity;
}
