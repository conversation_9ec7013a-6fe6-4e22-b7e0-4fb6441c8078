package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FieldQueryReq extends PageRequest {
    @HttpApiDocClassDefine(value = "功能编码/表编码", required = false, description = "功能编码/表编码")
    private String funcCode;

    @HttpApiDocClassDefine(value = "表编码", required = false, description = "功能编码/表编码")
    private String tableCode;

    @HttpApiDocClassDefine(value = "管理类型", required = false, description = "管理类型")
    private Integer manageType;

    @HttpApiDocClassDefine(value = "业务线", required = false, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "用途：1-功能列字段，2-表单列字段，0-表字段", required = false, description = "1-功能列字段，2-表单列字段，0-表字段")
    private Integer useWay;

    @HttpApiDocClassDefine(value = "模糊查询key", required = false, description = "模糊查询key")
    private String key;

    @HttpApiDocClassDefine(value = "id", required = false, description = "id")
    private Integer id;
}
