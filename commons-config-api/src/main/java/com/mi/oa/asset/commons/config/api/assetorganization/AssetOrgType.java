package com.mi.oa.asset.commons.config.api.assetorganization;

import com.mi.oa.asset.common.enums.EAMConstants;
import com.mi.oa.asset.common.enums.IEnum;
import com.mi.oa.asset.common.model.CodeNameProperty;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/15 16:35
 */

@Getter
public enum AssetOrgType implements IEnum<AssetOrgType> {

    DEPARTMENT("department", "部门"),
    OFFICE("office", "职场"),
    LAB("lab", "实验室"),
    OEM("oem", "代工厂"),
    ;

    private final String code;

    private final String desc;

    AssetOrgType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<CodeNameProperty> getAllType(String language) {
        AssetOrgType[] values = AssetOrgType.values();
        List<CodeNameProperty> list = new ArrayList<>(values.length);
        for (AssetOrgType assetOrgType : values) {
            if (EAMConstants.CHINESE.equals(language)) {
                list.add(new CodeNameProperty(assetOrgType.getCode(), assetOrgType.getDesc()));
            } else {
                list.add(new CodeNameProperty(assetOrgType.getCode(), assetOrgType.getCode()));
            }
        }
        return list;
    }
}
