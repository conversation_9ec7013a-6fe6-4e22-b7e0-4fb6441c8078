package com.mi.oa.asset.commons.config.api.menuresource;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.*;
import java.util.List;

/**
 * 菜单功能
 *
 * <AUTHOR>
 * @date 2023/9/15 18:38
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MenuRes implements Serializable {

    private static final long serialVersionUID = 8301374032692413584L;

    @HttpApiDocClassDefine(value = "菜单编码")
    private String resourceCode;

    @HttpApiDocClassDefine(value = "菜单名称")
    private String resourceName;

    @HttpApiDocClassDefine(value = "菜单描述")
    private String description;

    @HttpApiDocClassDefine(value = "菜单描述json")
    private Object desc;

    @HttpApiDocClassDefine(value = "菜单路由")
    private String menuRoute;

    @HttpApiDocClassDefine(value = "菜单名称")
    private String menuTitle;

    @HttpApiDocClassDefine(value = "菜单图标")
    private String iconUrl;

    @HttpApiDocClassDefine(value = "是否展示")
    private Boolean isShow;

    @HttpApiDocClassDefine(value = "父菜单编码")
    private String parentCode;

    @HttpApiDocClassDefine(value = "敏感级别")
    private Integer sensitiveLevel;

    @HttpApiDocClassDefine(value = "维度编码")
    private String dimensionCode;

    @HttpApiDocClassDefine(value = "排序")
    private Integer sort;

    @HttpApiDocClassDefine(value = "子菜单功能")
    private List<MenuRes> children;

    @HttpApiDocClassDefine(value = "子菜单功能")
    private List<ResourceRes> resourceList;

    public MenuRes deepClone() throws Exception {
        // 序列化
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(bos);
        oos.writeObject(this);
        // 反序列化
        ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bis);
        return (MenuRes) ois.readObject();
    }

}
