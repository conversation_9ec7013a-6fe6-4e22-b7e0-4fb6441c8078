package com.mi.oa.asset.commons.config.api.address;


import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *  request 请求对象
 * <AUTHOR>
 * @date 2024-04-08 11:14:07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssetReceiveAddressRes implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 
	 */
	@HttpApiDocClassDefine(value = "主键ID")
	private Long id;
	/**
	 * 是否是默认收货地址
	 */
	@HttpApiDocClassDefine(value = "是否是默认收货地址")
	private Boolean isDefault;
	/**
	 * 收货人姓名
	 */
	@HttpApiDocClassDefine(value = "收货人姓名")
	private String receiveName;
	/**
	 * 收货人电话
	 */
	@HttpApiDocClassDefine(value = "收货人电话")
	private String receiveMobile;
	/**
	 * 收货人邮箱
	 */
	@HttpApiDocClassDefine(value = "收货人邮箱")
	private String receiveEmail;
	/**
	 * 省编码
	 */
	@HttpApiDocClassDefine(value = "省编码")
	private String provinceCode;
	/**
	 * 省名
	 */
	@HttpApiDocClassDefine(value = "省名")
	private String provinceName;
	/**
	 * 市编码
	 */
	@HttpApiDocClassDefine(value = "市编码")
	private String cityCode;
	/**
	 * 市名
	 */
	@HttpApiDocClassDefine(value = "市名")
	private String cityName;
	/**
	 * 区编码
	 */
	@HttpApiDocClassDefine(value = "区编码")
	private String areaCode;
	/**
	 * 区名
	 */
	@HttpApiDocClassDefine(value = "区名")
	private String areaName;
	/**
	 * 街道 编码
	 */
	@HttpApiDocClassDefine(value = "街道 编码")
	private String streetCode;
	/**
	 * 街道名
	 */
	@HttpApiDocClassDefine(value = "街道名")
	private String streetName;
	/**
	 * 详细地址
	 */
	@HttpApiDocClassDefine(value = "详细地址")
	private String detailAddress;
}
