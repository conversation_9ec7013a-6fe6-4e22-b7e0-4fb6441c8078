package com.mi.oa.asset.commons.config.api.regionconfig;

import java.util.List;

public interface RegionConfigProvider {

    /**
     * 获取地区列表（不包含国家绑定信息）
     * @return
     */
    List<RegionConfigRes> getRegionConfigList();

    /**
     * 获取单个地区和地区下的国家列表
     * @param regionConfigId
     * @return
     */
    EmployeeRegionCountryRes getRegionAndCountrys(Integer regionConfigId);

    /**
     *
     * 获取所有地区和地区下的国家列表
     * @return
     */
    List<EmployeeRegionCountryRes> getRegionAndCountrysList();

    void saveOrUpdate(RegionConfigReq req);

    void removeByIds(List<Integer> ids);

}
