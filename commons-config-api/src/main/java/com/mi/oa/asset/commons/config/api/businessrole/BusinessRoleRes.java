package com.mi.oa.asset.commons.config.api.businessrole;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/01/15 19:42
 */

@Data
public class BusinessRoleRes {

    @HttpApiDocClassDefine(value = "角色编码")
    private String roleCode;

    @HttpApiDocClassDefine(value = "角色名称")
    private String roleName;

    @HttpApiDocClassDefine(value = "角色英文名称")
    private String roleNameEn;

    @HttpApiDocClassDefine(value = "角色描述")
    private String roleDesc;

    @HttpApiDocClassDefine(value = "角色英文描述")
    private String roleDescEn;

}
