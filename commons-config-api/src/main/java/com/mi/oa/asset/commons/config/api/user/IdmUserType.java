package com.mi.oa.asset.commons.config.api.user;

import com.mi.oa.asset.common.enums.IEnum;
import com.mi.oa.asset.excel.enums.ExcelEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/9/04 15:27
 */

@Getter
public enum IdmUserType implements IEnum<IdmUserType>, ExcelEnum<IdmUserType> {
    BLANK("", ""),
    EMPLOYEE("employee", "小米员工"),
    PARTNER("partner", "合作伙伴"),
    ;

    private final String code;

    private final String desc;

    IdmUserType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
