package com.mi.oa.asset.commons.config.api.assetshare;

import com.mi.oa.asset.common.model.SaveAttach;
import com.mi.oa.asset.commons.config.api.common.ComplexRange;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-01-06 17:05
 */
@Data
public class SaveShareRecordReq {
    @HttpApiDocClassDefine(value = "共享主键", description = "不为空时更新")
    private Integer id;

    @HttpApiDocClassDefine(value = "业务线", required = true, description = "业务线")
    @NotBlank(message = "业务线不能为空")
    private String businessLine;

    @HttpApiDocClassDefine(value = "共享基础信息", description = "共享基础信息")
    @Valid
    @NotNull(message = "基础信息不能为空")
    private ShareRecordBaseReq baseInfo;

    @HttpApiDocClassDefine(value = "附件", description = "不能超过上传最多20个附件大小")
    private SaveAttach attachInfo = new SaveAttach();

    @HttpApiDocClassDefine(value = "更多范围条件", description = "更多范围条件")
    @Valid
    private List<ComplexRange> complexRanges;

    @HttpApiDocClassDefine(value = "自定义共享清单", description = "自定义共享清单")
    @Valid
    private List<CustomShareListReq> customShareLists;
}
