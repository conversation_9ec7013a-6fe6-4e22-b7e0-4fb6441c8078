package com.mi.oa.asset.commons.config.api.measurement;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

@Data
public class MeasurementUnitRes implements Serializable {

    private static final long serialVersionUID = 3927308301930690442L;
    @HttpApiDocClassDefine(value = "主键")
    private Integer id;

    @HttpApiDocClassDefine(value = "计量单元编码")
    private String measureCode;

    @HttpApiDocClassDefine(value = "计量单元名称")
    private String measureName;

    @HttpApiDocClassDefine(value = "计量单元名称-英文")
    private String measureNameEn;

    @HttpApiDocClassDefine(value = "技术代码")
    private String techCode;

    @HttpApiDocClassDefine(value = "计量单位唯一值")
    private String muId;

    @HttpApiDocClassDefine(value = "单元类型码")
    private String unitTypeCode;

    @HttpApiDocClassDefine(value = "单元类型名")
    private String unitTypeName;

    @HttpApiDocClassDefine(value = "来源")
    private String dataSource;

    @HttpApiDocClassDefine(value = "禁用")
    private Integer disabled;


}
