package com.mi.oa.asset.commons.config.api.position;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/10/16 11:14
 */

@Data
public class PositionRes implements Serializable {

    private static final long serialVersionUID = -2892806370915939309L;

    @HttpApiDocClassDefine(value = "位置id")
    private Integer positionId;

    @HttpApiDocClassDefine(value = "位置编码")
    private String positionCode;

    @HttpApiDocClassDefine(value = "位置名称")
    private String positionName;

    @HttpApiDocClassDefine(value = "英文名称")
    private String positionNameEn;

    @HttpApiDocClassDefine(value = "位置类型")
    private String positionType;

    @HttpApiDocClassDefine(value = "位置名称全")
    private String fullPositionName;

    @HttpApiDocClassDefine(value = "上级编码")
    private String parentCode;

    @HttpApiDocClassDefine(value = "上级名称")
    private String parentName;

    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "禁用")
    private Boolean disabled;

    @HttpApiDocClassDefine(value = "外部系统编码")
    private String outSysCode;

    @HttpApiDocClassDefine(value = "备注")
    private String remark;

    @HttpApiDocClassDefine(value = "创建来源")
    private String dataSource;

    @HttpApiDocClassDefine(value = "创建时间")
    private Date createTime;
}