package com.mi.oa.asset.commons.config.api.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mi.oa.infra.oaucf.core.validator.UpdateGroup;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 项目配置表 查询条件
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class SaveProjectCfgReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @HttpApiDocClassDefine(value = "ID")
    @NotNull(groups = {UpdateGroup.class}, message = "ID不能为空")
    private Integer id;
    /**
     * 执行状态 待创建wait_create 已创建created
     */
    @HttpApiDocClassDefine(value = "执行状态 待创建wait_create 已创建created")
    @NotBlank(message = "执行状态不能为空")
    private String recordStatus;
    /**
     * 业务线
     */
    @HttpApiDocClassDefine(value = "业务线")
    @NotBlank(message = "业务线不能为空")
    private String businessLine;
    /**
     * 项目编号
     */
    @HttpApiDocClassDefine(value = "项目编号")
    @NotBlank(message = "项目编号不能为空")
    private String projectCode;
    /**
     * 项目名称
     */
    @HttpApiDocClassDefine(value = "项目名称")
    @NotBlank(message = "项目名称不能为空")
    private String projectName;
    /**
     * 父项目编号
     */
    @HttpApiDocClassDefine(value = "父项目编号")
    private String parentProjectCode;
    /**
     * PM账户
     */
    @HttpApiDocClassDefine(value = "PM账户")
    @NotBlank(message = "PM账户不能为空")
    private String pmUserCode;
    /**
     * PM工号
     */
    @HttpApiDocClassDefine(value = "PM工号")
    @NotBlank(message = "PM工号不能为空")
    private String pmEmpCode;
    /**
     * PM
     */
    @HttpApiDocClassDefine(value = "PM")
    @NotBlank(message = "PM不能为空")
    private String pmUserName;
    /**
     * 是否JIS之前机型
     */
    @HttpApiDocClassDefine(value = "是否JIS之前机型  0 否 1 是")
    private Integer beforeJis;
    /**
     * 项目阶段
     */
    @HttpApiDocClassDefine(value = "项目阶段")
    @NotBlank(message = "项目阶段不能为空")
    private String projectPhase;
    /**
     * 项目类型
     */
    @HttpApiDocClassDefine(value = "项目类型")
    private String projectType;
    /**
     * 预研编号
     */
    @HttpApiDocClassDefine(value = "预研编号")
    private String preProjectCode;
    /**
     * 项目等级
     */
    @HttpApiDocClassDefine(value = "项目等级")
    @NotBlank(message = "项目等级不能为空")
    private String projectLevel;
    /**
     * 产品类别
     */
    @HttpApiDocClassDefine(value = "产品类别")
    @NotBlank(message = "产品类别不能为空")
    private String productType;
    /**
     * 试产工厂
     */
    @HttpApiDocClassDefine(value = "试产工厂")
    private String factoryName;
    /**
     * 试产工厂编码
     */
    @HttpApiDocClassDefine(value = "试产工厂编码")
    private String factoryCode;
    /**
     * 试产日期
     */
    @HttpApiDocClassDefine(value = "试产日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date trialDate;
    /**
     * 是否上市
     */
    @HttpApiDocClassDefine(value = "是否上市")
    @NotNull(message = "是否上市不能为空")
    private Integer isSale;
    /**
     * 上市日期
     */
    @HttpApiDocClassDefine(value = "上市日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "上市日期不能为空")
    private Date saleDate;

    @HttpApiDocClassDefine(value = "国家")
    private String country;
    /**
     * 项目信息来源 R平台 手动创建 UPC平台
     */
    @HttpApiDocClassDefine(value = "项目信息来源 R平台-R 手动创建-manual  UPC平台-UPC")
    private String source;
    /**
     * 是否生效，是-1，否-0
     */
    @HttpApiDocClassDefine(value = "是否生效，是-1，否-0")
    private Integer enabled;

    @HttpApiDocClassDefine(value = "创建人用户名", description = "创建人用户名")
    private String createUser;

    @HttpApiDocClassDefine(value = "创建人姓名", description = "创建人姓名")
    private String createUserName;

    @HttpApiDocClassDefine(value = "更新人用户名", description = "更新人用户名")
    private String updateUser;

    @HttpApiDocClassDefine(value = "更新人姓名", description = "更新人姓名")
    private String updateUserName;

    @HttpApiDocClassDefine(value = "创建时间", description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @HttpApiDocClassDefine(value = "更新时间", description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}