package com.mi.oa.asset.commons.config.api.function;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FuncFieldRes implements Serializable {
    @HttpApiDocClassDefine(value = "id", required = false, description = "id")
    private Integer id;

    @HttpApiDocClassDefine(value = "字段编码", required = false, description = "字段编码")
    private String code;

    @HttpApiDocClassDefine(value = "字段名称", required = false, description = "字段名称")
    private String name;

    @HttpApiDocClassDefine(value = "功能id", required = false, description = "功能id")
    private String funcId;

    @HttpApiDocClassDefine(value = "功能编码", required = false, description = "功能编码")
    private String funcCode;

    @HttpApiDocClassDefine(value = "英文编码", required = false, description = "英文编码")
    private String enCode;

    @HttpApiDocClassDefine(value = "展示类型", required = false, description = "展示类型")
    private String showType;

    @HttpApiDocClassDefine(value = "是否搜索", required = false, description = "是否搜索")
    private Integer search;

    @HttpApiDocClassDefine(value = "中文宽度", required = false, description = "中文宽度")
    private String chWide;

    @HttpApiDocClassDefine(value = "英文宽度", required = false, description = "英文宽度")
    private String enWide;

    @HttpApiDocClassDefine(value = "业务线", required = false, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "创建人", required = false, description = "创建人")
    private String createUserName;

    @HttpApiDocClassDefine(value = "创建人账号", required = false, description = "创建人账号")
    private String createUser;

    @HttpApiDocClassDefine(value = "创建人时间", required = false, description = "创建人时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    @HttpApiDocClassDefine(value = "更新人", required = false, description = "更新人")
    private String updateUser;

    @HttpApiDocClassDefine(value = "更新人姓名", required = false, description = "更新人姓名")
    private String updateUserName;

    @HttpApiDocClassDefine(value = "查看页面展示", required = false, description = "查看页面展示")
    private String view;

    @HttpApiDocClassDefine(value = "编辑页面属性", required = false, description = "编辑页面属性")
    private String edit;

    @HttpApiDocClassDefine(value = "新增页面属性", required = false, description = "新增页面属性")
    private String addType;

    @HttpApiDocClassDefine(value = "用途", required = false, description = "用途")
    private Integer useWay;

    @HttpApiDocClassDefine(value = "数据类型", required = false, description = "数据类型")
    private String dataType;

    @HttpApiDocClassDefine(value = "列控制", required = false, description = "列控制")
    private String colControl;

    @HttpApiDocClassDefine(value = "字典编码", required = false, description = "字典编码")
    private String controlCode;

    @HttpApiDocClassDefine(value = "数据样式", required = false, description = "数据样式")
    private String formatId;

    @HttpApiDocClassDefine(value = "是否已存在", required = false, description = "是否已存在")
    private boolean isExist;

    @HttpApiDocClassDefine(value = "分组", required = false, description = "分组")
    private String groupCode;

    @HttpApiDocClassDefine(value = "默认值", required = false, description = "默认值")
    private String defaultValue;

    @HttpApiDocClassDefine(value = "数据范围", required = false, description = "数据范围")
    private String dataRange;

    @HttpApiDocClassDefine(value = "字典翻译集合", required = false, description = "字典翻译集合")
    private List<DictRes> dictList;

    @HttpApiDocClassDefine(value = "表达式", required = false, description = "表达式")
    private String expression;
}
