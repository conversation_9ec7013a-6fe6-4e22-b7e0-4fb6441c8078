package com.mi.oa.asset.commons.config.api.user;

import com.mi.oa.asset.common.enums.YesNoEnum;
import com.mi.oa.asset.excel.utils.JacksonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户基本信息
 *
 * <AUTHOR>
 * @date 2023/9/11 11:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UserBaseInfoRes implements Serializable {

    private static final long serialVersionUID = -7757778694775890987L;

    private String uid;
    private String personId;
    private String originalPersonId;
    private String name;
    private String firstName;
    private String lastName;
    private String displayName;
    private String namePinyin;
    private String type;
    private String userName;
    private String idNumberHash;
    private String emailPrefix;
    private String email;
    private String sex;
    private String birthdate;
    private String country;
    private String deptId;
    private String deptDescr;
    private String fullDeptDescr;
    private String company;
    private String companyDescr;
    private String companyDescrEn;
    private String jobCode;
    private String jobDescr;
    private String positionCode;
    private String positionDescr;
    private String deptCostCt;
    private String deptCostCtDescr;
    private String costCt;
    private String costCtDescr;
    private String hrStatus;
    private String locationCode;
    private String officePark;
    private String officeCity;
    private String officeCityCode;
    private String officeLocation;
    private String officeLocationCode;
    private String officeStation;
    private String defaultLanguage;
    private String miliao;
    private String taobao;
    private String headUrl;
    private String thumbnailUrl;
    private String fullDeptName;
    private String fullDeptNameEn;

    public List<String> getAllDeptCode() {
        if (StringUtils.isNotBlank(fullDeptDescr)) {
            List<UserDeptRes> userDeptRes = JacksonUtils.json2List(fullDeptDescr, UserDeptRes.class);
            return userDeptRes.stream().filter(dept -> !YesNoEnum.NO.getCode().equals(dept.getLevel()))
                    .map(UserDeptRes::getDeptId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
