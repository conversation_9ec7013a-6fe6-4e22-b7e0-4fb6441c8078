package com.mi.oa.asset.commons.config.api.common;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/10/26 17:15
 */

@Data
@Builder
public class ProviderRes implements Serializable {

    private static final long serialVersionUID = -2679567318408704805L;

    @HttpApiDocClassDefine(value = "供应商编码")
    private String providerCode;

    @HttpApiDocClassDefine(value = "供应商名称")
    private String providerName;
}
