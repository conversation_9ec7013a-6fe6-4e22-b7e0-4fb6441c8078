package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FuncDataPermissionReq implements Serializable {

    @HttpApiDocClassDefine(value = "业务线", required = false, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "功能编码", required = false, description = "功能编码")
    @NonNull
    private String funcCode;

    @HttpApiDocClassDefine(value = "快速授权", required = false, description = "快速授权")
    private FuncFastAuthReq funcFastAuthReq;

    @HttpApiDocClassDefine(value = "拓展sql角色范围(新增)", required = false, description = "拓展sql角色范围(新增)")
    private List<FuncExtSqlReq> addExtList;

    @HttpApiDocClassDefine(value = "拓展sql角色范围(删除)", required = false, description = "拓展sql角色范围(删除)")
    private List<FuncExtSqlReq> delExtList;

    @HttpApiDocClassDefine(value = "资源维度权限(新增)", required = false, description = "资源维度权限(新增)")
    private List<FuncResourceReq> addResourceList;

    @HttpApiDocClassDefine(value = "资源维度权限(删除)", required = false, description = "资源维度权限(删除)")
    private List<FuncResourceReq> delResourceList;

    @HttpApiDocClassDefine(value = "例外角色角色", required = false, description = "例外角色角色")
    private List<FunctionRoleRes> extRoleList;
}
