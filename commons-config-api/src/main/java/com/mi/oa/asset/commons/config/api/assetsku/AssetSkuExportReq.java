package com.mi.oa.asset.commons.config.api.assetsku;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/30 10:27
 */
@Data
public class AssetSkuExportReq {

    @HttpApiDocClassDefine(value = "业务线", description = "业务线")
    private List<String> businessLine;
    @HttpApiDocClassDefine(value = "分类ID", description = "分类ID")
    private Integer cateId;
    @HttpApiDocClassDefine(value = "关键字查询", description = "关键字查询")
    private String keyword;
    private String language;
}
