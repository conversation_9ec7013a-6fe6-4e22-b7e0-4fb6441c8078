package com.mi.oa.asset.commons.config.api.common;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommonListReq {
    @HttpApiDocClassDefine(value = "id列表")
    private List<Integer> ids;
    @HttpApiDocClassDefine(value = "名称列表")
    private List<String> names;
    @HttpApiDocClassDefine(value = "key列表")
    private List<String> keys;
}
