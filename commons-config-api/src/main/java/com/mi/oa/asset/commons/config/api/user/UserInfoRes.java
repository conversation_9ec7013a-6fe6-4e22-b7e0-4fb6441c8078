package com.mi.oa.asset.commons.config.api.user;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/6 11:31
 */

@Data
public class UserInfoRes implements Serializable {

    private static final long serialVersionUID = 4511091227585854030L;

    @HttpApiDocClassDefine(value = "uid", description = "uid")
    private String uid;

    @HttpApiDocClassDefine(value = "员工工号", description = "员工工号")
    private String personId;

    @HttpApiDocClassDefine(value = "员工展示名", description = "员工展示名")
    private String displayName;

    @HttpApiDocClassDefine(value = "姓名", description = "姓名")
    private String name;

    @HttpApiDocClassDefine(value = "账号", description = "账号")
    private String userName;

    @HttpApiDocClassDefine(value = "邮箱", description = "邮箱")
    private String email;

    @HttpApiDocClassDefine(value = "头像", description = "头像")
    private String headUrl;

    @HttpApiDocClassDefine(value = "部门id", description = "部门编码")
    private String deptId;

    @HttpApiDocClassDefine(value = "部门描述", description = "部门名称")
    private String deptDesc;

    @HttpApiDocClassDefine(value = "部门全路径", description = "部门名称全路径")
    private String fullDeptDesc;

    @HttpApiDocClassDefine(value = "成本中心")
    private String costCenter;

    @HttpApiDocClassDefine(value = "公司编码", description = "成本中心取1-5位，公司编码")
    private String company;

    @HttpApiDocClassDefine(value = "公司名称", description = "公司名称")
    private String companyDescr;

}
