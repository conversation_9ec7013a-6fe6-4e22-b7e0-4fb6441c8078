package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictBusinessLineQueryReq extends PageRequest {

    @HttpApiDocClassDefine(value = "字典key", required = false, description = "字典key")
    private String code;

    @HttpApiDocClassDefine(value = "业务线", required = false, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "管理线", required = false, description = "管理线")
    private String manageLine;

    @HttpApiDocClassDefine(value = "功能编码", required = false, description = "功能编码")
    private String funcCode;
}
