package com.mi.oa.asset.commons.config.api.assetorganization;

import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleInfoReq;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/15 16:38
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveAssetOrgUnitReq implements Serializable {

    private static final long serialVersionUID = -1932035085054521869L;

    @HttpApiDocClassDefine(value = "组织单元id", required = false, description = "更新时必传")
    private Integer orgId;

    @HttpApiDocClassDefine(value = "组织单元编码", required = true, description = "组织单元编码")
    private String orgCode;

    @HttpApiDocClassDefine(value = "组织单元名称", required = true, description = "组织单元名称")
    private String orgName;

    @HttpApiDocClassDefine(value = "组织单元英文名称", description = "组织单元英文名称")
    private String orgNameEn;

    @HttpApiDocClassDefine(value = "别名", required = false, description = "别名")
    private String aliasName;

    @HttpApiDocClassDefine(value = "编码全路径", required = true, description = "编码全路径，-分隔")
    private String orgCodePath;

    @HttpApiDocClassDefine(value = "名称全路径", required = true, description = "名称全路径，-分隔")
    private String orgNamePath;

    @HttpApiDocClassDefine(value = "上级编码", required = true, description = "上级编码")
    private String parentCode;

    @HttpApiDocClassDefine(value = "上级部门名称", required = true, description = "上级部门名称")
    private String parentName;

    @HttpApiDocClassDefine(value = "业务线", required = true, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "是否资产管理组织", required = false, description = "是否资产管理组织")
    private Boolean isAssetManageOrg;

    @HttpApiDocClassDefine(value = "是否资产使用组织", required = false, description = "是否资产使用组织")
    private Boolean isAssetUseOrg;

    @HttpApiDocClassDefine(value = "组织类型", required = false, description = "组织类型")
    private String orgType;

    @HttpApiDocClassDefine(value = "委托记账主体编码", required = false, description = "委托记账主体编码")
    private String companyCode;

    @HttpApiDocClassDefine(value = "委托记账主体名称", required = false, description = "委托记账主体名称")
    private String companyName;

    @HttpApiDocClassDefine(value = "委托记账成本中心", required = false, description = "委托记账成本中心")
    private String costCenter;

    @HttpApiDocClassDefine(value = "地址", required = false, description = "地址")
    private String address;

    @HttpApiDocClassDefine(value = "业务角色列表", required = false, description = "业务角色列表")
    private List<BusinessRoleInfoReq> roleInfoList;
}
