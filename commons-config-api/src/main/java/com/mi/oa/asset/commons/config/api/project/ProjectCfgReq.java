package com.mi.oa.asset.commons.config.api.project;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目配置表 查询条件
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class ProjectCfgReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @HttpApiDocClassDefine(value = "ID")
    private Integer id;
}