package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class FuncFastAuthReq implements Serializable {
    @HttpApiDocClassDefine(value = "本人看本人", required = false, description = "本人看本人")
    private Boolean viewSelf;

    @HttpApiDocClassDefine(value = "本人修改本人", required = false, description = "本人修改本人")
    private Boolean editSelf;

    @HttpApiDocClassDefine(value = "本人修改本人", required = false, description = "本人修改本人")
    private Boolean deleteSelf;

    @HttpApiDocClassDefine(value = "本人角色范围", required = false, description = "本人角色范围")
    private List<String> currentUserRole;

    @HttpApiDocClassDefine(value = "本部门可见", required = false, description = "本部门可见")
    private Boolean currentDept;

    @HttpApiDocClassDefine(value = "监管部门可见", required = false, description = "监管部门可见")
    private Boolean manageDept;

    @HttpApiDocClassDefine(value = "本部门可见角色范围", required = false, description = "本部门可见角色范围")
    private List<String> currentDeptRole;

    @HttpApiDocClassDefine(value = "监管部门可见角色范围", required = false, description = "监管部门可见角色范围")
    private List<String> manageDeptRole;
}
