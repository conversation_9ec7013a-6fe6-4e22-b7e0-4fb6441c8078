package com.mi.oa.asset.commons.config.api.countrycurrency;

import java.util.List;

public interface CountryCurrencyProvider {

    List<CountryCurrencyRes> getCountryCurrencyList();

    CountryCurrencyRes getById(Integer countryCurrencyId);

    Integer saveOrUpdate(CountryCurrencyReq req);

    void removeByIds(List<Integer> ids);

    List<CountryCurrencyRes> getByCountryId(Integer countryId);

    List<CountryCurrencyRes> getByCountryCode(String countryCode);
}
