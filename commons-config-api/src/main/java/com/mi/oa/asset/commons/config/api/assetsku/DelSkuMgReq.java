package com.mi.oa.asset.commons.config.api.assetsku;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/8/16 17:28
 */
@Data
public class DelSkuMgReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @HttpApiDocClassDefine(value = "物料id")
    @NotNull(message = "物料id不能为空")
    private Integer skuId;
    @HttpApiDocClassDefine(value = "管理信息id")
    @NotNull(message = "管理信息id不能为空")
    private Integer manageId;
}
