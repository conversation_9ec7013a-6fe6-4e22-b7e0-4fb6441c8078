package com.mi.oa.asset.commons.config.api.user;

import com.mi.oa.asset.common.model.User;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/11 11:16
 **/
public interface UserInfoService {

    /**
     * 根据uid查询用户信息
     *
     * @param uid
     * @return
     */
    UserBaseInfoRes getUserInfoByUid(String uid);

    /**
     * 根据账号（邮箱前缀）查询用户信息
     *
     * @param userName
     * @return
     */
    UserBaseInfoRes getUserInfoByUserName(String userName);

    List<User> getUsersByUserName(List<String> userNames);

    User getUserByUserName(String userName);

    List<UserBaseInfoRes> getUserByLikeUserName(String userName, String limit);

    List<UserInfoRes> fuzzySearchUserInfo(String userName, String limit);

    List<UserInfoRes> getUsersByUserNames(List<String> userNames);

    List<UserBaseInfoRes> getUserBaseInfosByUserNames(List<String> userNames);

    List<UserInfoRes> getAllotLikeUserInfo(String businessLine, String userName, String limit);

    EmployeeInfoRes getEmpInfoByUserName(String userName);

    UserMobileRes getMobileByUserName(String userName);
}
