package com.mi.oa.asset.commons.config.api.countrybusinessLine;


import java.util.List;

public interface CountryBusinessLineProvider {

    List<CountryBusinessLineRes> getCountryRegionList();

    CountryBusinessLineRes getById(Integer countryRegionId);

    Integer saveOrUpdate(CountryBusinessLineReq req);

    void removeByIds(List<Integer> ids);

    List<CountryBusinessLineRes> getByBusinessLine(String businessLine);

    List<CountryBusinessLineRes> getCountryId(Integer countryId);
}
