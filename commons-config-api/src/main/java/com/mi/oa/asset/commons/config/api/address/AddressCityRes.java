package com.mi.oa.asset.commons.config.api.address;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 城市地址
 * @Date 2024/7/9 12:30
 **/
@Data
public class AddressCityRes implements Serializable {
    private static final long serialVersionUID = 3482434479608139447L;

    @HttpApiDocClassDefine(value = "国家id")
    private String countryId;

    @HttpApiDocClassDefine(value = "国家名称")
    private String countryName;

    @HttpApiDocClassDefine(value = "省份id")
    private String provinceId;

    @HttpApiDocClassDefine(value = "省份名称")
    private String provinceName;

    @HttpApiDocClassDefine(value = "城市id")
    private String cityId;

    @HttpApiDocClassDefine(value = "城市名称")
    private String cityName;

    @HttpApiDocClassDefine(value = "区域编码")
    private String areaCode;
}
