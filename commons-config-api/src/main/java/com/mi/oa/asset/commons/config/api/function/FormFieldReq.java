package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FormFieldReq implements Serializable {
    @HttpApiDocClassDefine(value = "功能编码", required = false, description = "功能编码")
    private String funcCode;

    @HttpApiDocClassDefine(value = "管理线", required = false, description = "业务线")
    private String manageLine;

    @HttpApiDocClassDefine(value = "业务线", required = false, description = "业务线")
    private String businessLine;


}
