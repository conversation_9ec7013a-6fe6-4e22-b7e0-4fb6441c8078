package com.mi.oa.asset.commons.config.api.common;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/11 15:42
 */

@Data
@Builder
public class PurchaseCatalogRes implements Serializable {

    @HttpApiDocClassDefine(value = "采购目录编码")
    private String catalogCode;

    @HttpApiDocClassDefine(value = "采购目录名称")
    private String catalogName;

    @HttpApiDocClassDefine(value = "级别")
    private Integer level;

    @HttpApiDocClassDefine(value = "上级编码")
    private String parentCode;

    @HttpApiDocClassDefine(value = "sap分类编码")
    private String sapCateCode;

    @HttpApiDocClassDefine(value = "sap分类名称")
    private String sapCateName;

    @HttpApiDocClassDefine(value = "使用年限")
    private int useYear;

    @HttpApiDocClassDefine(value = "下级列表")
    private List<PurchaseCatalogRes> subList;
}
