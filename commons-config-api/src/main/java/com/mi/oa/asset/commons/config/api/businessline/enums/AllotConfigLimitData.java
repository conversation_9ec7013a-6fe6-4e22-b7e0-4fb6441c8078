package com.mi.oa.asset.commons.config.api.businessline.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/16 21:16
 */

@Getter
public enum AllotConfigLimitData {

    ROLE_DEPT("role_dept", "业务角色所属部门"),
    EMP_DEPT("emp_dept", "员工所属部门"),
    USE_MANAGE_DEPT("use_manage_dept", "使用部门所属管理部门"),
    ;

    private final String code;

    private final String desc;

    AllotConfigLimitData(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // getByCode方法
    public static AllotConfigLimitData getByCode(String code) {
        for (AllotConfigLimitData value : AllotConfigLimitData.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
