package com.mi.oa.asset.commons.config.api.menuresource;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 业务线
 *
 * <AUTHOR>
 * @date 2023/10/12 11:26
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MenuBusinessLineRes implements Serializable {

    private static final long serialVersionUID = 3266830408631083074L;
    /**
     * 业务线（管理线）
     */
    private String businessCode;

    /**
     * 子业务线编码
     */
    private List<Children> children;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Children implements Serializable {

        private static final long serialVersionUID = -5399632475691717725L;

        private String id;

        private String title;
    }
}
