package com.mi.oa.asset.commons.config.api.address;


import com.mi.oa.asset.common.model.AdsCityDTO;
import com.mi.oa.asset.common.model.AdsDTO;
import com.mi.oa.asset.commons.config.api.myfunctions.Country;

import java.util.List;

/**
 *  dubbo接口
 * <AUTHOR>
 * @date 2024-04-08 11:14:07
 */
public interface AssetReceiveAddressProvider {
    /**
     * 行政区域列表
     * @param type  COUNTRY(1, "country"),PROVINCE(2, "province"),CITY(3, "city"),DISTRICT(4, "district"),STREET(5, "street")
     * @param parentId
     * @return
     */
    List<AdsDTO> qryAdsList(String type, Integer parentId);

    /**
     * 解析地址
     * @param address
     * @return
     */
    Object parseAddress(String address);

    List<AssetReceiveAddressRes> list(AssetReceiveAddressReq request);

    AssetReceiveAddressRes getById(Long id);

    Long saveOrUpdateReceiveAddress(AssetReceiveAddressReq request);

    void removeByIds(List<Long> idList);

    AssetReceiveAddressRes getDefault(String userName);

    Boolean setDefault(Long id);

    List<AddressCityRes> getCityByName(String cityName);

    List<AdsDTO> getCountry();

    List<Country> getCountryV1();
}

