package com.mi.oa.asset.commons.config.api.attach;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/9 16:48
 * @description 批量查询附件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchAttachRes implements Serializable {
    private static final long serialVersionUID = -2617084807235638439L;
    private List<AttachInfoRes> attachInfoList;
    private Integer recordId;
}
