package com.mi.oa.asset.commons.config.api.auth;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/21 18:44
 * @description
 */
@Data
public class QueryRemoteDataResourceReq {
    /**
     * 维度编码（必填）
     */
    private String dimensionCode;
    /**
     * 父数据权限编码（选填）
     * 针对全量返回：
     * parentCode字段为null
     */
    private String parentCode;
    /**
     * 资源名称，搜索条件，模糊匹配 （选填）
     * 平铺数据后端分页搜索，树状数据前端搜索
     */
    private String keyword;
    /**
     * 当前页  （选填）
     */
    private String pageNum;
    /**
     * 每页的个数  （选填）
     */
    private String pageSize;
    /**
     * 拓展查询条件
     */
    private Map<String,Object> ext;
}
