package com.mi.oa.asset.commons.config.api.regionconfig;

import com.mi.oa.asset.commons.config.api.countryconfig.CountryConfigRes;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 员工端区域和区域国家的返回数据结构
 */
@Data
public class EmployeeRegionCountryRes implements Serializable {

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 区域名称（英文）
     */
    private String regionEnglishName;

    /**
     * 排序序号
     */
    private Integer regionSortOrder;

    /**
     * 是否删除
     */
    private Integer isDeleted;


    private List<CountryConfigRes> countryConfigRes;

}
