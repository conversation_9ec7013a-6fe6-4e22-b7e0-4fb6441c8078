package com.mi.oa.asset.commons.config.api.assetshare;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025-01-10 14:36
 */
@Data
public class CustomShareListImportReq {
    @HttpApiDocClassDefine(value = "导入文件", required = true, description = "导入文件")
    @NotNull(message = "导入文件不能为空！")
    private MultipartFile file;
    @HttpApiDocClassDefine(value = "共享主键", required = true, description = "共享主键")
    @NotNull(message = "共享记录ID不能为空！")
    private Integer shareId;
}
