package com.mi.oa.asset.commons.config.api.queryfield;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/9 11:53
 */

@Data
public class QueryCondition implements Serializable {
    private static final long serialVersionUID = 1L;

    @HttpApiDocClassDefine(value = "数据来源编码, 如果单表查询可不传，多表必须传")
    private String dataSourceCode;

    @HttpApiDocClassDefine(value = "字段编码", required = true, description = "字段编码")
    @NotBlank(message = "字段编码不能为空")
    private String fieldCode;

    @HttpApiDocClassDefine(value = "查询规则", required = true, description = "查询规则")
    @NotBlank(message = "查询规则不能为空", groups = {QueryConfigValidateGroup.NotDefault.class})
    private String queryCond;

    @HttpApiDocClassDefine(value = "查询条件")
    @NotNull(message = "查询条件不能为空", groups = {QueryConfigValidateGroup.NotDefault.class})
    private List<Object> fieldValues;

    @HttpApiDocClassDefine(value = "参数数据类型")
    private String dataType;

    @HttpApiDocClassDefine(value = "字段类型")
    private String filedType;

    @HttpApiDocClassDefine(value = "连接关系")
    private String connRelate;
}
