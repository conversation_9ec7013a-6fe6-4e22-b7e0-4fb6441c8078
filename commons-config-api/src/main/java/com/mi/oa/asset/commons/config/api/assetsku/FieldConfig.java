package com.mi.oa.asset.commons.config.api.assetsku;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2024/7/30 11:09
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FieldConfig {

    @HttpApiDocClassDefine(value = "字段编码", description = "字段编码")
    @NotBlank(message = "字段编码不能为空！")
    private String fieldCode;

    @HttpApiDocClassDefine(value = "字段名称", description = "字段名称")
    @NotBlank(message = "字段名称不能为空！")
    private String fieldName;
}
