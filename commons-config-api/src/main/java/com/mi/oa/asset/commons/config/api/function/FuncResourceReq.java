package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FuncResourceReq implements Serializable {
    @HttpApiDocClassDefine(value = "行id", required = false, description = "行id")
    private Integer id;

    @HttpApiDocClassDefine(value = "资源维度", required = false, description = "资源维度")
    private String resource;

    @HttpApiDocClassDefine(value = "资源维度关联字段", required = false, description = "资源维度关联字段")
    private String extSql;

    @HttpApiDocClassDefine(value = "role", required = false, description = "role")
    private String role;

    @HttpApiDocClassDefine(value = "roleName", required = false, description = "roleName")
    private String roleName;
}
