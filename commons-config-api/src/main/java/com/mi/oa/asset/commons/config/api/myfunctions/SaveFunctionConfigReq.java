package com.mi.oa.asset.commons.config.api.myfunctions;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 保存首页功能配置
 * @Date 2025/4/28 15:37
 **/
@Data
public class SaveFunctionConfigReq implements Serializable {
    private static final long serialVersionUID = -7099503450161019760L;

    /**
     * 配置id
     */
    @HttpApiDocClassDefine(value = "配置id")
    private Integer configId;

    /**
     * 管理线 manage_line
     */
    @NotBlank(message = "管理线编码不能为空")
    @HttpApiDocClassDefine(value = "管理线编码")
    private String manageLine;

    /**
     * 管理线名称 manage_line_name
     */
    @NotBlank(message = "管理线名称不能为空")
    @HttpApiDocClassDefine(value = "管理线名称")
    private String manageLineName;

    /**
     * 功能名称 name
     */
    @NotBlank(message = "功能名称不能为空")
    @HttpApiDocClassDefine(value = "功能名称")
    private String name;

    /**
     * 功能英文名称 name_en
     */
    @HttpApiDocClassDefine(value = "功能名称（英文）")
    private String nameEn;

    /**
     * icon url icon_url
     */
    @HttpApiDocClassDefine(value = "icon url")
    @NotBlank(message = "icon url不能为空")
    @NotNull(message = "icon url不能为空")
    private String iconUrl;

    /**
     * 功能url function_url
     */
    @NotBlank(message = "功能url不能为空")
    @HttpApiDocClassDefine(value = "功能url")
    private String functionUrl;

    /**
     * 功能描述 description
     */
    @NotBlank(message = "功能描述不能为空")
    @HttpApiDocClassDefine(value = "功能描述")
    private String description;

    /**
     * 功能英文描述 description_en
     */
    @HttpApiDocClassDefine(value = "功能描述（英文）")
    private String descriptionEn;

    /**
     * 排序值 sort
     */
    @NotNull(message = "排序不能为空")
    @HttpApiDocClassDefine(value = "排序")
    private Integer sort;

    /**
     * 业务线 business_line
     */
    @HttpApiDocClassDefine(value = "业务线")
    @Size(min = 1)
    @NotEmpty
    private List<SaveFunctionConfigItemReq> items;

}
