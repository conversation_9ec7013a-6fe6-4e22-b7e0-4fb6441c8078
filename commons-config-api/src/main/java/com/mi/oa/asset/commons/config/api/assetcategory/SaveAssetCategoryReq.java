package com.mi.oa.asset.commons.config.api.assetcategory;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:09
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveAssetCategoryReq implements Serializable {

    private static final long serialVersionUID = -1982136631632707979L;
    @HttpApiDocClassDefine(value = "分类id", required = false, description = "更新时必传")
    private Integer cateId;

    @HttpApiDocClassDefine(value = "分类编码", required = false, description = "更新时必传")
    @Length(max = 20, message = "分类编码长度不能超过20")
    private String cateCode;

    @HttpApiDocClassDefine(value = "业务线", required = true, description = "car - 汽车资产")
    @NotBlank(message = "业务线不能为空")
    private String businessLine;

    @HttpApiDocClassDefine(value = "分类名称", required = true, description = "分类名称")
    @NotBlank(message = "分类名称不能为空")
    private String cateName;

    @HttpApiDocClassDefine(value = "分类名称（英文）", required = true, description = "分类名称（英文）")
    private String cateNameEn;

    @HttpApiDocClassDefine(value = "上级分类编码", required = false, description = "新建非一级分类必传")
    private String parentCateCode;

    @HttpApiDocClassDefine(value = "排序", required = false, description = "排序")
    private Integer sort;

    @HttpApiDocClassDefine(value = "禁用", required = true, description = "禁用")
    private Boolean disabled;

    @HttpApiDocClassDefine(value = "国家编码", required = true, description = "国家编码")
    private String country;

    @HttpApiDocClassDefine(value = "国家名称", required = true, description = "国家名称")
    private String countryName;

    @HttpApiDocClassDefine(value = "sap资产分类编码", required = false, description = "sap资产分类编码")
    private String sapCateCode;

    @HttpApiDocClassDefine(value = "sap资产分类名称", required = false, description = "sap资产分类名称")
    private String sapCateName;

    @HttpApiDocClassDefine(value = "使用年限", required = false, description = "使用年限")
    private Integer useYear;

    @HttpApiDocClassDefine(value = "使用期间", required = false, description = "使用期间")
    private Integer useMonth;

    @HttpApiDocClassDefine(value = "关联采购目录编码", required = false, description = "关联采购目录编码")
    private String purchaseCatalogCode;

    @HttpApiDocClassDefine(value = "关联采购目录编码（全路径）", required = false, description = "关联采购目录编码（全路径）")
    private String purchaseCatalogCodePath;

    @HttpApiDocClassDefine(value = "关联采购目录名称", required = false, description = "关联采购目录名称")
    private String purchaseCatalogName;

    @HttpApiDocClassDefine(value = "关联采购目录名称（全路径）", required = false, description = "关联采购目录名称（全路径）")
    private String purchaseCatalogNamePath;

    @HttpApiDocClassDefine(value = "是否一卡多物管理", required = false, description = "是否一卡多物管理 1 是 0 否")
    private Boolean isMultipleManage;

    @HttpApiDocClassDefine(value = "是否唯一码管理", required = false, description = "是否SN管理 1 是 0 否")
    private Boolean isSerialCodeManage;

    @HttpApiDocClassDefine(value = "物料类型", required = false, description = "物料类型")
    private String materialType;

    @HttpApiDocClassDefine(value = "管理模式", required = true, description = "asset_account：资产台账管理 asset_stock：资产库存管理 stock：库存管理")
    private String mgModel;

    @HttpApiDocClassDefine(value = "品类经理账号")
    private String managerCode;

    @HttpApiDocClassDefine(value = "品类经理")
    private String managerName;

    @HttpApiDocClassDefine(value = "交期(天)",description = "整数，选填")
    private Integer deliveryDays;

    @HttpApiDocClassDefine(value = "资产编码生成方式",description = "资产编码生成方式:0:自动生成,1:手动输入,默认0保持原来不变")
    private Integer codeGenType;
}
