package com.mi.oa.asset.commons.config.api.assetorganization;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/9/18 9:52
 */

@Data
public class AssetOrgStructureRes implements Serializable {

    private static final long serialVersionUID = 4023209709822294203L;

    @HttpApiDocClassDefine(value = "组织结构编码")
    private String orgCode;

    @HttpApiDocClassDefine(value = "组织结构名称")
    private String orgName;

    @HttpApiDocClassDefine(value = "组织结构英文名称")
    private String orgNameEn;

    @HttpApiDocClassDefine(value = "组织结构全称")
    private String orgFullName;

    @HttpApiDocClassDefine(value = "组织结构全称英文")
    private String orgFullNameEn;

    @HttpApiDocClassDefine(value = "级别")
    private Integer level;

    @HttpApiDocClassDefine(value = "上级编码")
    private String parentCode;

    @HttpApiDocClassDefine(value = "默认成本中心")
    private String defaultCostCenter;

    @HttpApiDocClassDefine(value = "是否虚拟组织")
    private Boolean isVirtual;

    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;
}
