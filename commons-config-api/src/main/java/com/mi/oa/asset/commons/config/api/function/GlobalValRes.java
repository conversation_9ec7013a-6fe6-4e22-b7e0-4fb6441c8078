package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/07 14:14
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GlobalValRes implements Serializable {

    private Integer id;

    @HttpApiDocClassDefine(value = "管理类型", required = false, description = "管理类型")
    private Integer manageType;

    @HttpApiDocClassDefine(value = "业务线", required = true, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "用途 全局变量 0, 其他", required = true, description = "用途 全局变量 0, 其他")
    private Integer useWay;

    @HttpApiDocClassDefine(value = "类型 前端 0, 后端 1，其他 2", required = true, description = "类型 前端 0, 后端 1，其他 2")
    private Integer type;

    @HttpApiDocClassDefine(value = "编码", required = true, description = "编码")
    private String code;

    @HttpApiDocClassDefine(value = "值", required = true, description = "值")
    private String value;

    private String enValue;

    @HttpApiDocClassDefine(value = "分组", required = true, description = "分组")
    private String groupCode;

    @HttpApiDocClassDefine(value = "说明", required = true, description = "说明")
    private String remark;

    /**
     * 创建人账号
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人账号
     */
    private String updateUser;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

}
