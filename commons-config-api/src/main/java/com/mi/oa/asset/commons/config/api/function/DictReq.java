package com.mi.oa.asset.commons.config.api.function;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/07 14:14
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictReq implements Serializable {

    private Integer id;

    private String parentCode;

    private Integer type;

    private String code;

    private String enName;

    private String name;

    private String moduleCode;

    private String moduleName;

    private Integer valid;

    private Integer sort;

    private List<DictReq> items;

}
