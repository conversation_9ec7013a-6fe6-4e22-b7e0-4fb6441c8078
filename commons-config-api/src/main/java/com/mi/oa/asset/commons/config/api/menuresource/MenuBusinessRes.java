package com.mi.oa.asset.commons.config.api.menuresource;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * 业务线
 *
 * <AUTHOR>
 * @date 2023/10/24 9:28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MenuBusinessRes implements Serializable {

    private static final long serialVersionUID = 8752591726620811307L;

    private String id;

    private String title;

    private String isNewLine;
}
