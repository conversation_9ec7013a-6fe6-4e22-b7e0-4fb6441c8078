package com.mi.oa.asset.commons.config.api.assetsku;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 跨业务线共享物料
 */
@Data
public class SharedBusinessLineReq implements Serializable {

    private static final long serialVersionUID = -3498364901995276050L;

    @NotEmpty(message = "skuIds不能为空")
    private List<Integer> skuIds;

    @NotEmpty(message = "businessLines不能为空")
    private List<String> businessLines;

}
