package com.mi.oa.asset.commons.config.api.menuresource;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 18:35
 **/
public interface MenuResource {

    /**
     * 查询用户菜单功能权限
     *
     * @param userName
     * @param appCode
     * @param accountType
     * @param isShow
     * @return
     */
    MenuResourceRes getMenuResourceByUserName(String userName, String appCode, Integer accountType, Integer isShow, String eamLanguage);

    /**
     * 查询菜单功能权限数据（共用同一套菜单）
     *
     * @param userName
     * @param appCode
     * @param accountType
     * @param isShow
     * @param eamSource 客户端来源：admin:管理员，client
     * @param eamLanguage 语言
     * @return
     */
    List<MenuFunctionRes> getMenuFunctionResource(String userName, String appCode, Integer accountType, Integer isShow, String eamSource, String eamLanguage);

    /**
     * 查询业务线数据
     *
     * @param userName
     * @param appCode
     * @param accountType
     * @return
     */
    List<MenuBusinessLineRes> getMenuBusinessLineRes(String userName, String appCode, Integer accountType);

    /**
     * 查询员工有盘点菜单的所有管理线
     *
     * @param userName
     * @return
     */
    List<String> getInventoryMenuAuth(String userName);

}
