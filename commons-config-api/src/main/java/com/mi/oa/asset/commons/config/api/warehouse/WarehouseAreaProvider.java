package com.mi.oa.asset.commons.config.api.warehouse;

import java.util.List;

public interface WarehouseAreaProvider {

    /**
     * 保存仓库
     *
     * @param req
     * @return
     */
    Integer saveWarehouse(WarehouseAreaReq req);

    /**
     * 删除仓库
     */
    void removeWarehouse(RemoveWarehouseAreaReq req);


    List<WarehouseAreaRes> listByBizAndRegion(List<String> businessLines, String region, String areaName);

    /**
     * 根据关键词（仓编码或仓名称）模糊查询仓库区域，支持业务线过滤
     */
    List<WarehouseAreaRes> searchWarehouseArea(String keyword, List<String> businessLines);
}
