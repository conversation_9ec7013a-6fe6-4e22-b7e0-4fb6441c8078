package com.mi.oa.asset.commons.config.api.warehouse;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

@Data
public class WarehouseAreaRes implements Serializable {
    private static final long serialVersionUID = 4598020273827906123L;
    @HttpApiDocClassDefine(value = "主键")
    private Integer id;

    /**
     * 仓库编码
     */
    @HttpApiDocClassDefine(value = "仓库编码")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @HttpApiDocClassDefine(value = "仓库名称")
    private String warehouseName;

    /**
     * 业务线
     */
    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    /**
     * 优先级，正整数 必填
     */
    @HttpApiDocClassDefine(value = "优先级")
    private Integer priority;

    /**
     * 国家id
     */
    @HttpApiDocClassDefine(value = "国家id")
    private String countryId;

    /**
     * 省id
     */
    @HttpApiDocClassDefine(value = "省id")
    private String provinceId;

    /**
     * 市id
     */
    @HttpApiDocClassDefine(value = "市id")
    private String cityId;

    /**
     * 区id
     */
    @HttpApiDocClassDefine(value = "区id")
    private String areaId;

    /**
     * 街道id
     */
    @HttpApiDocClassDefine(value = "街道id")
    private String streetId;

    /**
     * 地区名称
     */
    @HttpApiDocClassDefine(value = "地区名称")
    private String areaName;

}
