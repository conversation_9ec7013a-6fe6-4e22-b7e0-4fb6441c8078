package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FuncDictAuthReq implements Serializable {
    @HttpApiDocClassDefine(value = "字典ID", required = true, description = "字典ID")
    private Integer dictId;

    @HttpApiDocClassDefine(value = "角色/用户列表", required = true, description = "角色/用户列表")
    private List<FuncDictRelReq> authList;

    @HttpApiDocClassDefine(value = "字典编码", required = true, description = "字典编码")
    private String code;

    @HttpApiDocClassDefine(value = "1:按角色，2:按用户", required = true, description = "1:按角色，2:按用户")
    private Integer type;

    @HttpApiDocClassDefine(value = "业务线", required = true, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "功能编码", required = true, description = "功能编码")
    private String funcCode;
}
