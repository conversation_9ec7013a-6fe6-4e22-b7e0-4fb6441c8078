package com.mi.oa.asset.commons.config.api.businessrole;

import com.mi.oa.asset.common.model.User;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.util.List;

@Data
public class BusinessRoleInfoRes {
    @HttpApiDocClassDefine(value = "角色编码", required = true, description = "角色编码")
    private String roleCode;

    @HttpApiDocClassDefine(value = "角色名称")
    private String roleName;

    @HttpApiDocClassDefine(value = "角色英文名称")
    private String roleNameEn;

    @HttpApiDocClassDefine(value = "角色描述")
    private String roleDesc;

    @HttpApiDocClassDefine(value = "角色英文描述")
    private String roleDescEn;

    @HttpApiDocClassDefine(value = "用户列表", required = true, description = "用户列表")
    private List<User> users;
}
