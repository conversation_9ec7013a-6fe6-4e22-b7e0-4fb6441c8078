package com.mi.oa.asset.commons.config.api.assetorganization;

import com.mi.oa.asset.common.enums.BusinessLine;
import com.xiaomi.mit.api.PageData;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/15 16:37
 */

public interface AssetOrgProvider {

    void createNoVirtualUnit(SaveAssetOrgUnitReq req);

    void createAssetOrgUnit(SaveAssetOrgUnitReq req);

    void refreshOrgStructure(String orgCode, String businessLine);

    void updateAssetOrgUnit(SaveAssetOrgUnitReq req);

    List<AssetOrgStructureRes> getOrgStructure(List<String> businessLineCodes);

    List<AssetOrgStructureRes> getOrgStructure(List<String> businessLineCodes, String orgCode);

    List<AssetOrgUnitRes> getBatchOrgUnitByCode(String businessLine, List<String> orgCode);

    AssetOrgUnitRes getOrgUnitByCode(String orgCode, String businessLine);

    AssetOrgUnitRes getOrgUnit(String keyword, String businessLine);

    AssetOrgUnitRes getOrgUnitById(Integer orgId);

    AssetOrgUnitRes getOrgUnitById(Integer orgId, String language);

    void deleteOrgStructure(String orgCode);

    void deleteOrgUnitByIds(List<Integer> orgIds);

    PageData<AssetOrgUnitRes> getAssetOrgUnitPageData(AssetOrgUnitQueryReq req, Integer pageNum, Integer pageSize);

    PageData<AssetOrgUnitRes> getAssetOrgUnitPageData(AssetOrgUnitQueryReq req, Integer pageNum, Integer pageSize, String language);

    PageData<AssetOrgUnitRes> getAllotOrgList(AssetOrgUnitQueryReq req, Integer pageNum, Integer pageSize);

    PageData<AssetOrgUnitRes> getAllotOrgList(AssetOrgUnitQueryReq req, Integer pageNum, Integer pageSize, String language);

    /**
     * 根据登录用户查询部门下所有实验室组织类型
     * @param businessLine
     * @param userCode
     * @return
     */
    List<AssetOrgUnitRes> getLabTypeOrgList(String businessLine, String userCode);

    AssetOrgStructureRes getOrgStructureByCode(String orgCode);

    List<AssetOrgStructureRes> getBatchOrgStructureByCode(List<String> orgCodes);

    List<AssetOrgStructureRes> getBatchOrgStructureByCode(List<String> orgCodes, BusinessLine businessLine);

    List<AssetOrgStructureRes> getOrgStructureWithSub(String orgCode);

    List<AssetOrgStructureRes> getOrgStructureWithSub(String orgCode, BusinessLine businessLine);

    List<AssetOrgStructureRes> getOrgStructureWithSub(String orgCode, List<String> businessLineCodes);

    List<AssetOrgUnitRes> getManageOrgUnit(String businessLineCode);

    List<AssetOrgUnitRes> getManageOrgUnit(String businessLineCode, String language);

    List<AssetOrgStructureRes> getDepartments();

    PageData<AssetOrgStructureRes> getAssetOrgStructurePageData(String businessLine, String keyword, Integer pageNum, Integer pageSize);

    void importOrgUnit(AssetOrgUnitImportReq req);

    /**
     * 通过指定部门获取所有上级部门
     *
     * @param orgCode
     * @return
     */
    List<String> getParentCodes(String orgCode);

    /**
     * 刷新组织单位表组织英文名称，部门全程英文名称
     * @param businessLine
     */
    void refreshOrgUnit(List<String> businessLine);
}
