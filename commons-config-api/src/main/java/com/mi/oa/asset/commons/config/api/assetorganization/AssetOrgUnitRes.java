package com.mi.oa.asset.commons.config.api.assetorganization;

import com.mi.oa.asset.commons.config.api.businessrole.BusinessRoleInfoRes;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/21 18:15
 */

@Data
public class AssetOrgUnitRes implements Serializable {

    private static final long serialVersionUID = 8510240311487666661L;
    
    @HttpApiDocClassDefine(value = "组织单元id")
    private Integer orgId;

    @HttpApiDocClassDefine(value = "组织单元编码")
    private String orgCode;

    @HttpApiDocClassDefine(value = "组织单元名称")
    private String orgName;

    @HttpApiDocClassDefine(value = "组织单元英文名称")
    private String orgNameEn;

    @HttpApiDocClassDefine(value = "别名")
    private String aliasName;

    @HttpApiDocClassDefine(value = "默认成本中心")
    private String defaultCostCenter;

    @HttpApiDocClassDefine(value = "编码全路径")
    private String orgCodePath;

    @HttpApiDocClassDefine(value = "名称全路径")
    private String orgNamePath;

    @HttpApiDocClassDefine(value = "名称英文全路径")
    private String orgNamePathEn;

    @HttpApiDocClassDefine(value = "上级编码")
    private String parentCode;

    @HttpApiDocClassDefine(value = "上级名称")
    private String parentName;

    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "级别")
    private Integer level;

    @HttpApiDocClassDefine(value = "是否虚拟组织")
    private Boolean isVirtual;

    @HttpApiDocClassDefine(value = "是否资产管理组织")
    private Boolean isAssetManageOrg;

    @HttpApiDocClassDefine(value = "是否资产使用组织")
    private Boolean isAssetUseOrg;

    @HttpApiDocClassDefine(value = "组织类型")
    private String orgType;

    @HttpApiDocClassDefine(value = "委托记账主体编码")
    private String companyCode;

    @HttpApiDocClassDefine(value = "委托记账主体名称")
    private String companyName;

    @HttpApiDocClassDefine(value = "委托记账成本中心")
    private String costCenter;

    @HttpApiDocClassDefine(value = "地址")
    private String address;

    @HttpApiDocClassDefine(value = "创建时间")
    private Date createTime;

    @HttpApiDocClassDefine(value = "业务角色列表", required = false, description = "业务角色列表")
    private List<BusinessRoleInfoRes> roleInfoList;
}
