package com.mi.oa.asset.commons.config.api.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/12 9:31
 */

@Getter
@AllArgsConstructor
public enum ExecutionStatus {

    IN_PROGRESS("in_progress", "进行中"),
    SUCCESS("success", "执行成功"),
    FAIL("fail", "执行失败"),
    ;

    private final String code;
    private final String desc;

    public static ExecutionStatus getByCode(String code) {
        for (ExecutionStatus value : ExecutionStatus.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
