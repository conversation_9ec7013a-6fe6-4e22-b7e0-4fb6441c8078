package com.mi.oa.asset.commons.config.api.assetcode;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024-06-25 16:40
 */
@Data
public class FieldRuleConfigRes implements Serializable {
    private static final long serialVersionUID = -7529151449371944652L;

    @HttpApiDocClassDefine(value = "台账字段", description = "台账字段，不可调整")
    private String code;

    @HttpApiDocClassDefine(value = "名称", description = "名称，不可调整")
    private String name;

    @HttpApiDocClassDefine(value = "连接线", description = "连接线或文本框或流水号长度")
    private String connector;

    @HttpApiDocClassDefine(value = "策略模式", description = "策略模式，不可调整")
    private String strategy;

    @HttpApiDocClassDefine(value = "格式化", description = "对值内容进行特殊处理，通过策略规则指定格式标准，不可调整")
    private String format;
}
