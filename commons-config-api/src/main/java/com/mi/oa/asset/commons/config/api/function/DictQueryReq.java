package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictQueryReq extends PageRequest {
    @HttpApiDocClassDefine(value = "模糊查询key", required = false, description = "模糊查询key")
    private String key;

    @HttpApiDocClassDefine(value = "类型：0, 字典key编码，1，字典value值", required = false, description = "类型")
    private Integer type;

    @HttpApiDocClassDefine(value = "管理类型: 0 默认系统，1 管理线，2 业务线", required = false, description = "管理类型 0 默认系统，1 管理线，2 业务线")
    private Integer manageType;

    @HttpApiDocClassDefine(value = "业务线", required = false, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "parentCode", required = false, description = "parentCode")
    private String parentCode;

    @HttpApiDocClassDefine(value = "功能编码", required = false, description = "功能编码")
    private String funcCode;
}
