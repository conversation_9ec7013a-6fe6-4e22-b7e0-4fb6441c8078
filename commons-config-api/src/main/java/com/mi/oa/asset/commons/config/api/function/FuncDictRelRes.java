package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FuncDictRelRes implements Serializable {
    @HttpApiDocClassDefine(value = "id", required = true, description = "id")
    private Integer id;

    /**
     * 角色编码
     */
    @HttpApiDocClassDefine(value = "角色编码/用户账号", required = true, description = "角色编码/用户账号")
    private String role;

    /**
     * 角色名称
     */
    @HttpApiDocClassDefine(value = "角色名称/用户名称", required = false, description = "角色名称/用户名称")
    private String roleName;

    private String createUserName;

    private Date createTime;

}
