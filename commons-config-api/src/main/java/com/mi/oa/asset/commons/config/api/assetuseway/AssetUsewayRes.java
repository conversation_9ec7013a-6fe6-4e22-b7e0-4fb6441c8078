package com.mi.oa.asset.commons.config.api.assetuseway;


import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 办公用途 request 请求对象
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssetUsewayRes implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 用途代码
	 */
	@HttpApiDocClassDefine(value = "用途代码")
	private String usewayCode;
	/**
	 * 备注
	 */
	@HttpApiDocClassDefine(value = "备注")
	private String usewayMemo;
	/**
	 * 用途名称
	 */
	@HttpApiDocClassDefine(value = "用途名称")
	private String usewayName;
	/**
	 * 用途区别
	 */
	@HttpApiDocClassDefine(value = "用途区别")
	private String usewayType;
	/**
	 * 主键
	 */
	@HttpApiDocClassDefine(value = "主键")
	private String usewayId;
	/**
	 * 添加时间
	 */
	@HttpApiDocClassDefine(value = "添加时间")
	private Date addDate;
	/**
	 * 添加人ID
	 */
	@HttpApiDocClassDefine(value = "添加人ID")
	private String addUserid;
	/**
	 * 修改时间
	 */
	@HttpApiDocClassDefine(value = "修改时间")
	private Date modifyDate;
	/**
	 * 修改人ID
	 */
	@HttpApiDocClassDefine(value = "修改人ID")
	private String modifyUserid;
	/**
	 * 系统租户ID
	 */
	@HttpApiDocClassDefine(value = "系统租户ID")
	private String tenantId;
	/**
	 * 适用范围
	 */
	@HttpApiDocClassDefine(value = "适用范围")
	private String usewayQualif;
	/**
	 * 是否生效
	 */
	@HttpApiDocClassDefine(value = "是否生效")
	private String isValid;
	/**
	 * 排序字段
	 */
	@HttpApiDocClassDefine(value = "排序字段")
	private BigDecimal id;
	/**
	 * EAM编码
	 */
	@HttpApiDocClassDefine(value = "EAM编码")
	private String eamCode;
	/**
	 * 用途名称英文
	 */
	@HttpApiDocClassDefine(value = "用途名称英文")
	private String usewayNameEn;
	/**
	 * 备注英文
	 */
	@HttpApiDocClassDefine(value = "备注英文")
	private String usewayMemoEn;

}
