package com.mi.oa.asset.commons.config.api.queryfield;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:28
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FieldConfigRes implements Serializable {
    private static final long serialVersionUID = -8578903895146236939L;

    @HttpApiDocClassDefine(value = "字段配置主键")
    private Integer fieldId;

    @HttpApiDocClassDefine(value = "字段配置信息")
    private List<FieldConfigInfo> fieldConfig;

    @HttpApiDocClassDefine(value = "扩展台账字段信息")
    private List<FieldConfigInfo> extra;

    @HttpApiDocClassDefine(value = "管理线")
    private String manageLine;

    @HttpApiDocClassDefine(value = "功能ID")
    private String funId;

}
