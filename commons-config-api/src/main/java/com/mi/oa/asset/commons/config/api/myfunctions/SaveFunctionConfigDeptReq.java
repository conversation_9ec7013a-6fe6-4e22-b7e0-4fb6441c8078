package com.mi.oa.asset.commons.config.api.myfunctions;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/28 16:51
 **/
@Data
public class SaveFunctionConfigDeptReq implements Serializable {

    private static final long serialVersionUID = -657269122305110800L;

    /**
     * 配置明细id
     */
    private Integer configItemId;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

}
