package com.mi.oa.asset.commons.config.api.businessrole;

import com.mi.oa.asset.common.model.User;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BusinessRoleInfoReq implements Serializable {

    private static final long serialVersionUID = 2557724894171778765L;

    @HttpApiDocClassDefine(value = "角色编码", required = true, description = "角色编码")
    private String roleCode;

    @HttpApiDocClassDefine(value = "用户列表", required = true, description = "用户列表")
    private List<User> users;
}
