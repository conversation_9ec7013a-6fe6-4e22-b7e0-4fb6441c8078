package com.mi.oa.asset.commons.config.api.countrybusinessLine;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Data
public class CountryBusinessLineRes {
    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 国家/地区代码（3位）
     */
    private String countryCodeAlphaThree;

    /**
     * 国家/地区代码（2位）
     */
    private String countryCodeAlphaTwo;

    /**
     * 国家/地区名称
     */
    private String countryName;

    /**
     * 国家/地区名称（英文）
     */
    private String countryEnglishName;

    /**
     * 地址库id（地址库中国家/地区 一级地区 对应的id
     */
    private Integer provinceId;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 国家数据表id
     */
    private Integer countryConfigId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 默认语言
     */
    private String defaultLanguage;

    /**
     * 是否删除
     */
    private Integer isDeleted;
}
