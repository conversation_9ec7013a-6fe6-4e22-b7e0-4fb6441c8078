package com.mi.oa.asset.commons.config.api.labelprint;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/02/17 19:15
 * @description 标签模版响应对象
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class LabelTemplateRes implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 模版id
     */
    @HttpApiDocClassDefine(value = "模版id")
    private Integer id;

    /**
     * 模版类型 业务模板：business、系统模板：system
     */
    @HttpApiDocClassDefine(value = "模版类型 业务模板：business、系统模板：system")
    private String templateType;

    /**
     * 业务线
     */
    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    /**
     * 是否默认模版，0-否，1-是
     */
    @HttpApiDocClassDefine(value = "是否默认模版，0-否，1-是")
    private String isDefault;


    /**
     * 扫码展示字段
     */
    @HttpApiDocClassDefine(value = "扫码展示字段")
    private String scanDisplayField;

    /**
     * 是否展示编码
     */
    @HttpApiDocClassDefine(value = "是否展示编码")
    private Integer isShow;

    /**
     * 其他配置信息
     */
    @HttpApiDocClassDefine(value = "扩展配置")
    private String extConf;

    /**
     * 数据来源
     */
    @HttpApiDocClassDefine(value = "标签数据来源")
    private String dataSource;

}
