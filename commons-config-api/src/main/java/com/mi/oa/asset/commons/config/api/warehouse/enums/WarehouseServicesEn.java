
package com.mi.oa.asset.commons.config.api.warehouse.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.mi.oa.asset.common.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/7/20 10:32
 */
@Getter
@AllArgsConstructor
public enum WarehouseServicesEn implements IEnum<WarehouseServicesEn> {

    COLLECT("collect", "Requisition"),
    BORROW("borrow", "Borrow"),
    RETURN("return", "Return"),
    REPAIR("repair", "Maintenance"),
    PURCHASE("purchase", "Internal Purchase"),
    LOSS("loss", "Loss"),
    SCRAP("scrap", "Scrap"),
    ;
    @JsonValue
    private final String code;

    private final String desc;

    public static WarehouseServicesEn getByCode(String code) {
        if(null == code) {
            return null;
        }

        for (WarehouseServicesEn compensateType: WarehouseServicesEn.values()) {
            if (compensateType.code.equals(code)) {
                return compensateType;
            }
        }
        return null;
    }

    public static WarehouseServicesEn getByDesc(String desc) {
        if(StringUtils.isBlank(desc)) {
            return null;
        }

        for (WarehouseServicesEn compensateType: WarehouseServicesEn.values()) {
            if (compensateType.desc.equals(desc)) {
                return compensateType;
            }
        }
        return null;
    }
}
