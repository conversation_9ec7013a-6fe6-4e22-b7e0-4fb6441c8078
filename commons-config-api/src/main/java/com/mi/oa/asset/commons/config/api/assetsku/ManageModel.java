
package com.mi.oa.asset.commons.config.api.assetsku;

import com.fasterxml.jackson.annotation.JsonValue;
import com.mi.oa.asset.common.enums.IEnum;
import com.mi.oa.asset.excel.enums.ExcelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/7/20 10:32
 */
@Getter
@AllArgsConstructor
public enum ManageModel implements IEnum<ManageModel>, ExcelEnum<ManageModel> {

    ASSET_ACCOUNT("asset_account", "资产台账管理"),
    ASSET_STOCK("asset_stock", "资产库存管理"),
    STOCK("stock", "库存管理"),
    ;
    @JsonValue
    private final String code;

    private final String desc;

    public static ManageModel getByCode(String code) {
        if(null == code) {
            return null;
        }

        for (ManageModel compensateType: ManageModel.values()) {
            if (compensateType.code.equals(code)) {
                return compensateType;
            }
        }
        return null;
    }

    public static ManageModel getByDesc(String desc) {
        if(StringUtils.isBlank(desc)) {
            return null;
        }

        for (ManageModel compensateType: ManageModel.values()) {
            if (compensateType.desc.equals(desc)) {
                return compensateType;
            }
        }
        return null;
    }
}
