package com.mi.oa.asset.commons.config.api.assetuseway;


import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 申请理由 request 请求对象
 * <AUTHOR>
 * @date 2024-04-11 03:17:29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssetUsewayReasonRes implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 
	 */
	@HttpApiDocClassDefine(value = "主键ID")
	private Integer id;
	/**
	 * 申请理由
	 */
	@HttpApiDocClassDefine(value = "申请理由")
	private String usewayReason;
	/**
	 * 添加人ID
	 */
	@HttpApiDocClassDefine(value = "添加人ID")
	private String addUserid;
	/**
	 * 添加时间
	 */
	@HttpApiDocClassDefine(value = "添加时间")
	private Date addDate;
	/**
	 * 修改人ID
	 */
	@HttpApiDocClassDefine(value = "修改人ID")
	private String modifyUserid;
	/**
	 * 修改时间
	 */
	@HttpApiDocClassDefine(value = "修改时间")
	private Date modifyDate;
	/**
	 * 系统租户ID
	 */
	@HttpApiDocClassDefine(value = "系统租户ID")
	private String tenantId;
	/**
	 * 申请用途id
	 */
	@HttpApiDocClassDefine(value = "申请用途id")
	private String usewayId;

}
