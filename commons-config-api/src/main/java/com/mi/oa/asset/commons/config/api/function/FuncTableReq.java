package com.mi.oa.asset.commons.config.api.function;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/2/25 14:14
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FuncTableReq implements Serializable {
    private Integer id;

    private String title;

    private String tableName;

    private String code;

    private String remark;

    private String parentId;

    private Integer valid;
}
