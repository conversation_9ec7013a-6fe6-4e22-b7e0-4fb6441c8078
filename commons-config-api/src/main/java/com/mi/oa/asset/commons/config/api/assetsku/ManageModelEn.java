
package com.mi.oa.asset.commons.config.api.assetsku;

import com.fasterxml.jackson.annotation.JsonValue;
import com.mi.oa.asset.common.enums.IEnum;
import com.mi.oa.asset.excel.enums.ExcelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/7/20 10:32
 */
@Getter
@AllArgsConstructor
public enum ManageModelEn implements IEnum<ManageModelEn>, ExcelEnum<ManageModelEn> {

    ASSET_ACCOUNT("asset_account", "Asset Ledger Managemen"),
    ASSET_STOCK("asset_stock", "Asset Inventory Management"),
    STOCK("stock", "Inventory Management"),
    ;
    @JsonValue
    private final String code;

    private final String desc;

    public static ManageModelEn getByCode(String code) {
        if(null == code) {
            return null;
        }

        for (ManageModelEn compensateType: ManageModelEn.values()) {
            if (compensateType.code.equals(code)) {
                return compensateType;
            }
        }
        return null;
    }

    public static ManageModelEn getByDesc(String desc) {
        if(StringUtils.isBlank(desc)) {
            return null;
        }

        for (ManageModelEn compensateType: ManageModelEn.values()) {
            if (compensateType.desc.equals(desc)) {
                return compensateType;
            }
        }
        return null;
    }
}
