package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FunctionRes implements Serializable {
    @HttpApiDocClassDefine(value = "id", required = false, description = "id")
    private Integer id;

    @HttpApiDocClassDefine(value = "功能编码", required = false, description = "功能编码")
    private String code;

    @HttpApiDocClassDefine(value = "功能名称", required = false, description = "功能名称")
    private String funName;

    @HttpApiDocClassDefine(value = "英文名称", required = false, description = "英文名称")
    private String enName;

    @HttpApiDocClassDefine(value = "表名称", required = false, description = "表名称")
    private String tableName;

    @HttpApiDocClassDefine(value = "fromSql", required = false, description = "fromSql")
    private String fromSql;

    @HttpApiDocClassDefine(value = "whereSql", required = false, description = "whereSql")
    private String whereSql;

    @HttpApiDocClassDefine(value = "orderSql", required = false, description = "orderSql")
    private String orderSql;

    @HttpApiDocClassDefine(value = "groupSql", required = false, description = "groupSql")
    private String groupSql;

    @HttpApiDocClassDefine(value = "本人看本人", required = false, description = "本人看本人")
    private Boolean viewSelf;

    @HttpApiDocClassDefine(value = "本人修改本人", required = false, description = "本人修改本人")
    private Boolean editSelf;

    @HttpApiDocClassDefine(value = "本人修改本人", required = false, description = "本人修改本人")
    private Boolean deleteSelf;

    @HttpApiDocClassDefine(value = "本部门可见", required = false, description = "本部门可见")
    private Boolean currentDept;

    @HttpApiDocClassDefine(value = "监管部门可见", required = false, description = "监管部门可见")
    private Boolean manageDept;

    @HttpApiDocClassDefine(value = "本部门可见角色范围", required = false, description = "本部门可见角色范围")
    private List<String> currentDeptRole;

    @HttpApiDocClassDefine(value = "监管部门可见角色范围", required = false, description = "监管部门可见角色范围")
    private List<String> manageDeptRole;

    @HttpApiDocClassDefine(value = "拓展sql角色范围", required = false, description = "拓展sql角色范围")
    private List<String> extSqlRole;

    @HttpApiDocClassDefine(value = "资源维度权限", required = false, description = "资源维度权限")
    private List<FuncResourceReq> resourceList;
}
