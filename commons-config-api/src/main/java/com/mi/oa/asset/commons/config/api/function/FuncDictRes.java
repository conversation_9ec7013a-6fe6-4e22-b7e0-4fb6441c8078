package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FuncDictRes implements Serializable {
    @HttpApiDocClassDefine(value = "字典ID", required = true, description = "字典ID")
    private Integer dictId;

    @HttpApiDocClassDefine(value = "字典编码", required = true, description = "字典编码")
    private String code;

    @HttpApiDocClassDefine(value = "字典名称", required = true, description = "字典名称")
    private String name;

    @HttpApiDocClassDefine(value = "字典值", required = true, description = "字典值")
    private String value;

}
