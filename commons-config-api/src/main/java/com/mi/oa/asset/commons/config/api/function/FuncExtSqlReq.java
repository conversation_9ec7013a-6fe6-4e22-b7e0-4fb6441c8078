package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FuncExtSqlReq implements Serializable {
    @HttpApiDocClassDefine(value = "行id", required = false, description = "行id")
    private Integer id;

    @HttpApiDocClassDefine(value = "拓展sql", required = false, description = "拓展sql")
    private String extSql;

    @HttpApiDocClassDefine(value = "role", required = false, description = "role")
    private String role;

    @HttpApiDocClassDefine(value = "roleName", required = false, description = "roleName")
    private String roleName;
}
