package com.mi.oa.asset.commons.config.api.assetorganization;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/18 14:49
 */

@Data
public class AssetOrgStructureTreeRes implements Serializable {

    private static final long serialVersionUID = -9187820657547953719L;

    @HttpApiDocClassDefine(value = "组织单元编码")
    private String code;

    @HttpApiDocClassDefine(value = "组织单元名称")
    private String name;

    @HttpApiDocClassDefine(value = "级别")
    private Integer level;

    @HttpApiDocClassDefine(value = "成本中心")
    private String costCenter;

    @HttpApiDocClassDefine(value = "是否虚拟组织")
    private Boolean isVirtual;

    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "下级列表")
    private List<AssetOrgStructureTreeRes> subList;
}
