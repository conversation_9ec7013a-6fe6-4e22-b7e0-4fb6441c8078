package com.mi.oa.asset.commons.config.api.assetsku;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/9 19:41
 */

@Data
@Builder
public class AssetSkuRes implements Serializable {

    private static final long serialVersionUID = -3914287940772098364L;
    @HttpApiDocClassDefine(value = "skuId")
    private Integer skuId;

    @HttpApiDocClassDefine(value = "sku编码")
    private String skuCode;

    @HttpApiDocClassDefine(value = "sku名称")
    private String skuName;

    @HttpApiDocClassDefine(value = "sku名称(英文)")
    private String skuNameEn;

    @HttpApiDocClassDefine(value = "别名")
    private String aliasName;

    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "业务线")
    private List<String> businessLines;

    @HttpApiDocClassDefine(value = "单价")
    private BigDecimal price;

    @HttpApiDocClassDefine(value = "分类id")
    private Integer cateId;

    @HttpApiDocClassDefine(value = "分类编码")
    private String cateCode;

    @HttpApiDocClassDefine(value = "分类名称")
    private String cateName;

    @HttpApiDocClassDefine(value = "小米sku编码")
    private String miSkuCode;

    @HttpApiDocClassDefine(value = "小米商品id")
    private String miGoodsId;

    @HttpApiDocClassDefine(value = "国家编码", description = "国家编码")
    private String country;

    @HttpApiDocClassDefine(value = "国家名称", description = "国家名称")
    private String countryName;

    @HttpApiDocClassDefine(value = "品牌")
    private String brand;

    @HttpApiDocClassDefine(value = "规格")
    private String spec;

    @HttpApiDocClassDefine(value = "型号")
    private String model;

    @HttpApiDocClassDefine(value = "新旧属性")
    private String newOld;

    @HttpApiDocClassDefine(value = "物料类型")
    private String materialType;

    @HttpApiDocClassDefine(value = "是否一卡多物管理")
    private Integer isMultipleManage;

    @HttpApiDocClassDefine(value = "是否SN管理")
    private Integer isSn;

    @HttpApiDocClassDefine(value = "计量单位编码")
    private String measureCode;

    @HttpApiDocClassDefine(value = "计量单位名称")
    private String measureName;

    @HttpApiDocClassDefine(value = "禁用")
    private Boolean disabled;

    @HttpApiDocClassDefine(value = "商品名称")
    private String productName;

    @HttpApiDocClassDefine(value = "项目编码")
    private String projectCode;

    @HttpApiDocClassDefine(value = "项目阶段")
    private String projectPhase;

    @HttpApiDocClassDefine(value = "上市时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date saleDate;

    @HttpApiDocClassDefine(value = "交期(天)")
    private Integer deliveryDays;

    @HttpApiDocClassDefine(value = "备注")
    private String remark;

    @HttpApiDocClassDefine(value = "创建时间")
    private Date createTime;

    @HttpApiDocClassDefine(value = "更新时间")
    private Date updateTime;

    @HttpApiDocClassDefine(value = "管理信息")
    private List<AssetSkuMgRes> manages;

    @HttpApiDocClassDefine(value = "MDM状态")
    private Integer mdmCreateStatus;
}
