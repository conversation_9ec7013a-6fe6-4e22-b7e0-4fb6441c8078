package com.mi.oa.asset.commons.config.api.labelprint;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/02/17 19:15
 * @description 模版请求字段对象
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class LabelTemplateFieldReq implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 字段编码
     */
    @HttpApiDocClassDefine(value = "字段编码", required = true)
    @NotBlank(message = "字段编码不能为空！")
    private String fieldName;

    /**
     * 字段名称
     */
    @HttpApiDocClassDefine(value = "字段名称", required = true)
    @NotBlank(message = "字段名称不能为空！")
    private String fieldDesc;

    /**
     * 字段上间距
     */
    @HttpApiDocClassDefine(value = "字段上间距")
    private String fieldMarginTop;

    /**
     * 字段下间距
     */
    @HttpApiDocClassDefine(value = "字段下间距")
    private String fieldMarginBottom;

    /**
     * 字段顺序
     */
    @HttpApiDocClassDefine(value = "字段顺序")
    private String fieldOrder;

    /**
     * 字段字号
     */
    @HttpApiDocClassDefine(value = "字段字号")
    private String fontSize;

    /**
     * 字段字体样式 正常：normal、加粗：bold
     */
    @HttpApiDocClassDefine(value = "字段字体样式 正常：normal、加粗：bold")
    private String fontStyle;

    /**
     * 字段对齐方式 左对齐：left，居中对齐:center，右对齐:right
     */
    @HttpApiDocClassDefine(value = "字段对齐方式 左对齐：left，居中对齐:center，右对齐:right")
    private String alignment;

    /**
     * 数据来源
     */
    @HttpApiDocClassDefine(value = "数据来源")
    private String dataSource;

    /**
     * 是否隐藏字段名称，0-否，1-是
     */
    @HttpApiDocClassDefine(value = "是否隐藏字段名称，0-否，1-是")
    private Integer isHide = 0;

}
