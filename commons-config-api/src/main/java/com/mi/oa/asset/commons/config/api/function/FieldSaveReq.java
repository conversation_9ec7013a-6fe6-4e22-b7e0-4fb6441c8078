package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FieldSaveReq implements Serializable {
    @HttpApiDocClassDefine(value = "字段列表", required = false, description = "字段列表")
    private List<FuncFieldReq> list;

    @HttpApiDocClassDefine(value = "功能编码/表编码", required = false, description = "功能编码/表编码")
    private String funcCode;

    @HttpApiDocClassDefine(value = "管理类型", required = false, description = "管理类型")
    private Integer manageType;

    @HttpApiDocClassDefine(value = "业务线", required = true, description = "业务线")
    @NonNull
    private String businessLine;
}
