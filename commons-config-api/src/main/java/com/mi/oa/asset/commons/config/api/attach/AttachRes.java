package com.mi.oa.asset.commons.config.api.attach;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * 附件
 *
 * <AUTHOR>
 * @date 2023/9/19 16:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AttachRes implements Serializable {

    private static final long serialVersionUID = -4184940264322320040L;

    /**
     * uri
     */
    private String uri;

    /**
     * FDS中唯一的文件名
     */
    private String fileName;

    /**
     * 原始文件名
     */
    private String originFileName;

    /**
     * 下载地址
     */
    private String downloadUrl;
}
