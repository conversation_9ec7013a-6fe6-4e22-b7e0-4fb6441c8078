package com.mi.oa.asset.commons.config.api.position;

import com.xiaomi.mit.api.PageData;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/16 11:21
 */

public interface PositionProvider {

    /**
     * 空间系统位置树
     * @return
     */
    List<PositionTreeRes> stdPositionTree(String language);

    /**
     * 新建位置
     * @param req
     */
    void createPosition(SavePositionReq req);

    /**
     * 更新位置
     * @param req
     */
    void updatePosition(SavePositionReq req);

    /**
     * 批量删除
     * @param ids
     */
    void deleteByIds(List<Integer> ids);

    /**
     * 位置分页查询
     * @param positionId
     * @param keyword
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<PositionRes> getPositionPageData(Integer positionId, String keyword, Integer pageNum, Integer pageSize);

    /**
     * 位置分页查询
     * @param businessLineCodes
     * @param keyword
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<PositionRes> getPositionPageData(List<String> businessLineCodes, String keyword, Integer pageNum, Integer pageSize);

    /**
     * 根据id获取位置
     * @param positionId
     * @return
     */
    PositionRes getPosition(Integer positionId);

    /**
     * 根据位置编码，业务线获取位置
     * @param positionCode
     * @param businessLine
     * @return
     */
    PositionRes getPosition(String positionCode, String businessLine);

    /**
     * 根据位置编码，业务线批量获取位置
     * @param positionCode
     * @param businessLine
     * @return
     */
    List<PositionRes> getBatchPosition(List<String> positionCode, String businessLine);

    List<PositionRes> getPositionWithSub(Integer positionId);

    /**
     * 导入空间系统位置
     * @param req
     */
    void importStdPosition(ImportStdPositionReq req);

    /**
     * 获取所有位置
     * @param businessLineCodes
     * @return
     */
    List<PositionRes> getAllPosition(List<String> businessLineCodes);

    /**
     * 根据id批量获取位置
     * @param positionIds
     * @param includeSub
     * @return
     */
    List<PositionRes> getPositions(List<Integer> positionIds, boolean includeSub);
}
