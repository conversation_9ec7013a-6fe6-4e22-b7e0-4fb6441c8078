package com.mi.oa.asset.commons.config.api.function;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class DownTaskRes implements Serializable {
    private Integer id;
    private String businessKey;

    private String funcCode;

    private String taskName;

    private String taskStatus;

    private String param;

    private String url;

    private String remark;

    /**
     * 创建人账号
     */
    private String createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人账号
     */
    private String updateUser;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

    public DownTaskRes() {
    }
}
