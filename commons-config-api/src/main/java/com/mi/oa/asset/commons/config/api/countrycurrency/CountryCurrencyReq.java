package com.mi.oa.asset.commons.config.api.countrycurrency;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Data
@NoArgsConstructor
public class CountryCurrencyReq {

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 币种代码
     */
    @Size(min = 0, max = 3, message = "The currency code must have a length less than or equal 3.")
    private String currencyCode;

    /**
     * 币种名称
     */
    private String currencyName;

    /**
     * 币种符号
     */
    @Size(min = 0, max = 3, message = "The currency symbol must have a length less than or equal 3.")
    private String currencySymbol;

    /**
     * 默认币种
     */
    private Integer defaultCurrency;

    /**
     * 国家数据表id
     */
    private Integer countryConfigId;

    /**
     * 国家/地区代码（3位）
     */
    private String countryCodeAlphaThree;

    /**
     * 国家/地区代码（2位）
     */
    private String countryCodeAlphaTwo;

    /**
     * 国家/地区
     */
    private String countryName;

    /**
     * 限制小数位
     */
    private Integer isLimitDecimal;

    /**
     * 是否删除
     */
    private Integer isDeleted;

}
