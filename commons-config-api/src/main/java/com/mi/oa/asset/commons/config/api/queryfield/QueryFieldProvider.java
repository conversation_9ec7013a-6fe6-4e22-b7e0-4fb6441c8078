package com.mi.oa.asset.commons.config.api.queryfield;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:28
 */
public interface QueryFieldProvider {

    List<QueryConfigRes> queryConfigList(String manageLine, String funId);

    QueryConfigRes queryDefaultConfig(String manageLine, String funId);

    void saveQueryConfig(QueryConfigReq queryConfigReq, Boolean isDefaultConfig);

    void deleteQueryConfig(Integer queryId);

    FieldConfigRes searchFieldConfig(String manageLine, String funId);

    FieldConfigRes searchItemFieldConfig(String manageLine, String businessLine, String funId, String type);

    void saveFieldConfig(FieldConfigReq fieldConfigReq);
}
