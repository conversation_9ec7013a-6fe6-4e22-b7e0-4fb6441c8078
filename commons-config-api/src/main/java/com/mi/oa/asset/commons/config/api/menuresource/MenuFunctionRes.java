package com.mi.oa.asset.commons.config.api.menuresource;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 菜单功能权限v2
 *
 * <AUTHOR>
 * @date 2023/10/23 19:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MenuFunctionRes implements Serializable {

    private static final long serialVersionUID = 19548872743880963L;

    @HttpApiDocClassDefine(value = "管理线id")
    private String id;

    @HttpApiDocClassDefine(value = "管理线名称")
    private String name;

    @HttpApiDocClassDefine(value = "子菜单功能")
    private List<MenuRes> children;

    @HttpApiDocClassDefine(value = "功能资源")
    private List<String> permissionResource;

    @HttpApiDocClassDefine(value = "业务线资源")
    private List<MenuBusinessRes> businessLineResource;

}
