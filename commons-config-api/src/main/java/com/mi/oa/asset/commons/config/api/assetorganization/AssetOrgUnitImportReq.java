package com.mi.oa.asset.commons.config.api.assetorganization;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 批量导入组织结构
 * @Date 2024/4/24 11:40
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssetOrgUnitImportReq implements Serializable {

    private static final long serialVersionUID = 6188033450457842680L;

    @HttpApiDocClassDefine(value = "业务线")
    @NotEmpty(message = "业务线不能为空")
    private String businessLine;

    @HttpApiDocClassDefine(value = "是否自动同步组织结构")
    private Integer isAutoSync;

    @HttpApiDocClassDefine(value = "组织编码")
    @NotEmpty(message = "导入组织编码不能为空")
    private List<String> orgCodes;


}
