package com.mi.oa.asset.commons.config.api.assetorganization;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/2 10:56
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssetOrgQueryReq implements Serializable {

    private static final long serialVersionUID = -29504325949331217L;

    /**
     * 组织单元id列表
     */
    private List<Integer> orgIds;

    /**
     * 组织单元编码
     */
    private String orgCode;

    /**
     * 组织单元编码列表
     */
    private List<String> orgCodes;

    /**
     * 上级编码
     */
    private String parentCode;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 业务线列表
     */
    private List<String> businessLines;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 是否虚拟组织
     */
    private Boolean isVirtual;

    /**
     * 是否资产管理组织
     */
    private Boolean isAssetManageOrg;

    /**
     * 是否资产使用组织
     */
    private Boolean isAssetUseOrg;

    /**
     * 组织类型
     */
    private String orgType;

    /**
     * 组织类型列表
     */
    private List<String> orgTypes;

    /**
     * 搜索关键字
     */
    private String keyword;

}
