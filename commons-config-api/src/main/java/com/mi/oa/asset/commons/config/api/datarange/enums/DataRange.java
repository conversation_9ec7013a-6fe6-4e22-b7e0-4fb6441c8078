package com.mi.oa.asset.commons.config.api.datarange.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/11/27 9:51
 * @desciption: hr在职状态
 */

@Getter
@AllArgsConstructor
public enum DataRange {
    ALL("all", "全部范围"),
    USE_DEPT("use_dept", "使用部门所属范围"),
    EMPLOYEE("employee", "员工所属范围"),
    BUSINESS_ROLE("business_role", "业务角色所属范围"),
    WAREHOUSE("warehouse", "仓库所属范围"),
    ;

    private final String code;

    private final String desc;


}
