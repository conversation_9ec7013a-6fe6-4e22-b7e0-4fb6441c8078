package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

import java.util.List;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FunctionReq implements Serializable {
    @HttpApiDocClassDefine(value = "id", required = false, description = "id")
    private Integer id;

    @HttpApiDocClassDefine(value = "管理类型: 0 默认系统，1 管理线，2 业务线", required = false, description = "管理类型 0 默认系统，1 管理线，2 业务线")
    private Integer manageType;

    @HttpApiDocClassDefine(value = "业务线", required = false, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "功能编码", required = false, description = "功能编码")
    private String code;

    @HttpApiDocClassDefine(value = "功能名称", required = false, description = "功能名称")
    private String funName;

    @HttpApiDocClassDefine(value = "英文名称", required = false, description = "英文名称")
    private String enName;

    @HttpApiDocClassDefine(value = "表名称", required = false, description = "表名称")
    private String tableName;

    @HttpApiDocClassDefine(value = "fromSql", required = false, description = "fromSql")
    private String fromSql;

    @HttpApiDocClassDefine(value = "whereSql", required = false, description = "whereSql")
    private String whereSql;

    @HttpApiDocClassDefine(value = "orderSql", required = false, description = "orderSql")
    private String orderSql;

    @HttpApiDocClassDefine(value = "groupSql", required = false, description = "groupSql")
    private String groupSql;
}
