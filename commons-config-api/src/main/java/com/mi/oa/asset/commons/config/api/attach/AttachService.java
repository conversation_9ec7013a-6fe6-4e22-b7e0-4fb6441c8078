package com.mi.oa.asset.commons.config.api.attach;

import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/19 15:29
 **/
public interface AttachService {

    /**
     * 上传单个附件
     *
     * @param fileNames
     * @return
     */
    AttachRes uploadAttach(String fileNames);

    /**
     * 批量上传附件
     *
     * @param fileNames
     * @return
     */
    List<AttachRes> uploadAttachBatch(List<String> fileNames);

    /**
     * 根据文件名删除附件
     *
     * @param fileName
     * @return
     */
    boolean deleteFDSFileByFileName(String fileName);

    /**
     * 根据文件名下载文件
     *
     * @param fileName
     * @return
     */
    ResponseEntity<byte[]> downloadFile(String fileName, String originName);

    /**
     * 根据文件标识，从附件表获取文件名，下载文件
     *
     * @param fileFlag
     * @return
     */
    ResponseEntity<byte[]> downloadTemplateFile(String fileFlag);
}
