package com.mi.oa.asset.commons.config.api.systemvar;

import com.mi.oa.asset.common.enums.BusinessLine;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/3/19 14:56
 */

public interface SystemVarProvider {

    /**
     * 保存系统变量
     * @param req
     */
    void saveSystemVar(SystemVarDetail req);

    /**
     * 获取系统变量
     * @param varCode
     * @param businessLine
     * @return
     */
    SystemVarDetail getSystemVar(String varCode, BusinessLine businessLine);

    /**
     * 获取系统变量值
     * @param varCode
     * @param businessLine
     * @return
     */
    String getSystemVarValue(String varCode, BusinessLine businessLine);

    /**
     * 获取多个系统变量值
     * @param varCodes
     * @param businessLine
     * @return
     */
    Map<String, String> getSystemVarValue(List<String> varCodes, BusinessLine businessLine);

    /**
     * 根据业务线查询资产编码生成规则
     * @param businessLine
     * @return
     */
    AssetCodeRuleConfig getAssetCodeRuleConfig(BusinessLine businessLine);
}
