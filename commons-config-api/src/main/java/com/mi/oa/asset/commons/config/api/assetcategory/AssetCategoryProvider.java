package com.mi.oa.asset.commons.config.api.assetcategory;

import com.xiaomi.mit.api.PageData;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:25
 */

public interface AssetCategoryProvider {

    /**
     * 保存资产分类
     * @param req
     */
    Integer saveAssetCategory(SaveAssetCategoryReq req);

    /**
     * 根据业务线获取全部分类
     *
     * @param businessLineCodes
     */
    List<AssetCategoryRes> getAllAssetCategory(List<String> businessLineCodes);

    /**
     * 获取单个分类信息
     * @param cateId
     * @return
     */
    AssetCategoryRes getAssetCategory(Integer cateId);

    /**
     * 批量删除分类
     * @param cateIds
     */
    void deleteAssetCategory(List<Integer> cateIds);

    /**
     * 分类分页
     * @param businessLineCodes
     * @param keyword
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<AssetCategoryRes> getAssetCategoryPageData(List<String> businessLineCodes, String keyword, Integer pageNum, Integer pageSize);

    /**
     * 分类分页
     * @param cateId
     * @param keyword
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<AssetCategoryRes> getAssetCategoryPageData(Integer cateId, String keyword, Integer pageNum, Integer pageSize);

    /**
     * 查询管理线下所有设备分类信息
     * @param businessLineCodes
     * @return
     */
    List<AssetCategoryX5Res> allAssetCategory(List<String> businessLineCodes);

    /**
     * 导入标准分类
     * @param req
     */
    void importStdCategory(ImportStdCategoryReq req);

    /**
     * 根据分类编码获取分类信息
     * @param businessLine
     * @param cateCode
     * @return
     */
    AssetCategoryRes getAssetCategoryByCode(String businessLine, String cateCode);

    /**
     * 根据分类编码获取分类信息
     * @param businessLine
     * @param cateCodes
     * @return
     */
    List<AssetCategoryRes> getAssetCategoryByCode(String businessLine, List<String> cateCodes);

    /**
     * 根据id批量获取分类
     * @param cateIds
     * @param includeSub
     * @return
     */
    List<AssetCategoryRes> getAssetCategoryByIds(List<Integer> cateIds, boolean includeSub);

    /**
     * 同步资产分类
     * @param businessLine
     */
    void syncAssetCategory(String businessLine);
}
