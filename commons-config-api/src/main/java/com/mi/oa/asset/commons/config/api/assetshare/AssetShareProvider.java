package com.mi.oa.asset.commons.config.api.assetshare;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-01-20 17:47
 */
public interface AssetShareProvider {
    /**
     * 根据业务线的操作场景和操作人的所在部门查询共享资产(部门需处理包含子级部门)
     *
     * @param businessLine
     * @param deptCode
     * @param shareScene
     * @return
     */
    String queryShareList(String businessLine, String userCode, String deptCode, String shareScene);

    /**
     * 根据业务线的操作场景和操作人的所在部门查询共享资产(部门需处理包含子级部门)
     *
     * @param businessLine
     * @param userCode
     * @param deptCode
     * @param shareScene
     * @param shareClient
     * @return
     */
    String queryShareList(String businessLine, String userCode, String deptCode, String shareScene, String shareClient);

    /**
     * 查询当前登录人指定业务线下的共享资产
     *
     * @param conditionReqs
     * @param businessLine
     * @param shareScene
     * @return
     */
    List<Map<String, String>> customQueryShareAsset(List<ShareConditionReq> conditionReqs, List<String> businessLine, String shareScene);

    /**
     * 查询当前登录人指定业务线下的共享资产
     *
     * @param conditionReqs
     * @param businessLine
     * @param shareScene
     * @param shareClient
     * @return
     */
    List<Map<String, String>> customQueryShareAsset(List<ShareConditionReq> conditionReqs, List<String> businessLine, String shareScene, String shareClient);
}
