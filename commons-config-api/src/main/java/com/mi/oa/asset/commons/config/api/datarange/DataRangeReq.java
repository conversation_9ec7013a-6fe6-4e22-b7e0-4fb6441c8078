package com.mi.oa.asset.commons.config.api.datarange;

import com.mi.oa.infra.oaucf.core.dto.Req;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

@Data
public class DataRangeReq implements Serializable {
    @HttpApiDocClassDefine(value = "数据范围，多个逗号分隔", required = false, description = "数据范围")
    private String dataRang;

    @HttpApiDocClassDefine(value = "业务线", required = false, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "使用部门编码", required = false, description = "使用部门编码")
    private String useDeptCode;

    @HttpApiDocClassDefine(value = "员工账号", required = false, description = "员工账号")
    private String userName;

    @HttpApiDocClassDefine(value = "公司编码，查成本中心必传", required = false, description = "公司编码")
    private String companyCode;

    @HttpApiDocClassDefine(value = "仓库编码", required = false, description = "仓库编码")
    private String warehouseCode;

    @HttpApiDocClassDefine(value = "搜索key", required = false, description = "搜索key")
    private String key;

    public DataRangeReq() {
    }
}
