
package com.mi.oa.asset.commons.config.api.warehouse.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.mi.oa.asset.common.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WarehouseTypeEn implements IEnum<WarehouseTypeEn> {

    ASSET("asset", "Asset Warehouse"),
    ATTACHMENT("attachment", "Spare Parts Warehouse"),
    APP_PURCHASE("app_purchase", "Inside purchase"),
    ;
    @JsonValue
    private final String code;

    private final String desc;

    public static WarehouseTypeEn getByCode(String code) {
        if(null == code) {
            return null;
        }

        for (WarehouseTypeEn compensateType: WarehouseTypeEn.values()) {
            if (compensateType.code.equals(code)) {
                return compensateType;
            }
        }
        return null;
    }

    public static WarehouseTypeEn getByDesc(String desc) {
        if(StringUtils.isBlank(desc)) {
            return null;
        }

        for (WarehouseTypeEn compensateType: WarehouseTypeEn.values()) {
            if (compensateType.desc.equals(desc)) {
                return compensateType;
            }
        }
        return null;
    }
}
