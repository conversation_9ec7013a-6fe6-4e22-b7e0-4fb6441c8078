package com.mi.oa.asset.commons.config.api.businessline.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum BusinessLineKeyEnum {

    ASSET_RECEIPT("assetReceipt", "资产入库设置"),
    ASSET_RECEIPT_REC("assetReceiptReceived", "直接收货完成"),
    ASSET_RECEIPT_ACC("assetReceiptAcceptance", "验收入库完成"),
    ASSET_RECEIPT_OTHER("assetReceiptOther", "其他入库完成"),
    ASSET_RECEIPT_COLLECT("assetReceiptCollect", "领用出库完成"),
    ASSET_RECEIPT_CAR("assetReceiptCar", "车辆入库完成"),
    ASSET_RECEIPT_SAP("assetReceiptSap", "推送SAP创建资产卡片"),
    DEFAULT_MN_DEPT("defaultMgDept", "默认管理部门"),
    DEFAULT_MN_DEPT_NAME("defaultMgDeptName", "默认管理部门名称"),
    REQUISITION_COMPANY("requisitionCompany", "领用出库主体"),
    APPLY("apply", "申请人主体"),
    USE("use", "使用人主体"),
    NO_CHANGE("noChange", "不改变主体"),
    STOCK_ALLOT_OUT("stockAllotOut", "库存调拨出库"),
    STOCK_ALLOT_OUT_AUTO("stockOutAuto", "库存调拨审批通过后，自动创建出库单"),
    STOCK_ALLOT_OUT_HAND("stockOutHand", "库存调拨审批通过后，手动创建出库单"),
    STOCK_ALLOT_DEFAULT_MN_DEPT("stockOutDefaultMgDept", "出库默认管理部门"),
    STOCK_ALLOT_DEFAULT_MN_DEPT_NAME("stockOutDefaultMgDeptName", "出库默认管理部门名称"),

    STOCK_ALLOT_OUT_AUTO_CONFIRM("stockOutAutoConfirm", "库存调拨审批通过后，自动创建出库单并确认出库"),
    STOCK_ALLOT_IN("stockAllotIn", "库存调拨入库"),
    STOCK_ALLOT_IN_AUTO("stockInAuto", "库存调拨审批通过后，自动创建入库单"),
    STOCK_ALLOT_IN_HAND("stockInHand", "库存调拨审批通过后，手动创建入库单"),
    ;


    private final String key;
    @JsonValue
    private final String desc;

    BusinessLineKeyEnum(String key, String desc ) {
        this.key = key;
        this.desc = desc;

    }

}
