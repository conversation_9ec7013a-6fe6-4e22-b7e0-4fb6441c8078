package com.mi.oa.asset.commons.config.api.assetquit;


import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 离职员工信息表 request 请求对象
 * <AUTHOR>
 * @date 2024-04-12 02:05:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssetQuitRes implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 员工姓名
	 */
	@HttpApiDocClassDefine(value = "员工姓名")
	private String userName;
	/**
	 * 员工账号
	 */
	@HttpApiDocClassDefine(value = "员工账号")
	private String userCode;
	/**
	 * 所属部门
	 */
	@HttpApiDocClassDefine(value = "所属部门")
	private String deptName;
	/**
	 * 申请离职日期
	 */
	@HttpApiDocClassDefine(value = "申请离职日期")
	private Date createDate;
	/**
	 * 所属部门ID
	 */
	@HttpApiDocClassDefine(value = "所属部门ID")
	private String deptId;
	/**
	 * 实际离职日期
	 */
	@HttpApiDocClassDefine(value = "实际离职日期")
	private Date quitDate;
	/**
	 * 岗位代码
	 */
	@HttpApiDocClassDefine(value = "岗位代码")
	private String postCode;
	/**
	 * 岗位名称
	 */
	@HttpApiDocClassDefine(value = "岗位名称")
	private String postName;
	/**
	 * 员工工号
	 */
	@HttpApiDocClassDefine(value = "员工工号")
	private String empCode;
	/**
	 * 部门编码
	 */
	@HttpApiDocClassDefine(value = "部门编码")
	private String deptCode;
	/**
	 * 手机号
	 */
	@HttpApiDocClassDefine(value = "手机号")
	private String phone;
	/**
	 * 岗位ID
	 */
	@HttpApiDocClassDefine(value = "岗位ID")
	private String postId;
	/**
	 * 主键
	 */
	@HttpApiDocClassDefine(value = "主键")
	private String quitId;
	/**
	 * 添加人ID
	 */
	@HttpApiDocClassDefine(value = "添加人ID")
	private String addUserid;
	/**
	 * 添加时间
	 */
	@HttpApiDocClassDefine(value = "添加时间")
	private Date addDate;
	/**
	 * 修改人ID
	 */
	@HttpApiDocClassDefine(value = "修改人ID")
	private String modifyUserid;
	/**
	 * 修改时间
	 */
	@HttpApiDocClassDefine(value = "修改时间")
	private Date modifyDate;
	/**
	 * 系统租户ID
	 */
	@HttpApiDocClassDefine(value = "系统租户ID")
	private String tenantId;
	/**
	 * 离职状态
	 */
	@HttpApiDocClassDefine(value = "离职状态")
	private String quitState;
	/**
	 * 工作地点
	 */
	@HttpApiDocClassDefine(value = "工作地点")
	private String officeAddress;
	/**
	 * 邮箱
	 */
	@HttpApiDocClassDefine(value = "邮箱")
	private String email;
	/**
	 * 是否确认离职
	 */
	@HttpApiDocClassDefine(value = "是否确认离职")
	private String isQuit;
	/**
	 * 撤销日期
	 */
	@HttpApiDocClassDefine(value = "撤销日期")
	private Date revokeDate;
	/**
	 * 操作人账号
	 */
	@HttpApiDocClassDefine(value = "操作人账号")
	private String confirmUser;
	/**
	 * 操作人账号
	 */
	@HttpApiDocClassDefine(value = "操作人账号")
	private String confirmUsercode;
	/**
	 * 确认离职时间
	 */
	@HttpApiDocClassDefine(value = "确认离职时间")
	private Date confirmDate;
	/**
	 * 是否逾期
	 */
	@HttpApiDocClassDefine(value = "是否逾期")
	private String isExceed;
	/**
	 * 逾期天数
	 */
	@HttpApiDocClassDefine(value = "逾期天数")
	private BigDecimal exceedDay;
	/**
	 * 资产数量(使用中)
	 */
	@HttpApiDocClassDefine(value = "资产数量(使用中)")
	private BigDecimal useNum;
	/**
	 * 资产数量(异动中)
	 */
	@HttpApiDocClassDefine(value = "资产数量(异动中)")
	private BigDecimal numAction;
	/**
	 * 一级部门编码
	 */
	@HttpApiDocClassDefine(value = "一级部门编码")
	private String deptCodeLv1;
	/**
	 * 一级部门名称
	 */
	@HttpApiDocClassDefine(value = "一级部门名称")
	private String deptNameLv1;
	/**
	 * 部门名称(全)
	 */
	@HttpApiDocClassDefine(value = "部门名称(全)")
	private String fullDeptName;
	/**
	 * 在管公共资产
	 */
	@HttpApiDocClassDefine(value = "在管公共资产")
	private String havePubAsset;
	/**
	 * 国家编码
	 */
	@HttpApiDocClassDefine(value = "国家编码")
	private String country;
	/**
	 * 国家名称
	 */
	@HttpApiDocClassDefine(value = "国家名称")
	private String countryName;
	/**
	 * 是否推送PS
	 */
	@HttpApiDocClassDefine(value = "是否推送PS")
	private String isPushPs;
	/**
	 * 资产价值总额
	 */
	@HttpApiDocClassDefine(value = "资产价值总额")
	private BigDecimal totalMoney;
	/**
	 * 离职资产处置单主键
	 */
	@HttpApiDocClassDefine(value = "离职资产处置单主键")
	private String scrapId;
	/**
	 * 是否删除
	 */
	@HttpApiDocClassDefine(value = "是否删除")
	private String isDelete;

}
