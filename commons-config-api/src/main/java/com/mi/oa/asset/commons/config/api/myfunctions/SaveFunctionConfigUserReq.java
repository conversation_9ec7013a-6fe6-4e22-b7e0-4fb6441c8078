package com.mi.oa.asset.commons.config.api.myfunctions;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 员工端用户首页功能排序
 * @Date 2025/5/6 21:27
 **/
@Data
public class SaveFunctionConfigUserReq implements Serializable {
    private static final long serialVersionUID = 6103921289480252890L;

    /**
     * 管理线 manage_line
     */
    @HttpApiDocClassDefine(value = "管理线编码")
    private String manageLine;

    /**
     * 管理线名称 manage_line_name
     */
    @HttpApiDocClassDefine(value = "管理线名称")
    private String manageLineName;

    /**
     * 排序
     */
    @HttpApiDocClassDefine(value = "排序")
    private Integer sort;
}
