package com.mi.oa.asset.commons.config.api.use;

import com.mi.oa.infra.oaucf.core.dto.Req;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * 用途
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UseReq extends Req {

    private static final long serialVersionUID = 951052897294786629L;
    @NotBlank(message = "业务线不能为空")
    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "用途编码")
    private String useCode;

    @HttpApiDocClassDefine(value = "用途名称")
    private String useName;

    @HttpApiDocClassDefine(value = "用途类别")
    private Integer useType;

    @HttpApiDocClassDefine(value = "适用范围")
    private Integer scope;

    @HttpApiDocClassDefine(value = "员工账号")
    private String userCode;
}