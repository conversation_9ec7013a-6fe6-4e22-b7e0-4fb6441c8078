package com.mi.oa.asset.commons.config.api.assetcode;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @<PERSON> zhan
 * @Date 2024-06-25 16:40
 */
@Data
public class FieldRuleConfigReq implements Serializable {
    private static final long serialVersionUID = 5091230415152971946L;

    @HttpApiDocClassDefine(value = "台账字段", description = "台账字段，不可调整")
    @NotBlank(message = "规则编码不能为空")
    private String code;

    @HttpApiDocClassDefine(value = "名称", description = "名称，不可调整")
    @NotBlank(message = "规则描述不能为空")
    private String name;

    @HttpApiDocClassDefine(value = "连接线", description = "连接线或文本框或流水号长度")
    @NotBlank(message = "规则内容不能为空")
    private String connector;

    @HttpApiDocClassDefine(value = "策略模式", description = "策略模式，不可调整")
    @NotBlank(message = "策略模式不能为空")
    private String strategy;

    @HttpApiDocClassDefine(value = "格式化", description = "对值内容进行特殊处理，通过策略规则指定格式标准，不可调整")
    @NotNull(message = "格式化规则不能为空")
    private String format;
}
