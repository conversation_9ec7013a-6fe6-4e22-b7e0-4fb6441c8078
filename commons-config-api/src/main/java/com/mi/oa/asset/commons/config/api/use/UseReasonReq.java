package com.mi.oa.asset.commons.config.api.use;

import com.mi.oa.infra.oaucf.core.dto.Req;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.*;

/**
 * 用途原因
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UseReasonReq extends Req {

    private static final long serialVersionUID = -7790090936764766629L;
    @HttpApiDocClassDefine(value = "用途ID")
    private Integer useId;

    @HttpApiDocClassDefine(value = "用途理由")
    private String useReason;
}