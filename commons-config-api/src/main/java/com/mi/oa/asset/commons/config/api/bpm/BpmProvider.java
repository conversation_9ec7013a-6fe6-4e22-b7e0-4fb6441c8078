package com.mi.oa.asset.commons.config.api.bpm;

import java.util.List;

public interface BpmProvider {

    /**
     * 查询审批历史记录
     * 默认不需要预测审批流
     *
     * @param businessKey
     * @return
     */
    List<BpmApprovalTaskRes> getApprovalTaskList(String businessKey);

    /**
     * 查询审批历史记录
     *
     * @param businessKey
     * @param needPredict 是否需要预测审批路径
     * @return
     */
    List<BpmApprovalTaskRes> getApprovalTaskList(String businessKey, boolean needPredict);

    /**
     * 查询审批历史记录
     *
     * @param businessKey
     * @param needPredict 是否需要预测审批路径
     * @param language
     * @return
     */
    List<BpmApprovalTaskRes> getApprovalTaskList(String businessKey, boolean needPredict, String language);

}
