package com.mi.oa.asset.commons.config.api.myfunctions;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/6 19:15
 * @description 我的常用功能排序
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class MyFunctionsSortResp implements Serializable {
    private static final long serialVersionUID = 3266833408631083074L;
    /**
     * 管理线编码
     */
    @HttpApiDocClassDefine(value = "管理线编码")
    private String manageLineCode;
    /**
     * 功能
     */
    @HttpApiDocClassDefine(value = "功能编码")
    private List<String> resourceCode;
    /**
     * 是否默认
     */
    @HttpApiDocClassDefine(value = "是否默认")
    private boolean isDefault;

}
