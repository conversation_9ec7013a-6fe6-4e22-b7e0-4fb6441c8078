package com.mi.oa.asset.commons.config.api.assetshare;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-02-27 14:40
 */
@Data
public class ShareAssetAllotReq implements Serializable {
    private static final long serialVersionUID = -5060243347402255229L;

    @HttpApiDocClassDefine(value = "资产ID", required = true, description = "资产ID")
    @NotNull(message = "资产ID不能为空")
    private Integer assetId;

    @HttpApiDocClassDefine(value = "共享数量", required = true, description = "共享数量")
    @NotNull(message = "共享数量不能为空")
    private Integer shareQuantity;
}
