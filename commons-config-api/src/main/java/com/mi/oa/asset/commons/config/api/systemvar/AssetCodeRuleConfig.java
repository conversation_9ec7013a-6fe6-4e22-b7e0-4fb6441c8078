package com.mi.oa.asset.commons.config.api.systemvar;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-07-08 16:46
 */
@Data
public class AssetCodeRuleConfig implements Serializable {
    private static final long serialVersionUID = 6053819181344054217L;
    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 是否可手动录入
     */
    private Boolean isInput;

    /**
     * 是否使用SN作为编码
     */
    private Boolean isSn;

    /**
     * 选择生成编号的字段配置
     */
    private List<FieldRuleConfig> selectedItemList;

    @Data
    public static class FieldRuleConfig implements Serializable {
        private static final long serialVersionUID = 5696488957809255893L;
        /**
         * 资产台账对应的字段名称，自定义文本使用custom表示，流水号使用serial_code表示
         */
        private String code;

        /**
         * 名称：例如：验收日期-年月日（8位）
         */
        private String name;

        /**
         * 资产台账字段一般表示为连接符，选项有：无、-；自定义文本可输入1-5位的数字或字母;流水号可输入5-8位之间的正整数
         */
        private String connector;

        /**
         * 策略模式
         */
        private String strategy;

        /**
         * 默认为空字符串，用于格式化处理
         */
        private String format;
    }
}
