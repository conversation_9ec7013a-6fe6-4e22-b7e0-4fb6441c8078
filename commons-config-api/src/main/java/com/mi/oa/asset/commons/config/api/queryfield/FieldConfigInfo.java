package com.mi.oa.asset.commons.config.api.queryfield;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/10 20:21
 */

@Data
public class FieldConfigInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    @HttpApiDocClassDefine(value = "字段编码")
    @NotBlank(message = "字段编码不能为空")
    private String dataKey;

    @HttpApiDocClassDefine(value = "字段名称")
    private String dataValue;
    @HttpApiDocClassDefine(value = "当前字段必须勾选")
    @NotNull(message = "是否必须勾选信息不能为空")
    private Boolean disabled;
    @HttpApiDocClassDefine(value = "是否展示隐藏")
    @NotNull(message = "是否展示隐藏信息不能为空")
    private Boolean hidden;

    @HttpApiDocClassDefine(value = "是否台账字段  true 是 false 否")
    private Boolean accountField;
}
