
package com.mi.oa.asset.commons.config.api.warehouse.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.mi.oa.asset.common.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/7/20 10:32
 */
@Getter
@AllArgsConstructor
public enum WarehouseType implements IEnum<WarehouseType> {

    ASSET("asset", "资产仓"),
    ATTACHMENT("attachment", "备件仓"),
    APP_PURCHASE("app_purchase", "内购仓"),
    ;
    @JsonValue
    private final String code;

    private final String desc;

    public static WarehouseType getByCode(String code) {
        if(null == code) {
            return null;
        }

        for (WarehouseType compensateType: WarehouseType.values()) {
            if (compensateType.code.equals(code)) {
                return compensateType;
            }
        }
        return null;
    }

    public static WarehouseType getByDesc(String desc) {
        if(StringUtils.isBlank(desc)) {
            return null;
        }

        for (WarehouseType compensateType: WarehouseType.values()) {
            if (compensateType.desc.equals(desc)) {
                return compensateType;
            }
        }
        return null;
    }
}
