package com.mi.oa.asset.commons.config.api.assetcode;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024-06-25 16:58
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssetCodeRuleReq implements Serializable {
    private static final long serialVersionUID = 8195935950242616751L;

    @HttpApiDocClassDefine(value = "业务线", required = true, description = "业务线")
    @NotBlank(message = "业务线不能为空")
    private String businessLine;

    @HttpApiDocClassDefine(value = "是否支持手动录入", description = "是否支持手动录入")
    @Builder.Default
    private Boolean isInput = false;

    @HttpApiDocClassDefine(value = "是否使用SN作为资产编码", description = "是否使用SN作为资产编码")
    @Builder.Default
    private Boolean isSn = false;

    @HttpApiDocClassDefine(value = "已选择配置", description = "保存在数据表中的json")
    @Valid
    @NotNull(message = "已选择配置信息不能为空")
    private List<FieldRuleConfigReq> selectedItemList;
}
