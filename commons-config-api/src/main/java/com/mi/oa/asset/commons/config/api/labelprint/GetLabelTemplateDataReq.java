package com.mi.oa.asset.commons.config.api.labelprint;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/02/17 19:15
 * @description 获取生成标签模版数据列表
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class GetLabelTemplateDataReq {

    @HttpApiDocClassDefine(value = "资产台账id列表")
    @NotEmpty(message = "资产台账id列表不能为空！")
    private List<String> ids;

    @HttpApiDocClassDefine(value = "数据来源,1-设备sn,2-串号明细")
    @NotEmpty(message = "数据来源不能为空！")
    private String dataSource;

    @HttpApiDocClassDefine(value = "模版id")
    @NotNull(message = "模版id不能为空！")
    private Integer templateId;


}
