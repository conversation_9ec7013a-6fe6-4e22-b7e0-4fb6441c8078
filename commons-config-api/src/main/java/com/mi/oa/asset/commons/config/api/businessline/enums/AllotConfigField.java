package com.mi.oa.asset.commons.config.api.businessline.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/16 19:07
 */

@Getter
public enum AllotConfigField {

    IN_USER_NAME("inUserName", "调入人", Arrays.asList(AllotConfigDataRage.ALL, AllotConfigDataRage.BIZ_ROLE)),
    IN_LOCATION_CODE("inLocationCode", "调入位置", Collections.singletonList(AllotConfigDataRage.ALL)),
    IN_USE_DEPT_CODE("inUseDeptCode", "调入使用部门", Arrays.asList(AllotConfigDataRage.ALL, AllotConfigDataRage.USE_DEPT),
            Arrays.asList(AllotConfigLimitData.ROLE_DEPT, AllotConfigLimitData.EMP_DEPT)),
    IN_MANAGE_DEPT_CODE("inManageDeptCode", "调入管理部门", Arrays.asList(AllotConfigDataRage.ALL, AllotConfigDataRage.MANAGE_DEPT),
            Arrays.asList(AllotConfigLimitData.ROLE_DEPT, AllotConfigLimitData.USE_MANAGE_DEPT)),
    IN_COMPANY_CODE("inCompanyCode", "调入公司主体", Collections.singletonList(AllotConfigDataRage.ALL)),
    ADDRESS("inAddress", "详细地址", Collections.singletonList(AllotConfigDataRage.ALL)),
    ;

    private final String code;

    private final String desc;

    private final List<AllotConfigDataRage> dataRageList;

    private List<AllotConfigLimitData> dataLimitList;

    AllotConfigField(String code, String desc, List<AllotConfigDataRage> dataRageList) {
        this.code = code;
        this.desc = desc;
        this.dataRageList = dataRageList;
    }

    AllotConfigField(String code, String desc, List<AllotConfigDataRage> dataRageList, List<AllotConfigLimitData> dataLimitList) {
        this.code = code;
        this.desc = desc;
        this.dataRageList = dataRageList;
        this.dataLimitList = dataLimitList;
    }

    // getByCode方法
    public static AllotConfigField getByCode(String code) {
        for (AllotConfigField value : AllotConfigField.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
