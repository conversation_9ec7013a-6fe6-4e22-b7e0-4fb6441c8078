package com.mi.oa.asset.commons.config.api.countryconfig;

import java.util.List;

public interface CountryConfigProvider {

    /**
     * 获取国所有国家列表和对应的业务线
     * @return
     */
    List<CountryConfigRes> getCountryConfigList();

    CountryConfigRes getById(Integer countryConfigId);

    void saveOrUpdate(CountryConfigReq req);

    void removeByIds(List<Integer> ids);

    /**
     * 根据地区id 获取国家列表
     * @param regionId
     * @return
     */
    List<CountryConfigRes> getByRegionId(int regionId);

    /**
     * 获取所有国家列表
     * @return
     */
    List<CountryConfigRes> getAllCountries();

    /**
     * 根据国家代码获取国家配置
     * @param countryCodeAlphaThree 国家代码
     * @return 国家配置
     */
    CountryConfigRes getByThreeCode(String countryCodeAlphaThree);
}
