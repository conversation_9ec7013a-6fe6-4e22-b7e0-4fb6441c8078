package com.mi.oa.asset.commons.config.api.user;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Desc 员工信息
 * <AUTHOR>
 * @Date 2021/11/4 15:21
 */

@Data
public class EmployeeInfoRes implements Serializable {

    private static final long serialVersionUID = 2155781165408686572L;
    private String userName;

    private String displayName;

    private String employeeId;

    private String deptId;

    private String deptName;

    private String deptIdLv1;

    private String deptNameLv1;

    private String deptIdLv2;

    private String deptNameLv2;

    private String deptIdLv3;

    private String deptNameLv3;

    private String deptIdLv4;

    private String deptNameLv4;

    private String deptIdLv5;

    private String deptNameLv5;

    private String email;

    private String hrStatus;

    private String legalId;

    private String companyName;

    private String companyCode;

    private String companyCountryCodeChar3;

    private String costCenter;

    private String costCenterDesc;

    private String deptCostCenter;

    private String deptCostCenterDesc;

    private String leaderUserName;

    private String leaderDisplayName;

    private Date entryDate;

    public String getCompanyCode() {
        if (null == costCenter || 0 == costCenter.length() || costCenter.length() < 5) return null;

        return costCenter.substring(1, 5);
    }

    public String getFullDeptName() {
        StringBuilder fullDeptName = new StringBuilder(deptNameLv1);

        if (null != deptNameLv2 && 0 != deptNameLv2.length()) {
            fullDeptName.append("-").append(deptNameLv2);
        }

        if (null != deptNameLv3 && 0 != deptNameLv3.length()) {
            fullDeptName.append("-").append(deptNameLv3);
        }

        if (null != deptNameLv4 && 0 != deptNameLv4.length()) {
            fullDeptName.append("-").append(deptNameLv4);
        }

        if (null != deptNameLv5 && 0 != deptNameLv5.length()) {
            fullDeptName.append("-").append(deptNameLv5);
        }

        return fullDeptName.toString();
    }

    public List<String> getAllDeptCode() {
        List<String> deptCodes = new ArrayList<>(5);
        if (StringUtils.isNotBlank(deptIdLv1)) {
            deptCodes.add(deptIdLv1);
        }
        if (StringUtils.isNotBlank(deptIdLv2)) {
            deptCodes.add(deptIdLv2);
        }
        if (StringUtils.isNotBlank(deptIdLv3)) {
            deptCodes.add(deptIdLv3);
        }
        if (StringUtils.isNotBlank(deptIdLv4)) {
            deptCodes.add(deptIdLv4);
        }
        if (StringUtils.isNotBlank(deptIdLv5)) {
            deptCodes.add(deptIdLv5);
        }
        return deptCodes;
    }
}


