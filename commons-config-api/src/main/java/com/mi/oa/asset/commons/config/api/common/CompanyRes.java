package com.mi.oa.asset.commons.config.api.common;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/9/18 17:58
 */

@Data
public class CompanyRes implements Serializable {

    private static final long serialVersionUID = 2221550597790781399L;
    @HttpApiDocClassDefine(value = "公司代码")
    private String companyCode;

    @HttpApiDocClassDefine(value = "公司名称")
    private String companyName;
}
