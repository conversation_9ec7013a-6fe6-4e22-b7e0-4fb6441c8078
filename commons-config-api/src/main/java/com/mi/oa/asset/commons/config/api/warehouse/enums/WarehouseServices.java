
package com.mi.oa.asset.commons.config.api.warehouse.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.mi.oa.asset.common.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/7/20 10:32
 */
@Getter
@AllArgsConstructor
public enum WarehouseServices implements IEnum<WarehouseServices> {

    COLLECT("collect", "领用"),
    BORROW("borrow", "借用"),
    RETURN("return", "退还"),
    REPAIR("repair", "维修"),
    PURCHASE("purchase", "内购"),
    LOSS("loss", "丢失"),
    SCRAP("scrap", "报废"),
    ;
    @JsonValue
    private final String code;

    private final String desc;

    public static WarehouseServices getByCode(String code) {
        if(null == code) {
            return null;
        }

        for (WarehouseServices compensateType: WarehouseServices.values()) {
            if (compensateType.code.equals(code)) {
                return compensateType;
            }
        }
        return null;
    }

    public static WarehouseServices getByDesc(String desc) {
        if(StringUtils.isBlank(desc)) {
            return null;
        }

        for (WarehouseServices compensateType: WarehouseServices.values()) {
            if (compensateType.desc.equals(desc)) {
                return compensateType;
            }
        }
        return null;
    }
}
