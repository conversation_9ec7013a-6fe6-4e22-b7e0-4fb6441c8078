package com.mi.oa.asset.commons.config.api.menuresource;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 功能资源
 *
 * <AUTHOR>
 * @date 2023/9/15 18:49
 */
@NoArgsConstructor
@Data
public class ResourceRes implements Serializable {

    private static final long serialVersionUID = 116502273544124947L;

    @HttpApiDocClassDefine(value = "功能编码")
    private String resourceCode;

    @HttpApiDocClassDefine(value = "功能名称")
    private String resourceName;

    @HttpApiDocClassDefine(value = "描述")
    private String description;

}
