package com.mi.oa.asset.commons.config.api.measurement;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class MeasurementUnitQueryReq implements Serializable {

    private static final long serialVersionUID = 2556716013028285098L;
    @HttpApiDocClassDefine(value = "计量单元编码")
    private String measureCode;

    @HttpApiDocClassDefine(value = "计量单元名称")
    private String measureName;

    @HttpApiDocClassDefine(value = "搜索关键字")
    private String keyword;
}
