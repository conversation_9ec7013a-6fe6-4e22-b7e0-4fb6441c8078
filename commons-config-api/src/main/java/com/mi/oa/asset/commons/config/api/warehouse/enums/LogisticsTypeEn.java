
package com.mi.oa.asset.commons.config.api.warehouse.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.mi.oa.asset.common.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/7/20 10:32
 */
@Getter
@AllArgsConstructor
public enum LogisticsTypeEn implements IEnum<LogisticsTypeEn> {

    SELF("self", "Self Pickup"),
    MAIL("mail", "Mail"),
    ;
    @JsonValue
    private final String code;

    private final String desc;

    public static LogisticsTypeEn getByCode(String code) {
        if(null == code) {
            return null;
        }

        for (LogisticsTypeEn compensateType: LogisticsTypeEn.values()) {
            if (compensateType.code.equals(code)) {
                return compensateType;
            }
        }
        return null;
    }

    public static LogisticsTypeEn getByDesc(String desc) {
        if(StringUtils.isBlank(desc)) {
            return null;
        }

        for (LogisticsTypeEn compensateType: LogisticsTypeEn.values()) {
            if (compensateType.desc.equals(desc)) {
                return compensateType;
            }
        }
        return null;
    }
}
