package com.mi.oa.asset.commons.config.api.assetcategory;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/26 11:32
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportStdCategoryReq implements Serializable {

    private static final long serialVersionUID = -1030795136839573228L;

    @HttpApiDocClassDefine(value = "业务线", required = true, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "导入项目", required = true, description = "导入项目")
    @Builder.Default
    private List<Item> items = new ArrayList<>();

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item implements Serializable {

        private static final long serialVersionUID = -6748270153546210792L;

        @HttpApiDocClassDefine(value = "关联采购目录编码", required = true, description = "关联采购目录编码")
        private String purchaseCatalogCode;

        @HttpApiDocClassDefine(value = "关联采购目录编码（全路径）", required = true, description = "关联采购目录编码（全路径）")
        private String purchaseCatalogCodePath;

        @HttpApiDocClassDefine(value = "关联采购目录名称", required = true, description = "关联采购目录名称")
        private String purchaseCatalogName;

        @HttpApiDocClassDefine(value = "关联采购目录名称（全路径）", required = true, description = "关联采购目录名称（全路径）")
        private String purchaseCatalogNamePath;

        @HttpApiDocClassDefine(value = "级别", required = true, description = "级别")
        private Integer level;

        @HttpApiDocClassDefine(value = "上级编码", required = true, description = "上级编码")
        private String parentCode;

        @HttpApiDocClassDefine(value = "sap分类编码", required = true, description = "sap分类编码")
        private String sapCateCode;

        @HttpApiDocClassDefine(value = "sap分类名称", required = true, description = "sap分类名称")
        private String sapCateName;

        @HttpApiDocClassDefine(value = "使用年限", required = true, description = "使用年限")
        private int useYear;
    }
}
