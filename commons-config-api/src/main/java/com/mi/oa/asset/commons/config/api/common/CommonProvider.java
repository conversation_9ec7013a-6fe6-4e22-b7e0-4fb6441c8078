package com.mi.oa.asset.commons.config.api.common;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/11 19:25
 */

public interface CommonProvider {
    /**
     * 根据供应商编码或名称信息
     * @param keyword
     * @return
     */
    ProviderRes getProvider(String keyword);

    /**
     * 根据供应商编码集合查询所有供应商信息
     * @param providerCodes 供应商编码集合
     * @return
     */
    List<ProviderRes> getProvider(List<String> providerCodes);
    /**
     * 根据国家、公司编码或名称信息
     * @param keyword
     * @return
     */
     CompanyRes getCompany(String keyword, String countryCode);

    /**
     * 获取公司信息
     * @param companyCode 公司代码
     * @return
     */
    CompanyRes getCompany(String companyCode);

    /**
     * 获取公司信息
     * @param companyCodes 公司代码
     * @return
     */
    List<CompanyRes> getBatchCompany(List<String> companyCodes);

    Integer createAsyncTask(AsyncTaskReq asyncTaskReq);

    void updateAsyncTask(AsyncTaskReq asyncTaskReq);

    AsyncTaskRes getAsyncTaskById(Integer taskId);
}
