package com.mi.oa.asset.commons.config.api.businessline;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/09 18:42
 */

@Data
public class BusinessLineReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @HttpApiDocClassDefine(value = "业务线Id")
    private Integer businessLineId;

    @HttpApiDocClassDefine(value = "业务线编码", required = true)
    @NotBlank(message = "业务线编码必填")
    private String businessLine;

    @HttpApiDocClassDefine(value = "业务线名称", required = true)
    @NotBlank(message = "业务线名称必填")
    private String businessLineName;

    @HttpApiDocClassDefine(value = "业务线英文名称")
    private String businessLineNameEn;

    @HttpApiDocClassDefine(value = "管理线编码", required = true)
    @NotBlank(message = "管理线编码必填")
    private String manageLineCode;

    @HttpApiDocClassDefine(value = "管理线名称", required = true)
    @NotBlank(message = "管理线名称必填")
    private String manageLineName;

    @HttpApiDocClassDefine(value = "使用部门名称展示方式", required = false)
    private String useDeptNameShowWay;

    @HttpApiDocClassDefine(value = "管理部门名称展示方式", required = false)
    private String manageDeptNameShowWay;

    @HttpApiDocClassDefine(value = "是否生效")
    private Boolean isEffective = true;

    @HttpApiDocClassDefine(value = "委托记账主体是否变动")
    private Boolean isModifyCompany;

    @HttpApiDocClassDefine(value = "调拨配置", required = true)
    @NotEmpty(message = "调拨配置不能为空")
    @Valid
    private List<AllotConfigReq> allotConfigs;

    @HttpApiDocClassDefine(value = "资产库存管理配置", required = false)
    private HashMap<String, String> configs;

}
