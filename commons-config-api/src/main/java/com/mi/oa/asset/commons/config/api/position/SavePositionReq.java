package com.mi.oa.asset.commons.config.api.position;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/10/16 11:14
 */

@Data
public class SavePositionReq implements Serializable {

    private static final long serialVersionUID = 6041606448199135703L;
    @HttpApiDocClassDefine(value = "位置id", required = false, description = "更新时必传")
    private Integer positionId;

    @HttpApiDocClassDefine(value = "位置编码", required = true, description = "位置编码")
    @NotBlank(message = "位置编码不能为空！")
    private String positionCode;

    @HttpApiDocClassDefine(value = "位置名称", required = true, description = "位置名称")
    @NotBlank(message = "位置名称不能为空！")
    private String positionName;

    @HttpApiDocClassDefine(value = "英文名称", required = true, description = "英文名称")
    private String positionNameEn;

    @HttpApiDocClassDefine(value = "位置类型", description = "园区、楼栋、楼层、房间、产线、工段、工序、工位")
    private String positionType;

    @HttpApiDocClassDefine(value = "上级编码", required = true, description = "上级编码")
    @NotBlank(message = "上级编码不能为空！")
    private String parentCode;

    @HttpApiDocClassDefine(value = "业务线", required = true, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "禁用", required = true, description = "禁用")
    @NotNull(message = "状态不能为空！")
    private Boolean disabled;

    @HttpApiDocClassDefine(value = "外部系统编码", required = false, description = "外部系统编码")
    private String outSysCode;

    @HttpApiDocClassDefine(value = "备注", required = false, description = "备注")
    private String remark;
}
