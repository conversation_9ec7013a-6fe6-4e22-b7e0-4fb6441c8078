package com.mi.oa.asset.commons.config.api.assetdispose;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/14 14:09
 * @description 资产处置类型
 */
public interface AssetDisposeTypeProvider {
    /**
     * 保存处置类型
     * @param req
     */
    Integer saveAssetDisposeType(SaveAssetDisposeTypeReq req);

    /**
     * 批量删除处置类型
     * @param typeIds
     */
    void deleteAssetDisposeType(List<Integer> typeIds);
}
