package com.mi.oa.asset.commons.config.api.warehouse;


import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/19 21:42
 */

public interface WarehouseProvider {

    /**
     * 保存仓库
     *
     * @param req
     * @return
     */
    Integer saveWarehouse(SaveWarehouseReq req);

    /**
     * 删除仓库
     *
     * @param ids
     */
    void deleteWarehouse(List<Integer> ids);

    /**
     * 根据编码查询
     *
     * @param code
     * @return
     */
    WarehouseRes findByCode(String code);
    /**
     * 根据编码集合查询
     * @param codes
     * @return
     */
    List<WarehouseRes> findByCodes(List<String> codes);

    /**
     * 根据业务线和服务类型获取批次仓库资源列表
     *
     * @param businessLine 业务线
     * @param service      服务类型
     * @return 批次仓库表
     */
    List<WarehouseRes> getBatchByServiceType(String businessLine, String service, String key);
    /**
     * 批量导入
     * @param file
     */
    void importWarehouse(MultipartFile file);


    /**
     * 根据业务线仓库资源列表
     * @param businessLine 业务线
     */
    List<WarehouseRes> getByBusinessLine(String businessLine);


}
