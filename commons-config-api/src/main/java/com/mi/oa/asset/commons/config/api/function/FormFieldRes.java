package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FormFieldRes implements Serializable {
    @HttpApiDocClassDefine(value = "分组", description = "分组")
    private String groupCode;

    @HttpApiDocClassDefine(value = "分组", description = "分组")
    private String groupName;

    @HttpApiDocClassDefine(value = "分组", description = "分组")
    private String enName;

    @HttpApiDocClassDefine(value = "表单字段", description = "表单字段")
    private List<FuncFieldRes> fields;

}
