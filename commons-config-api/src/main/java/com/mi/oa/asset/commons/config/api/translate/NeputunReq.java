package com.mi.oa.asset.commons.config.api.translate;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class NeputunReq {

    @HttpApiDocClassDefine(value = "键标识符", required = false, description = "键标识符")
    private String key;

    @HttpApiDocClassDefine(value = "翻译语言", required = false, description = "翻译语言")
    private String lang;

    /**
     * true：代表部署在海外机房服务
     * false：代表部署在国内机房服务
     */
    @HttpApiDocClassDefine(value = "是否海外应用", required = true, description = "是否海外应用")
    private boolean isSg ;

}
