package com.mi.oa.asset.commons.config.api.assetdispose;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/14 14:54
 * @description 保存资产处置类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveAssetDisposeTypeReq implements Serializable {
    private static final long serialVersionUID = -1982136632632707979L;
    @HttpApiDocClassDefine(value = "处置类型主键", required = false, description = "处置类型主键")
    private Integer id;

    @HttpApiDocClassDefine(value = "处置类型编码", required = false, description = "处置类型编码")
    @NotBlank(message = "处置类型编码不能为空")
    private String recordNo;

    @HttpApiDocClassDefine(value = "类型名称", required = false, description = "类型名称")
    private String recordName;

    @HttpApiDocClassDefine(value = "状态", required = false, description = "记录状态")
    @NotNull(message = "状态不能为空")
    private Integer recordStatus;

    @HttpApiDocClassDefine(value = "业务分类", required = false, description = "业务分类")
    private String businessCate;

    @HttpApiDocClassDefine(value = "处置类型", required = false, description = "处置类型：transfer-转让处置，normal-正常损毁报废，disasters-自然灾害损毁报废，scan_lose-盘亏，lost-丢失，other-其他 dispose_type")
    @NotBlank(message = "处置类型不能为空")
    private String disposeType;

    @HttpApiDocClassDefine(value = "备注", required = false, description = "备注")
    private String remark;

    @HttpApiDocClassDefine(value = "业务线", required = false, description = "业务线")
    @NotBlank(message = "业务线不能为空")
    private String businessLine;
}
