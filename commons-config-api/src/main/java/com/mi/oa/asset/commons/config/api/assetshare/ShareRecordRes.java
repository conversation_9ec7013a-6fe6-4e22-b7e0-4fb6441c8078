package com.mi.oa.asset.commons.config.api.assetshare;

import com.mi.oa.asset.common.model.Attach;
import com.mi.oa.asset.commons.config.api.common.ComplexRange;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-01-07 16:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareRecordRes implements Serializable {
    private static final long serialVersionUID = 4379122972135935411L;

    @HttpApiDocClassDefine(value = "共享主键", description = "共享主键")
    private Integer id;

    @HttpApiDocClassDefine(value = "业务线", description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "共享基础信息", description = "共享基础信息")
    private ShareRecordBaseRes baseInfo;

    @HttpApiDocClassDefine(value = "共享表单附件", description = "共享表单附件")
    private List<Attach> attachInfo;

    @HttpApiDocClassDefine(value = "共享资产更多筛选条件", description = "共享资产更多筛选条件")
    private List<ComplexRange> complexRanges;
}
