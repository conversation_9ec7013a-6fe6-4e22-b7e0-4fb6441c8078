package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GlobalImportReq implements Serializable {

    @HttpApiDocClassDefine(value = "管理类型", required = false, description = "管理类型")
    private Integer manageType;

    @HttpApiDocClassDefine(value = "业务线", required = true, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "导入id", required = true, description = "导入id")
    private List<Integer> ids;
}
