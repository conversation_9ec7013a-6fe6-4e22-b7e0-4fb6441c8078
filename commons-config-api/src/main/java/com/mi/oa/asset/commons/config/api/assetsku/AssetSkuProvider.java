package com.mi.oa.asset.commons.config.api.assetsku;

import com.xiaomi.mit.api.PageData;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/10/9 14:50
 */

public interface AssetSkuProvider {

    Integer saveAssetSku(SaveAssetSkuReq req);

    void deleteAssetSku(List<Integer> ids);

    void deleteAssetSku(DelAssetSkuReq req);

    PageData<AssetSkuRes> getAssetSkuPageData(List<String> businessLineCodes, String keyword, Integer pageNum, Integer pageSize, Boolean disabled, Boolean containMiGoods);

    PageData<AssetSkuRes> getAssetSkuPageData(Integer cateId, List<String> businessLineCodes, String keyword, Integer pageNum, Integer pageSize, Boolean disabled, Boolean containMiGoods);

    AssetSkuRes getAssetSku(Integer skuId);

    AssetSkuRes getAssetSkuBySkuCode(String skuCode);

    AssetSkuRes getAssetSkuBySkuCode(String businessLine, String skuCode);

    List<AssetSkuRes> listBySkuCodes(List<String> skuCodes);

    List<AssetSkuRes> listBySkuCodes(String businessLine, List<String> skuCodes);

    void deleteAssetSkuManage(Integer skuId, Integer manageId);

    /**
     * 批量导入物料
     * @param file
     */
    void importSku(MultipartFile file, HttpServletResponse response);
    /**
     * 批量更新物料
     * @param file
     */
    void updateImportSku(MultipartFile file, HttpServletResponse response);

    /**
     * 导出物料
     * @param response
     * @param req
     */
    void exportSku(HttpServletResponse response, AssetSkuExportReq req);

    /**
     * 推送 MDM 创建物料
     * @return
     */
    void createPurchaseItem(List<String> skuCode) throws Exception;

    /**
     * 共享物料
     * @param req
     */
    void sharedBusinessLine(SharedBusinessLineReq req);
}
