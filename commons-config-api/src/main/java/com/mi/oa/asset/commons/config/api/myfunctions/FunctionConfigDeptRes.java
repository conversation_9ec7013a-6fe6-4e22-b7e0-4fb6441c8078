package com.mi.oa.asset.commons.config.api.myfunctions;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/13 15:37
 **/
@Data
public class FunctionConfigDeptRes implements Serializable {
    private static final long serialVersionUID = -5909842819303051594L;

    /**
     * 配置明细id
     */
    private Integer configItemId;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;
}
