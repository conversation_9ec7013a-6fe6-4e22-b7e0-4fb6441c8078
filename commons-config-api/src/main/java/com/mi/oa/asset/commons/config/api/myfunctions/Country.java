package com.mi.oa.asset.commons.config.api.myfunctions;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/28 16:49
 **/
@Data
public class Country implements Serializable {

    private static final long serialVersionUID = 5636803358303392185L;

    @HttpApiDocClassDefine(value = "国家或地区编码", required = true)
    @NotBlank(message = "国家或地区编码不能为空")
    private String country;

    @HttpApiDocClassDefine(value = "国家或地区名称", required = true)
    private String countryName;
}
