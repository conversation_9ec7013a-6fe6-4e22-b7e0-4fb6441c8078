package com.mi.oa.asset.commons.config.api.businessline.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/16 19:58
 */

@Getter
public enum AllotConfigDataRage {

    BIZ_ROLE("biz_role", "限定业务角色"),
    USE_DEPT("use_dept", "限定使用部门"),
    MANAGE_DEPT("manage_dept", "限定管理部门"),
    ALL("all", "全部"),
    ;

    private final String code;

    private final String desc;

    AllotConfigDataRage(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // getByCode方法
    public static AllotConfigDataRage getByCode(String code) {
        for (AllotConfigDataRage value : AllotConfigDataRage.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
