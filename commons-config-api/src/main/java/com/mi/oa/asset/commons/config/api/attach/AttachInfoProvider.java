package com.mi.oa.asset.commons.config.api.attach;

import java.util.List;

/**
 * 附件表增删改查接口
 *
 * @autho zhan
 * @date 2023-10-26 15:21
 */
public interface AttachInfoProvider {

    void batchSaveAttach(List<AttachInfoReq> attachInfoReqList);

    void deleteAttach(Integer recordId, String recordType);

    void deleteAttach(Integer recordId, List<String> recordType);

    void batchDeleteAttach(List<Integer> recordIds, String recordType);

    void deleteAttach(Integer recordId, List<String> attachFileNames, String recordType);

    void deleteById(List<Integer> idList);

    List<AttachInfoRes> getAttachList(Integer recordId, String recordType);

    List<AttachInfoRes> listByTypes(Integer recordId, List<String> recordTypes);

    List<BatchAttachRes> getBatchAttach(List<Integer> recordIds, String recordType);
}
