package com.mi.oa.asset.commons.config.api.auth;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/21 18:45
 * @description
 */
@Data
public class ResourceDimensionRemoteResp {
    /**
     * 树状资源列表
     */
    private List<ResourceDto> resourceTreeDtoList;

    /**
     * 当前页
     */
    private long pageNum;

    /**
     * 总页数
     */
    private long pageTotal;

    /**
     * 页面容量
     */
    private long pageSize;

    /**
     * 总数据数
     */
    private long totalSize;
}
