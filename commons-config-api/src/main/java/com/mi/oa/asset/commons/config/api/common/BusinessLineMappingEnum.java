package com.mi.oa.asset.commons.config.api.common;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务线映射枚举
 *
 * <AUTHOR>
 * @date 2023/10/12 10:54
 * 该枚举不再使用，后续删除
 */

@Deprecated
@Getter
public enum BusinessLineMappingEnum {

    /**
     * 行政资产
     */
    ADM_EMP("adm_emp", "员工资产", "adm", "行政资产"),
    ADM_PUB("adm_pub", "公共资产", "adm", "行政资产"),
    /**
     * 工程机
     */
    PT_MOBILE("pt_mobile", "手机", "proto", "工程机"),
    PT_TV("pt_tv", "电视", "proto", "工程机"),
    PT_WEAR("pt_wear", "可穿戴", "proto", "工程机"),
    PT_MODEL("pt_model", "售后样机", "proto", "工程机"),
    PT_ECO("pt_eco", "生态链", "proto", "工程机"),
    PT_COMPUTER("pt_computer", "笔记本电脑", "proto", "工程机"),
    PT_ROBOT("pt_robot", "机器人", "proto", "工程机"),
    CAR("car", "汽车资产", "car_asset", "汽车资产"),
    LAB("lab", "实验室设备", "lab_asset", "实验室设备"),
    OEM("oem", "代工厂设备", "oem_asset", "代工厂设备"),
    FACTORY("f_mobile", "昌平工厂", "factory", "昌平工厂"),
    IMMOVABLE("immovable", "集团不动产", "immovable_asset", "集团不动产"),
    GROUP("group", "集团资产", "group_asset", "集团资产"),
    COMPETE_MOBILE("compete_mobile", "手机竞品机", "compete", "竞品机"),
    // 以下业务线上线前需要删除
    GROUP_EMP("adm_emp", "员工资产", "group_asset", "集团资产"),
    GROUP_PUB("adm_pub", "公共资产", "group_asset", "集团资产"),
    GROUP_MOBILE("pt_mobile", "手机", "group_asset", "集团资产"),
    GROUP_TV("pt_tv", "电视", "group_asset", "集团资产"),
    GROUP_WEAR("pt_wear", "可穿戴", "group_asset", "集团资产"),
    GROUP_MODEL("pt_model", "售后样机", "group_asset", "集团资产"),
    GROUP_ECO("pt_eco", "生态链", "group_asset", "集团资产"),
    GROUP_COMPUTER("pt_computer", "笔记本电脑", "group_asset", "集团资产"),
    GROUP_ROBOT("pt_robot", "机器人", "group_asset", "集团资产"),
    GROUP_CAR("car", "汽车资产", "group_asset", "集团资产"),
    GROUP_LAB("lab", "实验室设备", "group_asset", "集团资产"),
    GROUP_OEM("oem", "代工厂设备", "group_asset", "集团资产"),
    GROUP_FACTORY("f_mobile", "手机工厂", "group_asset", "集团资产"),
    GROUP_IMMOVABLE("immovable", "集团不动产", "group_asset", "集团资产"),
    GROUP_COMPETE_MOBILE("compete_mobile", "手机竞品机", "group_asset", "集团资产"),
    ;

    private String code;

    private String desc;

    private String manageLine;

    private String manageLineDesc;

    BusinessLineMappingEnum(String code, String desc, String manageLine, String manageLineDesc) {
        this.code = code;
        this.desc = desc;
        this.manageLine = manageLine;
        this.manageLineDesc = manageLineDesc;
    }

    public static String getManageLine(String code) {
        for (BusinessLineMappingEnum mappingEnum : BusinessLineMappingEnum.values()) {
            if (mappingEnum.getManageLine().equals(code)) {
                return mappingEnum.getManageLine();
            }
        }
        return null;
    }

    public static String getManageLineDesc(String code) {
        for (BusinessLineMappingEnum mappingEnum : BusinessLineMappingEnum.values()) {
            if (mappingEnum.getManageLine().equals(code)) {
                return mappingEnum.getManageLineDesc();
            }
        }
        return null;
    }

    public static String getBusinessLineDesc(String code) {
        for (BusinessLineMappingEnum mappingEnum : BusinessLineMappingEnum.values()) {
            if (mappingEnum.getCode().equals(code)) {
                return mappingEnum.getDesc();
            }
        }
        return null;
    }

    public static List<String> listBusinessLineByManageLine(String manageLine) {
        return Arrays.stream(BusinessLineMappingEnum.values())
                .filter(f -> f.getManageLine().equals(manageLine))
                .map(BusinessLineMappingEnum::getCode).collect(Collectors.toList());
    }

    public static String getManageLineByBusinessLine(String code) {
        for (BusinessLineMappingEnum mappingEnum : BusinessLineMappingEnum.values()) {
            if (mappingEnum.getCode().equals(code)) {
                return mappingEnum.getManageLine();
            }
        }
        return null;
    }
}
