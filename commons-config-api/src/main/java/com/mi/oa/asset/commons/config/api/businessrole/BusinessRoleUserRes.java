package com.mi.oa.asset.commons.config.api.businessrole;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/19 15:32
 */

@Data
public class BusinessRoleUserRes implements Serializable {

    private static final long serialVersionUID = 722464880891302246L;
    /**
     * 角色编码 role_code
     */
    private String roleCode;

    /**
     * 角色名称 role_name
     */
    private String roleName;

    /**
     * 角色描述 role_desc
     */
    private String roleDesc;

    /**
     * 账号 user_code
     */
    private String userCode;

    /**
     * 姓名 user_name
     */
    private String userName;

    /**
     * 组织结构编码
     */
    private String orgCode;

}
