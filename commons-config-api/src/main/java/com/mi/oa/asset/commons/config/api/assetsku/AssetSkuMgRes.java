package com.mi.oa.asset.commons.config.api.assetsku;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/19 16:43
 */

@Data
public class AssetSkuMgRes implements Serializable {

    private static final long serialVersionUID = -3498364901995276050L;

    @HttpApiDocClassDefine(value = "id", required = false, description = "管理线主键")
    private Integer id;

    @HttpApiDocClassDefine(value = "skuId", required = false, description = "skuId")
    private Integer skuId;

    @HttpApiDocClassDefine(value = "业务线", required = false, description = "业务线")
    private String businessLine;

    @HttpApiDocClassDefine(value = "业务线名称", required = false, description = "业务线名称")
    private String businessLineName;

    @HttpApiDocClassDefine(value = "分类", required = true, description = "分类")
    private Integer cateId;

    @HttpApiDocClassDefine(value = "分类", required = true, description = "分类")
    private String cateCode;
    @HttpApiDocClassDefine(value = "分类名称")
    private String cateName;
    @HttpApiDocClassDefine(value = "管理模式", required = false, description = "asset_account：资产台账管理 asset_stock：资产库存管理 stock：库存管理")
    private String mgModel;
    @HttpApiDocClassDefine(value = "管理模式", required = false, description = "asset_account：资产台账管理 asset_stock：资产库存管理 stock：库存管理")
    private String mgModelName;
    @HttpApiDocClassDefine(value = "串号管理", required = false, description = "sn：SN管理  multiple：一卡多物管理")
    private String serialMg;

    @HttpApiDocClassDefine(value = "串号管理", required = false, description = "sn：SN管理  multiple：一卡多物管理")
    private String serialMgName;
    @HttpApiDocClassDefine(value = "存货成本核算", required = false, description = "moving_avg:移动加权平均")
    private String costing;
    @HttpApiDocClassDefine(value = "存货成本核算", required = false, description = "moving_avg:移动加权平均")
    private String costingName;
    @HttpApiDocClassDefine(value = "安全库存", required = false, description = "安全库存")
    private BigDecimal secureQuantity;

    @HttpApiDocClassDefine(value = "最高库存", required = false, description = "最高库存")
    private BigDecimal highestQuantity;

    @HttpApiDocClassDefine(value = "最低库存", required = false, description = "最低库存")
    private BigDecimal miniQuantity;

    @HttpApiDocClassDefine(value = "物料类型", required = false, description = "物料类型")
    private String materialType;

    @HttpApiDocClassDefine(value = "物料类型名称")
    private String materialTypeName;

}
