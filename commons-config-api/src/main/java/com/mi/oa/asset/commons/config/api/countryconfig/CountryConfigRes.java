package com.mi.oa.asset.commons.config.api.countryconfig;

import com.mi.oa.asset.commons.config.api.countrybusinessLine.CountryBusinessLineRes;
import com.mi.oa.asset.commons.config.api.countrycurrency.CountryCurrencyRes;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Data
public class CountryConfigRes {

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 国家/地区代码（3位）
     */
    private String countryCodeAlphaThree;

    /**
     * 国家/地区代码（2位）
     */
    private String countryCodeAlphaTwo;

    /**
     * 国家/地区名称
     */
    private String countryName;

    /**
     * 国家/地区名称（英文）
     */
    private String countryEnglishName;

    /**
     * 所属区域表ID
     */
    private Integer regionId;

    /**
     * 地址库id（地址库中国家/地区 一级地区 对应的id
     */
    private Integer provinceId;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 默认语言
     */
    private String defaultLanguage;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 业务线
     */
    private List<String> businessLineResList;

    /**
     * 默认的货币信息
     */
    private CountryCurrencyRes defaultCurrency;
}
