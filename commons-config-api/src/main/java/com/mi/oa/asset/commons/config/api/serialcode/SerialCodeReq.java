package com.mi.oa.asset.commons.config.api.serialcode;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-07-11 16:07
 */
@Data
public class SerialCodeReq implements Serializable {

    private static final long serialVersionUID = 5225879637212019574L;

    @HttpApiDocClassDefine(value = "前缀")
    private String prefix;

    @HttpApiDocClassDefine(value = "补0位数")
    private int zeroPadSize;

    @HttpApiDocClassDefine(value = "生成数量")
    private int counts;

    @HttpApiDocClassDefine(value = "索引编码, 如果使用相同的前缀，可定义唯一索引编码，单独计数")
    private String indexCode;
}
