package com.mi.oa.asset.commons.config.api.assetshare;

import com.mi.oa.asset.common.model.ExtraFieldInfo;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-08 11:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomShareListRes implements Serializable {
    private static final long serialVersionUID = 8683099376995860102L;

    @HttpApiDocClassDefine(value = "主键", description = "不为空时更新")
    private Integer id;

    @HttpApiDocClassDefine(value = "共享主键", description = "共享主键")
    private Integer shareId;

    @HttpApiDocClassDefine(value = "资产台账主键", description = "资产台账主键")
    private Integer assetId;

    @HttpApiDocClassDefine(value = "资产编号", description = "资产编号")
    private String assetCode;

    @HttpApiDocClassDefine(value = "资产名称", description = "资产名称")
    private String assetName;

    @HttpApiDocClassDefine(value = "责任人账号", description = "责任人账号")
    private String userCode;

    @HttpApiDocClassDefine(value = "责任人名称", description = "责任人名称")
    private String userName;

    @HttpApiDocClassDefine(value = "原值", description = "原值")
    private BigDecimal originValue;

    @HttpApiDocClassDefine(value = "净值", description = "净值")
    private BigDecimal netValue;

    @HttpApiDocClassDefine(value = "台账数量", description = "台账数量")
    private Integer assetQuantity;

    @HttpApiDocClassDefine(value = "共享数量", description = "共享数量")
    private Integer shareQuantity;

    @HttpApiDocClassDefine(value = "扩展属性", description = "扩展属性")
    private List<ExtraFieldInfo> extraFields = new ArrayList<>(); // NOSONAR
}
