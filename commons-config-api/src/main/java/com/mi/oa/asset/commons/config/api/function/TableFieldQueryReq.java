package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mit.api.PageRequest;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TableFieldQueryReq extends PageRequest {
    @HttpApiDocClassDefine(value = "模糊查询key", required = false, description = "模糊查询key")
    private String key;

    @HttpApiDocClassDefine(value = "表id", required = false, description = "表id")
    private String tableId;
}
