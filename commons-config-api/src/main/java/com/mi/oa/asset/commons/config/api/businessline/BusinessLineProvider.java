package com.mi.oa.asset.commons.config.api.businessline;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/09 18:42
 */
public interface BusinessLineProvider {

    BusinessLineRes getBusinessLineDetail(Integer businessLineId);

    BusinessLineRes getBusinessLineDetail(String businessLine);

    // 台账使用，勿删
    List<BusinessLineRes> batchGetBusinessLine(List<String> businessLines);

    void businessLineSave(BusinessLineReq businessLineReq);

    List<AllotConfigRes> allotConfigList(Integer businessLineId);

    List<AllotConfigRes> allotConfigList(String businessLine);

    List<BusinessLineRes> getBusinessLineList(String language);

    List<ManageLineRes> getManageLineList(String language);

    ManageLineRes getManageLine(String manageLine);

    List<ManageLineRes> getManageLines(List<String> manageLines);
}
