package com.mi.oa.asset.commons.config.api.menuresource;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 菜单功能资源
 *
 * <AUTHOR>
 * @date 2023/9/18 10:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MenuResourceRes implements Serializable {

    private static final long serialVersionUID = -5994196942517698728L;

    @HttpApiDocClassDefine(value = "菜单资源")
    private List<MenuRes> menuResource;

    @HttpApiDocClassDefine(value = "功能资源")
    private List<ResourceRes> permissionResource;

    @HttpApiDocClassDefine(value = "业务线资源")
    private List<MenuBusinessLineRes> businessLineResource;

}
