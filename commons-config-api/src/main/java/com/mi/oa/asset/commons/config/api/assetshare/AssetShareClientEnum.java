package com.mi.oa.asset.commons.config.api.assetshare;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 共享资产应用的客户端
 */
@Getter
@AllArgsConstructor
public enum AssetShareClientEnum {

    APP_EMP("app_emp", "员工端APP"),
    APP_MANAGE("app_manage", "管理端APP"),
    EAM_EMP("client", "EAM员工端"),
    EAM_MANAGE("admin", "EAM管理端"),
    ;
    private String code;

    private String desc;

    /**
     * 根据编码获取枚举值
     */
    public static AssetShareClientEnum getByCode(String code) {
        for (AssetShareClientEnum client : AssetShareClientEnum.values()) {
            if (client.getCode().equals(code)) {
                return client;
            }
        }
        return null;
    }

    public static String getDescByCode(String code) {
        for (AssetShareClientEnum client : AssetShareClientEnum.values()) {
            if (client.getCode().equals(code)) {
                return client.getDesc();
            }
        }
        return null;
    }
}
