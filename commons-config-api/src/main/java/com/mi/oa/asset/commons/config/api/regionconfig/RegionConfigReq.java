package com.mi.oa.asset.commons.config.api.regionconfig;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class RegionConfigReq {

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 区域名称（英文）
     */
    private String regionEnglishName;

    /**
     * 排序序号
     */
    private Integer regionSortOrder;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 地区下辖的国家列表数据id
     */
    private List<Integer> countryConfigIdList;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建人用户名
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新人用户名
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}
