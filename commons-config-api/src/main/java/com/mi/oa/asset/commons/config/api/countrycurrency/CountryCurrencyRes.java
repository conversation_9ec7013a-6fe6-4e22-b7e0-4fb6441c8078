package com.mi.oa.asset.commons.config.api.countrycurrency;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Data
public class CountryCurrencyRes {

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 币种名称
     */
    private String currencyName;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 默认币种
     */
    private Integer defaultCurrency;

    /**
     * 国家数据表id
     */
    private Integer countryConfigId;

    /**
     * 国家/地区代码（3位）
     */
    private String countryCodeAlphaThree;

    /**
     * 国家/地区代码（2位）
     */
    private String countryCodeAlphaTwo;

    /**
     * 国家/地区
     */
    private String countryName;

    /**
     * 限制小数位
     */
    private Integer isLimitDecimal;

    /**
     * 是否删除
     */
    private Integer isDeleted;

}
