package com.mi.oa.asset.commons.config.api.assetshare;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 通过共享清单执行调拨的资产查询
 *
 * <AUTHOR>
 * @date 2025-02-27 14:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareAssetAllotRes implements Serializable {
    private static final long serialVersionUID = 297973250478169090L;

    @HttpApiDocClassDefine(value = "资产ID", description = "资产ID")
    private Integer assetId;

    @HttpApiDocClassDefine(value = "资产编号", description = "资产编号")
    private String assetCode;

    @HttpApiDocClassDefine(value = "资产名称", description = "资产名称")
    private String assetName;

    @HttpApiDocClassDefine(value = "使用状态", description = "使用状态，在库-in_lib，在用-in_use，闲置-idle，维修-repair，借出-lend，异动中-moving，已报废-scraped，已处置-disposed，已封存-sealed")
    private String useStatus;

    @HttpApiDocClassDefine(value = "品牌", description = "品牌")
    private String brand;

    @HttpApiDocClassDefine(value = "型号", description = "型号")
    private String model;

    @HttpApiDocClassDefine(value = "可用调拨数量", description = "可用调拨数量")
    private Integer availableQuantity;

    @HttpApiDocClassDefine(value = "本次调拨数量", description = "本次调拨数量")
    private Integer currUseQuantity;

    @HttpApiDocClassDefine(value = "原值(元)", description = "原值(元)")
    private BigDecimal originValue;

    @HttpApiDocClassDefine(value = "净值(元)", description = "净值(元)")
    private BigDecimal netValue;

    @HttpApiDocClassDefine(value = "调出人账号", description = "调出人账号")
    private String userName;

    @HttpApiDocClassDefine(value = "调出人名称", description = "调出人名称")
    private String userDisplayName;

    @HttpApiDocClassDefine(value = "调出使用部门编码", description = "调出使用部门编码")
    private String useDeptCode;

    @HttpApiDocClassDefine(value = "调出使用部门名称", description = "调出使用部门名称")
    private String useDeptName;

    @HttpApiDocClassDefine(value = "调出管理部门编码", description = "调出管理部门编码")
    private String manageDeptCode;

    @HttpApiDocClassDefine(value = "调出管理部门名称", description = "调出管理部门名称")
    private String manageDeptName;

    @HttpApiDocClassDefine(value = "调出公司代码", description = "调出公司代码")
    private String companyCode;

    @HttpApiDocClassDefine(value = "调出公司名称", description = "调出公司名称")
    private String companyName;

    @HttpApiDocClassDefine(value = "调出成本中心", description = "调出成本中心")
    private String centerCode;

    @HttpApiDocClassDefine(value = "原位置编码", description = "原位置编码")
    private String locationCode;

    @HttpApiDocClassDefine(value = "原位置名称", description = "原位置名称")
    private String locationName;

    @HttpApiDocClassDefine(value = "原详细地址", description = "原详细地址")
    private String address;

    @HttpApiDocClassDefine(value = "SN", description = "SN")
    private String sn;
}
