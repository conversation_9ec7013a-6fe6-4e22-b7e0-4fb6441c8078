package com.mi.oa.asset.commons.config.api.businessline;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/07/09 18:42
 */
import java.util.Map;

@Data
public class BusinessLineRes implements Serializable {
    private static final long serialVersionUID = 8790166486900134535L;

    @HttpApiDocClassDefine(value = "业务线Id")
    private Integer businessLineId;

    @HttpApiDocClassDefine(value = "业务线编码")
    private String businessLine;

    @HttpApiDocClassDefine(value = "业务线名称")
    private String businessLineName;

    @HttpApiDocClassDefine(value = "业务线英文名称")
    private String businessLineNameEn;

    @HttpApiDocClassDefine(value = "管理线编码")
    private String manageLineCode;

    @HttpApiDocClassDefine(value = "管理线名称")
    private String manageLineName;

    @HttpApiDocClassDefine(value = "使用部门名称展示方式")
    private String useDeptNameShowWay;

    @HttpApiDocClassDefine(value = "管理部门名称展示方式")
    private String manageDeptNameShowWay;

    @HttpApiDocClassDefine(value = "是否生效")
    private Boolean isEffective;

    @HttpApiDocClassDefine(value = "查询管理部门是否需要传使用部门")
    private Boolean isMustUseDept;

    @HttpApiDocClassDefine(value = "创建人账号")
    private String createUser;

    @HttpApiDocClassDefine(value = "创建人姓名")
    private String createUserName;

    @HttpApiDocClassDefine(value = "创建时间")
    private Date createTime;

    @HttpApiDocClassDefine(value = "委托记账主体是否变动")
    private Boolean isModifyCompany;

    @HttpApiDocClassDefine(value = "资产库存管理配置", required = false)
    private HashMap<String, String> configs;

}
