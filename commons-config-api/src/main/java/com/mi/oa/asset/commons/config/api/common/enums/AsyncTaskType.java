package com.mi.oa.asset.commons.config.api.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/12 9:33
 */

@Getter
@AllArgsConstructor
public enum AsyncTaskType {

    ASSET_ACCOUNT_IMPORT("asset_account_import", "资产台账导入"),
    ACCOUNT_DIFF_EXPORT("account_diff_export", "台账差异导出"),
    ;

    private final String code;
    private final String desc;

    public static AsyncTaskType getByCode(String code) {
        for (AsyncTaskType value : AsyncTaskType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
