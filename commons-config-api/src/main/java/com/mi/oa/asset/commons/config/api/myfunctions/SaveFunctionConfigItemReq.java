package com.mi.oa.asset.commons.config.api.myfunctions;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 首页功能配置-业务线规则
 * @Date 2025/4/29 19:09
 **/
@Data
public class SaveFunctionConfigItemReq {

    /**
     * 明细id
     */
    @HttpApiDocClassDefine(value = "明细id")
    private Integer itemId;

    /**
     * 配置id
     */
    @HttpApiDocClassDefine(value = "配置id")
    private Integer configId;

    /**
     * 业务线 business_line
     */
    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;

    /**
     * 业务线名称 business_line_name
     */
    @HttpApiDocClassDefine(value = "业务线名称")
    private String businessLineName;

    /**
     * 授权范围 auth_type
     */
    @NotNull(message = "授权范围不能为空")
    @HttpApiDocClassDefine(value = "授权范围")
    private Integer authType;

    /**
     * 授权部门
     */
    @HttpApiDocClassDefine(value = "授权部门")
    private List<SaveFunctionConfigDeptReq> authDeptList;

    /**
     * 启用的国家或地区 country
     */
    @NotEmpty(message = "启用的国家或地区不能为空")
    @HttpApiDocClassDefine(value = "启用的国家或地区")
    private List<Country> countries;

}
