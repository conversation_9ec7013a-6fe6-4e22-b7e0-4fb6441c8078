package com.mi.oa.asset.commons.config.api.serialcode;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/9/19 14:16
 * @Desc 生成序列号
 * prefix: 前缀
 * rollingType: 滚动类型, y: 年, ym: 年月, ymd: 年月日, 不传表示无限增长
 * zeroPadSize: 补0位数
 * counts: 生成数量
 * indexCode: 索引编码, 如果使用相同的前缀，可定义唯一索引编码，单独计数
 */

public interface SerialCodeProvider {

    List<String> genSerialCodes(String prefix, RollingType rollingType, int zeroPadSize, int counts, String indexCode);

    List<String> genSerialCodesWithLock(String prefix, RollingType rollingType, int zeroPadSize, int counts, String indexCode);

    String genSerialCode(String prefix, RollingType rollingType, int zeroPadSize);

    List<String> genSerialCodes(String prefix, int zeroPadSize, int counts);

    String genSerialCode(String prefix, int zeroPadSize);

    List<String> genSerialCodesWithIndex(String prefix, int zeroPadSize, int counts, String indexCode);

    String genSerialCodeWithIndex(String prefix, int zeroPadSize, String indexCode);

    Map<String, List<String>> batchGenSerialCodes(List<SerialCodeReq> serialCodeReqs);
}
