package com.mi.oa.asset.commons.config.api.function;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FunctionRoleRes implements Serializable {
    @HttpApiDocClassDefine(value = "角色编码", required = false, description = "角色编码")
    private String code;

    @HttpApiDocClassDefine(value = "角色名称", required = false, description = "角色名称")
    private String roleName;
}
