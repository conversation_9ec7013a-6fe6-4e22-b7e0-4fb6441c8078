package com.mi.oa.asset.commons.config.api.countryconfig;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Data
@NoArgsConstructor
public class CountryConfigReq {
    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 国家/地区代码（3位）
     */
    @Size(min = 0, max = 3, message = "The country three-alpha code must have a length of 3.")
    private String countryCodeAlphaThree;

    /**
     * 国家/地区代码（2位）
     */
    @Size(min = 0, max = 2, message = "The country two-alpha code must have a length of 2.")
    private String countryCodeAlphaTwo;

    /**
     * 国家/地区名称
     */
    private String countryName;

    /**
     * 国家/地区名称（英文）
     */
    private String countryEnglishName;

    /**
     * 所属区域表ID
     */
    private Integer regionId;

    /**
     * 地址库id（地址库中国家/地区 一级地区 对应的id
     */
    private Integer provinceId;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 默认语言
     */
    private String defaultLanguage;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 业务线
     */
    private List<String> businessLineResList;

}
