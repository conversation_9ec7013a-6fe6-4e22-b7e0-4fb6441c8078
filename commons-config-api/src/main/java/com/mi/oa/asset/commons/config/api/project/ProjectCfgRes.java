package com.mi.oa.asset.commons.config.api.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.util.Date;

/**
 * 项目配置表
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class ProjectCfgRes {
    /**
     * ID
     */
    @HttpApiDocClassDefine(value = "ID")
    private Integer id;
    /**
     * 执行状态 待创建init 已创建created
     */
    @HttpApiDocClassDefine(value = "执行状态 待创建init 已创建created")
    private String recordStatus;
    /**
     * 业务线
     */
    @HttpApiDocClassDefine(value = "业务线")
    private String businessLine;
    /**
     * 项目编号
     */
    @HttpApiDocClassDefine(value = "项目编号")
    private String projectCode;
    /**
     * 项目名称
     */
    @HttpApiDocClassDefine(value = "项目名称")
    private String projectName;
    /**
     * 父项目编号
     */
    @HttpApiDocClassDefine(value = "父项目编号")
    private String parentProjectCode;
    /**
     * PM账户
     */
    @HttpApiDocClassDefine(value = "PM账户")
    private String pmUserCode;
    /**
     * PM工号
     */
    @HttpApiDocClassDefine(value = "PM工号")
    private String pmEmpCode;
    /**
     * PM
     */
    @HttpApiDocClassDefine(value = "PM")
    private String pmUserName;
    /**
     * 是否JIS之前机型
     */
    @HttpApiDocClassDefine(value = "是否JIS之前机型")
    private Integer beforeJis;
    /**
     * 项目阶段
     */
    @HttpApiDocClassDefine(value = "项目阶段")
    private String projectPhase;
    /**
     * 项目类型
     */
    @HttpApiDocClassDefine(value = "项目类型 预研项目-pre")
    private String projectType;
    /**
     * 预研编号
     */
    @HttpApiDocClassDefine(value = "预研编号")
    private String preProjectCode;
    /**
     * 项目等级
     */
    @HttpApiDocClassDefine(value = "项目等级")
    private String projectLevel;
    /**
     * 产品类别
     */
    @HttpApiDocClassDefine(value = "产品类别 OEM/ODM/OBM")
    private String productType;
    /**
     * 试产工厂
     */
    @HttpApiDocClassDefine(value = "试产工厂")
    private String factoryName;
    /**
     * 试产工厂编码
     */
    @HttpApiDocClassDefine(value = "试产工厂编码")
    private String factoryCode;
    /**
     * 试产日期
     */
    @HttpApiDocClassDefine(value = "试产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date trialDate;
    /**
     * 是否上市
     */
    @HttpApiDocClassDefine(value = "是否上市")
    private Integer isSale;
    /**
     * 上市日期
     */
    @HttpApiDocClassDefine(value = "上市日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date saleDate;
    /**
     * 项目信息来源 R平台 手动创建 UPC平台
     */
    @HttpApiDocClassDefine(value = "项目信息来源 R平台-R 手动创建-manual  UPC平台-UPC")
    private String source;
    /**
     * 是否生效
     */
    @HttpApiDocClassDefine(value = "是否生效")
    private Integer enabled;
    @HttpApiDocClassDefine(value = "创建人用户名", description = "创建人用户名")
    private String createUser;

    @HttpApiDocClassDefine(value = "创建人姓名", description = "创建人姓名")
    private String createUserName;

    @HttpApiDocClassDefine(value = "更新人用户名", description = "更新人用户名")
    private String updateUser;

    @HttpApiDocClassDefine(value = "更新人姓名", description = "更新人姓名")
    private String updateUserName;

    @HttpApiDocClassDefine(value = "创建时间", description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @HttpApiDocClassDefine(value = "更新时间", description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}