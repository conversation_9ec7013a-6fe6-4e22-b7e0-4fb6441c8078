#!/bin/sh

# Usage: mitag.sh [ENV [TAG]]
#   ENV: environment name. accept values: 'test', 'pre', 'prod'.
#        default value:
#          * 'prod' if given git commit is in master branch and associates with pre tag;
#          * 'pre'  if given git commit is in master branch and associates with test tag;
#          * 'test' if given git commit has no test tag.
#   TAG: existing git tag name. for example 'test-20220310-00'.
#        default value: current branch HEAD.

### START_MIT ###
git archive --remote=********************:mit/infra/ci-templates.git feature/unify-docker-image mitag.sh | tar -xOf - | sh -s "$@"

